import React, { useState, useEffect, useCallback } from 'react';
import Form from 'react-bootstrap/Form';
import ListGroup from 'react-bootstrap/ListGroup';
import Row from 'react-bootstrap/Row';
import <PERSON>ton from 'react-bootstrap/Button';
import Modal from 'react-bootstrap/Modal';

import '../ManageUsers/ManageUsers.scss';

/**
* Creates a button for managing a user's status. This will show a modal with a select to choose a new
* status for the user.
* 
* @param {Object} user The user
* @param {[string]} statuses The array of statuses in the group
* @param {string} btnIcon The icon for the button
* @param {string} btnColor The color of the icon
*/
const StatusButton = (props) => {

    const [user, setUser] = useState({});
    const [statuses, setStatuses] = useState([]);
    const [icon, setIcon] = useState("");
    //const [color, setColor] = useState("");

    useEffect(() => {
        try {
            if (props.user) { setUser(props.user); }
            else { console.error("No user passed to user status button"); }
            if (props.statuses) { setStatuses(props.statuses); }
            else { console.error("No group statuses array passed to user status button"); }
            if (props.btnIcon) { setIcon(props.btnIcon); }
            else { setIcon("fas fa-question-square"); }
            /*if (props.btnColor) { setColor(props.btnColor); }
            else { setColor("gray"); }*/
        } catch(e) {}
    },[props]); 

    const name =`${user.first_name} ${user.last_name}`;
    const [userStatus, setUserStatus] = useState(user.status_id);

    const [selectedStatus, setSelectedStatus] = useState(user.status);
    const [showStatusModal, setShowModal] = useState(false);
    
    const selectedStatusHandler = (e) => {setSelectedStatus(e.target.value)};
    const closeModalHandler = () => {setShowModal(false)};
    const openModalHandler = () => {setShowModal(true)};
    
    /**
     * Calls the API to update the status of a group user.
     * 
     * @param {string} id The ID of the group user
     * @param {string} targetStatusID The ID of the desired group user status
     */
    const changeUserStatus = useCallback(
        (id) => {
        //TODO: API call here
            if (id) {
                console.log(`change status of user ${id} to ${selectedStatus}`);
                setUserStatus(selectedStatus);
                closeModalHandler();
            }
        },[selectedStatus]
    );

    useEffect(changeUserStatus, [selectedStatus, changeUserStatus]);

    return(
        <React.Fragment>
            <ListGroup.Item className="user-buttons" action href="#ChangeStatus" onClick={()=>{
                setSelectedStatus(userStatus);
                openModalHandler();
                }}>
                <i className={icon} /*style={{color:color}}*/ data-tip={"Change Status"}></i>
            </ListGroup.Item>
            <Modal show={showStatusModal} onHide={closeModalHandler} size={"lg"}>
                <Modal.Header closeButton>
                    <Modal.Title>Change Status</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Row className="role-modal-row">
                        <span style={{marginRight:'0.5rem'}}>
                            {`Change status of ${name} to`}
                        </span>
                        <Form>
                            <Form.Control required custom as="select" name="status_select" value={selectedStatus || ""} onChange={selectedStatusHandler}>
                                {statuses.map((status,i) =><option key={'status_'+i} value={status.id}>{status.name}</option>)}
                            </Form.Control>
                        </Form>
                    </Row>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeModalHandler}>
                        Cancel
                    </Button>
                    <Button variant="primary" onClick={() => changeUserStatus(user.id)}>
                        OK
                    </Button>
                </Modal.Footer>
            </Modal>
        </React.Fragment>
    );
}

export default StatusButton;