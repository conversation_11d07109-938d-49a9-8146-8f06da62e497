import React, { useState, useEffect, useCallback } from 'react';
import { useSelector,useDispatch } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import {confirm} from '../../../components/Confirmation';
import Table from '../../../components/common/Table';
import {Typeahead } from 'react-bootstrap-typeahead';
import ReactTooltip from 'react-tooltip';

import * as actions from '../../../store/actions';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import '../../../components/common/Table/Table.scss';

import Users from '../../../api/Users';

export const AssignUsers = (props) => {
    const dispatch = useDispatch();
    const ref = React.createRef();

    const [eventUsers, setEventUsers] = useState();
    const [userSelections, setUserSelections] = useState(useSelector(state => state.map.attendees) || []);

    useEffect(() => {
        let mounted = true;

        Users.getList()
        .then(response => {
            if(mounted) {
                setEventUsers(response.data?.users);
            }
        }).catch(e => console.error(e));

        // adds the users in the event to the list
        if (props.event && props.event.users){
            setUserSelections(props.event.users);
        }

        return () => {
            mounted = false;
        }
	}, [props.event]);

    const confirmHandler = useCallback(
        (props) => {
            confirm(props.text,{
                title:"Whoa!",
                okText:"Yes",
                cancelText:"No",
                cancelButtonStyle:"light"
            }).then(result =>{
                if (result===true) props.click();
            }).catch(e => console.error(e));
        },[]
    );

    const columns = React.useMemo(
        () => [{
            id: 'table',
            columns: [
                {
                    Header: 'Full Name',
                    id: 'first_name',
                    className: "align-middle",
                    accessor: 'first_name',
                    Cell: d => d.row.original.first_name+" "+d.row.original.last_name
                },
                {
                    id: 'last_name',
                    show: false,
                    accessor: 'last_name'
                },
                {
                    Header: 'Email',
                    id: 'email',
                    accessor: 'email',
                    className: "align-middle",
                },
                {
                    Header: '',
                    id: 'action_buttons',
                    className: "align-middle",
                    disableSortBy: true,
                    accessor: d => "",
                    Cell: props => (
                        <a href="#!" onClick={() => {
                            confirmHandler({
                                text:`Are you sure you want to remove ${props.row.original.first_name} from the event?`,
                                click:()=>{
                                    dispatch(actions.removeAttendee(props.row.original.id));
                                    return props.typeAheadOnRemove(props.row.original)
                                }
                            });
                        }}>
                            <i className="far fa-times" data-tip="Remove User"></i>
                        </a> 
                    )                     
                },
            ],
        }],[confirmHandler,dispatch]
    );
    

    const renderInput = ({ inputClassName, inputRef, referenceElementRef, ...props },{ onRemove, selected }) => (
        userSelections &&
        <React.Fragment>
            <input
                {...props}
                className="form-control"
                ref={input => {
                    referenceElementRef(input);
                    inputRef(input);
                }}
                type="text"
            />
            <Table columns={columns} data={userSelections} searchBar={false} typeAheadOnRemove={onRemove} className="mt-3" />
            <ReactTooltip globalEventOff="click" effect="solid" backgroundColor="#262D33" arrowColor="#262D33" />
            {/*
            <Table striped hover style={{ marginTop: '10px' }}>
                <thead>
                    <tr>
                        <th>Full Name</th>
                        <th>Role</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {userSelections.map((option, i) => (
                        option &&
                        <tr>
                            <td>{option.first_name+" "+option.last_name}</td>
                            <td>{option.role_name}</td>
                            <td>
                                <a href="#!" onClick={() => onRemove(option)}><i className="far fa-times"></i></a>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </Table>*/}
        </React.Fragment>
    );

    return (
        <React.Fragment>
            <Row>
                <Col sm="12" lg="8">
                    <h1>Who's coming?</h1>
                    <p>
                        Select the persons that will be attending the event.
                    </p>
                </Col>
            </Row>
            <Row>
                <Col sm="12" lg="8">
                    <Typeahead
                        id="user_autocomplete"
                        labelKey={option => `${option.first_name} ${option.last_name}`}
                        multiple
                        onChange={item=>{
                            dispatch(actions.addAttendee(item));
                            return setUserSelections(item);
                        }}
                        options={eventUsers || []}
                        placeholder="Enter a user name..."
                        selected={userSelections}
                        renderInput={renderInput}
                        ref={ref}
                    />
                </Col>
            </Row>
        </React.Fragment>
    );
}