/*eslint-disable*/
/// <reference types="cypress" />
import { ProSidebarProvider } from 'react-pro-sidebar';

import SideBar from '../SideBar';
import MenuData from './Menu.json'

//collapse and toggle will not work with the component independent of everything else, as it has no where to go when it's collapsed and no button to hide it
//this can be tested in an E2E test
describe('<SideBar>',()=>{

    const menu = MenuData.data
    const interceptObject={
        data:[
            {sidebar: menu}
        ]
    }
    const userInfo={
        user:{
            profile:{
                id: 3075
            },
            menus: [
                {sidebar: menu}
            ]
        }
    }

    before(()=>{
        localStorage.setItem('user',JSON.stringify(userInfo))
    })

    beforeEach(()=>{
        cy.intercept('POST', 'api/user/menu', interceptObject).as('getMenu')
        localStorage.setItem('user',JSON.stringify({
            menus: [
                {sidebar: menu}
            ]
        }))
    })
    const functionMock = (props) => { return null }

    it('will not load sidebar for user not logged in', ()=>{
        cy.mount(
            <ProSidebarProvider>
                <SideBar logged_in={false} />
            </ProSidebarProvider>
        )
        cy.get('#side-bar-menu').should('not.be.visible')
    })

    it('will mount sidebar for user logged in', ()=>{
        cy.mount(
            <ProSidebarProvider>
                <SideBar logged_in={true} userInfo={userInfo} />
            </ProSidebarProvider>   
        )
        cy.get('#side-bar-menu').should('be.visible')
    })

    it('will check the contents of the menu', ()=>{
        cy.mount(
            <ProSidebarProvider>
                <SideBar logged_in={true} userInfo={userInfo} />
            </ProSidebarProvider>
        )
        cy.get('.ps-menu-root > :nth-child(1) > :nth-child(1) > [data-testid="ps-menu-button-test-id"]')
            .should('contain', "Collapse");
        cy.get('[data-cy="menu-item-My Profile-318"]')
            .should('contain', "My Profile").and('have.attr', 'href', '/p/profile');
        cy.get('[data-cy="menu-item-My Services-320')
            .should('contain', "My Services").and('have.attr', 'href', '/p/my/services');
        cy.get('[data-cy="sub-menu-item-Users-323')
            .should('contain', "Users");
        cy.get('[data-cy="menu-item-User Dashboard-339')
            .should('not.be.visible')
        cy.get('[data-cy="sub-menu-item-Users-323').click({force:true})
        cy.get('[data-cy="menu-item-User Dashboard-339')
            .should('be.visible')
        // cy.get('[data-cy="menu-item-Live Site-312')
        //     .should('contain', "Live Site").and('have.attr', 'href', 'https://iac.siteboss.net');
    })

})