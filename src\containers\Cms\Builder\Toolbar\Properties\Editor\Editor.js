import React, { useState, useEffect, lazy, useCallback } from 'react';
import { <PERSON>, <PERSON>, Container, Button } from 'react-bootstrap';
import CodeEditor from '@uiw/react-textarea-code-editor';

export const Editor = (props) => {
    const [value, setValue] = useState();

    const shady<PERSON>and<PERSON> = useCallback(params => {
        let values = params;
        if (params){
            if (Array.isArray(params)){
                values=params.map(a=>a?.id || a);
            }
        }
        setValue(values);
    },[]);

    const saveHandler = (e) => {
        props.save(e,value,props.data.id);
        props.close();
    }
    
    useEffect(() => {
        if (props?.data?.value) setValue(props.data.value);
    },[props?.data?.value]);

    let LazyComponent = "div";
    let comp_props = {};
    if (props?.data?.source?.component){
        LazyComponent = lazy(() => import(`../../${props.component_url}`));        
        if (props.data.source?.props){
            for (let prop in props.data.source.props){
                // if its a method, we need to set it up
                if (typeof props.data.source.props[prop]==="string" && props.data.source.props[prop].indexOf("method:")>-1){
                    comp_props[prop] = shadyHandler.bind(null);
                } else comp_props[prop] = props.data.source.props[prop];
            }
        }
    }

    useEffect(() => {
        return() => {
            setValue(null);
        }
    },[]);

    return (
        <Container fluid className="modal-editor">
            <Row>
                <Col sm={12}>
                    <h3>{props?.data?.display_name || props?.data?.name || ""}</h3>
                </Col>
                <Col sm={12} data-color-mode="light">
                    {props?.data?.name === "css" &&
                        <CodeEditor
                            value={props.data.value}
                            language="css"
                            placeholder="Please enter CSS code."
                            onChange={(e) => setValue(e.target.value)}
                            padding={15}
                            className="code-editor"
                            style={{
                                fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace',
                                backgroundColor: "#fff",
                            }}
                        />                    
                    }
                    <LazyComponent {...comp_props} />
                </Col>
                <Col sm={12}>
                    <Button variant="primary" onClick={saveHandler}>Save</Button>
                </Col>
            </Row>
        </Container>    
    )
}