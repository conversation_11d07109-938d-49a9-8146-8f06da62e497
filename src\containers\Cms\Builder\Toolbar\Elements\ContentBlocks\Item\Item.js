import React, {useEffect} from 'react';
import { Tooltip, OverlayTrigger } from 'react-bootstrap';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { useDrag } from 'react-dnd';

export const Item = (props) => {
	const [{ isDragging }, drag, dragPreview] = useDrag(() => ({
		type: props?.element_type || "content-block",
		item: {...props},
		end: (item, monitor) => {
			const dropResult = monitor.getDropResult(); // this comes from the return of the drop function in useDrop
			if (item && dropResult) {

				//if (item.id === "")

				// changes the id so that multiple of the same item can be added
				//item.id=`${props.id}-${randomUUID()}`;
			}
		},
		collect: (monitor) => ({
			isDragging: monitor.isDragging(),
			handlerId: monitor.getHandlerId(),
		}),
	}));

	useEffect(() => {
		dragPreview(getEmptyImage(), { captureDraggingState: true });
	}, [dragPreview]);

	return (
		<OverlayTrigger placement="top" overlay={<Tooltip>{props?.tooltip || props.name}</Tooltip>}>
			<div ref={drag} className={`content-block-container`} onClick={props.click}>
				<div className={`content-block-icon _${props.class}`}/>
				{props.display_name || props.name}
			</div>
		</OverlayTrigger>
	);
}