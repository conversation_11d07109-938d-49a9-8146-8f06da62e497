import React, { useState, useEffect, useMemo, Suspense, useRef, useCallback } from "react";
import { useHist<PERSON>, Link } from "react-router-dom";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import {
  Spinner,
  Container,
  Card,
  Col,
  Row,
  Button,
  Dropdown,
  Modal,
} from "react-bootstrap";
import { formatISO } from "date-fns";
import SubHeader from "../../../components/common/SubHeader";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import interactionPlugin from "@fullcalendar/interaction";
import resourceTimelinePlugin from "@fullcalendar/resource-timeline";
import { useSelector } from "react-redux";
import { endOfDay, isDate, getDay } from "date-fns";

import EventModal from "./EventModal";
import { authCheck, authUserHasModuleAccessMany } from "../../../utils/auth"
import usePrevious, { useRoleCheck } from '../../../components/common/CustomHooks';
import Stack from "../../../components/common/Stack";

import "./Calendar.scss";

import Events from "../../../api/Events";
import Locations from "../../../api/Locations";
import Services from "../../../api/Services";
import { ServiceModalView } from "./ServiceModalView/ServiceModalView";
import { selectAcceptableColors } from "../EventUtils";
import { authUserHasModuleAccess } from "../../../utils/auth";

const WIDGET_MODULE_ID = 122; // view service bookings on calendar
const EVENT_TYPE_SERVICE = 9;
const MANAGE_EVENT_MODULE_ID = 118 // to see the link to manage events from the calendar

//References to elimante event_type or event_type_id 9 are service events
//Eliminating event_type 5 from calls with event type is to eliminate meta events from the calendar.  
let initialConflictingEvent = {
	id: "",
	location_id: NaN,
	name: "",
	start_datetime: Date(),
	end_datetime: Date(),
	resourceId: NaN,
	event_type_id: NaN,
	requires_membership: 0,
	requires_registration: 0,
};

const selectedEventStatuses = [1, 2, 3, 4, 5];

const EVENT_STATUS_IDS = [1, 2, 3, 4, 5, 6]; 

const Calendar = (props) => {
  let history = useHistory();
  // Redux variable
  const eventUpdate = useSelector((state) => state.calendar.updateNeeded);

  let user = authCheck(history);

  const newButtonHandler = (event) => {
	history.push("/p/events/wizard");
  };

  const mountedRef = useRef(false);
  const calendarRef = useRef(null);
  const userRole=useRoleCheck(); //to check the role of logged in user
  const [loading, setLoading] = useState(true);
  const [events, setEvents] = useState([]);
  const [eventTypes, setTypes] = useState([]); //types for dropdown
  const [locations, setLocations] = useState([]);
  const [calendarTitle, setCalendarTitle] = useState("");
  const [timelineDayOfWeek, setTimelineDayOfWeek]=useState(null);
  const [calendarPicker, isCalendarPicker] = useState(0);
  const [newDate, setNewDate] = useState(new Date());
  const [locationArray, setLocationArray] = useState();
  const [locationName, setLocationName]=useState("Filter by Location") //for the dropdown to change name as things are selected
  const [calendarStart, setCalendarStart] = useState();
  const [selectedLocation, setSelectedLocation]=useState(null);
  const [eventType, setEventType]=useState([1,2,3,4,6,7,8,10]); //type of event for filter.  defaults to everything except meta and service
  const [typeName, setTypeName]=useState("Filter by Type"); //to set the filter value 
  const oldEventType = usePrevious(eventType); //to check the type actually changed to avoid unnecessary calls
  const oldLocation=usePrevious(selectedLocation); //to avoid uneccessary calls
  const [serviceModalShow, setServiceModalShow]=useState(false);
  const [show, setShow] = useState(false);
  const [edit, setEdit] = useState(false);
  const [conflicts, setConflicts] = useState([]);
  const [eventDetails, setEventDetails] = useState({
	title: "",
	start_datetime: new Date(),
	end_datetime: new Date(),
	location: "",
	requires_registration: "",
	requires_membership: "",
  });
  const defaultDate = localStorage.getItem("fcDefaultDate");
  let initialView = localStorage.getItem("fcDefaultView") || "dayGridMonth";
  let initialDate = useMemo(()=>Date.parse(localStorage.getItem("fcDefaultDate")) || new Date(),[]);
  let endDate = calendarRef.current?.getApi().view.activeEnd.toISOString();
  const [userHasModulePermission, setUserHasModulePermission] = useState(false);

  const editEvent = () => setEdit(false);
  const smClose=()=>{
	setServiceModalShow(false);
	setEventDetails({
	  title: "",
	  start_datetime: new Date(),
	  end_datetime: new Date(),
	  location: "",
	  requires_registration: "",
	  requires_membership: "",
	})
  } 
  const handleClose = () =>{
	setShow(false);
	setEventDetails({
	  title: "",
	  start_datetime: new Date(),
	  end_datetime: new Date(),
	  location: "",
	  requires_registration: "",
	  requires_membership: "",
	})
  }
  
//#region useCallbacks
  /** This function turns each different block into it's own "event" for display on the calendar. */
  const servicesToEvents=useCallback(sortEvents=>{
	let tempEvents=[];
	sortEvents.forEach((event)=>{
	  let randomColor=createRandomColor();
	  event.blocks.forEach((block)=>{
		let endTime = calculateEndTime(block)
		block={
		  ...event,
		  resourceId: event.location_ids[0],
		  conflict: 0,
		  event_type_id: EVENT_TYPE_SERVICE,
		  days_of_week:block.day_of_week,
		  startTime:block.start_time,
		  endTime: endTime,
		  duration: block.duration,
		  color: randomColor
		}
		tempEvents.push(block)
	  });
	});
	return tempEvents;
  },[]);

//#endregion useCallbacks

//#region useEffect

  useEffect(() => {
	if (eventUpdate === true) {
	  calendarRef.current.getApi().refetchEvents();
	  // do we need to set eventUpdate back to zero afterwards?
	}
  }, [eventUpdate]);

  useEffect(()=>{
	if(isDate(new Date(calendarTitle))){
		let dayOfWeekInt = getDay(new Date(calendarTitle));
		if(dayOfWeekInt===0) setTimelineDayOfWeek('Sunday');
		if(dayOfWeekInt===1) setTimelineDayOfWeek('Monday');
		if(dayOfWeekInt===2) setTimelineDayOfWeek('Tuesday');
		if(dayOfWeekInt===3) setTimelineDayOfWeek('Wednesday');
		if(dayOfWeekInt===4) setTimelineDayOfWeek('Thursday');
		if(dayOfWeekInt===5) setTimelineDayOfWeek('Friday');
		if(dayOfWeekInt===6) setTimelineDayOfWeek('Saturday');
	}
  },[calendarTitle]);

  //Component Did Mount (labeling to find with ctrl+f more easily)
  useEffect(()=>{
	mountedRef.current = true;

	const nest = (items, id = null, link = "parent_id") =>
	  items
		.filter((item) => item[link] === id)
		.map((item) => ({
		  ...item,
		  title: item.name,
		  children: nest(items, item.id),
		}));


	const _getLocations = async () => {
		try{
			setLoading(true);
			const response = await Locations.get();
			setLoading(false);
			if(response.data){
				// Once locations are fetched, this formats them into a tree for the claendar timeline view
				
				// Filter out 'parent' locations
				// These are specified here because they are unselectable and would be filtered out in the selectable step
				let tempParents = response.data.filter(l =>
					l.name === "Impact Athletics NY" ||
					l.name.includes("Basketball Courts") ||
					l.name.includes("Volleyball Courts")
				);
				// Filter out locations with selectable = 0, and combine
				let tempArr = response.data.filter((l) => l.selectable === 1);
				tempArr.push(...tempParents);

				// This array is used in the calendar. Resources = locations in fullCalendar
				setLocationArray(nest(tempArr));

				// Need separate locations for event conflicts
				// TODO: refactor this
				setLocations(response.data); // Set locations
			}
		}catch(ex){
			console.error(ex);
		}
	}

	if (mountedRef.current) _getLocations();

    const checkPermission = async () => {
		try {
            let response = await authUserHasModuleAccessMany([WIDGET_MODULE_ID, MANAGE_EVENT_MODULE_ID]);
            setUserHasModulePermission(response);
        } catch (error) { console.error(error) }
    }
    checkPermission();

	return()=>{
		setLoading(false);
		mountedRef.current = false
		setEventType([1,2,3,4,6,7,8,10]);
		setEvents([]);
		setLocations([]);
		setTypeName("Filter by Type");
		setLocationName("Filter by Location");
	}
  },[]);

  //Adds the prop to be displayed on the calendar.  In future, if any need to be changed to not display, add a setProp("display", "none")
  useEffect(()=>{
	if (mountedRef.current){
		try{
			const a=calendarRef.current?.getApi().getEvents().forEach((event)=>{
				event.setProp("display","auto");
			});
		}catch(ex){console.error(ex)}
	}
  },[events]);

  // Get the last displayed day on the calendar and convert to correct format
  useEffect(() => {
	if (endDate) {
	  setCalendarStart(endDate);
	}
  }, [endDate]);

  //Handles getting events when the type changes - different call based on if it's a service or regular event
  useEffect(()=>{

	const getEventTypes=async()=>{
		try{
		  let response = await Events.Types.get();
		  if(!response.errors){
			if (userRole.id < 5) setTypes(response.data);
			else setTypes(response.data.filter((event)=>{return event.is_service_type === 0;})) //if logged in user is not admin+, they cannot see service types on the calendar.
			
		  }
		}catch(ex){
		  console.error(ex)
		}
	}

	const getServiceEvents=async()=>{
		try{
		  let response = await Services.get({
			start_datetime: formatISO(new Date(initialDate)),
			max_records: 100,
			location_ids: selectedLocation
		  });
		  if(!response.errors && mountedRef.current){
			let amendedResponse = servicesToEvents(response.data.services);
			setEvents(amendedResponse);
			setLoading(false)
		  };
		}catch(ex){
		  console.error(ex);
		  setLoading(false);
		}
	}

	const getEvents=async()=>{
		if(eventType[0]!==EVENT_TYPE_SERVICE){
		  try{
			let response = await Events.getSimple({
			  start_dateTime: formatISO(new Date(initialDate)),
			  end_dateTime: endDate,
			  max_records: 100,
			  event_types: eventType,
			  locations: selectedLocation,
			  event_status_id: selectedEventStatuses,
			})
			if(!response.errors && mountedRef.current){
			  let amended = response.data?.events?.map((event)=>{
				return{
				  ...event, 
				  resourceId: event.location_id,
				  conflict: 0,
				}
			  });
			  setEvents(amended);
			}
			setLoading(false)
		  }catch(ex){
			console.error(ex);
			setLoading(false);
		  }
		}
	}
	
	if (mountedRef.current) {
		setLoading(true);
		if (eventTypes.length===0) getEventTypes();
		if (oldEventType !== eventType || oldLocation !== selectedLocation){
			if(eventType[0] !==EVENT_TYPE_SERVICE) getEvents();
			else getServiceEvents();
		}
		setLoading(false)
	}
  },[calendarStart, userRole.id, selectedLocation, initialDate, servicesToEvents, eventType, eventTypes, oldEventType, oldLocation, endDate]);
  

  const updateConflictLocations = useCallback((_conflicts, location) => {
	if (!conflicts.length>0){
		let conflictEvents=[];
		if (_conflicts) {
			_conflicts.forEach(conflict => {
				// Get events at conflicting locations
				const conflictArray = events.filter(
					(event) => event.location_id === conflict
				);
				conflictArray.forEach((event) => {
					conflictEvents.push({
						...initialConflictingEvent,
						id: `${event.id}-${location.id}`,
						name: `Conflict: ${event.name}`,
						location_id: location.id,
						start_datetime: event.start_datetime,
						end_datetime: event.end_datetime,
						resourceId: location.id,
						event_type_id: event.event_type_id,
						requires_membership: event.requires_membership,
						requires_registration: event.requires_registration,
						conflict: 1,
					});
				});
			});
		}
		if (conflictEvents.length > 0) setConflicts(prev=>[...prev,conflictEvents]);
	}
  }, [events,conflicts]);

  // Add conflicting events
  useEffect(() => {
	if	(mountedRef.current){
	  	// Get locations with conflict array
	  	const locationsWithConflicts = locations.filter(location => location.location_conflicts?.length > 0);
	  	locationsWithConflicts.forEach(location => {
			updateConflictLocations(location.location_conflicts, location);
	  	});
	}
  }, [locations,updateConflictLocations]);

  useEffect(()=>{
	if (conflicts.length>0){
		setEvents((events) => [...events, conflicts]);
	}
  },[conflicts]);


//#endregion useEffect  

//#region utilityFunctions

  const datePickerClick = (e) => {
	if (e.target.className.includes("fc-titleButton-button")) {
	  isCalendarPicker(1);
	}
	setShow(true);
  };

  const sendNewDate = (newDate) => {
	calendarRef.current.getApi().gotoDate(newDate);
	setShow(false);
  };

  function boolToWord(bool) {
	var a = bool.toString();
	if (a === "true") return "Yes";
	else return "No";
  }

  const calculateEndTime=(block)=>{
	let hours = Math.floor(block.duration/60)
	let remainingMinutes = block.duration%60;
	let startHours=block.start_time.substr(0,2);
	let startMinutes=block.start_time.substr(3,2);
	let endHours = +hours + +startHours;
	let endMinutes = +remainingMinutes + +startMinutes;
	if(endMinutes.toString().length===1) endMinutes = "0" + endMinutes;
	let endTime = `${endHours}:${endMinutes}:000`
	if(endHours > 24) endTime="24:00:00"
	return endTime
  }

  //Rather than setting a color by type for service, each service gets its own color since it can have multiple blocks for the same service spread over the calendar
  const createRandomColor=()=>{
	let color = Math.floor(Math.random()*16777215).toString(16);
	do {  //prevents any of the colors to be too dark by keeping the R,G, && B first values of the hex code above 6
	  color = Math.floor(Math.random()*16777215).toString(16)
	} while(color[0]<7 || color[2] < 7 || color[4]<7)
	let colorCode=`#${color}`
	return colorCode;
  }

  //Converting filter type to id and to name for dropdown to what they need to be.  
  const changeKeysToValue=(e)=>{
 
	const [id, typeName]=e.split('~')

	let allArray = [1,2,3,4,6,7,8];
	let meta = [5];
	let service=[EVENT_TYPE_SERVICE];

	//to handle the id/array of types
	if(id==="5") setEventType(meta);
	else if(id===`${EVENT_TYPE_SERVICE}`)setEventType(service);
	else if(id==="0")setEventType(allArray);
	else setEventType([parseInt(id)]);

	//to handle the name
	if(id===0 || id==="0") setTypeName("Filter by Type")
	else setTypeName(typeName)
  }

  //Converts location data from drop down to relative states
  const locationToValue=(e)=>{
	const [id, locationName]=e.split("~");

	//handle id
	if(id===0 || id==="0") setSelectedLocation(null);
	else if(!Array.isArray(id)) setSelectedLocation([parseInt(id)]);
	else setSelectedLocation(id);

	//handle name
	if(id===0 || id==="0") setLocationName("Filter by Location");
	else setLocationName(locationName)
  }
//#endregion utilityFunctions


  return (
	<Container fluid>
	  <Suspense
		fallback={
		  <SkeletonTheme color="#e0e0e0">
			<Skeleton height={30} style={{ marginBottom: "1rem" }} />
			<Skeleton height={12} count={5} />
		  </SkeletonTheme>
		}
	  ></Suspense>
	  
	  <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
				{ text: "Event Calendar" }
			]} />		  

	  <Card className="content-card">
		<Stack direction="horizontal" gap={2}>
			<h4 className="tm-1 section-title order-2 order-lg-1">Event Calendar</h4>
			<div className="ms-sm-auto d-flex justify-content-end order-1 order-lg-2">
				<Dropdown onSelect={changeKeysToValue}>
					<Dropdown.Toggle variant="light" id="dropdown-basic">{typeName}</Dropdown.Toggle>
					<Dropdown.Menu>
						<Dropdown.Item eventKey={0}>All Events</Dropdown.Item>
						{eventTypes.map((type) => {
							let idName = `${type.id}~${type.name}`;
                            if (!userHasModulePermission[WIDGET_MODULE_ID] && type.id === EVENT_TYPE_SERVICE) { // do not display service type
                                return (<></>);
                            }
							return (
								<Dropdown.Item key={`event-type-dd-${type.id}`} eventKey={idName}>{type.name}</Dropdown.Item>
							);
						})}
					</Dropdown.Menu>
				</Dropdown>
				<Dropdown onSelect={locationToValue}>
					<Dropdown.Toggle variant="light" id="dropdown-basic2">{locationName}</Dropdown.Toggle>
					<Dropdown.Menu>
						<Dropdown.Item eventKey={0}>Entire Facility</Dropdown.Item>
						{locations?.map((location) => {
							if (location.selectable === 1) {
								let idName = `${location.id}~${location.name}`
								return (
									<Dropdown.Item key={`calendar-location-dd-${location.id}`} eventKey={idName}>
										{location.name}
									</Dropdown.Item>
								);
							}
							return null;
						})}
					</Dropdown.Menu>
				</Dropdown>
				<Button variant="primary" onClick={newButtonHandler}>Create Events</Button>
			</div>
		</Stack>
		<hr/>
		{loading && <span><Spinner animation="grow" variant="success" /> Loading Events...</span>}
		{!loading && 
		<FullCalendar
			  firstDay={1}
			  ref={calendarRef}
			  plugins={[
				dayGridPlugin,
				timeGridPlugin,
				listPlugin,
				interactionPlugin,
				resourceTimelinePlugin,
			  ]}
			  schedulerLicenseKey="CC-Attribution-NonCommercial-NoDerivatives"
			  initialView={initialView}
			  initialDate={initialDate}
			  contentHeight="auto"
			  fixedWeekCount={false}
			  // Called any time the calendar view/dates are changed
			  datesSet={function (dateInfo) {
				localStorage.setItem("fcDefaultView", dateInfo.view.type);
				localStorage.setItem(
				  "fcDefaultDate",
				  dateInfo.view.currentStart
				);
			  }}
			  customButtons={{
				timelineButton: {
				  text: "Timeline View",
				  click: function () {
					calendarRef.current
					  .getApi()
					  .changeView("resourceTimelineDay");
					calendarRef.current.getApi().refetchEvents();
				  },
				},
				titleButton: {
				  text: `${calendarRef?.current?.getApi()?.currentDataManager?.state?.currentViewType === "resourceTimelineDay" 
				  || calendarRef?.current?.getApi()?.currentDataManager?.state?.currentViewType === "timeGridDay"
				  ? timelineDayOfWeek : ""}
				  ${calendarTitle}`,
				  click: function (e) {    
					datePickerClick(e);
				  },
				},
				// Custom prev/next buttons make sure the calendar date text updates
				prevDay: {
				  text: `<`,
				  click: function () {
					calendarRef.current.getApi().prev();
					const date = calendarRef.current
					  .getApi()
					  .getDate()
					  .toISOString();
					setCalendarTitle(date);
				  },
				},
				nextDay: {
				  text: `>`,
				  click: function () {
					calendarRef.current.getApi().next();
					const date = calendarRef.current
					  .getApi()
					  .getDate()
					  .toISOString();
					setCalendarTitle(date);
				  },
				},
			  }}
			  viewClassNames={function (info) {
				if (info) setCalendarTitle(info.view.title);
			  }}
			  headerToolbar={{
				start: "titleButton",
				center: "timeGridDay,timeGridWeek,dayGridMonth",
				end: "timelineButton, prevDay,nextDay",
			  }}
			  resources={locationArray}
			  resourceAreaHeaderContent={"Location/Resources"}
			  eventClick={function (info) {
				if (info.event.extendedProps.conflict === 0) {
				  isCalendarPicker(0);
				  // const r = payload.getResources();
				  // const resourceId = r[0].id;
				  // console.log(payload);
				  let resources = info.event.getResources();
				  //   console.log(resources);
				  // let resourceIds = resources.map((resource) => {
				  //   return resource.id;
				  // });
				  let locationId = info.event.extendedProps.location;
				  if (locationId) {
					const l = calendarRef.current.getApi().getResourceById(17);
				  }
				  // let eventLocation = calendarRef.current
				  //   .getApi()
				  //   .getResourceById(17);
				  //   console.log(info.event.resourceId);
				  if(info.event.extendedProps.event_type_id !==EVENT_TYPE_SERVICE){
					setShow(true);
					setEventDetails({
					  ...info.event,
					  id: info.event.id,
					  title: info.event.title,
					  start: info.event.start.toDateString(),
					  end_datetime: info.event.end,
					  start_datetime: info.event.start,
					  end: info.event.end.toDateString(),
					  is_event: info.event.extendedProps.is_event,
					  // location: location.title,
					  requires_registration: boolToWord(
						Boolean(info.event.extendedProps.requires_registration)
					  ),
					  requires_membership: boolToWord(
						Boolean(info.event.extendedProps.requires_membership)
					  ),
					});
				  }
				  else{
					setServiceModalShow(true);
					setEventDetails({
					  ...info.event,
					  id: info.event.id,
					  title: info.event.title,
					  props: info.event.extendedProps,
					})
				  }
				}
			  }}
			  eventClassNames={function (arg) {
				if (arg.event.extendedProps.conflict === 1) {
				  return ["conflict"];
				} else {
				  return [""];
				}
			  }}
			  eventDidMount={function (arg) {
				if (arg.view.type !== "resourceTimelineDay") {
				  if (arg.event.extendedProps.conflict !== 0) {
					arg.event.setProp("display", "none");
				  }
				}
			  }}
			  eventDataTransform={function (eventData) {
				if (eventData.extendedProps.event_type_id === 5) { //meta event
				  eventData.allDay = true;
				}
				return eventData;
			  }}
			  
			  events={events && events?.map((event, i) => {
				  let acceptableColorsForType = selectAcceptableColors(eventTypes.length)
				  if(event.event_type_id !==EVENT_TYPE_SERVICE){ //Everything but service events
				  return {
					id: event.id,
					editable: false,
					title: event.name,
					start: new Date(event.start_datetime),
					end: new Date(event.end_datetime),
					className: event.conflict>0?"event-conflict":"event-"+event.event_type_id,
					resourceId: event.resourceId,
					borderColor: acceptableColorsForType.find(colors=>event.event_type_id===colors.event_type_id)?.color,
					extendedProps: {
					  is_event: true,
					  location: event.resourceId,
					  event_type_id: event.event_type_id,
                      event_type_name: event.event_type_name,
                      event_status_id: event.event_status_id,
                      event_status_name: event.event_status_name,
                      // requires_membership: event.requires_membership,
					  requires_registration: event.requires_registration,
					  conflict: event.conflict,
					},
				  };
				}
				else{ //services
				  return{
					id: event.id,
					editable: false,
					title: event.name,
					start: new Date(event.start_date),
					end: new Date(event.end_date),
					startTime: event.startTime,
					endTime: event.endTime,
					daysOfWeek: [event.days_of_week],
					className: "event-"+EVENT_TYPE_SERVICE,
					resourceId: event.resourceId,
					borderColor: event.color,
					extendedProps: {
					  is_service: true,
					  is_event: false,
					  description: event.description,
					  location: event.resourceId,
					  event_type_id: event.event_type_id,
					  requires_registration: true,
					  conflict: event.conflict,
					  min_participants: event.min_participants,
					  max_participants: event.max_participants,
					  times: {
						startTime: event.startTime,
						endTime: event.endTime,
						startDate: event.start_date,
						endDate: event.end_date,
					  }
					},
				  }
				}
			  })}
		/>
		}
	  </Card>

	  <Modal show={serviceModalShow} onHide={smClose}>
		<Modal.Header closeButton>
		  Service Event
		</Modal.Header>
		<Modal.Body>
		  <ServiceModalView 
			serviceEvent={eventDetails}
		  />
		</Modal.Body>
	  </Modal>
	  <Modal size="lg" show={show} onHide={handleClose} dialogClassName="calendar-event-rb-modal">
		<Modal.Header closeButton>
		  <Modal.Title id="calendar-event-modal">
			{calendarPicker ? "Jump to Date" : eventDetails.title }
		  </Modal.Title>
		</Modal.Header>
		<Modal.Body id="calendar-event-date-modal-body">
		  <EventModal 
			data={eventDetails}
			datePickerClick={calendarPicker}
			newDate={newDate}
			sendNewDate={sendNewDate}
			handleClose={handleClose}
			eventTypes={eventTypes}
			userRole={userRole}
			userHasModulePermission={userHasModulePermission}
		  />
		</Modal.Body>
	  </Modal>
	</Container>
  );
};

export default Calendar;