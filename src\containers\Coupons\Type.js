import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, InputGroup } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Type = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Is this a fixed discount amount or a percentage and how much?</span>
                <Row>
                    <Col className="col-sm-auto form-row">
                        <Form.Check 
                            type="radio"
                            id="discount_type-1"
                            label="Fixed Amount"
                            name="discount_type"
                            value={1}
                            checked={coupon.discount_type===1}
                            onChange={onChangeInput}
                            isInvalid={!!errors.discount_type}
                            className="form-radio"
                        />
                        <Form.Check 
                            type="radio"
                            id="discount_type-0"
                            label="Percentage"
                            name="discount_type"
                            value={0}
                            checked={coupon.discount_type===0}
                            onChange={onChangeInput}
                            isInvalid={!!errors.discount_type}
                            className="form-radio"
                        />
                    </Col>
                    <Col className="col-sm-auto">
                        <i className="far fa-arrow-right mt-4"/>
                    </Col>
                    <Col>
                        <Form.Label>Number amount</Form.Label>
                        <InputGroup style={{width: "150px"}}>
                            {coupon.discount_type===1 &&
                                <InputGroup.Text id="basic-addon1">$</InputGroup.Text>
                            }
                            <Form.Control
                                type="numeric"
                                id="discount_amount"
                                name="discount_amount"
                                value={coupon.discount_amount}
                                onChange={onChangeInput}
                                isInvalid={!!errors.discount_amount}
                                
                            />
                            {coupon.discount_type===0 &&
                                <InputGroup.Text id="basic-addon2">%</InputGroup.Text>
                            }
                        </InputGroup>
                    </Col>
                </Row>
                <div className={`err ${!!errors.discount_type || !!errors.discount_amount ? "" : "hidden"}`}>
                    {errors.discount_type}
                    {errors.discount_amount}
                </div>
            </Col>
        </Row>
    );
}

export default Type;