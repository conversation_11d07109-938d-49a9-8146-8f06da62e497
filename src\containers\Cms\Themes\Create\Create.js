import React,{useEffect, useState, Suspense} from 'react';
import { useLocation, Link } from "react-router-dom";
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Card from 'react-bootstrap/Card';
import Breadcrumb from 'react-bootstrap/Breadcrumb';
import ListGroup from 'react-bootstrap/ListGroup';
import SubHeader from '../../../../components/common/SubHeader';

import BasicInfo from '../BasicInfo'
import '../Details/Details.scss';

const Create = (props) => {    
    const location = useLocation();

	useEffect(() => {
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                <BasicInfo />
            </Suspense>
        );

	}, [location.pathname]);
    
    const [pagePart,setPagePart]=useState(
        <Suspense fallback={             
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
            </SkeletonTheme>
        }>            
        </Suspense>
    );

    const loadPagePartHandler= (e) => {
        let component;
        switch (e.target.hash.substr(1)){
            case "BasicInfo":
            default:
                component=<BasicInfo />;
                break;
        }
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {component}
            </Suspense>
        );
    }

    return (
        <Container fluid>
            <SubHeader items={[
                {linkAs:Link,linkProps:{to:"/p/home"},text:"Home"},
                {linkAs:Link,linkProps:{to:"/p/themes/dashboard"},text:"Themes Dashboard"},
                {text:"New Theme"}
            ]} />
            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <Row>
                            <Col sm="auto" className="order-1 order-lg-2">
                                <ListGroup className="profileMenu" variant="flush">
                                    <ListGroup.Item action href="#BasicInfo" onClick={loadPagePartHandler}>
                                        <i className="far fa-border-style-alt"></i> Theme Info
                                    </ListGroup.Item>
                                </ListGroup>
                            </Col>
                            <Col className="order-2 order-lg-1">
                                {pagePart /*this is where the magic is happening :-O */ }
                            </Col>
                        </Row>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Create;