import React,{useEffect, useState, Suspense} from 'react';
import { useParams,useLocation,Link  } from "react-router-dom";
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Card from 'react-bootstrap/Card';
import ListGroup from 'react-bootstrap/ListGroup';
import SubHeader from '../../../components/common/SubHeader';
import BasicInfo from '../BasicInfo';
import AssignUsers from '../AssignUsers';
import Groups from '../Groups';

import Events from '../../../api/Events';

import './Details.css';

const Details = (props) => {    
    const location = useLocation();
    const { id } = useParams();

    const [eventInfo,setEventInfo]=useState();

	useEffect(() => {
        let mounted = true;

        Events.getSingle({id:id})
        .then(event =>{
            if(mounted) {
                setEventInfo(event.data[0]);
                setPagePart(
                    <Suspense fallback={             
                        <SkeletonTheme color="#e0e0e0">
                            <Skeleton height={30} style={{marginBottom:"1rem"}} />
                            <Skeleton height={12} count={5} />
                        </SkeletonTheme>
                    }>
                        <BasicInfo event={event.data[0]} event_id={event.data[0].id} referer={location.pathname} />
                    </Suspense>
                );
            }
        }).catch(e => console.error(e));

        return () => {
            mounted = false;
        }
	}, [id, location.pathname]);

    const [pagePart,setPagePart]=useState(
        <Suspense fallback={             
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
            </SkeletonTheme>
        }>
            
        </Suspense>
    );

    const loadPagePartHandler= (e) => {
        let component;
        switch (e.target.hash.substr(1)){
            case "AssignUsers":
                component=<AssignUsers event_id={eventInfo.id} referer={location.pathname} />;
                break;
            case "Groups":
                component=<Groups event_id={eventInfo.id} referer={location.pathname} />;
                break;
            case "BasicInfo":
            default:
                component=<BasicInfo event={eventInfo} event_id={eventInfo.id} referer={location.pathname} />;
                break;
        }
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {component}
            </Suspense>
        );
    }

    if (!eventInfo) return (
        <Container fluid>
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
                <Skeleton height={30} style={{marginBottom:"1rem",marginTop:"2rem"}} />
                <Skeleton height={12} count={10} />
            </SkeletonTheme>
        </Container>
    );

    return ( eventInfo &&
        <Container fluid>
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/booking" }, text: "Event Booking" },
                { text: eventInfo.name }
            ]} />
            <Row>
                <Col>
                    <Card>
                        <Row>
                            <Col sm="auto" className="order-1 order-lg-2">
                                <ListGroup className="profileMenu" variant="flush">
                                    <ListGroup.Item action href="#BasicInfo" onClick={loadPagePartHandler}>
                                        <i className="far fa-store-alt"></i> Event Info
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#AssignUsers" onClick={loadPagePartHandler}>
                                        <i className="far fa-users"></i> Add Users
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#Groups" onClick={loadPagePartHandler}>
                                        <i className="far fa-users"></i> Add Groups
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#Payment" onClick={loadPagePartHandler}>
                                        <i className="far fa-credit-card"></i> Payment
                                    </ListGroup.Item>
                                </ListGroup>
                            </Col>
                            <Col className="order-2 order-lg-1">
                                {pagePart /*this is where the magic is happening :-O */ }
                            </Col>
                        </Row>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Details;