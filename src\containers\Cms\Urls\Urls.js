import React, { useState, useEffect, useMemo } from 'react';
import { useHistory } from "react-router-dom";
import {Container,Col,Row,Form,InputGroup,Button} from 'react-bootstrap';

import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';
import Table from '../../../components/common/Table';

import * as actions from '../../../store/actions';

import APICms from '../../../api/Cms';

const Urls = (props) => {
    let history = useHistory();

    const [urls, setUrls] = useState([]);
    const [pages, setPages] = useState([]);
    const [themes, setThemes] = useState([]);

    const [domain, setDomain] = useState("siteboss");
    const [selectedTheme, setSelectedTheme] = useState(1);
    const [selectedPage, setSelectedPage] = useState();
    const [selectedUrl, setSelectedUrl] = useState();

    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

	useEffect(() => {
        const _getUrls=async () => {
            try {
                const res=await APICms.urls.get({website_id:props.website_id});
                if (res.data && mounted) {
                    setUrls(res.data);
                }
            } catch (e){
                console.error(e);
            }
        }

        const _getPages=async () => {
            try {
                const res=await APICms.pages.get({website_id:props.website_id,page_type_id:1});
                if (res.data && mounted) {
                    setPages(res.data);
                }
            } catch (e){
                console.error(e);
            }
        }

        const _getThemes=async () => {
            try {
                const res=await APICms.themes.get();
                if (res.data && mounted) {
                    setThemes(res.data);
                }
            } catch (e){
                console.error(e);
            }
        }        

        let mounted = true;
        _getPages();
        _getUrls();
        _getThemes();

        return () => {
            mounted = false;
        }

	}, [props.website_id, props.website]);


    useEffect(() => {
        return () => {
            setUrls([]);
            setPages([]);
            setThemes([]);
            setSelectedTheme(1);
            setSelectedPage(null);
            setDomain("siteboss");
        }
    }, []);

    const themeChangeHandler = (e) => {
        setSelectedTheme(e.target.value);
    }    
    
    const pageChangeHandler = (e) => {
        setSelectedPage(e.target.value);
    }    

    const clickHandler = (cell) => {
        setSelectedTheme(cell.website_theme_id);
        setSelectedPage(cell.index_page);
        if (!cell.subdomain || cell.domain !== "siteboss.net") setDomain("custom");
        else setDomain("siteboss");
        setSelectedUrl(cell);
    }

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();
        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            if (selectedUrl?.id) formData.append("id", selectedUrl.id);
            if (props.website_id) formData.append("website_id", props.website_id);
            if (selectedTheme) formData.append("website_theme_id", selectedTheme);
            if (selectedPage) formData.append("index_page", selectedPage);
            if (domain==="siteboss") formData.append("domain", "siteboss.net");
            const formDataObj = Object.fromEntries(formData.entries());

            const response=await APICms.urls.create(formDataObj);
            if (!response.errors) {
                setSubmitting(false);
                setValidated(false);
                setSuccess(<Toast>URL saved successfully!</Toast>);
                history.go(0); // pushes to profile again to avoid resubmission
            } else { // api returned errors
                setSubmitting(false);
                setError(<ErrorCatcher error={response.errors} />);
            }
        } else setSubmitting(false);
    };

    const columns = React.useMemo(() => [{
        id: 'table',
        columns: [
            {
                Header: 'URL',
                id: 'name',
                accessor: d=>(d.subdomain?d.subdomain+".":"")+d.domain,
            },
            {
                Header: 'Theme',
                id: 'theme_name',
                accessor: d=>d.theme?.name || "Default",
            },
            {
                Header: 'Index Page',
                id: 'index_page',
                accessor : 'index_page',
            },
            {
                id: 'id',
                accessor: 'id',
                onClick: (cell) => {
                    clickHandler(cell);
                },
                show:false,
            },
        ],
    }],[]);

    return (
        <Container fluid>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Row>
                    <Col sm="auto" className="wizard filter-row">
                        <Form.Check
                            inline
                            type="radio"
                            id="siteboss-domain"
                            label="SiteBoss subdomain"
                            name="domain"
                            className="form-radio"
                            onChange={()=>setDomain("siteboss")}/>
                        <Form.Check
                            inline
                            type="radio"
                            id="custom-domain"
                            label="Custom domain"
                            name="domain"
                            className="form-radio"
                            onChange={()=>setDomain("custom")}/>
                    </Col>
                    <Col sm="auto" className="wizard filter-row custom">
                        <i className="far fa-arrow-right mt-4"/>
                    </Col>
                    <Col>
                        {domain==="siteboss" &&
                            <>
                                <Form.Label className="mt-0">Subdomain</Form.Label>
                                <InputGroup key={selectedUrl?.id}>
                                    <Form.Control type="text" max="155" name="subdomain" defaultValue={selectedUrl?.subdomain || ""} style={{textAlign:"right"}} />
                                    <InputGroup.Text>.siteboss.net</InputGroup.Text>
                                </InputGroup>
                            </>
                        }

                        {domain==="custom" &&
                            <>
                                <Form.Group key={selectedUrl?.id}>
                                    <Form.Label className="mt-0">Subdomain</Form.Label>
                                    <Form.Control type="text" max="155" name="subdomain" defaultValue={selectedUrl?.subdomain || ""} />
                                    <Form.Label className="mt-0">Domain</Form.Label>
                                    <Form.Control type="text" max="155" name="domain" defaultValue={selectedUrl?.domain || ""} />
                                </Form.Group>
                            </>
                        }
                        <Form.Group controlId="theme">
                            <Form.Label>Theme</Form.Label>
                            <Form.Control as="select" custom value={selectedTheme} onChange={themeChangeHandler}>
                                {themes && themes.map((theme,i) => (
                                    <option key={`theme-${i}`} value={theme.id}>{theme.name}</option>
                                ))}
                            </Form.Control>
                        </Form.Group>

                        <Form.Group controlId="page">
                            <Form.Label>Index Page</Form.Label>
                            <Form.Control as="select" custom value={selectedPage} onChange={pageChangeHandler}>
                                {pages && pages.map((page,i) => (
                                    <option key={`page-${i}`} value={page.slug}>{page.title}</option>
                                ))}
                            </Form.Control>
                        </Form.Group>
                        <Button variant="primary" type="submit" disabled={submitting} className={`mt-4 mb-3 ${submitting?" submitting":""}`}>Save</Button>
                    </Col>
                </Row>
            </Form>
            {error}
            <hr/>
            <Table columns={columns} data={urls} />
        </Container>
    );
}

export default Urls;