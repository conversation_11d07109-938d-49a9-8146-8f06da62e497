/*eslint-disable*/

let baseUrl = 'https://portal-qa.impactathleticsny.com/p/';
let staffUserName = Cypress.env('impact_staff_user');
let adminUserName = Cypress.env("impact_admin_user")
let password = Cypress.env('login_password');
let local;

describe('It will log in and visit a register as a staff user', {testIsolation: false, scrollBehavior: "center"}, ()=>{
    
    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, staffUserName, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach(()=>{
        cy.viewport(1920, 1080);
    });


    context("It will create a transaction that splits a payment with 2 cash payments with a user",()=>{
        let orderNumber;
        let transOne;
        let transTwo;
        let transVoided;

        let productPrice = "2.92"
        let tax = "0.20"
        let totalWithoutAdmin = "3.12"

        it('will navigate to the registers with the menu',()=>{
            cy.get('[data-cy="menu-item-Smack Shack (Impact-05)-38"]')
                .click({force: true});
            cy.url()
                .should("include", "/p/pos/1")
            cy.wait(1000)
        });

        it("will add an item and check data",()=>{
            cy.get('[data-cy="product-card"]')
                .contains('Arizona Gummy')
                .click();
            cy.get('.item-name > :nth-child(1) > [data-cy="product-name"]')
                .invoke('text')
                .should('include', 'Arizona Gummy')
            cy.get('[data-cy="preview-item"] > .item-price')
                .invoke('text')
                .should('include', productPrice)
            cy.get('.product-buttons')
                .should('exist')
            cy.get('[data-cy="order-subtotal"]')
                .invoke('text')
                .should('include', productPrice);
            cy.get('[data-cy="order-tax"]')
                .invoke('text')
                .should('include', tax);
            cy.get('[data-cy="order-total"]')
                .invoke('text')
                .should('include', totalWithoutAdmin)
        })

        it("will add a user and check the user is added and save order number",()=>{
            cy.get('[data-cy="preview-clear-user-btn"]')
                .should('not.exist')
            cy.get('[data-cy="search-users"]')
                .click()
                .type("roro")
            cy.get('#user_autocomplete-item-0')
                .click();
            cy.get('.current-customer')
                .invoke('text')
                .should('include', "RoRo R")

            cy.get('[data-cy="order-number"]')
                .invoke('text').as('orderNumber')
            cy.get('@orderNumber').then((text)=>{
                orderNumber = text
            })
            cy.log(orderNumber);
            cy.get('[data-cy="preview-clear-user-btn"]')
                .should('exist')

        });

        it("will make the first payment",()=>{
            cy.get('[data-cy="pos-button-checkout"]')
                .should('not.have.attr', 'disabled')
            cy.get('[data-cy="pos-button-checkout"]')
                .click();
            cy.get('[data-cy="details-add-to-cart"]')
                .should('have.attr', 'disabled')
            cy.get('.Transactions_button__C8mHD') //a partial transaction indication
                .should('not.exist')
            cy.get('[data-cy="payment-type-2"]')
                .click();
            cy.get('[data-cy="cash-payment-button-group"] > :nth-child(1)')
                .click();
            cy.get('[data-cy="details-add-to-cart"]')
                .should('not.have.attr', 'disabled')
            cy.get('[data-cy="details-add-to-cart"]')
                .click();
            cy.wait(500)
            cy.get('[data-cy="checkout-totals-order-status"]') //partial paid graphic
                .contains("Partially Paid")
                .should('exist')
            cy.get('[data-cy="checkout-totals-container"]').within(()=>{
                cy.get('button')
                    .contains('Cash')
                    .should('exist');
            })
            cy.get('[data-cy="checkout-totals-outstanding"]')
                .should('be.visible')
                .invoke('text')
                .should('include', '2.12');      
        })

        it("will void the transaction",()=>{
            cy.get('.modal-content')
                .eq(1)
                .should('not.exist')
            cy.get('.modal-content')
                .eq(2)
                .should('not.exist')
            cy.get('[data-cy="checkout-totals-container"]').within(()=>{
                cy.get('button')
                    .contains('Cash')
                    .click();
            })
            cy.get('.modal-content')
                .eq(1)
                .within(()=>{
                    cy.get('button')
                    .contains('Void')
                    .click();
                })
            //     .invoke('text')
            //     .should('include', "Void")
            // cy.get('.Options_wrapper__yrGpC > :nth-child(5)')
            //     .click();
            cy.get('.modal-content').eq(2)
                .should('exist')
            cy.get('.modal-title')
                .invoke('text')
                .should('include', "Void Transaction")
            cy.get('[data-cy="confirmation-proceed-btn"]')
                .click();
            cy.wait(500)
            cy.get('[data-cy="checkout-totals-outstanding"]')
                .invoke('text')
                .should('include', totalWithoutAdmin)
        });

        it("will make another two cash payments",()=>{
            cy.intercept('POST', "/api/payment/process").as('processPayment');
            cy.get('.close > [aria-hidden="true"]')
                .click();
            cy.wait(500);
            cy.get('[data-cy="payment-type-2"]')
                .click();
            cy.get('[data-cy="payment-other-input"]')
                .click()
                .type("2")
            cy.get('hr') //we have to click somewhere else before the button will be undisabled and clicking the hr won't trigger anything else
                .click();
            cy.get('[data-cy="details-add-to-cart"]')
                .click()
            cy.wait('@processPayment');
            cy.wait(2000)
            cy.get('[data-cy="payment-type-2"]')
                .click();
                cy.get('[data-cy="payment-other-input"]')
                .clear()
                .click()
                .type("2")
            cy.get('hr')
                .click()
            cy.wait(2000)
            cy.get('[data-cy="details-add-to-cart"]')
                .click()
            cy.wait('@processPayment');
            cy.stubPrint();
        });
        
        it("will save all the transaction numbers",()=>{
            cy.stubPrint();
            cy.intercept('GET', '/api/order/order/**').as('getOrder');
            cy.wait(2000);
            cy.get('.col-lg-4 > :nth-child(1)').within(()=>{
                cy.get('[data-cy="success-transaction-number"]')
                    .invoke('text').as('transTwo');
                cy.get('@transTwo').then((text)=>{
                    let test = text.split("#")
                    transTwo = test[1].trim()
                })
            })
            cy.get('.col-lg-4 > :nth-child(4)').within(()=>{
                cy.get('[data-cy="success-transaction-number"]')
                        .invoke('text').as('transOne');
                    cy.get('@transOne').then((text)=>{
                        let test = text.split("#")
                        transOne = test[1].trim()
                    })
            });
            cy.get('.col-lg-4 > :nth-child(7)').within(()=>{
                cy.get('[data-cy="success-transaction-number"]')
                    .invoke('text').as('transVoided');
                cy.get('@transVoided').then((text)=>{
                    let test = text.split("#")
                    transVoided = test[1].trim()
                });
            })
        });

        it("will visit the order page and check the details",()=>{
            cy.visit(`${baseUrl}order/${orderNumber}`)
            cy.wait(1000)
            cy.get('.section-title')
                .invoke('text')
                .should('include', orderNumber)
            cy.get('[data-cy="order-subtotal"]')
                .invoke('text')
                .should('include', productPrice);
            cy.get('[data-cy="order-shipping-total"]')
                .invoke('text')
                .should('include', "0.00");
            cy.get('[data-cy="order-tax-total"]')
                .invoke('text')
                .should('include', tax);
            cy.get('[data-cy="order-total-price"]')
                .invoke('text')
                .should('include', 3.12);
            cy.get('.transaction-hist-wrapper')
                .children()
                .should('have.length', 3)
            cy.get('.transaction-hist-wrapper')
                .children().eq(0)
                    .within(()=>{
                        cy.get('[data-cy="transaction-number"]')
                        .invoke('text')
                        .should('include', transTwo);
                    })
            cy.get('.transaction-hist-wrapper')
                .children().eq(1)
                .within(()=>{
                    cy.get('[data-cy="transaction-number"]')
                    .invoke('text')
                    .should('include', transOne)
                })
                cy.get('.transaction-hist-wrapper')
                .children().eq(2)
                .within(()=>{
                    cy.get('[data-cy="transaction-number"]')
                        .invoke('text')
                        .should('include', transVoided)
                    cy.get('[data-cy="transaction-status"]')
                        .invoke('text')
                        .should('include', "Voided")
                })
        })
    })
});