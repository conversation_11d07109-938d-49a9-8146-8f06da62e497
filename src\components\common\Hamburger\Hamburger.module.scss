@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';

.hamburger-wrapper{
    display: flex;
    align-items: flex-start;
    &.left{
        justify-content: flex-start;
    }
    &.right{
        justify-content: flex-end;
    }
    .opened-burger{
        position:fixed;
        z-index: 105; //for some reason, a bunch of z-indexes are obscenely high and seemingly random....this may need to be adjusted eventually
        background-color: $primary-color;
        border-radius: 5px;
        padding: 5px;
        &.left{
            margin-left: 20px;
            text-align: start;
        }
        &.right{
            margin-right: 20px;
            text-align: end;
        }
    }
    .each-entry{
        cursor: pointer;
        i{
            padding: 0 5px 0 5px;
        }
    }
    .backdrop{
        position: fixed;
        height:100%;
        width:100%;
        background-color: transparent;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index:1;
    }
}