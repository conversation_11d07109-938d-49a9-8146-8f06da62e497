import React, { useState, useEffect } from 'react';
import { useHistory } from "react-router-dom";
import {Container,Col,Row,Form,Button} from 'react-bootstrap';

import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';
import Stack from '../../../components/common/Stack';

import APICms from '../../../api/Cms';

const BasicInfo = (props) => {
    let history = useHistory();

    const [website, setWebsite] = useState({});
    const [pageTypes, setPageTypes] = useState();

    const [checked, setChecked] = useState([1,2,7,8,9]);
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

	useEffect(() => {
        const _getPageTypes=async () => {
            try {
                const res=await APICms.pageTypes.get();
                if (res.data) {
                    setPageTypes(res.data);
                }
            } catch (e){
                console.error(e);
            }
        }

        const _getWebsite=async () => {
            try {
                const res=await APICms.websites.get({id:props.website_id});
                if (res.data) {
                    setWebsite(res.data);
                }
            } catch (e){
                console.error(e);
            }
        }

        if (props.website) setWebsite(props.website);
        else _getWebsite();

        _getPageTypes();

	}, [props.website_id, props.website]);


    useEffect(() => {
        return () => {
            setWebsite({});
            setPageTypes(null);
        }
    }, []);

    const typeClickHandler = (e) => {
        if (+e.target.value!==1 && +e.target.value!==2 && +e.target.value!==7 && +e.target.value!==8 && +e.target.value!==9 ){  // not all types are clickable
            if (e.target.checked) setChecked([...checked, +e.target.value]);
            else setChecked(checked.filter( item => +item !== +e.target.value));
        }
    }

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            if (props.website_id) formData.append("id", props.website_id);
            const formDataObj = Object.fromEntries(formData.entries());

            if (pageTypes){

            }
            
            const response=await APICms.websites.create(formDataObj);            
            if (!response.errors) {
                setSubmitting(false);
                setValidated(false);
                setSuccess(<Toast>Website saved successfully!</Toast>);
                history.push(props.referer || "/p/cms/dashboard"); // pushes to profile again to avoid resubmission
            } else { // api returned errors
                setSubmitting(false);
                setError(<ErrorCatcher error={response.errors} />);
            } 
        } else setSubmitting(false);
    };
    

    return (
        <Container fluid>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Row>
                    <Col sm="12">
                        <Form.Group controlId="name">
                            <Form.Label>Website Name</Form.Label>
                            <Form.Control required type="text" name="name" defaultValue={website?.name || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12">
                        <Form.Group controlId="description">
                            <Form.Label>Description</Form.Label>
                            <Form.Control type="text" name="description" defaultValue={website?.description || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12">
                        <Form.Group controlId="keywords">
                            <Form.Label>Keywords</Form.Label>
                            <Form.Control type="text" name="keywords" defaultValue={website?.keywords || ""} />
                        </Form.Group>
                    </Col>
                </Row>
                <Row>
                    <Col sm="12" className="mt-4 mb-3">
                        <label style={{marginBottom:"0.5rem"}}>Features to activate</label>
                        <Stack direction="horizontal" gap={2}>
                            {pageTypes && pageTypes?.filter(a=>+a.id!==1 && +a.id!==2 && +a.id!==7 && +a.id!==8 && +a.id!==9 && +a.id!==10 && +a.id!==12).map((type,i) => (
                                <Form.Check 
                                    key={`page-type-${i}`} 
                                    id={`page-type-${i}`} 
                                    type="checkbox" 
                                    label={type.name} 
                                    value={type.id} 
                                    onChange={typeClickHandler}
                                    checked={checked.includes(+type.id)}
                                />
                            ))}
                        </Stack>
                    </Col>
                </Row>
                <Row>
                    <Col sm="12" className="mt-4 mb-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={`${submitting?" submitting":""}`}>Save</Button>
                    </Col>
                </Row>
            </Form>
            {error}
        </Container>
    );
}

export default BasicInfo;