let mapIds = [];
const ListToTree = (data, parentName) => {
    let node;
    let counter;
    const roots = [];
    mapIds = [];
    data.forEach((item,index) => {
        mapIds[item?.id || new Date()] = index;
        item.children = [];
    });
    for (counter = 0; counter < data.length; counter += 1) {
        node = data[counter];
        if (node?.parent_id && node.parent_id !== 0) {
            if (data?.[mapIds[node.parent_id]]?.children) data[mapIds[node.parent_id]]?.children.push(node);
        } else {
            roots.push(node);
        }
    }
    return {
        id: "root",
        name: parentName || "Parent",
        children: roots,
    };
};

export default ListToTree;