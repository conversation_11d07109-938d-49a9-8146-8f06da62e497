{"patronUser": {"errors": null, "data": [{"id": 3649, "first_name": "Master", "last_name": "Chief", "middle_name": "", "email": "<EMAIL>", "mobile_phone": "", "home_phone": null, "dob": "1991-11-15", "username": "@@", "user_status_id": 1, "api_key": null, "remember_token": 0, "created_at": "2022-06-30T23:29:52.000000Z", "address1": "", "address2": "", "city": "", "state": "", "postal_code": "", "country": "", "profile_img_path": null, "cover_img_path": null, "signed_waiver_path": null, "family_group_member_role_id": 9, "family_group_member_role": "Admin", "roles": [{"id": 7, "name": "<PERSON><PERSON>"}], "managed_services": [], "subscriptions": [], "outstanding_charges": [{"event_id": 311, "requires_membership": 0, "event_name": "TFT Tournament", "product_id": 407, "product_name": "TFT Tournament", "product_variants": [{"product_variant_id": 684, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 276, "requires_membership": 1, "event_name": "Taekwondo", "product_id": 399, "product_name": "Taekwondo", "product_variants": [{"product_variant_id": 675, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "payment_profiles": [], "needs_membership": 1, "family": [{"id": 3605, "first_name": "RoRo", "last_name": "<PERSON><PERSON>", "group_member_role_id": 8, "group_member_role": "Friend", "outstanding_charges": [{"event_id": 266, "requires_membership": 1, "event_name": "The Northern Lights", "product_id": 392, "product_name": "The Northern Lights", "product_variants": [{"product_variant_id": 667, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 280, "requires_membership": 1, "event_name": "HIIT Club", "product_id": 400, "product_name": "HIIT Club", "product_variants": [{"product_variant_id": 676, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "subscriptions": [{"id": 1097, "product_id": 69, "product_variant_id": 86, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "m", "interval_quantity": null, "product_name": "Club Memberships", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-05-12 06:47:37", "last_bill_date": "2022-05-12 06:47:37", "next_bill_date": "2022-06-12 00:00:00", "final_bill_date": null}], "needs_membership": 0}, {"id": 3603, "first_name": "<PERSON><PERSON>", "last_name": "RRR", "group_member_role_id": 11, "group_member_role": "Child", "outstanding_charges": [{"event_id": 281, "requires_membership": 1, "event_name": "HIIT Club", "product_id": 401, "product_name": "HIIT Club", "product_variants": [{"product_variant_id": 677, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}, {"event_id": 313, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 408, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 685, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "subscriptions": [{"id": 1098, "product_id": 69, "product_variant_id": 86, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "m", "interval_quantity": null, "product_name": "Club Memberships", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-05-12 07:13:58", "last_bill_date": "2022-05-12 07:13:58", "next_bill_date": "2022-06-12 00:00:00", "final_bill_date": null}], "needs_membership": 0}, {"id": 3602, "first_name": "<PERSON>", "last_name": "R", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 3598, "first_name": "<PERSON><PERSON>", "last_name": "Oob", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [{"event_id": 274, "requires_membership": 1, "event_name": "Taekwondo", "product_id": 397, "product_name": "Taekwondo", "product_variants": [{"product_variant_id": 673, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "subscriptions": [], "needs_membership": 1}, {"id": 3604, "first_name": "Midders", "last_name": "M", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 3593, "first_name": "Mid", "last_name": "Night", "group_member_role_id": 9, "group_member_role": "Admin", "outstanding_charges": [{"event_id": 315, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 410, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 687, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "subscriptions": [{"id": 1095, "product_id": 51, "product_variant_id": 33, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "y", "interval_quantity": null, "product_name": "Annual Facility Fee", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-04-10 04:30:30", "last_bill_date": "2022-04-10 04:30:30", "next_bill_date": "2023-04-10 00:00:00", "final_bill_date": "1970-01-01 23:59:59"}], "needs_membership": 0}, {"id": 3075, "first_name": "Midnight", "last_name": "<PERSON><PERSON>", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 2691, "first_name": "Natalie", "last_name": "St Jean", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [{"event_id": 313, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 408, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 685, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 314, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 409, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 686, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 315, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 410, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 687, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "subscriptions": [], "needs_membership": 0}], "has_signed_waiver": 0, "channel_texts": 0, "channel_emails": 1, "channel_push_notifications": 0}]}, "companyStaffDualRoleUser": {"errors": null, "data": [{"id": 3605, "first_name": "RoRo", "last_name": "<PERSON><PERSON>", "middle_name": "Ro", "email": "<EMAIL>", "mobile_phone": "", "home_phone": null, "dob": "2015-03-01", "username": "RoRo", "user_status_id": 1, "api_key": null, "remember_token": 0, "created_at": "2022-03-09T22:42:10.000000Z", "address1": "", "address2": "", "city": "", "state": "", "postal_code": "", "country": "", "profile_img_path": null, "cover_img_path": null, "signed_waiver_path": "https://impact-upload-storage.us-east-1.linodeobjects.com/documents/user-3605/waiver-3605.pdf?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=U36ZC8AAZPEZKBLZ4IFQ%2F20220725%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20220725T051615Z&X-Amz-SignedHeaders=host&X-Amz-Expires=1200&X-Amz-Signature=a374dd67ccb675bfe50f07b85c34ab4520f83d7f3299a5a22bab54f610b340fc", "family_group_member_role_id": 8, "family_group_member_role": "Friend", "roles": [{"id": 5, "name": "Company Staff"}, {"id": 7, "name": "<PERSON><PERSON>"}], "managed_services": [{"id": 9, "name": "Midnight's Party"}, {"id": 15, "name": "The Best Service Ever!"}], "subscriptions": [{"id": 1097, "product_id": 69, "product_variant_id": 86, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "m", "interval_quantity": null, "product_name": "Club Memberships", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-05-12 06:47:37", "last_bill_date": "2022-05-12 06:47:37", "next_bill_date": "2022-06-12 00:00:00", "final_bill_date": null}], "outstanding_charges": [{"event_id": 266, "requires_membership": 1, "event_name": "The Northern Lights", "product_id": 392, "product_name": "The Northern Lights", "product_variants": [{"product_variant_id": 667, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 280, "requires_membership": 1, "event_name": "HIIT Club", "product_id": 400, "product_name": "HIIT Club", "product_variants": [{"product_variant_id": 676, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "payment_profiles": [], "needs_membership": 0, "family": [{"id": 3603, "first_name": "<PERSON><PERSON>", "last_name": "RRR", "group_member_role_id": 11, "group_member_role": "Child", "outstanding_charges": [{"event_id": 281, "requires_membership": 1, "event_name": "HIIT Club", "product_id": 401, "product_name": "HIIT Club", "product_variants": [{"product_variant_id": 677, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}, {"event_id": 313, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 408, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 685, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "subscriptions": [{"id": 1098, "product_id": 69, "product_variant_id": 86, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "m", "interval_quantity": null, "product_name": "Club Memberships", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-05-12 07:13:58", "last_bill_date": "2022-05-12 07:13:58", "next_bill_date": "2022-06-12 00:00:00", "final_bill_date": null}], "needs_membership": 0}, {"id": 3602, "first_name": "<PERSON>", "last_name": "R", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 3598, "first_name": "<PERSON><PERSON>", "last_name": "Oob", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [{"event_id": 274, "requires_membership": 1, "event_name": "Taekwondo", "product_id": 397, "product_name": "Taekwondo", "product_variants": [{"product_variant_id": 673, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "subscriptions": [], "needs_membership": 1}, {"id": 3604, "first_name": "Midders", "last_name": "M", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 3593, "first_name": "Mid", "last_name": "Night", "group_member_role_id": 9, "group_member_role": "Admin", "outstanding_charges": [{"event_id": 315, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 410, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 687, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "subscriptions": [{"id": 1095, "product_id": 51, "product_variant_id": 33, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "y", "interval_quantity": null, "product_name": "Annual Facility Fee", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-04-10 04:30:30", "last_bill_date": "2022-04-10 04:30:30", "next_bill_date": "2023-04-10 00:00:00", "final_bill_date": "1970-01-01 23:59:59"}], "needs_membership": 0}, {"id": 3075, "first_name": "Midnight", "last_name": "<PERSON><PERSON>", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 2691, "first_name": "Natalie", "last_name": "St Jean", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [{"event_id": 313, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 408, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 685, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 314, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 409, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 686, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 315, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 410, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 687, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "subscriptions": [], "needs_membership": 0}, {"id": 3649, "first_name": "Master", "last_name": "Chief", "group_member_role_id": 9, "group_member_role": "Admin", "outstanding_charges": [{"event_id": 311, "requires_membership": 0, "event_name": "TFT Tournament", "product_id": 407, "product_name": "TFT Tournament", "product_variants": [{"product_variant_id": 684, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 276, "requires_membership": 1, "event_name": "Taekwondo", "product_id": 399, "product_name": "Taekwondo", "product_variants": [{"product_variant_id": 675, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "subscriptions": [], "needs_membership": 1}], "has_signed_waiver": 1, "channel_texts": 0, "channel_emails": 1, "channel_push_notifications": 0}]}, "patronUser2": {"errors": null, "data": [{"id": 1334, "first_name": "StarSearch", "last_name": "Andromeda", "middle_name": "", "email": "<EMAIL>", "mobile_phone": "************", "home_phone": null, "dob": "1982-01-01", "username": "test333", "user_status_id": 1, "api_key": null, "remember_token": 0, "created_at": "2022-05-09T17:06:49.000000Z", "address1": "", "address2": "", "city": "", "state": "", "postal_code": "", "country": "", "profile_img_path": null, "cover_img_path": null, "signed_waiver_path": null, "family_group_member_role_id": null, "family_group_member_role": null, "roles": [{"id": 7, "name": "<PERSON><PERSON>"}], "managed_services": [], "subscriptions": [{"id": 4, "product_id": 286, "product_variant_id": 435, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "m", "interval_quantity": null, "product_name": "Service Membership Otter Fun", "product_variant_name": "default", "first_bill_date": "2022-05-11 14:22:02", "last_bill_date": "2022-05-11 14:22:02", "next_bill_date": "2022-06-11 00:00:00", "final_bill_date": null}], "outstanding_charges": [{"event_id": 1245, "requires_membership": 0, "event_name": "Parents Night Out", "product_id": 325, "product_name": "Parents Night Out", "product_variants": [{"product_variant_id": 475, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "payment_profiles": [], "needs_membership": 0, "family": [{"id": 1330, "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "St Jean", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [{"id": 2, "product_id": 286, "product_variant_id": 435, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "m", "interval_quantity": null, "product_name": "Service Membership Otter Fun", "product_variant_name": "default", "first_bill_date": "2022-05-11 14:15:48", "last_bill_date": "2022-05-11 14:15:48", "next_bill_date": "2022-06-11 00:00:00", "final_bill_date": null}], "needs_membership": 0}, {"id": 1329, "first_name": "Natalie", "last_name": "St Jean", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 1331, "first_name": "NataliePatron", "last_name": "St Jean", "group_member_role_id": 11, "group_member_role": "Child", "outstanding_charges": [{"event_id": 1244, "requires_membership": 0, "event_name": "Athletic Training Day", "product_id": 324, "product_name": "Athletic Training Day", "product_variants": [{"product_variant_id": 474, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "subscriptions": [], "needs_membership": 0}], "has_signed_waiver": 0, "channel_texts": 0, "channel_emails": 1, "channel_push_notifications": 0}]}, "listUsers": {"errors": null, "data": {"users": [{"id": 3599, "username": "natalie<PERSON><PERSON>", "first_name": "NataliePatron", "last_name": "St Jean", "middle_name": "", "email": "<EMAIL>", "mobile_phone": "4138888888", "home_phone": null}, {"id": 3597, "username": "natal<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "St Jean", "middle_name": "", "email": "<EMAIL>", "mobile_phone": "4138888888", "home_phone": null}, {"id": 2691, "username": "natal<PERSON><PERSON><PERSON>", "first_name": "Natalie", "last_name": "St Jean", "middle_name": "", "email": "<EMAIL>", "mobile_phone": "4136362614", "home_phone": null}, {"id": 3605, "username": "RoRo", "first_name": "RoRo", "last_name": "<PERSON><PERSON>", "middle_name": "Ro", "email": "<EMAIL>", "mobile_phone": "", "home_phone": null}, {"id": 3649, "username": "@@", "first_name": "Master", "last_name": "Chief", "middle_name": "", "email": "<EMAIL>", "mobile_phone": "", "home_phone": null}], "this_page": 1, "page_record_count": 5, "total_record_count": 5}}, "adminUser": {"errors": null, "data": [{"id": 3603, "first_name": "<PERSON><PERSON>", "last_name": "RRR", "middle_name": "<PERSON>", "email": "<EMAIL>", "mobile_phone": "", "home_phone": null, "dob": "1989-04-01", "username": "SkylerR", "user_status_id": 1, "api_key": null, "remember_token": 0, "created_at": "2022-03-08T18:54:50.000000Z", "address1": "", "address2": "", "city": "", "state": "", "postal_code": "", "country": "", "profile_img_path": null, "cover_img_path": null, "signed_waiver_path": null, "family_group_member_role_id": 11, "family_group_member_role": "Child", "roles": [{"id": 4, "name": "Company Super Admin"}], "managed_services": [{"id": 9, "name": "Midnight's Party"}], "subscriptions": [{"id": 1098, "product_id": 69, "product_variant_id": 86, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "m", "interval_quantity": null, "product_name": "Club Memberships", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-05-12 07:13:58", "last_bill_date": "2022-05-12 07:13:58", "next_bill_date": "2022-06-12 00:00:00", "final_bill_date": null}, {"id": 1104, "product_id": 51, "product_variant_id": 33, "subscription_status_id": 3, "subscription_status": "Cancelled", "bill_interval": "y", "interval_quantity": null, "product_name": "Annual Facility Fee", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-07-30 19:51:53", "last_bill_date": "2022-07-30 19:51:53", "next_bill_date": "2023-07-28 00:00:00", "final_bill_date": null}, {"id": 1105, "product_id": 52, "product_variant_id": 35, "subscription_status_id": 4, "subscription_status": "Expired", "bill_interval": "m", "interval_quantity": null, "product_name": "Individual Membership", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-07-30 19:51:53", "last_bill_date": "2022-07-30 19:51:53", "next_bill_date": "2022-08-28 00:00:00", "final_bill_date": null}], "outstanding_charges": [{"event_id": 281, "requires_membership": 1, "event_name": "HIIT Club", "product_id": 401, "product_name": "HIIT Club", "product_variants": [{"product_variant_id": 677, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}, {"event_id": 313, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 408, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 685, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "payment_profiles": [], "needs_membership": 0, "family": [{"id": 3605, "first_name": "RoRo", "last_name": "<PERSON><PERSON>", "group_member_role_id": 8, "group_member_role": "Friend", "outstanding_charges": [{"event_id": 266, "requires_membership": 1, "event_name": "The Northern Lights", "product_id": 392, "product_name": "The Northern Lights", "product_variants": [{"product_variant_id": 667, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 280, "requires_membership": 1, "event_name": "HIIT Club", "product_id": 400, "product_name": "HIIT Club", "product_variants": [{"product_variant_id": 676, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "subscriptions": [{"id": 1097, "product_id": 69, "product_variant_id": 86, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "m", "interval_quantity": null, "product_name": "Club Memberships", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-05-12 06:47:37", "last_bill_date": "2022-05-12 06:47:37", "next_bill_date": "2022-06-12 00:00:00", "final_bill_date": null}], "needs_membership": 0}, {"id": 3602, "first_name": "<PERSON>", "last_name": "R", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 3598, "first_name": "<PERSON><PERSON>", "last_name": "Oob", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [{"event_id": 274, "requires_membership": 1, "event_name": "Taekwondo", "product_id": 397, "product_name": "Taekwondo", "product_variants": [{"product_variant_id": 673, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "2.00", "product_status_id": 1}]}], "subscriptions": [], "needs_membership": 1}, {"id": 3604, "first_name": "Midders", "last_name": "M", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 3593, "first_name": "Mid", "last_name": "Night", "group_member_role_id": 9, "group_member_role": "Admin", "outstanding_charges": [{"event_id": 315, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 410, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 687, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "subscriptions": [{"id": 1095, "product_id": 51, "product_variant_id": 33, "subscription_status_id": 1, "subscription_status": "Active", "bill_interval": "y", "interval_quantity": null, "product_name": "Annual Facility Fee", "product_variant_name": "<PERSON><PERSON><PERSON>", "first_bill_date": "2022-04-10 04:30:30", "last_bill_date": "2022-04-10 04:30:30", "next_bill_date": "2023-04-10 00:00:00", "final_bill_date": "1970-01-01 23:59:59"}], "needs_membership": 0}, {"id": 3075, "first_name": "Midnight", "last_name": "<PERSON><PERSON>", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [], "subscriptions": [], "needs_membership": 0}, {"id": 2691, "first_name": "Natalie", "last_name": "St Jean", "group_member_role_id": 12, "group_member_role": "Guardian", "outstanding_charges": [{"event_id": 313, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 408, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 685, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 314, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 409, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 686, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}, {"event_id": 315, "requires_membership": 0, "event_name": "The Event of All Events!!", "product_id": 410, "product_name": "The Event of All Events!!", "product_variants": [{"product_variant_id": 687, "product_variant_name": "<PERSON><PERSON><PERSON>", "price": "1.00", "product_status_id": 1}]}], "subscriptions": [], "needs_membership": 0}], "has_signed_waiver": 0, "channel_texts": 0, "channel_emails": 1, "channel_push_notifications": 0}]}}