import React, { useState, useEffect, useCallback, useRef } from 'react';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { Container, Card, Breadcrumb, Table } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import { authUserHasModuleAccess } from '../../../utils/auth';

import Email from '../../../api/Email';

import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';
import styles from './EmailTemplateDashboard.module.scss'
import { set } from 'date-fns';

const EDIT_DEFAULT_MODULE_ID = 317;

export const EmailTemplateDashboard = () => {

    const mountedRef = useRef(false);
    const templatesByType = useRef({default: [], company: [], combined:[]})
    const history = useHistory();
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [ emailTemplates, setEmailTemplates ] = useState([]);
    const [ defaultActive, setDefaultActive] = useState(false);
    const [ userModulePermission, setUserModulePermission ] = useState(false);

    const removeDefaults = useCallback((data)=>{
        let defaults = data.filter((template) => template.company_id === null);
        let company = data.filter((template) => template.company_id )
        templatesByType.current = {default: [...defaults], company: []}
        let addDefaults = []
        for(let i = 0; i < defaults.length; i++){
            //see if the company has this email type or not
            let found = company.find((template)=>template.email_template_type_id === defaults[i].email_template_type_id);
            defaults[i].company_id = company[0].company_id; //steal the id from any of the other emails, they will all be the same.  This way, when it's edited, it will no longer be default for this company.
            defaults[i].copy = true;
            if(!found) addDefaults.push(defaults[i]);
        }
        templatesByType.current.company = [...company];
        templatesByType.current.combined = [...addDefaults, ...company];
        return [...addDefaults, ...company];
    },[]);

    const checkPermission = useCallback(async ()=>{
        try{
            let response = await authUserHasModuleAccess(EDIT_DEFAULT_MODULE_ID);
            if(response){
                setUserModulePermission(response);
            }
        }catch(ex){
            console.error(ex)
        }
    },[])

    useEffect(()=>{
        mountedRef.current = true

        const getAllEmails=async()=>{
            try{
                let response = await Email.templates.get()
                let filtered = removeDefaults(response.data)
                setEmailTemplates(filtered)
                setLoading(false)
            }catch(ex){
                console.error(ex)
            }
        } 

        getAllEmails();
        checkPermission();

        return()=> mountedRef.current = false
    },[removeDefaults, checkPermission]);

    const handleDefaultSwitch = async()=>{
        //we're using the current state BEFORE the button was pushed, so it's going to be opposite in this function
        if(defaultActive) setEmailTemplates(templatesByType.current.combined);
        else if(!defaultActive) setEmailTemplates(templatesByType.current.default);
        setDefaultActive(!defaultActive);
    }

    return (
        <Container fluid>
            <div className="header">
                <Breadcrumb>
                    <Breadcrumb.Item href="/p/">Home</Breadcrumb.Item>
                    <Breadcrumb.Item active>Template Dashboard</Breadcrumb.Item>
                </Breadcrumb>
            </div>
            <Card className={`content-card ${styles["email-template-dash-wrapper"]}`}>
                <div className={styles["header-row"]}>
                    <h4 className="section-title">
                        Email Templates
                    </h4>
                        {userModulePermission[317] &&
                            <p>
                                <button onClick={handleDefaultSwitch}>
                                    {defaultActive ? "Switch to Company" : "Switch to Defaults"}
                                </button>
                                {/* <button onClick={()=>history.push("/p/email/text/newdefault")}>
                                    New Default
                                </button> */}
                            </p>
                        }
                        {/* <button onClick={()=>history.push('/p/emailTemplates/Edit/Simple')}>
                            Simple New
                        </button>
                        <button onClick={()=>history.push('/p/emailTemplates/Edit/Advanced')}>
                            Advanced New
                        </button> */}
                </div>
                <div>
                    {loading &&
                        <SkeletonTheme color="#e0e0e0">
                            <div className="mt-3 text-center">
                                <Skeleton height={28} width={200}/>
                                <Skeleton height={16} count={10} />
                            </div>
                        </SkeletonTheme>
                    }
                    {!loading && emailTemplates?.length > 0 &&
                        <Table>
                            <thead>
                                <tr>
                                    <td>
                                        Id
                                    </td>
                                    <td>
                                        Name
                                    </td>
                                    <td>
                                        Subject
                                    </td>
                                    <td>
                                        Edit Text
                                    </td>
                                    {/* <td>
                                        Advanced Edit
                                    </td>
                                    <td>
                                        Simple Edit
                                    </td> */}
                                </tr>
                            </thead>
                            <tbody>
                                {emailTemplates?.map((template)=>(
                                    <tr key={template.id}>
                                        <td>
                                            {template.id}
                                        </td>
                                        <td>
                                            {template.name}
                                        </td>
                                        <td>
                                            {template.subject}
                                        </td>
                                        <td onClick={()=>{history.push(`/p/email/text/${template.id}`)}}>
                                            <i className="fas fa-pencil-alt" />
                                        </td>
                                        {/* <td>
                                            <i className="fas fa-pen-fancy" />
                                        </td> */}
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    }
                </div>
            </Card>
        </Container>
    )
}
