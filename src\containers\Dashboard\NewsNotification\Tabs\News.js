import React, { useContext } from 'react';
import { FileURLContext } from '../../../../contexts/FileURLContext';
import './News.scss';

export const News = () => {

    const imageURL = useContext(FileURLContext);

    return (
        <React.Fragment>
        <div>
           <img src={imageURL.base+'news.png'} alt="IMPACT ATHLETICS NEWS" className="news-img" />
        <p className="news-desc">Welcome to Impact!
        </p>
        </div>
           
        
        </React.Fragment>


    )
}