@import '../../../../../assets/css/scss/variables';
@import '../../../../../assets/css/scss/themes';

.plan {
    padding: 2rem !important;
    margin: 2px 0;
    background-color: $card-background-color;
    border-radius: $card-border-radius;
    color: $card-color;
    //border: 2px solid transparent;
    transition: all 0.25s ease-in-out;

    h4{
        font-size: 1.5rem;
        font-weight: 500;
        font-family: $secondary-font-family;
        width:100%;
    }
 
    &.active{
        border: 3px solid $primary-color !important;
        position: relative;
        margin: 1rem 0;
        padding: 3rem !important;

        &::before{
            content: '';
            position: absolute;
            top:0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: $primary-color;
            opacity: 0.055;
        }
    }

    .total {
        text-align: right;
        margin-top: 1rem;
        white-space: nowrap;
        font-weight: 400;
        font-size: 0.65rem;
        font-family: $secondary-font-family;
        text-transform: uppercase;
        
        span {
            font-size: 1rem;
            font-weight: 500;
            font-family: $secondary-font-family;
        }
    }    
}