@import '../../../assets/css/scss/variables.scss';
@import '../../../assets/css/scss/themes.scss';

.calendar-event-modal-wrapper {
    display: flex;
    flex-direction: column;
    margin-left: auto;
    margin-right: auto;
    width: 90%;

    & h4 {
        margin-top: $main-padding;
    }

    & p {
        margin-bottom: 2px;
    }
}

.calendar-event-rb-modal .modal-body {
    padding: $main-padding;
}
#calendar-event-date-modal-body{
    .react-datepicker__triangle{
        display: none;
    }
}