import Request from './Api';

const get = async (props) => {

    // use test data on localhost only

    let discount={};
    //if (["localhost", "127.0.0.1", ""].includes(window.location.hostname)){

    // gets mock data for testing - will be removed when api is ready
    let mockdata = await test();
    if (!props) discount=mockdata;
    else {
        mockdata.forEach((item,i,test) => {
            if (props.id) {
                if (item.id===parseInt(props.id)){
                    discount=test[i];
                    return false;
                }
            }
        });
    }

    return (
        Request({
            url: "/discounts/" + (!props ? "" : props.id),
            test: {data:discount} // send mock data to simulate an api call
        })
    );
}

// create
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Group Name is required", "Email is required."]};
    let mockdata = {data:1,errors:null};

    return (
        Request({
            url: "/discounts/create",
            data: {props},
            test: mockdata // send mock data to simulate an api call
        })
    );
}

// update
const update = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Group Name is required", "Email is required."]};
    let mockdata = {data:1,errors:null};

    return (
        Request({
            url: "/discounts/update",
            data: {props},
            test: mockdata // send mock data to simulate an api call
        })
    );
}

// sets up mock data
const test = async() => {
    return [
		{
			id: 1,
			name: "Product Discount",
			code: "var discount, product_id;if (product_id === 1 || 1===1) {discount = 5;}"
		},
		{
			id: 2,
			name: "Black Friday",
			code: "var discount, coupon_code;if (coupon_code === 'BLACKFRIDAYLIVESMATTER') {discount = 30;}"
		},
	];
}


const Discounts = {
	get, create, update/*, delete, etc. ...*/
}
  
export default Discounts;