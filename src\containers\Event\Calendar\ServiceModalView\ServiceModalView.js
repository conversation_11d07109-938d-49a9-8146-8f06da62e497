import React, {useState, useEffect, useRef, useCallback} from 'react'
import {format} from 'date-fns'

import Services from '../../../../api/Services';
import Events from '../../../../api/Events';

export const ServiceModalView = (props) => {

    const mountedRef=useRef(false);
    const [modalEvent, setModalEvent]=useState();
    const [loading, setLoading]=useState(true);

    const convertDates=useCallback((times)=>{
        if(times?.startDate==="1000-01-01") times.startDate=""
        if(times?.endDate.substr(0,4)==="3000") times.endDate=""
        setModalEvent(props.serviceEvent)
    },[props.serviceEvent]);

    useEffect(()=>{
        mountedRef.current = true

        return ()=>{
            mountedRef.current = false
            setModalEvent();
        } 
    },[]);

    useEffect(()=>{
        if(props.serviceEvent.id && props.serviceEvent?.props?.is_service){
            convertDates(props.serviceEvent.props.times);
        }
    },[props, convertDates])

    useEffect(()=>{
        if(modalEvent){
            setLoading(false);
        }
    },[modalEvent]);

  return (
    <>
        {modalEvent && !loading &&
            <>
                <div className="d-flex flex-column align-items-center">
                    <h5>{modalEvent?.title}</h5>
                    <p>
                        {modalEvent.props.times.startDate ? 
                            <span>From: {modalEvent?.props?.times?.startDate}</span>
                            :
                            <span>No provided start date.</span>
                        } 
                        <br />
                        {modalEvent.props.times.endDate ?
                            <span>To: {modalEvent?.props?.times?.endDate}</span>
                            :
                            <span>No scheduled end date.</span>
                        }
                    </p>
                    <p>
                        <span>Minimum Participants: {modalEvent?.props?.min_participants}</span> 
                        <br />
                        <span>Maximum Participants: {modalEvent?.props?.max_participants}</span>
                        <br />
                        {/* <span>Registered Participants: </span> */}
                    </p>
                </div>
                <hr />
                <div>
                    <p
                        dangerouslySetInnerHTML={{ __html: modalEvent?.props?.description }}
                    />
                </div>
            </>
        }
    </>
  )
}
