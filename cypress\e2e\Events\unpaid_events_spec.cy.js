/*eslint-disable*/
//Before running through the tests, set the parameters as you desire in the before

let qaDevPatron = Cypress.env('impact_patron_user');
let devPatron = Cypress.env('impact_patron_symbol_name');
let qaDevStaff = Cypress.env('impact_staff_user');
let qaDevAdmin = Cypress.env('impact_admin_user');
let qaDevSB = Cypress.env('impact_sb_user');
let password = Cypress.env('login_password');
let baseUrl = "https://portal-qa.impactathleticsny.com/p/";

describe("This test will run through adding and removing events from the cart", {scrollBehavior: "center", testIsolation: false}, ()=>{
    
    //setting up any variables needed from intercepts or throughout the tests
    let outstanding;
    let local;
    let firstName;
    let eventName;

    before("It will log a user in and determine the company id",()=>{
    //Choose a URL and company ID
        cy.visit(baseUrl);

    //Choose a user
        // cy.get('[data-cy="login-username"]').type(qaDevSB); //QA or Dev SiteBoss
        // cy.get('[data-cy="login-username"]').type(qaDevPatron); //QA or Dev Patron 
        // cy.get('[data-cy="login-username"]').type(devPatron); //Dev Patron - using this one ensures symbols can still be used to log in
        // cy.get('[data-cy="login-username"]').type(qaDevAdmin); //QA or Dev Admin
        cy.get('[data-cy="login-username"]').type(qaDevStaff); //QA or Dev Staff, also has patron role as a lot of the staff do
        
        cy.get('[data-cy="login-password"]').type(password);
        cy.get('[data-cy="login-submit"]').click()

        cy.intercept('GET', "/api/user/user/**").as('getUserUser');
        cy.intercept('POST', "/api/user/outstanding_charges").as('getOutstandingCharges');
        cy.wait('@getOutstandingCharges').then((response)=>{
            outstanding = (JSON.parse(response.response.body)).data
            cy.log(outstanding)
        })
        cy.wait('@getUserUser').then(()=>{
            local = (localStorage.getItem('user'))
        })
    })//end before
    
    beforeEach("restore local storage", ()=>{
        cy.restoreLocalUser(JSON.parse(local))
        firstName = JSON.parse(local).profile.first_name
    }); //end restore local user

    it("will register for a new event",()=>{
        cy.intercept('POST', '/api/event').as('getEvents');
        cy.intercept('POST', '/api/user/event/register').as('registerEvent');
        cy.intercept('POST', '/api/product').as('getProduct')
        // cy.intercept('POST', '/api/order/add').as('addOrder');
        cy.intercept('PUT', '/api/order/update').as('updateOrder');
        cy.get('[data-cy="home-eventList"]')
            //have to force because cypres scrolls the screen and in doing so, covers part of the event cards with the header and cannot click them without force
            .contains("Pickleball").click({force: true})
        cy.get('[data-cy="register-event-name"]')
            .invoke('text')
            .as('eventName');
        cy.get('@eventName').then(($text)=>{
            eventName = $text
        })
        cy.get('.orange-btn')
            .contains('Register')
            .wait(1000)
            .click()
        cy.wait('@registerEvent')
        cy.wait(1000)
        cy.wait('@getProduct')
        cy.wait('@updateOrder')
        cy.wait('@getEvents')
        // cy.wait('@addOrder')
        cy.get('.modal-content')
            .get('button')
            .contains("OK")
            .click()
    
    })//end register for a new event

    it("will check that event exists in the cart and remove it from the cart",()=>{
        cy.url().should('equal', baseUrl)
        cy.get('.cart-btn')
            .should('be.visible')
            .click()
        cy.get('[data-cy="preview-item"]')
            .invoke('text')
            .should('contain', firstName)
            .and('contain', eventName)
        cy.get('[data-cy="delete-prod-btn"]')
            .should('be.visible')
            .click()
        cy.get('button')
            .should('have.class', "btn-primary")
            .contains('Yes')
            .click()
        
        cy.log(`${String.fromCodePoint(0x1F92F)}Will make sure that the cart now says it's empty`)
        cy.get('.cart-btn')
            .click({force: true})
        cy.get('.popover-body')
            .invoke('text')
            .should('contain', 'Your cart is empty.')
    })

    it("will click on an event but not add it to the cart by clicking 'no'",()=>{
        cy.window().its("store")
            .invoke("getState")
            .its('pos')
            .its('7')
            .its('items')
            .should('have.length', 0)
        cy.get('[data-cy="home-each-charge-row"]')
            .children()
            .eq(0)
            .click()
        cy.get('.btn-secondary')
            .contains("No")
            .should('be.visible')
            .click()
            
        cy.log(`${String.fromCodePoint(0x1F92F)}it will check to make sure that Redux and the cart have not changed`)
        cy.window()
            .its("store")
            .invoke("getState")
            .its('pos')
            .its('7')
            .its('items')
            .should('have.length', 0)
    })//end click on an event and not add it

    it("will add an item to the cart",()=>{
        cy.log(eventName)
        cy.get('[data-cy="home-each-charge-row"]')
            .each(($row)=>{
                cy.get($row)
                let text = $row.text()
                if(text.includes(firstName) && text.includes(eventName)){
                    cy.get($row)
                    .click({force:true})
                    return
                }
            })
        cy.get('[data-cy="add-charge-modal"]')
            .should('be.visible').within(()=>{
                cy.get('.modal-body')
                    .invoke('text')
                    .should('contain', "Add this event fee to your shopping cart?")
                cy.get('button')
                    .contains("No")
                    .should('be.visible')
                cy.get('button')
                    .contains("Unregister")
                    .should('be.visible')
                cy.get('button')
                    .contains("Yes")
                    .should('be.visible')
                    .click()
            })
            cy.wait(3000)

        cy.log(`${String.fromCodePoint(0x1F92F)} make sure that items have length of 1`)
        cy.window()
            .its("store")
            .invoke("getState")
            .its('pos')
            .its('7')
            .its('items')
            .should('have.length', 1)
        cy.get('.cart-num-items').invoke('text').should('contain', '1')

        cy.log(`${String.fromCodePoint(0x1F92F)}check that you cannot add the item again`)
        cy.get('[data-cy="home-each-charge-row"]')
        .each(($row)=>{
            cy.get($row)
            let text = $row.text()
            if(text.includes(firstName) && text.includes(eventName)){
                cy.get($row)
                    .should('have.class', "-50")
                    .click()
                return
            }
        })
        cy.get('[data-cy="add-charge-modal"]').should('be.visible').within(()=>{    
            cy.get('.modal-body')
                .invoke('text')
                .should('contain', "This event fee is already in your cart.")
            cy.get('button')
                .contains("No")
                .should('not.exist')
            cy.get('button')
                .contains("Unregister")
                .should('not.exist')
            cy.get('button')
                .contains("Yes")
                .should('not.exist')
        })
        cy.get('.close').click()

    })//end will add an item to the cart

    it("will remove the item from the cart", ()=>{
        cy.get('.cart-btn').click({force: true})
        cy.get('[data-cy="preview-item"]')
            .invoke('text')
            .should('contain', firstName)
            .and('contain', eventName)
            .get('[data-cy="delete-prod-btn"]')
            .click()
        cy.get('button')
            .should('have.class', "btn-primary")
            .contains('Yes')
            .click()
        cy.log(`${String.fromCodePoint(0x1F92F)} make sure that items have length of 0`)
        cy.window()
            .its("store")
            .invoke("getState")
            .its('pos')
            .its('7')
            .its('items')
            .should('have.length', 0)
    })

    it("will unregister for the event",()=>{
        cy.intercept('DELETE', 'api/user/event/remove').as('removeEvent');
        cy.get('[data-cy="home-each-charge-row"]')
            .each(($row)=>{
                cy.get($row)
                let text = $row.text()
                if(text.includes(firstName) && text.includes(eventName)){
                    cy.get($row)
                    .click({force: true})
                    return
                }
            })
        cy.get('[data-cy="add-charge-modal"]')
            .should('be.visible')
            .within(()=>{  
                cy.get('button')
                .contains("Unregister")
                .click()
                cy.wait('@removeEvent')
            })
        cy.log(`${String.fromCodePoint(0x1F92F)}it will make sure that the item has been removed from the unpaid event list`)
        cy.get('[data-cy="toast-message"]')
            .invoke('text')
            .should('include', 'User successfully unregistered from')
        if(outstanding.length === 0){
            cy.get(':nth-child(6) > .prof-bg > :nth-child(3) > div')
                .invoke('text')
                .should('include', 'There are no pending charges.')
        }
    })//end will unregister for an event
})

describe("It will run through the same tests of adding, removing, and unregistering a family member", ()=>{
    //setting up any variables needed from intercepts or throughout the tests
    let outstanding;
    let local;
    let firstName;
    let lastName;
    let eventName;

    before("It will log a user in and determine the company id",()=>{
    //Choose a URL and company ID
        cy.visit(baseUrl);

    //Choose a user
        //Make sure to use a user that has family for this part
        cy.get('[data-cy="login-username').type(qaDevPatron); //QA or Dev Patron 
        
        cy.get('[data-cy="login-password"]').type(password);
        cy.get('[data-cy="login-submit"]').click()

        cy.intercept('GET', "/api/user/user/**").as('getUserUser');
        cy.intercept('POST', "/api/user/outstanding_charges").as('getOutstandingCharges');
        cy.wait('@getOutstandingCharges').then((response)=>{
            outstanding = (JSON.parse(response.response.body)).data
        })
        cy.wait('@getUserUser').then(()=>{
            local = (localStorage.getItem('user'))
        })
        })//end before

    beforeEach("restore local storage", ()=>{
        cy.restoreLocalUser(JSON.parse(local))
    }); //end restore local user
    
    it("will add an event for a family member",()=>{
        cy.intercept('POST', '/api/user/event/register')
            .as('registerEvent');
        cy.intercept('POST', '/api/product')
            .as('getProduct');
        cy.intercept('POST', '/api/order/add')
            .as('addOrder');
        cy.intercept('PUT', '/api/order/update')
            .as('updateOrder');
        cy.get('[data-cy="home-eventList"]')
            .contains("Pickleball")
            .click({force: true})
        cy.get('[data-cy="register-event-name"]')
            .invoke('text')
            .as('eventName');
        cy.get('@eventName').then(($text)=>{
            eventName = $text
        })
        cy.get('#participant')
            .select(2)
            .invoke('val').as('participant')
        cy.get('@participant').then(($text)=>{
            firstName = JSON.parse($text).first_name
            lastName = JSON.parse($text).last_name
        }).then(()=>{
            cy.get('.orange-btn')
                .contains('Register')
                .wait(1000)
                .click()
            cy.get('.modal-content')
                .get('button')
                .contains('OK')
                .click()
            cy.wait(3000)

            cy.log(`${String.fromCodePoint(0x1F92F)}It will make sure the family member charge has been added to the cart and redux`)
            cy.get('.cart-btn').click()
            cy.log(firstName)
            cy.get('[data-cy="preview-item"]')
                .invoke('text')
                .should('contain', firstName)
            cy.window()
                .its('store')
                .invoke('getState')
                .its('pos')
                .its('7')
                .its('items')
                .its('0')
                .its('event')
                .its('for_user_name')
                .should('equal', firstName + " " + lastName)
        })

    })//end will add an event for a family member
    
    it("will remove the charge for the family member from the cart",()=>{
        cy.get('.cart-btn').click()
        cy.get('[data-cy="preview-item"]')
            .invoke('text')
            .should('contain', firstName)
            .and('contain', eventName)
        cy.get('[data-cy="delete-prod-btn"]')
            .should('be.visible')
            .click()
        cy.get('button')
            .should('have.class', "btn-primary")
            .contains('Yes')
            .click()
        cy.log(`${String.fromCodePoint(0x1F92F)}it will make sure that charge has been removed from the cart/redux`)
        cy.get('.cart-btn')
            .click()
            .wait(250)
        cy.get('.popover-body')
            .invoke('text')
            .should('contain', 'Your cart is empty.')
        cy.window().its('store')
            .invoke('getState')
            .its('pos')
            .its('7')
            .its('items')
            .should('have.length', 0)
    })//end will remove the charge for the family member from the cart
    
    it("will unregister for the event for the family member",()=>{
        cy.intercept('DELETE', 'api/user/event/remove').as('removeEvent');
        cy.intercept('PUT', 'api/order/update').as('updateOrder');
        cy.get('[data-cy="home-each-charge-row"]')
            .children()
            .its('length')
            .as('numberOfRows')
        cy.get('[data-cy="home-each-charge-row"]')
            .each(($row)=>{
                cy.get($row)
                let text = $row.text()
                if(text.includes(firstName) && text.includes(eventName)){
                    cy.get($row)
                        .click()
                    return
                }
            })
        cy.get('button')
            .contains('Unregister')
            .click()
        cy.wait('@removeEvent')
        cy.wait('@updateOrder')
        //after waiting for the call, giving the UI time to update
        cy.wait(2000)

        cy.log(`${String.fromCodePoint(0x1F92F)}it will make sure that event has been removed from the list`)
        cy.get('[data-cy="home-each-charge-row"]')
            .children()
            .its('length')
            .as('rowsAfterRemoval')
        cy.get('@numberOfRows').then(count=>{
            cy.get('@rowsAfterRemoval').then(countAfter=>{
                //children have 3 parts to each row, so it will have 3 less than the original rather than 1
                expect(count - 3).to.equal(countAfter)
            })
        })
    })//end will unregister for the event for the family member
})
