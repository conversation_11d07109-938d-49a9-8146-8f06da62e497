import React,{useEffect, useState, Suspense} from 'react';
import { useParams, useLocation, Link  } from "react-router-dom";
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Card from 'react-bootstrap/Card';
import Breadcrumb from 'react-bootstrap/Breadcrumb';
import ListGroup from 'react-bootstrap/ListGroup';
import SubHeader from '../../../components/common/SubHeader';
import Companies from '../../../api/Companies';

import BasicInfo from '../BasicInfo';
import ViewAssignFeatures from '../../Permissions/CompanyFeatures/ViewAssignFeatures';
import AssignUsers from '../AssignUsers';
import MenuItems from '../../MenuItems';

import './Details.css';
import { Configs } from '../../Configs/Configs';

const Details = (props) => {    
    const location = useLocation();
    const { id } = useParams();

    const [companyInfo,setCompanyInfo]=useState();

    useEffect(() => {

        // get the company info
        const _getCompany = async () => {
            try {
                let res=await Companies.get({id:id});
                if (res?.data?.length>0 && mounted) {
                    if (Array.isArray(res.data)) res.data=res.data[0];
                    setCompanyInfo(res.data);
                    setPagePart(
                        <Suspense fallback={             
                            <SkeletonTheme color="#e0e0e0">
                                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                                <Skeleton height={12} count={5} />
                            </SkeletonTheme>
                        }>
                            <BasicInfo company={res.data} company_id={res.data.id} referer={location.pathname} />
                        </Suspense>
                    );                    
                }
            } catch (e){
                console.error(e);
            }
        }
        

        let mounted = true;
        _getCompany();


        // add a listener to the hash change event
        window.addEventListener("hashchange", (e) => {
            let hash=e.target.location.hash.substr(1);
            let link=document.querySelector(`.profileMenu a[href="#${hash}"]`);
            if (link) link.click();
        });

        



        return () => {
            mounted = false;
            
        }
	}, [id, location.pathname]);
    
    const [pagePart,setPagePart]=useState(
        <Suspense fallback={             
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
            </SkeletonTheme>
        }>
            
        </Suspense>
    );

    const loadPagePartHandler= (e) => {
        let component;
        console.log(e.target.hash.substr(1));
        switch (e.target.hash.substr(1)){
            case "AssignUsers":
                component=<AssignUsers company_id={companyInfo.id} referer={location.pathname} />;
                break;
            case "AssignFeatures":
                component=<ViewAssignFeatures company={companyInfo} />
                break;
            case "EditConfigs":
                component=<Configs company_id={companyInfo.id} />
                break;
            case "BasicInfo":
            default:
                component=<BasicInfo company={companyInfo} company_id={companyInfo.id} referer={location.pathname} />;
                break;
        }
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {component}
            </Suspense>
        );
    }

    if (!companyInfo) return (
        <Container fluid>
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
                <Skeleton height={30} style={{marginBottom:"1rem",marginTop:"2rem"}} />
                <Skeleton height={12} count={10} />
            </SkeletonTheme>
        </Container>
    );

    return ( companyInfo &&
        <Container fluid>
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/companies/dashboard" }, text: "Company Dashboard" },
                { text: companyInfo.name }
            ]} />
            <Card className="content-card">
                <Row>
                    <Col sm="auto" className="order-1 order-lg-2">
                        <ListGroup className="profileMenu" variant="flush">
                            <ListGroup.Item action href="#BasicInfo" onClick={loadPagePartHandler}>
                                <i className="far fa-store-alt"></i> Edit Company
                            </ListGroup.Item>
                            <ListGroup.Item action href="#AssignFeatures" onClick={loadPagePartHandler}>
                                <i className="far fa-pennant" /> Assign Features
                            </ListGroup.Item>
                            <ListGroup.Item action href="#EditConfigs" onClick={loadPagePartHandler}>
                                <i className="far fa-tools" /> Edit Configs
                            </ListGroup.Item>
                            {/*
                            <ListGroup.Item action href="#AssignUsers" onClick={loadPagePartHandler}>
                                <i className="far fa-users"></i> Assign Users
                            </ListGroup.Item>
                            <ListGroup.Item action href="#Assets">
                                <i className="far fa-pencil-ruler"></i> Web Assets
                            </ListGroup.Item>
                            */}
                        </ListGroup>
                    </Col>
                    <Col className="order-2 order-lg-1">
                        {pagePart /*this is where the magic is happening :-O */ }
                    </Col>
                </Row>
            </Card>
        </Container>
    );
}

export default Details;