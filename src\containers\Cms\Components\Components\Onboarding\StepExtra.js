import React, {useRef, useState, useEffect, useCallback} from 'react';
import { Row, Col } from 'react-bootstrap';

import Forgot from '../Forgot';
import FormGroupText from '../FormGroupText';
import FormGroupButton from '../FormGroupButton';

import APIUsers from '../../../../../api/Users';
import APIGroups from '../../../../../api/Groups';
import styles from './Onboarding.module.scss';

export const StepExtra = (props) => {
    const {callback} = props;

    const usernameRef = useRef();
    const passwordRef = useRef();

    const [content, setContent] = useState({title: null, description: null, content: null});
    const [error, setError] = useState();
    const [submitting, setSubmitting] = useState(false);
    const [extraText, setExtraText] = useState(props.extra_text || null);
    const [value, setValue] = useState(props.value);

    const checkUserHandler = useCallback(async () => {
        setSubmitting(true);
        const username = usernameRef.current.querySelector("#username").value;
        const password = passwordRef.current.querySelector("#password").value;
        
        if (!username || !password) {
            setError("Please enter your user name and password");
            setSubmitting(false);
            return;
        }

        const res = await APIUsers.login({username, password});
        if (res.errors) setError(res.errors);
        else if (res.data){
            const _ret = [res.data.profile.first_name]
            
            // get a radom family member if any, because we want to say hi to them too like mama told us to
            const res2 = await APIGroups.groupFilter({group_type_id: 4, user_id:res.data.profile.id});
            if (res2.data?.groups?.[0]){
                const fam = res2.data.groups[0].group_members.filter(a=> a.user_id !== res.data.profile.id);
                if (fam.length > 0) _ret.push(fam[Math.floor(Math.random() * fam.length)].first_name);
            }

            // TODO: check if the user has a company

            setExtraText(_ret);
            setValue(3);
            callback(res.data.profile);

        }
        setSubmitting(false);
    }, [callback]);

    const forgotHandler = useCallback((e, type) => {
        e.preventDefault();
        e.stopPropagation();
        setContent({title: null, description: null, content: <Forgot type={type} close={()=>setValue(1)} callback={callback} />});
    }, [callback]);
    
    useEffect(() => {
        let _title = null, _description = null, _content = null;
        switch(+value){
            case 1: // load existing user
                _title = "Whoa!";
                _description = <p>It looks like you already created a user!<br/>Please enter your user name and password so we can load your existing information?</p>;
                _content = (
                    <>
                        <Col sm={12} lg={6}>
                            <FormGroupText ref={usernameRef} name="username" type="text" label="User" required feedback="Please enter your user name" disabled={submitting} />
                        </Col>
                        <Col sm={12} lg={6}>
                            <FormGroupText ref={passwordRef} name="password" type="password" label="Password" required feedback="Please enter your password" disabled={submitting} />
                        </Col>
                        <Col sm={12} md={true}>
                            <FormGroupButton label="Sign In" disabled={submitting} click={()=>checkUserHandler()} />
                        </Col>
                        <Col sm={12} md={"auto"}>
                            <a href="#!" onClick={e=>forgotHandler(e)}>Forgot your password?</a><br/>
                        </Col>
                    </>);
                break;
            case 2: // existing company
                _title = "Whoa!";
                _description = (
                    <p>
                        It looks like you have a company with us!
                        <br/><br/>
                        <a href="/p/login">Would you like to Sign In?</a>
                        <br/><br/>
                        If not, keep going to create a new company.
                    </p>);
                break;
            case 3: // hi there
                _title = `Hey ${extraText?.[0] || ""}!`;
                _description = (
                    <>
                        {extraText?.[1] && <p>How's {extraText[1]} doing?</p>}
                        <p>We're excited to help you get started with your new company. So when you're ready, click the button below to get started!</p>
                    </>);
                break
            default: // do nothing
                break;
        }
        setContent({title: _title, description: _description, content: _content});
    }, [value, submitting, extraText, checkUserHandler, forgotHandler]);

    useEffect(() => {
        return () => {
            setContent({title: null, description: null, content: null});
            setError(null);
            setSubmitting(false);
            setExtraText(null);
            setValue(null);
        }
    }, []);

    if (!content.title && !content.description && !content.content) return null;

    return (
        <>
            <Row>
                <Col sm={12}>
                    <div className={styles["step-description"]}>
                        <div className={styles.line}/>
                        <h4>{content.title}</h4>
                        {content.description}
                    </div>
                </Col>
                {content.content}
            </Row>
            {error && 
                <Row>
                    <Col sm={12} className="error-text-ctr">
                        {error}
                    </Col>
                </Row>
            }
        </>
    );
}