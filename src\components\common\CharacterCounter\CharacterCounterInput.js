import React, { useState, useEffect } from 'react';
import { Form } from 'react-bootstrap';
import './CharacterCounterInput.scss';

//src\containers\Permissions\Modules\CreateEditModuleForm\CreatEditModuleForm.js
//src\containers\Configs\ConfigComponents\CreateConfig.js

/** 
 * there is no limitedVariable imported, it will use a local state to determine the length of input regardless of onChange imported
 * the margins when it's row are set to bottom and right 0 - other two controlled by parent component
 */
export const CharacterCounterInput = ({
    characterLimit, 
    limitedVariable, //if a state is being passed down from the parent and held by the parent
    label="",
    required=false, 
    name, 
    placeholder="",
    value=null,  //if the state needs to be held onto here, rather than in the parent
    onChange=()=>{}, //if the variable needs to be passed back up to the parent
    columnRow = "column" //if column, will be column, else will be row
}) => {

    const [ variable, setVariable ] =useState(limitedVariable || "");

    useEffect(()=>{
        if(value && !variable && !limitedVariable) setVariable(value);

        //we are only using variable to check and see if it already exists or not, we don't want to create a loop
        //eslint-disable-next-line react-hooks/exhaustive-deps
    },[value])

    const localOnChange =(e)=>{
        if(!limitedVariable && limitedVariable !== ""){
            setVariable(e.target.value)
        }
        else onChange(e.target.value)
    }

    return (
        <Form.Group controlId="charactercounter" className={`character-counter-input-wrapper ${columnRow==="column" ? "column" : "row"}`}>
            <Form.Label>
                {label}
                {required && <span className="ms-1 required-star">*</span>}
            </Form.Label>
            <Form.Control
                type="text"
                required = {required}
                name = {name}
                placeholder = {placeholder}
                value = {limitedVariable ? limitedVariable : variable}
                onChange = {localOnChange}
            />
            {limitedVariable ?
                <Form.Control.Feedback className={`count-limit`} type={limitedVariable.length > characterLimit ? "invalid" : "valid"}>
                    {limitedVariable.length} / {characterLimit}
                </Form.Control.Feedback>
            :
                <Form.Control.Feedback className={`count-limit`} type={variable.length > characterLimit ? "invalid" : "valid"}>
                    {variable.length} / {characterLimit}
                </Form.Control.Feedback>
            }
        </Form.Group>
    )
}

export default CharacterCounterInput;