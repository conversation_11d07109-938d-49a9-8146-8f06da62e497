import React, {useCallback} from 'react';
import { Typeahead } from './Typeahead';
import Events from '../../api/Events';

/**Accespt the following parameters:
 * @param {{}} multiple
 * @param {{}} passSelection
 * @param {{}} placeholder
 * @param {{}} async
 */
const EventStatusTypeahead = (props) => {
    const getEventStatus = useCallback(async () => {
        try {
            let response = await Events.getEventStatus();
            let responseObj = {
                data: response.data || null,
                errors: response.errors || null
            }
            return responseObj;
        } catch (ex) {
            console.error(ex)
        }
    }, [])

    return (
        <Typeahead
            {...props}
            async={props.async}
            id="event-status-search"
            makeRequest={getEventStatus}
            placeholder={props.placeholder}
            paginated={false}
        />
    )
}

export default EventStatusTypeahead;