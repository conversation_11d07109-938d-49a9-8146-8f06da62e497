import React from 'react';
import {getMonths, showTasks} from './Common.js';
import './Scheduler.css';

// create a month array
const monthArray = (year, tasks) =>{    
    
    if (!tasks) tasks=[];

    let monthsArray = {
        months:[], // will fill with sub arrays for each month
        tasks:[]
    };
    
    let dayIndex="";

    const months=getMonths();
    months.forEach((month,i)=>{
        monthsArray.months.push({
            month: month.value,
            name: month.text,
            year: year,
            tasks:[]
        });

        let firstDay=new Date(year, i, 1).getDate();
        let lastDay=new Date(year, i+1, 0).getDate();

        for (let j=firstDay;j<=lastDay;j++){
            dayIndex=`${year}/${(("0" + (i+1)).slice(-2))}/${("0" + j).slice(-2)}`;
            monthsArray.months[monthsArray.months.length-1].tasks.push(...(tasks[dayIndex] || []));
        }
        monthsArray.months[monthsArray.months.length-1].tasks.push(...(tasks["everyday"] || []));        
    });
    
    return monthsArray;

}

const Year = (props) => {
    const calendar = monthArray(props.year, props.tasks);

    return (
        <div className="scheduler scheduler-year-table">
            {calendar.months.map((month,i) => {
                return (
                    <div className="day" key={`mth${i}`}>
                        <span>{month.name}</span>
                        { showTasks(month.tasks, month.year) }
                    </div>
                );
            })}
        </div>
    );    
}
export default Year;