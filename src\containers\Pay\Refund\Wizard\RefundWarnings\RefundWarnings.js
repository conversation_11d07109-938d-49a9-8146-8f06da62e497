import React, { useState, useEffect } from 'react';

export const RefundWarnings =({order, transactions, ...props})=>{

    const [typesToShow, setTypesToShow]=useState(null);
    
    useEffect(()=>{
        const filterTransaction = async() =>{
            let types = [];
            await order?.transactions.forEach((transaction)=>{
                if(!types.includes(transaction.transaction_payment_method_name)){
                    types.push(transaction.transaction_payment_method_name)
                }
            })
            setTypesToShow(types);
        }

        if(order) filterTransaction();
    },[order]);

    return(
        <div>
            {transactions &&
                <div>
                    {transactions.map((transaction)=>(
                        <div>
                            {transaction.payment_method_id === 4 &&
                                <p>
                                    This is a gift card transaction.  As such, the refund will be applied to the gift card it came from and no further action is necessary.
                                </p>
                            }
                            {transaction.payment_method_id === 2 &&
                                <p>
                                    This refund is on a cash transaction.  Cash needs to be given to the customer at the time of this refund.
                                </p>
                            }
                            {transaction.payment_method_id === 3 &&
                                <p>
                                    This refund is on a check transaction.  Cash needs to be given to the customer at the time of this refund.
                                </p>
                            }
                            {transaction.payment_method_id === 1 &&
                                <p>
                                    This refund will automatically be reapplied to the method of use.  This can take up to 14 business days but maybe completed sooner.
                                </p>
                            }
                        </div>
                    ))}
                </div>
            }
            {typesToShow && 
                <div>
                    {typesToShow?.includes('Credit Card') &&
                        <p>
                            <span className="bold">Credit Card</span> refunds can take up to 7 days to process in the bank's systems.
                        </p>
                    }
                    {typesToShow?.includes('Gift Card') &&
                        <p>
                            <span className="bold">Gift Card</span> refunds will be refunded within the hour.  
                        </p>
                    }
                    {(typesToShow?.includes('Cash') || typesToShow.includes('Check')) &&
                        <p>
                            <span className="bold">Cash and Check</span> refunds need to be issued in cash at the time of refunding.
                        </p>
                    }
                    {typesToShow?.includes('Admin Authorized') &&
                        <p>
                            <span className="bold">Manager discounts</span> cannot be refunded as they are not monies recieved.  
                        </p>
                    }
                </div>
            }
        </div>
    )
}