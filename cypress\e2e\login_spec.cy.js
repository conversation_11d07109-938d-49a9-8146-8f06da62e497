/*eslint-disable*/
//BEFORE RUNNING THIS TEST
//use CTRL + F and type "Choose a" to find the options to change
//Go through the before to set up the test the way you want it
//You may select a different URL for testing (QA vs local)
//After you select a url, check the company Id to match
//Change the side nav check if you change the URL
//Select a user that you want to login to check against the tests

/// <reference types="cypress" />
import eventData from '../../cypress/fixtures/Events/mens_league_summer.json'

let devPatron = Cypress.env('impact_patron_symbol_name')
let qaPatron = Cypress.env('impact_patron_user')
let qaDevStaff = Cypress.env('impact_staff_user')
let qaDevAdmin = Cypress.env('impact_admin_user')
let qaDevSB = Cypress.env('impact_sb_user')
let password = Cypress.env('login_password')
let baseUrl = "http://portal-qa.impactathleticsny.com/p/";

//Note that occasionally this test will fail because a call (usually login) will happen TOO FAST and will finish before the wait.
//Also, for some reason, every once in a while, the aliases are not properly applying to tests.  
//If your test fails, double check these aren't the problems! 
describe("A user will be logged in", {scrollBehavior: "center", testIsolation:false}, ()=>{
    let local;
    let role; //possible roles- "Patron", "SiteBoss", "Admin", "Staff"
    let outstanding 
    let tokens;
    let events;
    let groups;
    before("will input the username and login and check the homepage", ()=>{
        cy.viewport(1920, 1080);
        let companyId

    //Choose a URL
        cy.visit(baseUrl);
    
    //Choose a company id
        companyId = 2

    //Choose a user
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, qaDevSB, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        }) 
    
    
    //intercepting calls to check for completion and that they have data
        cy.intercept('POST', "/api/user/outstanding_charges").as('getOutstandingCharges');
        cy.intercept('POST', "/api/event", {fixture: 'Events/event_list_data.json'} ).as('getEventList')
        // cy.intercept('POST', "/api/event").as('getEventList') //no fixture data(use for QA)
        cy.intercept('GET', "/api/user/user/**").as('getUserUser');
        cy.intercept('GET', "/api/user/wallet?**").as('getWallet');
        cy.intercept('POST', "/api/user/tokens").as("getTokens")
        cy.intercept('GET', "/api/user/groups/**").as("getGroups")
        cy.intercept('POST', "/api/location").as("getLocations")
        // cy.intercept('GET', "/api/user/product_purchased/*").as('getLivestreams')

        cy.wait('@getOutstandingCharges').then((response)=>{
            outstanding = (JSON.parse(response.response.body)).data
            cy.log(outstanding)
        })
        cy.wait('@getEventList')
            // .its('data').should('not.be', null)
            .then((response)=>{
                // events = (JSON.parse(response.response.body)).data //live data
                events = response.response.body.data //fixture data
                cy.log(events)
            })
        cy.wait('@getWallet')
        cy.wait('@getTokens').then((response)=>{
            tokens = (JSON.parse(response.response.body)).data
            cy.log(tokens)
        })
        cy.wait('@getGroups').then((response)=>{
            groups= (JSON.parse(response.response.body)).data
            cy.log(groups)
        })
        // cy.wait('@getLocations')
        // cy.wait('@getLivestreams')
        
    }) //end before

    beforeEach("restore local storage", ()=>{
        cy.viewport(1920, 1080);
        cy.restoreLocalUser(JSON.parse(local))
    }); //end restore local user

    //should allow tests to keep running even if they fail
    afterEach(()=>{
        cy.continueAfterFail() 
    }) //end continue after fail

    //can only set the user after sure that the calls are all done

    it("will make sure the proper user has the proper roles",()=>{
        cy.log(local)
        let pLocal = JSON.parse(local)
        expect(local).to.include("roles")
        if(pLocal.profile.username === "@@"){
            expect(pLocal.roles)
            .to.have.lengthOf(1)
            .deep.equal([{id: 7, name: "Patron"}])
            role = "Patron"
        }
        else if(pLocal.profile.username==="MidnightSP"){
            expect(pLocal.roles)
            .to.have.lengthOf(1)
            .deep.equal([{id: 1, name: "SiteBoss Master Admin"}])
            role="SiteBoss"
        }
        else if(pLocal.profile.username==="MidnightP"){
            expect(pLocal.roles)
            .to.exist
            role="Patron"
        }
        else if(pLocal.profile.username==="SkylerR"){
            expect(pLocal.roles)
            .to.exist
            role="Admin"
        }
        else if(pLocal.profile.username ==="RoRo"){
            expect(pLocal.roles)
            .to.exist
            role="Staff"
        }
        cy.log(role)
    }); //end role check

    it("will make sure the waiver banner is present for a user with an unsigned waiver",()=>{
        let pLocal = JSON.parse(local)
        if(pLocal.profile.has_signed_waiver === 0){
            cy.get('[data-cy="waiver-banner-unsigned"]')
                .should('be.visible')
                .contains('button', "Sign Waiver Now")
                .click()
            cy.get('iframe').should('be.visible')
            //waiting so tester can see that it opens and closes
            cy.wait(500)
            cy.get('.close').click()
        }
        else if(pLocal.profile.has_signed_waiver===1){
            cy.get('[data-cy="waiver-banner"]').should('not.exist')
            cy.log(`${String.fromCodePoint(0x1F92F)} The user has a signed waiver and the banner is correctly missing.`)
        }
    }) //end waiver check


    it("will have no memberships in the Membership Details",()=>{
        let plocal = (JSON.parse(local)).profile.subscriptions
        cy.log(local)
        cy.log(plocal)
        if(plocal.length === 0){
            cy.get('[data-cy="home-memberships"]')
                .invoke('text')
                .should('contain', "No active membership")
        }
        else if(plocal.length > 0 ){           
            for(let i = 0; i < plocal.length; i++){
                cy.get('[data-cy="home-memberships"]')
                    .children()
                    .then(()=>{
                        let cyGet = `sub-${plocal[i].id}`
                        cy.get(`[data-cy=${cyGet}]`)
                        .within(()=>{
                            cy.get('span')
                                .should('have.class', 'subs-active')
                                .invoke('text')
                                .should('exist')
                                .then(($text)=>{
                                    expect($text)
                                    .to.contain(plocal[i].subscription_status)
                            })
                            cy.get('h5')
                                .should('have.class', 'profile-name')
                                .invoke('text')
                                .should('exist')
                                .then(($text)=>{
                                    expect($text)
                                    .to.contain(plocal[i].product_name)
                                })
                            cy.get('span')
                                .should('have.class', 'prof-amount')
                                .invoke('text')
                                .should('exist')
                                .then(($text)=>{
                                    expect($text)
                                    .to.contain("$")
                                    switch(plocal[i].bill_interval){
                                        case "m":
                                            expect($text).to.contain("Month")
                                            break;
                                        case "y":
                                            expect($text).to.contain("Year")
                                            break;
                                        case "d":
                                            expect($text).to.contain("Day")
                                            break;
                                    }
                                })
                        })
                    })
            }

            //ATOM check
            cy.timeFormatCheck(plocal[0].first_bill_date)
            cy.timeFormatCheck(plocal[0].last_bill_date)
            cy.timeFormatCheck(plocal[0].next_bill_date)
        }

    }); //end membership check

    let textNumber;
    it("will check the wallet",()=>{
        cy.intercept('POST', '/api/user/tokens').as('getTokens')
        cy.get('[data-cy="home-wallet"]')
            .should('be.visible')
            .invoke('text').then(($text)=>{
                textNumber=$text
                cy.log(`${String.fromCodePoint(0x1F92F)} text on the home page in the wallet component`, textNumber)
                expect(textNumber).to.match(/^[0-9]/)
                // cy.get('@text')
                //     .should('match', /^[0-9]/)
            })
            //checking that it starts with a number
            cy.get('button')
            .contains('More Info')
            .click()
    }) //end wallet check

    it("will check the available tokens,", ()=>{
        //checking that the proper tab is active and the only one active
        cy.get('#tokens-tab-tab-available')
            .should('have.attr', 'aria-selected', 'true')
        cy.log(`${String.fromCodePoint(0x1F92F)} the number of tokens under available will be compared to the text in the wallet component on the home screen`)
        
        if(tokens.length > 0){
            cy.get('[data-cy="wallet-available-tab"]')
                .children()
                .its('length')
                .as('amountOfTokens')
            cy.get('@amountOfTokens')
                .then(count => 
                    expect(textNumber).to.contain(count)
                )
        }
        cy.get('#tokens-tab-tab-redeemed')
            .should('have.attr', 'aria-selected', 'false')
        cy.get('#tokens-tab-tab-expired')
            .should('have.attr', 'aria-selected', 'false')
    }) //end available token check

    it("will change to the redeemed tab,", ()=>{
        let hasTokens = true;
        cy.intercept('POST', '/api/user/tokens').as('getTokens')
        cy.log(`${String.fromCodePoint(0x1F92F)} switch to the 'redeemed tab'`)
        cy.get('#tokens-tab-tab-redeemed').click()
        cy.wait('@getTokens')
            .then((response)=>{
                let resp
                if(response.data){
                    resp = JSON.parse(response.response.body).data
                }
                if(resp && resp[0].exp_date !==null){
                    cy.log(`${String.fromCodePoint(0x1F92F)} date has an expiration`)
                    cy.timeFormatCheck(resp[0].exp_date)
                    resp.data.forEach((token)=>{
                        expect(token).to.have.ownProperty('use_date')
                    })
                    cy.timeFormatCheck(resp[0].use_date)
                } else if(resp){
                    cy.log(`${String.fromCodePoint(0x1F92F)} Date has no expiration`)
                }else{
                    hasTokens = false
                    cy.log(`${String.fromCodePoint(0x1F92F)} Has Tokens?`, hasTokens)
                } 
            }).then(()=>{
                if(hasTokens===true){
                    cy.get('[data-cy="wallet-redeemed-tab').children()
                        .within(()=>{
                            cy.get('div')
                            .should('not.have.class', 'react-loading-skeleton')
                        })
                        .children()
                        .then($child=>{
                            cy.log($child)
                            expect($child).to.contain('Redeemed on')
                        })
                        
                }
                //checking they switched
                cy.get('#tokens-tab-tab-available')
                    .should('have.attr', 'aria-selected', 'false')
                cy.get('#tokens-tab-tab-expired')
                    .should('have.attr','aria-selected','false')
                cy.get('#tokens-tab-tab-redeemed')
                    .should('have.attr', 'aria-selected', 'true')
            })
    }) //end redeemed tokens check
      
    it("will check the expired tab", ()=>{
        let hasTokens = true;
        let numberOfTokens;
        cy.intercept('POST', '/api/user/tokens').as('getTokens')
        cy.get('#tokens-tab-tab-expired').click()
          
        //checking that load skeleton is present
        cy.get('[data-cy="wallet-expired-tab"]')
            .children()
            .within(()=>{
                cy.get('span')
                .should('have.class', 'react-loading-skeleton')
            });
        cy.wait('@getTokens').then((response)=>{
            if((JSON.parse(response.response.body)).data.length===0){
                hasTokens = false
                numberOfTokens=((JSON.parse(response.response.body).data.length))
            } 
        }).then(()=>{
            //Checking that load skeleton is gone
            cy.get('[data-cy="wallet-expired-tab')
                .should('have.class', 'active')
            if(hasTokens && numberOfTokens >1){
                cy.get('[data-cy="wallet-expired-tab"]')
                    .children().then(()=>{
                        cy.get('span')
                            .contains('class', 'react-loading-skeleton')
                            .should('not.exist')
                        //checks that the expired dates are not after today's date
                        const today = new Date()
                        cy.log(today)
                        cy.get('[data-cy="wallet-expired-tab"]').within(()=>{
                            cy.get('.token-row').each(($child)=>{
                                cy.get($child)
                                    .get('div')
                                    .eq(1) //will be the name of the event
                                    .should('exist')
                                    .get('div')
                                    .eq(2) //will be (Expired on 00/00/0000)
                                    .should('contain', "Expired")
                                    .invoke('text')
                                    .then(($text)=>{
                                        let splitDate = $text.split(" ")
                                        cy.log(splitDate[2])
                                        expect(new Date(splitDate[2])).to.be.lessThan(today) 
                                    })
                            })  
                        })
                    })
                
            }
        })
        cy.get('#tokens-tab-tab-available')
            .should('have.attr', 'aria-selected', 'false')
        cy.get('#tokens-tab-tab-redeemed')
            .should('have.attr', 'aria-selected', 'false')
        cy.get('#tokens-tab-tab-expired')
            .should('have.attr','aria-selected','true')

        cy.get('[data-cy="wallet-modal"]').within(()=>{
            cy.get('.modal-header').get('button').should('have.class', 'close').click()
        })
    }) //end expire token check

    it("Will load unpaid events or an indication if there aren't any",()=>{
        if(outstanding.family.length + outstanding.outstanding_charges.length === 0){
            cy.get(':nth-child(6) > .prof-bg > :nth-child(3)')
                .invoke('text')
                .should('contain', 'There are no pending charges')
        }
        else{
            cy.get('[data-cy="unpaid-events-table"]').within(()=>{
                let chargeAmount = 0;
                outstanding.family.forEach((family)=>{
                    if(family.outstanding_charges.length > 0){
                        chargeAmount += family.outstanding_charges.length      
                    }
                })
                chargeAmount += outstanding.outstanding_charges.length
                //adding one to the charge amount for heading row    
                cy.get('tr').should('have.length', chargeAmount+1)
                })
            
        }
    })//end unpaid events or an indication if there aren't any

    it("Will properly load family and groups and allow swapping between tabs",()=>{
        cy.window()
            .its('store')
            .invoke('getState')
            .its('family')
            .its('all_family_no_dupes')
            .then(message=>{
                let msg = message;
                cy.wrap(msg).as('msg')
            })
        cy.get('[data-cy="home-group-tabs"]')
            .should('exist')
            .and('be.visible')
        cy.get('[data-cy="home-group-tabs"]')
            .invoke('text')
            .should('include', 'My FamilyMy Groups')
        cy.get('[data-cy="home-group-tabs"]')
            .children()
            .eq(0)
            .click();

        cy.get(':nth-child(1) > .nav-link')
            .click();
        cy.log("Make sure the family tab is loaded and matches backend")
        cy.get('@msg').then(msg=>{
            if(msg.length>0){
                cy.get('[data-cy="home-family-member"]')
                    .should('have.length', msg.length);
            }
        });
        
        cy.log(("Make sure you can see and click on the the group tab"))
        cy.get('li')
            .should('have.class', 'nav-item')
            .get('[data-rb-event-key="#groups"]')
            .should('be.visible')
            .click()
        
        cy.log(`${String.fromCodePoint(0x1F92F)} Make sure the group tab is loaded and matches backend data`)
        cy.get('[data-cy="home-groups"]')
            .should('have.length', groups.length)

            
        cy.log((`${String.fromCodePoint(0x1F92F)} Make sure you can see and click on the the family tab`))
        cy.get(':nth-child(1) > .nav-link')
            .click()
        cy.log(`${String.fromCodePoint(0x1F92F)} Make sure there are no loading skeletons present`)
        cy.get('span').should('not.have.class', 'react-loading-skeleton')
        cy.get('div').should('not.have.class', 'react-loading-skeleton')
        
        cy.get('li')
            .should('have.class', 'nav-item')
            .get('[data-rb-event-key="#families"]')
            .should('be.visible')
            .click()

        cy.log("Make sure there is also no loading skeleton on the family tab")
        cy.get('@msg').then(msg=>{
            if(msg.length>0){
                cy.get('[data-cy="home-family-member"]')
                    .should('be.visible')
            }
        })
        cy.get('span').should('not.have.class', 'react-loading-skeleton')
        cy.get('div').should('not.have.class', 'react-loading-skeleton')
        
    })//end properly load family and groups and allow swapping between tabs

    it("The first card in the event list will always be present and clickable",()=>{
        cy.intercept ('POST', '/api/register/*').as('getRegister')
        cy.intercept('POST', '/api/event').as('getEvents')
        
        cy.get('[data-cy="home-eventList').should('exist').and('be.visible')
        .within(()=>{
            cy.get('.rel-title')
                .invoke('text')
                .should('contain', "Available Classes and Events")
            
            cy.log(`${String.fromCodePoint(0x1F92F)} the first card should always be visible, regarless of event list length`)
            cy.get('#rel-first-card')
                .should('be.visible')
                .children()
                cy.get('#rel-first-card-body')
                    .should('be.visible')
                cy.get('#rel-first-card-text')
                    .invoke('text')
                    .should('include', "Upcoming")
                    .should('include', "Events")
                cy.get('#rel-first-card-footer')
                    .invoke('text')
                    .should('include', "Event Calendar")
            cy.get('#rel-first-card').click()
            cy.wait('@getRegister')
            cy.wait(750)
            cy.url().should('include', '/event-register')
        })
    })//end first event card

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })

})