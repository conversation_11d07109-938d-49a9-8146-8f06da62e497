import React from 'react';
import Products from '../Products';

import styles from './Onboarding.module.scss';

export const Step3 =  props => {
    const {stepValues, saveStepValues} = props;

    const setProduct = (product) => {
        if (product){
            saveStepValues({plan: product});
        }
    }

    return (
        <>
            <div className={styles["step-description"]}>
                <h5>Step 3</h5>
                <h4>Choose a plan, any plan...</h4>
                <p>From multi-company access to event management, we have a subscription plan that will fit your needs.</p>
            </div>
            <Products {...props} callback={setProduct} />
        </>
    );
}
