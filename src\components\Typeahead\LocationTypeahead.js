import React, { useCallback } from 'react';

import { Typeahead } from './Typeahead';

import LocationsAPI from '../../api/Locations';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import './Typeahead.scss';

/**Basic async typeahead for searching locations.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Services back
 * @param {()} initialData to set the initial value of the typeahead - array of ids
*/
export const LocationTypeahead = ({noPrinters = false, ...props}) => {

    
    const makeRequest = useCallback(async (query, perPage, page=1) => {
        let locationTypes = [1,2,3,4,5,6,7,8,9,10,];
        if(!noPrinters) locationTypes = [...locationTypes, 11];
        let response = await LocationsAPI.get({location_type_ids: locationTypes});
        let responseObj = {
            data: response.data || null,
            errors: response.errors || null
        }
        return responseObj;
    },[noPrinters]);

    // each item in responseObj.data is an option
    const formatForLabel = (option) => (
        `${option?.name}`
    );

    return (
        <Typeahead
            {...props}
            id="location-search"
            formatForLabel={formatForLabel}
            makeRequest={makeRequest}
            async={false}
            paginated={false}
            placeholder={props.placeholder ? props.placeholder : "Enter a location name..."}
        />
    )
}
