import React, { useContext, useMemo, useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { Form } from 'react-bootstrap';

import Wysiwyg from '../Wysiwyg';
import Custom from './Custom';

import ColorPicker from '../../../../../../../../components/common/ColorPicker';
import { randomUUID } from '../../../../../../../../utils/cms';
import { FileURLContext } from '../../../../../../../../contexts/FileURLContext';

import AddItems from '../../../../../../Components/Properties/AddItems';

export const Element = React.forwardRef((props, ref) => {
    const {save} = props;
    const cmsSelectorElements = useSelector(state => state.cmsElements.present);
    const company_context = useContext(FileURLContext);

    const [selectedItem, setSelectedItem] = useState(props.selectedItem || null);

    const passValueToElement = useMemo(() => {    
        if (props.currentElementId && props?.restrict_to){
            const _elem = cmsSelectorElements.elements.filter(a=>a && a?.id===props.currentElementId)?.[0];
            if (!_elem) return null;
            let _restricted_elements=[];
            props.restrict_to.forEach(r => {
                if (r.toLowerCase()==="_self_") _restricted_elements.push(_elem);
                else {
                    if (_elem.element_id.toLowerCase()==="layout"){
                        _restricted_elements.push(_elem[_elem.element_id]?.content?.filter(a=>a[_elem.element_id].props?.["layout-type"]?.toLowerCase()===r.toLowerCase()?a.id:null)?.[0] || null);
                    } else {
                        //console.log(_elem[_elem.element_id]?.content)
                        _restricted_elements.push(..._elem[_elem.element_id]?.content?.filter(a=>a && a?.element_id?.toLowerCase()===r.toLowerCase()?a.id:null) || null);
                    }
                }
            });
            return _restricted_elements.length>0 ? _restricted_elements.filter(a=>a!=null) : null;
        }
    }, [props.currentElementId, props?.restrict_to, cmsSelectorElements.elements]);


    const changeHandler = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();            
        if (props.type !== "textarea") e.target.value = e.target.value.replace(/(\r\n|\n|\r)/gm, "");
        //save(e,null,{id: props.id, name: props.name});
    },[props.type]);


    const input = useMemo(() => {
        // let default_value = props?.default_value || props?.value || "";
        let default_value = props?.value || "";
        let placeholder = props?.default_value || props?.value || "";        
        let type = props?.type || "text";

        if (type!=="custom" && type!=="wysiwyg" && ((props?.value && typeof props?.value === "object") || (props?.default_value && typeof props?.default_value === "object" && !props?.value))) {
            if (props?.default_value) { // this comes from cssProperties, so we dont set a default value just put a placeholder
                default_value=Object.keys(props.default_value).map(key => props.default_value[key]).join(" ");
                placeholder=default_value;
            }
            if (props?.value){ // this is a json object passed as a prop to a custom component
                default_value=JSON.stringify(Object.keys(props.value).map(key => props.value[key]));
                type="json";
            }
        }        

        // if theres a restricted to element, it means this is the parent, so we grab the value from the restricted element and set it as the default value for this parent
        if (passValueToElement && passValueToElement.length>0){
            let _pp=passValueToElement[0]?.properties?.filter(a=>a.id===props.id)?.[0];
            if (_pp && _pp?.value) default_value=_pp.value;
        }

        // if there is a source for the option, we return null because we're dealing with special inputs with a useEffect
        if (props?.source?.component) return null;

        if (props?.addItems === true && props?.items?.length>0){
            return (
                <AddItems 
                    key={`add-more-tool-${props.id}`} 
                    groupingType={2 /*props?.allowGrouping?1:props?.allowHierarchy?2:0*/} 
                    items={props.items} 
                    id={props.id} 
                    data={default_value || []} 
                    selectedItem={selectedItem}
                    selection={(v, selectedItem)=>{
                        setSelectedItem(selectedItem);
                        save({
                            preventDefault() {},
                            stopPropagation() {},
                            target: { value: v },
                        }, v, {id: props.id, name: props.name}, null, passValueToElement || null, selectedItem);
                    }}
                />
            );
        }

        // render the input
        const key = randomUUID();
        switch (type){
            case "textarea":
            case "json":
            case "array":
                return (
                    <Form.Control ref={ref} id={key} key={key} as="textarea" rows={4} placeholder={placeholder} defaultValue={default_value} onChange={changeHandler} onBlur={(e)=>{
                        if (type === "json" || type === "array") e.target.value = e.target.value.replace(/(\r\n|\n|\r)/gm, "");
                        save(e, null, {id: props.id, name: props.name}, type==="json" ? "json": null, passValueToElement || null);
                    }}/>
                );
            case "select":
                return (
                    <Form.Control ref={ref} id={key} key={key} as="select" custom onChange={(e)=>save(e, null, {id: props.id, name: props.name}, null, passValueToElement || null)} value={default_value} disabled={props?.readonly || undefined} >
                        <option></option>
                        {props?.options?.map((option, i) => {
                            let _option_value=option, _option_display=option;
                            if (typeof option === "object") {
                                _option_value = option.value.trim();
                                _option_display = option.text.trim();
                            }

                            if (_option_value && isNaN(_option_value) && _option_value.indexOf("{company_context}") > -1){
                                let _option = _option_value.split("{company_context}.")[1];
                                if (_option) {
                                    if (_option_display === _option_value) _option_display=company_context[_option];
                                    _option_value=company_context[_option];
                                }
                            }

                            return <option key={`select-${props.name}-${i}`} value={_option_value}>{_option_display}</option>
                        })}
                    </Form.Control>
                );
            case "color":
                return (
                    <ColorPicker 
                        ref={ref}
                        id={key} 
                        key={key}
                        type="text"
                        name={props.name} 
                        placeholder={placeholder} 
                        value={default_value} 
                        onBlur={(e,color)=>save(e, color, {id: props.id, name: props.name}, null, passValueToElement || null)} 
                        onKeyUp={(e,color) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                e.stopPropagation();
                                save(e, color, {id: props.id, name: props.name}, null, passValueToElement || null);
                            }
                        }}
                    />
                );
            case "checkbox":
                if (typeof default_value==="boolean") default_value = default_value ? 1 : 0;
                return (
                    <Form.Check
                        id={key} 
                        ref={ref}
                        key={key}
                        className="form-switch"
                        type="checkbox"
                        name={props.name}
                        label={null}
                        checked={default_value}
                        value={default_value}
                        onChange={(e)=>save(e, default_value, {id: props.id, name: props.name}, "boolean", passValueToElement || null)}
                    />
                );
            case "range":
                return (
                    <Form.Control 
                        id={key} 
                        key={key}
                        ref={ref}
                        type="range"
                        className="form-range"
                        step={props.step || 1}
                        min={props.min}
                        max={props.max}
                        name={props.name} 
                        defaultValue={default_value} 
                        onMouseUp={(e)=>save(e, null, {id: props.id, name: props.name}, null, passValueToElement || null)}
                        onKeyUp={(e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                e.stopPropagation();
                                save(e, null, {id: props.id, name: props.name}, null, passValueToElement || null);
                            }
                        }}
                    />
                );
            case "wysiwyg":
                return (
                    <Wysiwyg
                        ref={ref} 
                        id={key}
                        key={key}
                        rows={4}
                        name={props.name}
                        defaultValue={default_value}
                        onChange={changeHandler}
                        onBlur={(e, value) => {
                            save(e, value, {id: props.id, name: props.name}, null, passValueToElement || null);
                        }}
                    />
                );
            default:
                return (
                    <Form.Control 
                        ref={ref} 
                        id={key} 
                        key={key}
                        type={type==="image"? "text": type || "text"}
                        name={props.name} 
                        placeholder={placeholder} 
                        defaultValue={default_value} 
                        readOnly={props?.readonly || undefined}
                        onChange={changeHandler} 
                        onBlur={(e)=>save(e, null, {id: props.id, name: props.name}, null, passValueToElement || null)} 
                        onKeyUp={(e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault();
                                e.stopPropagation();
                                save(e, null, {id: props.id, name: props.name}, null, passValueToElement || null);
                            }
                        }}
                    />
                );
        }
    }, [ref, passValueToElement, changeHandler, selectedItem, save, company_context, props.value, props.default_value, props.type, props.name, props.id, props.addItems, props.items, /*props.allowGrouping, props.allowHierarchy,*/ props.readonly, props.source, props.max, props.min, props.options, props.step]);

    return (
        <>
            {input}
            {props?.source?.component && <Custom {...props} ref={ref} passValueToElement={passValueToElement}/>}
        </>
    );
});