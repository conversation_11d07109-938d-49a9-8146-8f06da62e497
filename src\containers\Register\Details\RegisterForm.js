import React, { useEffect, useState, use<PERSON><PERSON>back, useMemo, useRef } from "react";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import { useHistory, } from "react-router-dom";
import { Container, Col, Form, Button, InputGroup, Row } from "react-bootstrap";
import { JsonEditor } from 'jsoneditor-react'; // this one has a top toolbar I can't entirely get rid of but displays JSON errors immediately

import { confirm } from '../../../components/Confirmation';
import ErrorCatcher from "../../../components/common/ErrorCatcher";
import Toast from "../../../components/Toast";
import { CategoryTypeahead, LocationTypeahead, ProductTypeTypeahead, Typeahead } from "../../../components/Typeahead";
import { Typeahead as BootstrapTypeahead } from 'react-bootstrap-typeahead';

import Registers from "../../../api/Registers";

import "../Register.scss";
import 'jsoneditor-react/es/editor.min.css';
import Products from "../../../api/Products";
import { Link } from "react-router-dom/cjs/react-router-dom.min";
import { authUserHasModuleAccessMany } from "../../../utils/auth";
import Tooltip from '../../../components/common/Tooltip';

//checkin list component
//order pickup list component

const HEADER_BUTTONS_LIST = [
    { id: 1, label: "Preview", name: "preview" },
    { id: 2, label: "Num Tabs", name: "numtabs" },
    { id: 3, label: "Print", name: "print" },
    { id: 4, label: "Orders", name: "orders" },
    { id: 5, label: "Reports", name: "reports" },
    { id: 6, label: "Split Orders", name: "split" },
    { id: 7, label: "Open Orders", name: "openOrders" },
    { id: 8, label: "Register Hours", name: "registerHours" }
];
const RECEIPT_MESSAGE_LOCATION_OPTIONS = ["top", "bottom"];

// all of the defaults - if we have an input for it it MUST be listed in here to not break things
const defaultRegister = {
    id: 0,
    description: "",
    location_id: NaN,
    name: "",
    is_disabled: false,
    register_definition: {
        url: "",
        category_id: [],
        is_patron_register: -1,
        terminal_device_id: "",
        pickup_location: "",
        url_based_props: false,
        // url_based_props: false,
        //these are aspects of the register definition, but they aren't required for function as they've all been added retroactively.
        //so they don't need to be default in every register def, but they're here so we can see that they are
        // change_id: 0,
        // type_id: [],
        // prompt_for_tips: false,
        // prompt_tip_percentages: [],
        // print_kitchen_receipt: false,
        // receipt_message: "",
        // receipt_message_location: "",
        // receipt_email: "",
        // killswitch: false,
        // pickup_location: "",
        // events: false,
        // patron_layout: false,
        // allow_variants: false,
        // skip_initial_load: false,
        // hide_edit_cart_button: false,
        // content:[{Items: {props: {
        //      cardImages: false,
        //      addonImages: false,
        //      showDescription: false
        //  }}}]
    },
};

const defaultNoBanner = {
    "div": {
        "props": {
            "className": "site-col"
        },
        "content": [
            {
                "PosHeader": {
                    "props": {
                        "buttons": []
                    }
                }
            },
            {
                "div": {
                    "props": {
                        "className": "site-row pos-main-row"
                    },
                    "content": [
                        {
                            "Users": []
                        },
                        {
                            "Items": {
                                "props": {}
                            }
                        },
                        {
                            "Preview": []
                        }
                    ]
                }
            }
        ]
    }
}

const defaultPickup = {
    "div": {
        "props": {
            "className": "site-col"
        }, "content": [
            {
                "PosHeader": {
                    "props": {
                        "buttons": [
                            "orders"
                        ]
                    }
                }
            },
            {
                "div": {
                    "props": {
                        "className": "site-row pos-main-row"
                    },
                    "content": [
                        {
                            "Users": []
                        },
                        {
                            "div": {
                                "props": {
                                    "className": "site-col pos-above-checkins-row"
                                },
                                "content": [
                                    {
                                        "Items": {
                                            "props": {}
                                        }
                                    },
                                    {
                                        "OnlinePickupBanner": []
                                    }
                                ]
                            }
                        },
                        {
                            "Preview": []
                        }
                    ]
                }
            }
        ]
    }
}

const defaultCheckin = {
    "div": {
        "props": {
            "className": "site-col"
        },
        "content": [
            {
                "PosHeader": {
                    "props": {
                        "buttons": []
                    }
                }
            },
            {
                "div": {
                    "props": {
                        "className": "site-row pos-main-row"
                    },
                    "content": [
                        {
                            "div": {
                                "props": {
                                    "className": "site-col"
                                },
                                "content": [
                                    {
                                        "div": {
                                            "props": {
                                                "className": "site-row pos-above-checkins-row"
                                            },
                                            "content": [
                                                {
                                                    "Users": []
                                                },
                                                {
                                                    "Items": {
                                                        "props": {}
                                                    }
                                                }
                                            ]
                                        }
                                    },
                                    {
                                        "CheckinUserList": []
                                    }
                                ]
                            }
                        },
                        {
                            "Preview": []
                        }
                    ]
                }
            }
        ]
    }
}

const defaultContainedOnline = {
    "div": {
        "content": [
            {
                "PatronCartHeader": []
            },
            {
                "Card": {
                    "props": {
                        "className": "online-pickup-pos cart-checkout"
                    },
                    "content": [
                        {
                            "div": {
                                "content": {
                                    "ClosedHoursTrigger": []
                                }
                            }
                        },
                        {
                            "div": {
                                "props": {
                                    "className": "online-pickup-row"
                                },
                                "content": [
                                    {
                                        "Items": {
                                            "props": {
                                                "patron_layout": "1",
                                            }
                                        }
                                    },
                                    {
                                        "ShoppingCart": {
                                            "props": {
                                                "is_patron_register": true
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        ]
    }
}

const defaultEventOnline = {
    "div": {
        "content": [
            {
                "PatronCartHeader": []
            },
            {
                "Card": {
                    "props": {
                        "className": "cart-checkout"
                    },
                    "content": [
                        {
                            "div": {
                                "props": {
                                    "className": "events-container"
                                },
                                "content": [{
                                    "Events": {
                                        "props": {
                                            "modal": "0"
                                        }
                                    }
                                }]
                            }
                        },
                        {
                            "div": {
                                "content": [
                                    {
                                        "Items": {
                                            "props": {
                                                "   type": "services",
                                                "patron_layout": "1"
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        ]
    }
}

const posLayoutDefaults = [
    {id: 1, name: "No Banners", default: defaultNoBanner}, 
    {id: 2, name: "Online Pickup Defaults", default: defaultPickup}, 
    {id: 3, name: "User Checkin Banner", default: defaultCheckin}
];
const patronLayoutDefaults = [
    {id: 4, name: "Contained Page Online Shop", default: defaultContainedOnline}, 
    {id: 5, name: "In Portal Page Online Shop", default: defaultEventOnline}
];

const EDIT_HOURS_MODULE_ID = 332;
const EDIT_CATEGORIES_MODULE_ID = 337;
const EDIT_ADVANCED_MODULE_ID = 342;

const RegisterForm = ({ id = null, setDisplayName = () => { } }) => {

    let history = useHistory();

    let jsonEditorRef = useRef(null);

    const [initialLocation, setInitialLocation] = useState(null);
    const [locationSelection, setLocationSelection] = useState([]);
    const [registerStart, setRegisterStart] = useState(null);
    const [registerDefinition, setRegisterDefinition] = useState(null);
    const [name, setName] = useState(defaultRegister.name);
    const [description, setDescription] = useState(defaultRegister.description);
    const [isDisabled, setIsDisabled] = useState(null);
    const [onBlurTrigger, setOnBlurTrigger] = useState(0);
    const [hideRegisterDefinition, setHideRegisterDefinition] = useState(true);
    const [itemProps, setItemProps] = useState(null);
    const [buttonProps, setButtonProps] = useState(null);
    const [initialProductTypes, setInitialProductTypes] = useState(null);
    const [initialCategories, setInitialCategories] = useState(null);
    const [tipValues, setTipValues] = useState(null);
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();
    const [userPermissions, setUserPermissions] = useState();

    //#region useCallback

    const handleChangeRegisterValue = useCallback((e) => {
        let value = null;
        if (e.target.type === "checkbox") {
            value = e.target.checked;
        } else {
            value = e.target.value;
        }

        if (value !== null) {
            if (value === "0" || value === "1") value = value === "1" ? true : false
            let newRegisterDefinition = { ...registerDefinition, [e.target.name]: value };

            // if it is is_patron_register being changed then we need to set some values back to defaults
            if (e.target.name === "is_patron_register") {
                value = parseInt(e.target.value);
                if (value === 1) {
                    newRegisterDefinition.terminal_device_id = defaultRegister.register_definition.terminal_device_id;
                } else if (value === 0) {
                    newRegisterDefinition.url = defaultRegister.register_definition.url;
                    newRegisterDefinition.pickup_location = defaultRegister.register_definition.pickup_location;
                    setIsDisabled(defaultRegister.is_disabled);
                }
            }
            setRegisterDefinition(newRegisterDefinition);
        }
    }, [registerDefinition]);

    const handleTypeCategoryChange = useCallback((data, type) => {
        if (registerDefinition) { //make sure we have it at all (meaning everythign has been loaded before changing it!)
            setRegisterDefinition({ ...registerDefinition, [type]: data?.map(item => item.id) })
        }
        //eslint-disable-next-line
    }, [registerDefinition]);

    const handleOnBlurRegisterDefinition = useCallback((e) => {
        // have to do it this way because the jsoneditor-react component doesn't update the onBlur function after it starts
        setOnBlurTrigger(prev => 1 - prev);
    }, []);

    const confirmDeleteHandler = useCallback((props) => {
        confirm(props.text, {
            title: "Whoa!",
            okText: "Yes",
            cancelText: "No",
            cancelButtonStyle: "light"
        }).then((result) => {
            if (result === true)
                props.click();
        });
    }, []);

    const handleInitialCategories = useCallback(async (ids) => {
        try {
            let response = await Products.Categories.filter({ id: ids });
            if (response.status === 200 && response.data) {
                let cats = response.data?.categories?.map((cat) => ({ name: cat.name, id: cat.id }))
                setInitialCategories(cats);
            }
        } catch (ex) { console.error(ex) }
    }, []);

    const handlePromptTipValues = useCallback(async (tips) => {
        let newTips = []
        tips.forEach((tip, i) => {
            newTips.push({ customOption: true, newTip: +tip, id: `${i}-${tip}`, tipValue: +tip })
        });
        setTipValues(newTips)
    }, [])

    const digForValue = useCallback((obj, target) => {
        const digForValue = (obj, target) =>
            target in obj
                ? obj[target]
                //eslint is triggering because it wants a return value...it has it, it's just conditional so eslint isn't happy
                //eslint-disable-next-line
                : Object.values(obj).reduce((acc, val) => {
                    if (acc !== undefined) return acc;
                    if (typeof val === 'object') return digForValue(val, target);
                }, undefined);

        return digForValue(obj, target);
    }, []);

    const checkPermissions = useCallback(async () => {
        try {
            let response = await authUserHasModuleAccessMany([EDIT_CATEGORIES_MODULE_ID, EDIT_HOURS_MODULE_ID, EDIT_ADVANCED_MODULE_ID]);
            setUserPermissions(response)
        } catch (ex) { console.error(ex) }
    }, [])

    //#endregion useCallback


    //#region useEffect

    // first load, check for id
    useEffect(() => {
        let mounted = true;
        // get register data if page is editing an existing register
        checkPermissions();
        if (id) {
            Registers.get({ id: id }).then((response) => {
                console.log(response.data[0].register_definition)
                if (mounted && response.data[0]) {
                    setRegisterStart(response.data[0]);
                }
            }).catch((e) => console.error(e));
        } else {
            setRegisterStart(defaultRegister);
        }

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
        };
    }, [id, checkPermissions]);

    useEffect(() => {
        // don't correct it if actively typing - only when the field loses focus
        let urlInput = document.getElementsByClassName("jsoneditor-text")[0];
        if (document.activeElement !== urlInput) { // if cursor is not in the text editor
            validateUrl(registerDefinition);
        }
    }, [registerDefinition, onBlurTrigger]);

    useEffect(() => {
        if (registerStart) {
            let registerDefinitionCopy = defaultRegister.register_definition;
            if (registerStart?.register_definition) {
                registerDefinitionCopy = { ...registerStart.register_definition };

                //remove all the cumbersome definitions to put at the bottom afterwards
                let availability = registerDefinitionCopy.availability
                let catAvail = registerDefinitionCopy.category_availability;
                let tempComponents = registerDefinitionCopy.components;
                delete registerDefinitionCopy.components;
                delete registerDefinitionCopy.category_availability;
                delete registerDefinitionCopy.availability;

                // make sure the register_definition has all the expected parts, if not use the default values
                Object.keys(defaultRegister.register_definition).forEach((key) => {
                    if (registerDefinitionCopy[key] === undefined) {
                        console.log("This doesn't exist: " + key);
                        registerDefinitionCopy[key] = defaultRegister.register_definition[key];
                    }
                });
                // add back the components key at the very bottom - just keeps the small items at the top where they can be seen
                registerDefinitionCopy.availability = availability;
                registerDefinitionCopy.category_availability = catAvail
                registerDefinitionCopy.components = tempComponents;

                if (registerStart.register_definition?.category_id?.length) handleInitialCategories(registerStart?.register_definition?.category_id);
                else setInitialCategories([])
                if (registerStart.register_definition?.type_id) setInitialProductTypes(registerStart?.register_definition?.type_id)
                else setInitialProductTypes([])
                if (registerStart.register_definition?.prompt_tip_percentages) handlePromptTipValues(registerStart.register_definition?.prompt_tip_percentages)
            }
            setRegisterDefinition(registerDefinitionCopy);
            setName(registerStart.name || defaultRegister.name);

            //since this is nested deeply and unpredictably (since it's partially dictated by layout of the POS, we want to save it to display the proper data in fields)
            if (registerDefinitionCopy?.components) {
                setItemProps(digForValue(registerDefinitionCopy?.components, "Items")?.props);
                setButtonProps(digForValue(registerDefinitionCopy?.components, "buttons"))
            }

            setDescription(registerStart.description || defaultRegister.description);
            setIsDisabled(registerStart.is_disabled || defaultRegister.is_disabled);
        }
    }, [registerStart, digForValue, handleInitialCategories, handlePromptTipValues]);

    useEffect(() => {
        if (name) {
            setDisplayName(name);
        }
    }, [name, setDisplayName]);

    useEffect(() => {
        if (success) {
            const timer = setTimeout(() => {
                history.push("/p/registers");
            }, 3000);
            return () => clearTimeout(timer);
        }
    }, [history, success]);

    // set initial location
    useEffect(() => {
        if (!initialLocation && registerStart) {
            if (registerStart.location_id) {
                setInitialLocation([registerStart.location_id]);
            } else {
                setInitialLocation([]);
            }
        }
    }, [registerStart, initialLocation]);



    // work around to making the JsonEditor update with changes to registerDefinition
    useEffect(() => {
        const editor = jsonEditorRef && jsonEditorRef.current && jsonEditorRef.current.jsonEditor;
        if (editor && registerDefinition) {
            editor.update(registerDefinition);
        }
    }, [jsonEditorRef, registerDefinition, itemProps])

    //#endregion useEffect

    const pagePartRegisterDefinitionInput = useMemo(() => {

        const handleChangeRegisterDefinition = (e) => {
            setRegisterDefinition(e);
        }

        if (registerDefinition === null) return <p>Loading...</p>
        return (
            <JsonEditor
                ref={jsonEditorRef}
                name="registerDefinition-input"
                value={registerDefinition}
                onChange={handleChangeRegisterDefinition}
                onBlur={handleOnBlurRegisterDefinition}
                mode='code'
                navigationBar={false}
                statusBar={false}
                search={false}
                sortObjectKeys={false}
            />
        );
    }, [registerDefinition, handleOnBlurRegisterDefinition, jsonEditorRef]);

    function clickCancel() {
        history.goBack();
    }

    const clickDelete = async (e) => {
        confirmDeleteHandler({
            text: `Are you sure you want to remove this Register? You cannot undo this!`,
            click: async () => {
                let response = await Registers.delete_register({ id: id }).catch((e) => console.error(e));
                try {
                    if (!response?.errors) {
                        setSubmitting(false);
                        setValidated(false);
                        setSuccess(
                            <Toast>Register deleted successfully!</Toast>
                        );
                    } else { // api returned errors
                        setSubmitting(false);
                        setError(
                            <ErrorCatcher error={
                                response.errors
                            } />
                        );
                    }
                } catch (e) { // no response at all
                    setSubmitting(false);
                    setError(
                        <ErrorCatcher error={e} />
                    );
                }
            }
        });
    };

    const handleChangeName = (e) => {
        setName(e.target.value);
    }

    const handleChangeDescription = (e) => {
        setDescription(e.target.value);
    }

    const handleChangeIsDisabled = (e) => {
        setIsDisabled(e.target.checked);
    }

    const validateUrl = (regDef) => {
        // make sure there is a / at the start of the url - automatically corrects it
        if (regDef?.url?.length > 0 && regDef.url.slice(0, 1) !== "/") {
            setRegisterDefinition({ ...regDef, url: "/" + regDef.url });
        }
    }

    const handleTipValuesChange = (selection) => {
        let confirmed = []
        if (selection.length > 0) {
            selection.forEach((item) => {
                if (typeof (parseInt(item?.tipValue) === "number")) {
                    item.tipValue = parseInt(item.tipValue)
                    confirmed.push(item)
                }
            });
        }
        setTipValues(confirmed);
        let temp;
        if (confirmed.length > 0) temp = confirmed.map((each) => each.tipValue);
        handleChangeRegisterValue({ target: { name: "prompt_tip_percentages", value: temp } });
    }

    const handleItemProps = (prop, value) => {
        if (value === "1" || value === "0") value === "1" ? value = true : value = false;
        let itemProps;
        if (registerDefinition?.components) itemProps = digForValue(registerDefinition?.components, "Items")
        if (itemProps?.props) {
            itemProps.props[prop] = value;
        }
        else if (itemProps) itemProps.props = { [prop]: value };
        else itemProps = { props: {} };
        setItemProps({ ...itemProps.props })
        setRegisterDefinition({ ...registerDefinition })
        //we're directly mutating it above, because Javascript objects, but because we don't know where it's nested, 
        // we're just going to reset that to the state and trust our little callback function to have worked properly
    }

    const handleButtonProp=(value)=>{
        if (registerDefinition?.components) {
            let simplified = [];
            if(value && value?.length) simplified = value.map(each => each?.name)
            let posHeader = digForValue(registerDefinition?.components, "PosHeader");
            if(posHeader){
                posHeader.props.buttons = simplified
                setRegisterDefinition({...registerDefinition})
            }
        }
    }

    const handleLayoutChange = (e) => {
        let allLayouts = [...posLayoutDefaults, ...patronLayoutDefaults];
        if(e.target.value){
            let match = allLayouts.filter(layout => layout.id === parseInt(e.target.value))
            if(match[0]){
                setRegisterDefinition({...registerDefinition, components: match[0].default})
            }
        }
    }

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            const formDataObj = Object.fromEntries(formData.entries());

            if (locationSelection.length) formDataObj.location_id = locationSelection[0].id;
            if (tipValues?.length > 0) registerDefinition.prompt_tip_percentages = tipValues?.map((each) => each.tipValue);
            else registerDefinition.prompt_tip_percentages = []

            if (registerDefinition.hasOwnProperty("change_id")) registerDefinition.change_id = ++registerDefinition.change_id;
            else registerDefinition.change_id = 1;
            formDataObj.register_definition = registerDefinition;   // please don't stringify anymore, the db needs an object to work properly

            // When editing a register
            if (id) {
                formDataObj.id = parseInt(id);
            }
            let response;
            if (id)
                response = await Registers.edit(formDataObj).catch((e) => console.error(e));
            else
                response = await Registers.create(formDataObj).catch((e) => console.error(e));

            try {
                if (!response?.errors && response.status === 200) {
                    setValidated(false);
                    setSuccess(
                        <Toast>Register saved successfully!</Toast>
                    );
                } else { // api returned errors
                    setSubmitting(false);
                    setError(
                        <ErrorCatcher error={
                            response.errors
                        } />
                    );
                }
            } catch (e) { // no response at all
                setSubmitting(false);
                setError(
                    <ErrorCatcher error={e} />
                );
            }
        } else {
            setSubmitting(false);
        }
    };

    return (
        <Container fluid>
            {success}
            <Form noValidate
                validated={validated}
                onSubmit={submitHandler}>
                <Form.Row className="register-form-container">
                    <div className="reg-form-row">
                        <div className="reg-form-col">
                            <h4>Basic Information</h4>
                            <Form.Group controlId="name">
                                <Form.Label>Name <span className="required-star">*</span> </Form.Label>
                                <Form.Control required type="text" name="name" value={name} onChange={handleChangeName} maxLength="45" />
                            </Form.Group>
                            <Form.Group controlId="description">
                                <Form.Label>Description</Form.Label>
                                <Form.Control type="text" name="description" value={description} onChange={handleChangeDescription} maxLength="255" />
                            </Form.Group>
                            {userPermissions && userPermissions[EDIT_HOURS_MODULE_ID] && id &&
                                <Button onClick={() => window.open(`/p/registers/${id}/hours`, "_blank")}>
                                    Edit Hours <i className="far fa-external-link-alt" />
                                </Button>
                            }
                            <Form.Group className="radio-left">
                                <label>Register Type: <span className="required-star">*</span></label>
                                <Form.Check
                                    type="radio"
                                    label="Regular (POS) Register"
                                    id="is_patron_register-false"
                                    name="is_patron_register"
                                    value={0}
                                    checked={registerDefinition?.is_patron_register === 0 || registerDefinition?.is_patron_register === false}
                                    onChange={handleChangeRegisterValue}
                                    required
                                />
                                <Form.Check
                                    type="radio"
                                    label="Patron (Online) Register"
                                    id="is_patron_register-true"
                                    name="is_patron_register"
                                    value={1}
                                    checked={registerDefinition?.is_patron_register === 1 || registerDefinition?.is_patron_register === true}
                                    onChange={handleChangeRegisterValue}
                                    required
                                />
                            </Form.Group>
                            <Form.Group className="radio-with-label">
                                <Form.Label>
                                    Is this an Event Register? <span className="required-star">*</span>
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Yes"
                                        id="is_events"
                                        name="events"
                                        value={1}
                                        checked={registerDefinition?.events === 1 || registerDefinition?.events === true}
                                        onChange={handleChangeRegisterValue}
                                        required
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="No"
                                        id="not_events"
                                        name="events"
                                        value={0}
                                        checked={registerDefinition?.events === 0 || registerDefinition?.events === false}
                                        onChange={handleChangeRegisterValue}
                                        required
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group className="radio-with-label">
                                <Form.Label>
                                    Patron Layout:<span className="required-star">*</span>
                                </Form.Label> 
                                <div className="radio-left">
                                    <Form.Check 
                                        type="radio"
                                        label="Yes"
                                        id="is_patron_layout"
                                        name="patron_layout"
                                        value={1}
                                        checked={registerDefinition?.patron_layout === 1 || registerDefinition?.patron_layout === true}
                                        onChange={handleChangeRegisterValue}
                                        required
                                    />
                                    <Form.Check 
                                        type="radio"
                                        label="No"
                                        id="is_patron_layout"
                                        name="patron_layout"
                                        value={0}
                                        checked={registerDefinition?.patron_layout === 0 || registerDefinition?.patron_layout === false}
                                        onChange={handleChangeRegisterValue}
                                        required
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group>
                                <Form.Label>
                                    Basic Layout
                                    <br />
                                    <sub className="error-text">
                                        This will override any components or custom styling you have if you select one of these options.  Best for resetting a POS or for setting up a new one.
                                    </sub>
                                </Form.Label>
                                <Form.Control
                                    as="select"
                                    type="select"
                                    name="content"
                                    onChange={handleLayoutChange}
                                >
                                    <option value={0}>No default</option>
                                    {(registerDefinition?.is_patron_register === 1 || registerDefinition?.is_patron_register === true) && patronLayoutDefaults?.map((each) => (
                                        <option value={each.id} key={`layout-option-${each.id}`}>{each.name}</option>
                                    ))}
                                    {(registerDefinition?.is_patron_register === 2 || registerDefinition?.is_patron_register === false) && posLayoutDefaults?.map((each) => (
                                        <option value={each.id} key={`layout-option-${each.id}`}>{each.name}</option>
                                    ))}
                                </Form.Control>
                            </Form.Group>
                            {(registerDefinition?.is_patron_register === 1 || registerDefinition?.is_patron_register === true) &&
                                <Form.Group>
                                    <Form.Label>Shop URL</Form.Label>
                                    <InputGroup>
                                        <InputGroup.Text>.../p</InputGroup.Text>
                                        <Form.Control type="text" name="url" value={registerDefinition?.url} onChange={handleChangeRegisterValue} />
                                    </InputGroup>
                                </Form.Group>
                            }
                        </div>
                        <div className="reg-form-col">
                            <h4>
                                Products
                                <Tooltip
                                    direction="top"
                                    text={`These can only be assigned if the "Items" component exists.  This will be added automatically with any default layout 
                                        ${userPermissions && userPermissions[EDIT_ADVANCED_MODULE_ID] && 
                                            `or you can manually add "Items" somewhere in the "components" section of the register`
                                        }`
                                    }
                                >
                                    <i className="far fa-question-circle ms-1"/>
                                </Tooltip>
                            </h4>
                            <Form.Group className="radio-with-label">
                                <Form.Label>
                                    Images on Product Cards:
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Yes"
                                        id="images-product-cards-true"
                                        name="cardImages"
                                        value={1}
                                        checked={itemProps?.cardImages === true ? true : false}
                                        onChange={(e) => handleItemProps(e.target.name, e.target.value)}
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="No"
                                        id="images-product-cards-false"
                                        name="cardImages"
                                        value={0}
                                        checked={itemProps?.cardImages === false ? true : false}
                                        onChange={(e) => handleItemProps(e.target.name, e.target.value)}
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group className="radio-with-label">
                                <Form.Label>
                                    Descriptions on Product Cards:
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Yes"
                                        id="descriptions_product_cards_true"
                                        name="showDescription"
                                        value={1}
                                        checked={itemProps?.showDescription === true ? true : false}
                                        onChange={(e) => handleItemProps(e.target.name, e.target.value)}
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="No"
                                        id="descriptions_product_cards_false"
                                        name="showDescription"
                                        value={0}
                                        checked={itemProps?.showDescription === false ? true : false}
                                        onChange={(e) => handleItemProps(e.target.name, e.target.value)}
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group className="radio-with-label">
                                <Form.Label>
                                    Images on Addons:
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Yes"
                                        id="addon_images_true"
                                        name="addonImages"
                                        value={1}
                                        checked={itemProps?.addonImages === true ? true : false}
                                        onChange={(e) => handleItemProps(e.target.name, e.target.value)}
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="No"
                                        id="addon_images_false"
                                        name="addonImages"
                                        value={0}
                                        checked={itemProps?.addonImages === false ? true : false}
                                        onChange={(e) => handleItemProps(e.target.name, e.target.value)}
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group className="radio-with-label">
                                <Form.Label>
                                    Handle Expired Events:
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Hide"
                                        id="expired_events_hide"
                                        name="expiredEvents"
                                        value={"hide"}
                                        checked={itemProps?.expiredEvents === "hide" ? true : false}
                                        onChange={(e) => handleItemProps(e.target.name, e.target.value)}
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="Flag"
                                        id="expired_events_flag"
                                        name="expiredEvents"
                                        value={"flag"}
                                        checked={itemProps?.expiredEvents === "flag" ? true : false}
                                        onChange={(e) => handleItemProps(e.target.name, e.target.value)}
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group controlId="categories">
                                <Form.Label>Assign Product Categories to this Register</Form.Label>
                                {registerDefinition && initialCategories &&
                                    <>
                                        <CategoryTypeahead
                                            multiple={true}
                                            passSelection={(selection) => handleTypeCategoryChange(selection, "category_id")}
                                            initialData={initialCategories || null}
                                        />
                                        <br />
                                        {registerDefinition?.category_availability &&
                                            <Form.Text>
                                                This register is utilizing categories based on time.
                                            </Form.Text>
                                        }
                                        {userPermissions && userPermissions[EDIT_CATEGORIES_MODULE_ID] && id &&
                                            <>
                                                You can utilize categories based on time instead of a singular category.  This will take you to a specialized page for it and it will override any categories picked here.
                                                <br />
                                                <Button onClick={() => window.open(`/p/registers/${id}/categories`, "_blank")}>
                                                    Time Categories <i className="far fa-external-link-alt" />
                                                </Button>
                                                <br />
                                            </>
                                        }
                                    </>
                                }
                            </Form.Group>
                            <Form.Group controlId="product_types">
                                <Form.Label>Assign Product Types to this Register</Form.Label>
                                {registerDefinition && initialProductTypes &&
                                    <ProductTypeTypeahead
                                        multiple={true}
                                        passSelection={(selection) => handleTypeCategoryChange(selection, "type_id")}
                                        initialDataIds={initialProductTypes || null}
                                    />
                                }
                            </Form.Group>
                        </div>
                    </div>
                    <div className="reg-form-row">
                        <div className="reg-form-col">
                            <h4>
                                Locations
                            </h4>
                            <Form.Group controlId="location">
                                <Form.Label>
                                    Location <span className="required-star">*</span>
                                    <Tooltip
                                        direction="top"
                                        text="Location is used to not only identify a register, but also to track what company a register is assigned to."
                                    >
                                        <i className="far fa-question-circle ms-1"/>
                                    </Tooltip>
                                </Form.Label>
                                {initialLocation &&
                                    <LocationTypeahead
                                        multiple={false}
                                        passSelection={(selection) => setLocationSelection(selection)}
                                        initialDataIds={initialLocation}
                                        required
                                    />
                                }
                            </Form.Group>
                            {(registerDefinition?.is_patron_register === 1 || registerDefinition?.is_patron_register === true) &&
                                <Form.Group controlId="description">
                                    <Form.Label>Pickup Location</Form.Label>
                                    <Form.Control type="text" name="pickup_location" value={registerDefinition?.pickup_location} onChange={handleChangeRegisterValue} />
                                </Form.Group>
                            }
                        </div>

                        

                    </div>

                    <div className="reg-form-row">
                        <div className="reg-form-col">
                            <h4>
                                Other
                            </h4>
                            {(registerDefinition?.is_patron_register === 0 || registerDefinition?.is_patron_register === false) &&
                                <>
                                    <Form.Group>
                                        <Form.Label>Terminal Device ID</Form.Label>
                                        <Form.Control type="text" name="terminal_device_id" value={registerDefinition?.terminal_device_id} onChange={handleChangeRegisterValue} />
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.Label>
                                            Header Buttons
                                        </Form.Label>
                                        <Typeahead
                                            makeRequest={async () => { return { data: HEADER_BUTTONS_LIST, errors: null } }}
                                            multiple={true}
                                            initialData={HEADER_BUTTONS_LIST?.filter((list) => buttonProps?.includes(list?.name))}
                                            passSelection={(selection) => handleButtonProp(selection)}
                                        />
                                    </Form.Group>
                                </>
                            }
                            <Form.Group className="radio-with-label" controlId="allow_variants">
                                <Form.Label>
                                    Allow Variants:
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Yes"
                                        id="allow-variants-true"
                                        name="allow_variants"
                                        value={1}
                                        checked={registerDefinition?.allow_variants === true ? true : false}
                                        onChange={handleChangeRegisterValue}
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="No"
                                        id="allow-variants-false"
                                        name="allow_variants"
                                        value={0}
                                        checked={registerDefinition?.allow_variants === false ? true : false}
                                        onChange={handleChangeRegisterValue}
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group>
                                <Form.Label>
                                    Skip Initial Load: <span className="required-star">*</span>
                                    <Tooltip
                                        direction="top"
                                        text="This can be useful when there are registers loading in the same location, such as with the home bar cart.  Skipping the initial load will skip checking for an open order (lastetOpen) or a service booking (from local)."
                                    >
                                        <i className="far fa-question-circle ms-1"/>
                                    </Tooltip>
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Yes"
                                        id="skip_initial_load_yes"
                                        name="skip_initial_load"
                                        value={1}
                                        checked={registerDefinition?.skip_initial_load === 1 || registerDefinition?.skip_initial_load === true}
                                        onChange={handleChangeRegisterValue}
                                        required
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="No"
                                        id="skip_initial_load_no"
                                        name="skip_initial_load"
                                        value={0}
                                        checked={registerDefinition?.skip_initial_load === 0 || registerDefinition?.skip_initial_load === false}
                                        onChange={handleChangeRegisterValue}
                                        required
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group>
                                <Form.Label>
                                    Hide Edit Cart Button: <span className="required-star">*</span>
                                    <Tooltip
                                        direction="top"
                                        text="This simply hides the edit cart button"
                                    >
                                        <i className="far fa-question-circle ms-1"/>
                                    </Tooltip>
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Yes"
                                        id="hide_edit_cart_button_yes"
                                        name="hide_edit_cart_button"
                                        value={1}
                                        checked={registerDefinition?.hide_edit_cart_button === 1 || registerDefinition?.hide_edit_cart_button === true}
                                        onChange={handleChangeRegisterValue}
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="No"
                                        id="hide_edit_cart_button_no"
                                        name="hide_edit_cart_button"
                                        value={0}
                                        checked={registerDefinition?.hide_edit_cart_button === 0 || registerDefinition?.hide_edit_cart_button === false}
                                        onChange={handleChangeRegisterValue}
                                    />
                                </div>
                            </Form.Group>
                            <Form.Group className="radio-with-label">
                                <Form.Label>
                                    Prompt for Tips:
                                </Form.Label>
                                <div className="radio-left">
                                    <Form.Check
                                        type="radio"
                                        label="Yes"
                                        id="prompt-for-tips-yes"
                                        name="prompt_for_tips"
                                        value={1}
                                        checked={registerDefinition?.prompt_for_tips === 1 || registerDefinition?.prompt_for_tips === true}
                                        onChange={handleChangeRegisterValue}
                                    />
                                    <Form.Check
                                        type="radio"
                                        label="No"
                                        id="prompt-for-tips-no"
                                        name="prompt_for_tips"
                                        value={0}
                                        checked={registerDefinition?.prompt_for_tips === 0 || registerDefinition?.prompt_for_tips === false}
                                        onChange={handleChangeRegisterValue}
                                    />
                                </div>
                            </Form.Group>
                            {(registerDefinition?.prompt_for_tips === 1 || registerDefinition?.prompt_for_tips === true) &&
                                <Form.Group>
                                    <Form.Label>
                                        Tip Values
                                    </Form.Label>
                                    <BootstrapTypeahead
                                        allowNew
                                        id="prompt_tip_percentages"
                                        name="prompt_tip_percentages"
                                        multiple
                                        newSelectionPrefix="Add new tip..."
                                        options={tipValues || []}
                                        placeholder="Add new tip value"
                                        onChange={(selection) => handleTipValuesChange(selection)}
                                        selected={tipValues}
                                        labelKey="tipValue"
                                    />
                                </Form.Group>
                            }
                        </div>
                    
                        <div className="reg-form-col">
                            <h4>Receipts</h4>
                            <Form.Group className="radio-left">
                                <Form.Check
                                    type="radio"
                                    label="Print Kitchen Receipt"
                                    id="print-kitchen-receipt-true"
                                    name="print_kitchen_receipt"
                                    value={1}
                                    checked={registerDefinition?.print_kitchen_receipt === 1 || registerDefinition?.print_kitchen_receipt === true}
                                    onChange={handleChangeRegisterValue}
                                />
                                <Form.Check
                                    type="radio"
                                    label="No Kitchen Receipt"
                                    id="print-kitchen-receipt-false"
                                    name="print_kitchen_receipt"
                                    value={0}
                                    checked={registerDefinition?.print_kitchen_receipt === 2 || registerDefinition?.print_kitchen_receipt === false}
                                    onChange={handleChangeRegisterValue}
                                />
                            </Form.Group><Form.Group>
                                <Form.Label>
                                    Receipt Message Location
                                </Form.Label>
                                <Form.Control
                                    as="select"
                                    type="select"
                                    name="receipt_message_location"
                                    value={registerDefinition?.receipt_message_location}
                                    onChange={handleChangeRegisterValue}
                                >
                                    <option value={0}>
                                        none
                                    </option>
                                    {RECEIPT_MESSAGE_LOCATION_OPTIONS.map((option) => (
                                        <option value={option} key={`receipt-message-location-${option}`}>{option}</option>
                                    ))}
                                </Form.Control>
                            </Form.Group>
                            <Form.Group controlId="receipt_message">
                                <Form.Label>
                                    Receipt Message
                                </Form.Label>
                                <Form.Control
                                    type="text"
                                    name="receipt_message"
                                    value={registerDefinition?.receipt_message}
                                    onChange={handleChangeRegisterValue}
                                    disabled={!registerDefinition?.receipt_message_location}
                                />
                            </Form.Group>
                            <Form.Group>
                                <Form.Label>
                                    Recipt email
                                    <Tooltip
                                        direction="top"
                                        text="If no email is provided, the email associated with the company will be used instead"
                                    >
                                        <i className="far fa-question-circle ms-1"/>
                                    </Tooltip>
                                </Form.Label>
                                <Form.Control
                                    type="text"
                                    name="receipt_email"
                                    value={registerDefinition?.receipt_email}
                                    onChange={handleChangeRegisterValue}
                                />
                            </Form.Group>
                        </div>
                    </div>

                    <Form.Group controlId="register_definition">
                        {userPermissions && userPermissions[EDIT_ADVANCED_MODULE_ID] &&
                            <div className="show-register-button-row">
                                <Button
                                    variant="light"
                                    onClick={() => setHideRegisterDefinition(prev => !prev)}
                                >
                                    {hideRegisterDefinition ? "Edit Register Definition (Advanced)" : "Hide Register Definition"}
                                </Button>
                            </div>
                        }

                        {!hideRegisterDefinition &&
                            <>
                                <Form.Label>Register Definition</Form.Label>
                                {pagePartRegisterDefinitionInput}
                            </>
                        }
                    </Form.Group>

                    {(registerDefinition?.is_patron_register === 1 || registerDefinition?.is_patron_register === true) &&
                        <Form.Group>
                            <Form.Check
                                type="checkbox"
                                label="Is Disabled"
                                id="is_disabled"
                                name="is_disabled"
                                value={1}
                                checked={!!isDisabled}
                                onChange={handleChangeIsDisabled}
                            />
                        </Form.Group>
                    }
                </Form.Row>

                <div className="button-row">
                    <div>
                        <Button
                            variant="danger"
                            onClick={clickDelete}
                            disabled={submitting}
                            className={id ? "" : "hidden"}
                        >
                            Delete This Register
                        </Button>
                    </div>
                    <div>
                        <Button
                            variant="secondary"
                            onClick={clickCancel}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={submitting}
                            className={`${submitting ? " submitting" : ""}`}
                        >
                            Save Changes
                        </Button>
                    </div>
                </div>
            </Form>
            {error} </Container>
    );
};


export default RegisterForm;
