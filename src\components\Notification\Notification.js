import React, { useEffect, useState } from 'react';
import Table from 'react-bootstrap/Table';
import Notifications from '../../api/Notifications';
import './Notification.css';

export const LoadNotifications = (props) => {
    return Notifications.get();
}

const Notification = (props) => {
    // show loading overlay
    const [loading,setLoading]=useState(false);
    const [notifications,setNotifications]=useState([]);

    // first load, get notifications from api
    useEffect(() => {
        let mounted = true;

        setLoading(true);
        /*LoadNotifications()
        .then(response => {
            if(mounted && response.data) {
                setNotifications(response.data[0]);
                setLoading(false);
            }
        }).catch(e => console.error(e));*/

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
            setNotifications([]);
        }        
    },[]);

    let date="";
    return (
        <div className={`notification-table${loading?" loading":""}`}>
            <Table className="table">
                <tbody>
                    {notifications.map((notification)=>{
                        date=new Date(notification.date).toLocaleDateString("en-US",{weekday:"short", year:"numeric", month:"2-digit", day:"2-digit"})
                        return (
                            <tr key={`notf${notification.id}`}>
                                <td>{date}</td>
                                <td>{notification.title}</td>
                            </tr>
                        )
                    })}
                </tbody>
            </Table>
        </div>
    );
}

export default Notification;