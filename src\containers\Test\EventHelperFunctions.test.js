import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { 
    getIndividualFamily,
    createNewFamilyGroup,
    checkFamilyAllowedByRole,
    checkAllowedAge,
    checkIsRegistered,
    getFamily,
    eachFamilyMember,
    checkFamilyStatuses
} from './EventHelperFunctions';

// Mock the Groups API
jest.mock('../../api/Groups', () => ({
    get: jest.fn(),
    create: jest.fn(),
    groupFilter: jest.fn()
}));

import Groups from '../../api/Groups';

describe('EventHelperFunctions', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getIndividualFamily', () => {
        it('should return family data when API call succeeds', async () => {
            const mockFamilyData = { id: 1, name: 'Test Family' };
            Groups.get.mockResolvedValue({ data: [mockFamilyData] });

            const result = await getIndividualFamily(1);

            expect(Groups.get).toHaveBeenCalledWith({ id: 1 });
            expect(result).toEqual(mockFamilyData);
        });

        it('should handle API errors gracefully', async () => {
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            Groups.get.mockRejectedValue(new Error('API Error'));

            const result = await getIndividualFamily(1);

            expect(consoleSpy).toHaveBeenCalled();
            expect(result).toBeUndefined();
            consoleSpy.mockRestore();
        });

        it('should return undefined when no data returned', async () => {
            Groups.get.mockResolvedValue({ data: [] });

            const result = await getIndividualFamily(1);

            expect(result).toBeUndefined();
        });
    });

    describe('createNewFamilyGroup', () => {
        it('should create family group and call getFamily on success', async () => {
            const mockUser = { last_name: 'Smith' };
            const mockGetFamily = jest.fn();
            Groups.create.mockResolvedValue({ status: 200 });

            await createNewFamilyGroup(mockUser, mockGetFamily);

            expect(Groups.create).toHaveBeenCalledWith({
                name: 'Smith Family',
                description: 'Family Group',
                group_type_id: 4,
                group_status_id: 1,
                group_member_role_id: 10
            });
            expect(mockGetFamily).toHaveBeenCalled();
        });

        it('should not call getFamily when API fails', async () => {
            const mockUser = { last_name: 'Smith' };
            const mockGetFamily = jest.fn();
            Groups.create.mockResolvedValue({ status: 400 });

            await createNewFamilyGroup(mockUser, mockGetFamily);

            expect(mockGetFamily).not.toHaveBeenCalled();
        });

        it('should handle API errors gracefully', async () => {
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            const mockUser = { last_name: 'Smith' };
            const mockGetFamily = jest.fn();
            Groups.create.mockRejectedValue(new Error('API Error'));

            await createNewFamilyGroup(mockUser, mockGetFamily);

            expect(consoleSpy).toHaveBeenCalled();
            expect(mockGetFamily).not.toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });

    describe('checkFamilyAllowedByRole', () => {
        const mockUser = { id: 123 };

        it('should return true when user is admin', async () => {
            const mockFamily = {
                group_members: [{ user_id: 123, group_member_role_id: 10 }],
                group_member_roles: [{ id: 10, is_admin: 1 }]
            };

            const result = await checkFamilyAllowedByRole(mockFamily, mockUser);

            expect(result).toBe(true);
        });

        it('should return false when user is not admin', async () => {
            const mockFamily = {
                group_members: [{ user_id: 123, group_member_role_id: 5 }],
                group_member_roles: [{ id: 5, is_admin: 0 }]
            };

            const result = await checkFamilyAllowedByRole(mockFamily, mockUser);

            expect(result).toBe(false);
        });

        it('should return false when user not found in family', async () => {
            const mockFamily = {
                group_members: [{ user_id: 456, group_member_role_id: 10 }],
                group_member_roles: [{ id: 10, is_admin: 1 }]
            };

            const result = await checkFamilyAllowedByRole(mockFamily, mockUser);

            expect(result).toBe(false);
        });

        it('should handle missing data gracefully', async () => {
            const mockFamily = {};

            const result = await checkFamilyAllowedByRole(mockFamily, mockUser);

            expect(result).toBe(false);
        });
    });

    describe('checkAllowedAge', () => {
        const mockUser = { dob: '2010-01-01' }; // 13-14 years old

        it('should allow user within age range', () => {
            const result = checkAllowedAge(mockUser, 10, 20);

            expect(result.isAllowed).toBe(true);
            expect(result.notAllowedReason).toBe("");
        });

        it('should reject user below minimum age', () => {
            const result = checkAllowedAge(mockUser, 18, 25);

            expect(result.isAllowed).toBe(false);
            expect(result.notAllowedReason).toBe("Participant is below the age limit");
        });

        it('should reject user above maximum age', () => {
            const result = checkAllowedAge(mockUser, 5, 10);

            expect(result.isAllowed).toBe(false);
            expect(result.notAllowedReason).toBe("Participant is not within the age range");
        });

        it('should allow user when only minimum age set and user meets it', () => {
            const result = checkAllowedAge(mockUser, 10);

            expect(result.isAllowed).toBe(true);
        });

        it('should allow user when only maximum age set and user meets it', () => {
            const result = checkAllowedAge(mockUser, null, 20);

            expect(result.isAllowed).toBe(true);
        });

        it('should allow user when no age restrictions', () => {
            const result = checkAllowedAge(mockUser);

            expect(result.isAllowed).toBe(true);
        });
    });

    describe('checkIsRegistered', () => {
        const mockUser = { user_id: 123 };

        it('should return true when user is registered', () => {
            const mockEventDetails = {
                advancedDetails: {
                    users: [{ id: 123 }, { id: 456 }]
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);

            expect(result).toBe(true);
        });

        it('should return false when user is not registered', () => {
            const mockEventDetails = {
                advancedDetails: {
                    users: [{ id: 456 }, { id: 789 }]
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);

            expect(result).toBe(false);
        });

        it('should return false when no users in event', () => {
            const mockEventDetails = {
                advancedDetails: {
                    users: []
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);

            expect(result).toBe(false);
        });

        it('should handle missing event details gracefully', () => {
            const result = checkIsRegistered(mockUser, {});

            expect(result).toBe(false);
        });
    });

    describe('eachFamilyMember', () => {
        const mockEventDetails = { min_age: 10, max_age: 20 };

        it('should process family members correctly', async () => {
            const mockFamily = {
                group_members: [
                    {
                        user_id: 123,
                        first_name: 'John',
                        last_name: 'Doe',
                        group_member_role_name: 'Child',
                        dob: '2010-01-01'
                    },
                    {
                        user_id: 456,
                        first_name: 'Jane',
                        last_name: 'Doe',
                        group_member_role_name: 'Parent',
                        dob: '1985-01-01'
                    }
                ]
            };

            const result = await eachFamilyMember(mockFamily, 0, mockEventDetails);

            expect(result).toHaveLength(2);
            expect(result[0]).toMatchObject({
                key: 2,
                id: 123,
                firstName: 'John',
                lastName: 'Doe',
                name: 'John Doe',
                isSelf: false,
                roleName: 'Child'
            });
        });

        it('should handle empty family members', async () => {
            const mockFamily = { group_members: [] };

            const result = await eachFamilyMember(mockFamily, 0, mockEventDetails);

            expect(result).toEqual([]);
        });
    });

    describe('getFamily', () => {
        const mockUser = { id: 123, last_name: 'Smith' };
        const mockLoadingStates = { current: { family: false } };

        it('should return family groups when user has families', async () => {
            const mockFamilyGroups = [
                { id: 1, group_type_name: 'Family', group_status_id: 1 }
            ];
            const mockExtraDetails = {
                tags: ['tag1'],
                group_member_roles: [{ id: 10, is_admin: 1 }],
                group_members: [{ user_id: 123, group_member_role_id: 10 }]
            };

            Groups.groupFilter.mockResolvedValue({ data: { groups: mockFamilyGroups } });
            Groups.get.mockResolvedValue({ data: [mockExtraDetails] });

            const result = await getFamily(mockUser, mockLoadingStates);

            expect(Groups.groupFilter).toHaveBeenCalledWith({ filters: { user_id: 123 } });
            expect(result).toHaveLength(1);
            expect(mockLoadingStates.current.family).toBe(false);
        });

        it('should create new family when user has no families', async () => {
            Groups.groupFilter.mockResolvedValue({ data: { groups: [] } });
            Groups.create.mockResolvedValue({ status: 200 });

            await getFamily(mockUser, mockLoadingStates);

            expect(Groups.create).toHaveBeenCalledWith({
                name: 'Smith Family',
                description: 'Family Group',
                group_type_id: 4,
                group_status_id: 1,
                group_member_role_id: 10
            });
        });

        it('should handle API errors and set loading state to ERROR', async () => {
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            Groups.groupFilter.mockRejectedValue(new Error('API Error'));

            await getFamily(mockUser, mockLoadingStates);

            expect(mockLoadingStates.current.family).toBe('ERROR');
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });

    describe('checkFamilyStatuses', () => {
        const mockUser = {
            id: 123,
            first_name: 'John',
            last_name: 'Doe',
            dob: '1990-01-01'
        };
        const mockLoadingStates = { current: { family: false } };
        const mockEventDetails = { min_age: 10, max_age: 50 };

        it('should process user and family members correctly', async () => {
            const mockFamily = [{
                group_members: [
                    {
                        user_id: 456,
                        first_name: 'Jane',
                        last_name: 'Doe',
                        group_member_role_name: 'Spouse',
                        dob: '1992-01-01'
                    }
                ]
            }];

            const result = await checkFamilyStatuses(mockLoadingStates, mockUser, mockFamily, mockEventDetails);

            expect(result).toHaveLength(2);
            expect(result[0]).toMatchObject({
                key: 1,
                id: 123,
                firstName: 'John',
                lastName: 'Doe',
                isSelf: true
            });
            expect(result[1]).toMatchObject({
                id: 456,
                firstName: 'Jane',
                lastName: 'Doe',
                isSelf: false
            });
            expect(mockLoadingStates.current.family).toBe(false);
        });

        it('should filter out duplicate family members', async () => {
            const mockFamily = [
                {
                    group_members: [
                        {
                            user_id: 456,
                            first_name: 'Jane',
                            last_name: 'Doe',
                            group_member_role_name: 'Spouse',
                            dob: '1992-01-01'
                        }
                    ]
                },
                {
                    group_members: [
                        {
                            user_id: 456, // Same user in different family group
                            first_name: 'Jane',
                            last_name: 'Doe',
                            group_member_role_name: 'Parent',
                            dob: '1992-01-01'
                        }
                    ]
                }
            ];

            const result = await checkFamilyStatuses(mockLoadingStates, mockUser, mockFamily, mockEventDetails);

            expect(result).toHaveLength(2); // Should only have user + 1 unique family member
            const janeEntries = result.filter(member => member.id === 456);
            expect(janeEntries).toHaveLength(1);
        });

        it('should handle empty family array', async () => {
            const result = await checkFamilyStatuses(mockLoadingStates, mockUser, [], mockEventDetails);

            expect(result).toHaveLength(1); // Only the user
            expect(result[0].isSelf).toBe(true);
        });
    });

    describe('Integration Tests', () => {
        it('should handle complete family workflow', async () => {
            const mockUser = { id: 123, last_name: 'Smith', first_name: 'John', dob: '1990-01-01' };
            const mockLoadingStates = { current: { family: false } };
            const mockEventDetails = { min_age: 10, max_age: 50 };

            // Mock API responses for complete workflow
            Groups.groupFilter.mockResolvedValue({
                data: {
                    groups: [{ id: 1, group_type_name: 'Family', group_status_id: 1 }]
                }
            });

            Groups.get.mockResolvedValue({
                data: [{
                    tags: [],
                    group_member_roles: [{ id: 10, is_admin: 1 }],
                    group_members: [
                        { user_id: 123, group_member_role_id: 10 },
                        {
                            user_id: 456,
                            first_name: 'Jane',
                            last_name: 'Smith',
                            group_member_role_name: 'Spouse',
                            dob: '1992-01-01'
                        }
                    ]
                }]
            });

            // Test the complete workflow
            const families = await getFamily(mockUser, mockLoadingStates);
            expect(families).toHaveLength(1);

            const familyStatuses = await checkFamilyStatuses(mockLoadingStates, mockUser, families, mockEventDetails);
            expect(familyStatuses).toHaveLength(2);
            expect(familyStatuses[0].isSelf).toBe(true);
            expect(familyStatuses[1].isSelf).toBe(false);
        });
    });
});
