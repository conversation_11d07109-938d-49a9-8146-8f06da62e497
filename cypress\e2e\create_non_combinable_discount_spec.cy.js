
/* eslint-disable*/
let qaDevSB = Cypress.env('impact_sb_user');
let password = Cypress.env('login_password')
let baseUrl = "https://portal-qa.impactathleticsny.com/p/";

describe("It will ultimately create a non-combinable discount needed for other tests, thereby testing creating discounts in the meantime", {scrollBehavior: "center", testIsolation:false}, ()=>{
    let local;
    let exists=true; //default to true to not make more than necessary discounts

    before("It will log in an admin user",()=>{
        
        cy.loginLocal(baseUrl, qaDevSB, password).then(()=>{
            local = localStorage.getItem('user');
        })
    })

    beforeEach("Renew local storage and viewport size",()=>{
        cy.viewport(1920, 1080);
        cy.restoreLocalUser(JSON.parse(local))
    })

    it('will check and see if the coupon already exists',()=>{
        cy.intercept('GET', '/api/coupon').as('getCoupons');
        cy.visit(`${baseUrl}discount/dashboard`);
        cy.wait('@getCoupons')
        cy.get('tr')
            .each(($row)=>{
                cy.get($row)
                    .invoke('text')
                    .as('discountRow')
                cy.get('@discountRow').then(($text)=>{
                    //could set exists directly off this variable but sometimes this runs before data is loaded and creates false positive if done as false and direct variable change here
                    let tempExist = false
                    if(($text).includes('Extra Discount')) tempExist = true
                    exists=tempExist
                })    
            })
    })

    context("enter coupon data, if it doesn't exist, will save the coupon",()=>{
            
        it("will navigate to the discount creator",()=>{
            cy.get('[data-cy="menu-item-New Discount-363"]')
                .click({force: true})
            cy.url()
                .should('include', '/discount/create')
        })
    
        it("will fill in name, check description",()=>{
            cy.get('#name')
                .type('Extra Discount');
            cy.get('#description')
                .should('exist')
            cy.get(':nth-child(2) > .btn')
                .should('contain', 'Next')
                .click();
        })
    
        it("will select auto add or coupon code",()=>{
            cy.get('#auto_apply-0') //use coupon code
                .should('exist')
                .click()
            cy.get('#coupon_code')
                .should('exist')
            cy.get('#auto_apply-1') //auto apply
                .click()
            cy.get(':nth-child(2) > .btn')
                .should('contain', 'Next')
                .click()
        })
    
        it("will select max number of uses",()=>{
            cy.get('#unlimited-0') //yes
                .should('exist')
                .click()
            cy.get('#max_uses')
                .should('exist')
            cy.get('#unlimited-1') //unlimited
                .click()
            cy.get(':nth-child(2) > .btn')
                .should('contain', "Next")
                .click()
        })
    
        it("will check the date selections",()=>{
            cy.get('.wizard > .row > :nth-child(1)')
                .should('exist')
                .and('contain', 'Start Date')
            cy.get('.row > :nth-child(2)')
                .should('exist')
                .and('contain', 'End Date')
            
            cy.get('#unlimited-0')
                .click()
            cy.get(':nth-child(2) > .btn')
                .should('contain', "Next")
                .click()
        })
    
        it("will select percentage amount",()=>{
            cy.get('#discount_type-1')
                .should('exist')
                .click()
            cy.get('#basic-addon1')
                .invoke('text')
                .should("include", "$")
            cy.get('#discount_type-0')
                .click()
            cy.get('#discount_amount')
                .type(10)
            cy.get('#basic-addon2')
                .invoke('text')
                .should("include", "%")
            cy.get(':nth-child(2) > .btn')
                .should('contain', "Next")
                .click()
        });

        it("should select whole order",()=>{
            cy.log(`${String.fromCodePoint(0x1F92F)} Whole Order or Qualifying?`);
            cy.get('#apply_to_all-0')
                .should('exist')
            cy.get('#apply_to_all-1')
                .click()
            cy.get(':nth-child(2) > .btn')
                .should('contain', "Next")
                .click()

        });

        it("Should select that it cannot be used with other discounts (important for the other test for this coupon)",()=>{
            cy.get('#combinable-1')
                .should('exist')
            cy.get('#combinable-0')
                .click();
            cy.get(':nth-child(2) > .btn')
                .should('contain', "Next")
                .click()
        });
        
        it('will not add any other conditions and only save if this coupon was already found to not exist',()=>{
            cy.get(':nth-child(2) > .btn')
                    .click();
        
                cy.log(`${String.fromCodePoint(0x1F92F)} Save the new coupon`)
                cy.get('.wizard')
                    .should('exist')
                    .and('not.be', null)
                
                //only save if the coupon doesn't exist    
                if(!exists){
                    cy.get('.submit')
                        .click()
                }
        })
    })
})