@import '../../../assets/css/scss/themes.scss';

/* Week Date Picker Container */
.datepicker {
    justify-content: center;
    align-items: center;
    margin-top: 0;
    margin-bottom: 0;
}

/* Date Picker Navigation Buttons */
.datepickerBackNext {
    border-radius: 0.5rem;
    padding: 4px 9px !important;
    line-height: 18px;

    &.back {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-right: 0;
    }

    &.next {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        /* border-left: 0; */
    }

    i {
        font-size: 1.3rem;
        margin-left: 0;
        margin-right: 0;
        color: $primary-color;
    }

    &:disabled {
        &:hover {
            background-color: #f8f9fa;
            border-color: #CCC;

            i {
                color: #afafaf;
            }
        }

        i {
            color: #afafaf;
        }
    }
}

/* Date Picker Calendar Button */
.datepickerCalendar {
    margin-left: 0;
    border-radius: 0;
}

/* Button Group */
.buttonGroup {
    margin-right: 0;

    .btn {
        margin-right: 0 !important;
    }
}

/* React DatePicker Overrides */
:global(.react-datepicker-popper) {
    z-index: 50;
}

:global(.react-datepicker__week) {
    .react-datepicker__day--selected {
        background: $primary-color;
        border-radius: $date-picker-day-border-radius;
    }

    &:hover .react-datepicker__day {
        color: $primary-inverse-color;
        font-weight: $bold-font-weight;
        background: $primary-color;
        border-radius: $date-picker-day-border-radius;
    }
}