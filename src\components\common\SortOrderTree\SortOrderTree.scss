@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/themes';

.sort-order-tree{
    @include basic-flex-row;
    @media (max-width: 700px){
        flex-direction: column-reverse;
    }
    .tree{
        width: 400px;
        .skeleton{
            @include basic-flex-column;
        }
        @media (max-width: 500px){
            max-width: 300px;
            width: auto;
        }
    }
    .details{
        width: 300px;
        max-width: 300px;
        margin: 2rem 0 2rem 2rem;
        padding: 8px;
        border: 1px solid $primary-color;
        border-radius: $card-border-radius;
        height: fit-content;
        position: sticky;
        top: 100px;
        .stack{
            @include basic-flex-column;
        }
        label{
            @include basic-label;
            font-size: 1rem;
        }
        span{
            margin-left: 1.5rem;
        }
        @media(max-width: 700px){
            position:inherit;
        }
        @media(max-width: 400px){
            max-width: 250px;
            width: auto;
        }
    }
    .rct-tree-item-button{
        font-size: 1rem;
        border: 1px solid $form-control-border-color;
        margin: 0;
        @media(max-width: 400px){
            height: fit-content;
        }
    }
    .rct-tree-item-title-container-selected .rct-tree-item-button::before{
        background-color: $tertiary-color;
    }
    .rct-tree-drag-between-line {
        background-color: $primary-color;
    }
    .rct-tree-item-button-selected{
        background-color: $button-hover-background-color;
        color: $primary-inverse-color;
    }
    .rct-tree-item-button.disabled-tree{
        background-color: blue;
    }
    .tree-disabled{
       
    }
}