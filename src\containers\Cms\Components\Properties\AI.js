import React, {useState, useEffect, useCallback } from 'react';
import { Row, Col, Form, Button, Modal } from 'react-bootstrap';
import Stack from '../../../../components/common/Stack';
import APICms from '../../../../api/Cms';

const AI = (props) => {
    const {selection} = props;
    
    const [loading, setLoading] = useState(false);
    const [aiResult, setAiResult] = useState();
    const [error, setError] = useState();
    const [show, setShow] = useState(false);
    const [type, setType] = useState(props.type || 'Headline');
    const [keywords, setKeywords] = useState('');

    useEffect(() => {
        if (aiResult){
            selection(aiResult);
        }
    }, [aiResult, selection]);

    useEffect(() => {
        return () => {
            setLoading(false);
            setAiResult(null);
            setType('Headline');
            setKeywords('');
            setShow(false);
            setError(null);
        }
    }, []);

    const clickHandler = useCallback(e => {
        e.preventDefault();
        setLoading(true);
        let _params={type};
        if (!props?.inline) _params = {..._params, keywords, allow_html: true};
        try {
            APICms.ai(_params).then(res => {
                if (res.errors) setError(res.errors);
                else if (res.data) {
                    setAiResult(res.data);
                    setError(null);
                }
                setShow(false);
                setLoading(false);
            });
        } catch (error) {
            setError(error);
            setLoading(false);
        }
    }, [props?.inline, type, keywords]);

    return (
        <>
            <Stack direction="horizontal" gap={1} className="mb-2">
                <Button variant="light" href="#!" onClick={(e)=>props?.inline?clickHandler(e):setShow(true)} className="mt-1" disabled={loading}>
                    <i className="far fa-robot"/>
                </Button>
                {loading &&
                    <span>Generating text...</span>
                }
                {!loading && error &&
                    <span className="text-danger">{error}</span>
                }
            </Stack>
            {!props?.inline && !loading &&
                <Modal show={show} onHide={() => setShow(false)} size="md" centered>
                    <Modal.Header closeButton />
                    <Modal.Body>
                        <Row>
                            <Col sm={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Type of text</Form.Label>
                                    <Form.Control as="select" custom onChange={(e)=>setType(e.target.value)} value={type} disabled={loading}>
                                        <option>Headline</option>
                                        <option>About</option>
                                        <option>Contact</option>
                                        <option>Profile</option>
                                        <option>Testimonial</option>
                                        <option>Blog</option>
                                        <option>Article</option>
                                        <option>Product</option>
                                        <option>Service</option>
                                        <option>Category</option>
                                        <option>Event</option>
                                        <option>News</option>
                                        <option>FAQ</option>
                                        <option>Terms</option>
                                        <option>Privacy</option>
                                        <option>Help</option>
                                        <option>Team</option>
                                        <option>Jobs</option>
                                        <option>Portfolio</option>
                                        <option>Review</option>
                                    </Form.Control>
                                </Form.Group>
                            </Col>
                            <Col sm={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Keywords <small> / separated by commas ","</small></Form.Label>
                                    <Form.Control placeholder="easy to read, short, cooking, indian food, spicy" defaultValue={keywords} onBlur={(e)=>setKeywords(e.target.value)} disabled={loading} />                                    
                                </Form.Group>
                            </Col>
                            <Col sm={12}>
                                <Button variant="primary" href="#!" onClick={clickHandler} disabled={loading}>Generate</Button>
                            </Col>
                        </Row>
                    </Modal.Body>
                </Modal>
            }
        </>
    );
}

export default AI;