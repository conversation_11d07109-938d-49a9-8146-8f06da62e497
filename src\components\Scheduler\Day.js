import React from 'react';
import {getDayNames, showTasks} from './Common.js';
import './Scheduler.css';

// create time array
const timeArray = (year, month, day, tasks) =>{    

    if (!day) day = new Date().getDate();
    if (!month) month = new Date().getMonth() +1;
    if (!year) year = new Date().getFullYear();

    if (!tasks) tasks=[];

    let timesArr = {
        header: {
            day: day,
            month: month,
            year: year
        },
        times:[], // will fill with sub arrays for each week
        tasks:[] // will fill with sub arrays for each event within a week
    }; 
    

    let dayIndex="";
    for (let i=0;i<24;i++){
        timesArr.times.push({
            date:new Date(year,month-1,day,i,0,0),
            hour24:new Date(year,month-1,day,i,0,0).toLocaleTimeString("en-US", { hour12: false, hour: '2-digit' }),
            hour:new Date(year,month-1,day,i,0,0).toLocaleTimeString("en-US", { hour12: true, hour: '2-digit' }),
            minute:new Date(year,month-1,day,i,0,0).toLocaleTimeString("en-US", { hour12: true, minute: '2-digit' })
        });
        timesArr.tasks.push([]);

        dayIndex=`${year}/${(("0" + (month)).slice(-2))}/${("0" + day).slice(-2)}`;

        if (tasks[dayIndex] || tasks["everyday"]){
            [...(tasks[dayIndex] || []), ...(tasks["everyday"] || [])].forEach(task => {
                let [hour]=task.time_start.split(":");
                if (hour===new Date(year,month-1,day,i,0,0).toLocaleTimeString("en-US", { hour12: false, hour: '2-digit' })){
                    timesArr.tasks[i].push(task);
                }
            });
        }
    }
    return timesArr;
}


// create a daily view scheduler
const Day = (props) => {
    const calendar=timeArray(props.date.getFullYear(), props.date.getMonth()+1, props.date.getDate(), props.tasks);

    let nowClass="";
    return (
        <div className="scheduler scheduler-day-table">
            <span className="day-name">{getDayNames(props.date.getDay()).text}</span>
            {calendar?.times.map((time,i)=>{
                nowClass="";
                if (time.date.getTime()===new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(),new Date().getHours(),0).getTime()) nowClass="day-today";
                return (
                        <div className={nowClass} key={`con${i}`}>
                            <div className="scheduler-time">{time.hour}</div>
                            <div>
                                { showTasks(calendar.tasks[i], props.date) }
                            </div>
                        </div>
                );
            })}
            
        </div>
    );
}

export default Day;