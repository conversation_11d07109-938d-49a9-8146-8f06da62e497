import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Container, Card, Breadcrumb, Modal } from 'react-bootstrap';
import { useSelector } from 'react-redux';
import { useLocation, Link } from 'react-router-dom';
import SubHeader from '../../components/common/SubHeader';

import Config from '../../api/Config';
import { setErrorCatcher, setSuccessToast } from '../../utils/validation';
import ConfigParams from './ConfigComponents/ConfigParams';
import ViewConfigTypes from './ConfigComponents/ViewConfigTypes';
import './Configs.scss'

export const Configs = ({company_id=null, ...props}) => {

    const mountedRef = useRef(false);
    const location = useLocation();
    const [companyId, setCompanyId] = useState(useSelector(state => state.auth.user.company_id));
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [companyConfigs, setCompanyConfigs] = useState([]);
    const [configTypes, setConfigTypes] = useState([]);
    const [selectedConfigs, setSelectedConfigs]=useState();
    const [leftoverConfigs, setLeftoverConfigs]=useState([]);
    const [view, setView] = useState(true); //true is for viewing, false is for editing
    const [createNewConfig, setCreateNewConfig] = useState(false); //true is for create modal
    const [deletedConfigType, setDeletedConfigType]=useState(false)
    const adminDash = useRef(JSON.parse(localStorage.getItem("adminDash")));

    const getCompanyConfigs = useCallback(async(id)=>{
        try{
            let response = await Config.CompanyConfig.get({company_id: +id});
            if(response.status === 200){
                let newResponse = await convertParams(response.data);
                setCompanyConfigs(newResponse);
                setLoading(false);
            }
            else{
                setError(setErrorCatcher(response.errors, false));
                setLoading(false)
            }
        }catch(ex){
            setLoading(false);
            console.error(ex);
        }
    },[]);

    const getConfigTypes = useCallback(async(id)=>{
        let params = {
            is_active: true
        }
        if(id) params.id = +id;
        try{
            let response = await Config.ConfigTypes.get(params);
            if(response.status === 200){
                let newResponse = await convertParams(response.data);
                setConfigTypes(newResponse);
            }
        }catch(ex){
            console.error(ex);
        }
    },[]);

    useEffect(()=>{
        mountedRef.current = true

        return()=> mountedRef.current = false
    },[]);

    useEffect(()=>{
        const filterLeftoverConfigs=()=>{
            let selectedConfigKeys = Object.keys(selectedConfigs?.config);
            let configTypeKeys = configTypes?.filter(config=>config?.id===selectedConfigs?.config_type_id);
            if(configTypeKeys.length > 0) {
                configTypeKeys = Object.keys(configTypeKeys[0]?.config_params)
                let needed = []
                for(let i = 0; i < configTypeKeys.length; i++){
                    if(!selectedConfigKeys.includes(configTypeKeys[i])){
                        let fullConfig = [configTypeKeys[i], configTypes[0]?.config_params[configTypeKeys[i]]]
                        needed.push(fullConfig)
                    }
                }
    
                setLeftoverConfigs(needed)
                setDeletedConfigType(false)
            }else setDeletedConfigType(true)
        }

        if(selectedConfigs && configTypes && selectedConfigs.hasOwnProperty("config")){
            filterLeftoverConfigs();
        }
    },[selectedConfigs, configTypes])

    //the company id of the user will only apply if not coming in as a SB admin. SB admin will override with a search string  
    useEffect(()=>{
        if(mountedRef.current) getConfigTypes();
        if(companyId && mountedRef.current){
            if(location.search.includes("?company=")) {
                let id = new URLSearchParams(window.location.search).get("company")
                getCompanyConfigs(id);
                setCompanyId(+id);
            }else if(typeof (location.pathname.split("/")[3]) === "number"){
                let id = location.pathname.split("/")[3];
                getCompanyConfigs(id);
                setCompanyId(+id);
            }else if(company_id) {
                getCompanyConfigs(company_id)
                setCompanyId(company_id)
            }else {
                getCompanyConfigs(companyId)
            }
        }
    },[location, companyId, company_id, getCompanyConfigs, getConfigTypes]);

    const handleSave = async(e)=>{
        e.preventDefault();
        let formData = new FormData(e.target);
        let formDataObj = Object.fromEntries(formData.entries());
        let isActive = formDataObj.is_active;
        let key = formDataObj.key;
        delete formDataObj.is_active;
        delete formDataObj.key;
        delete formDataObj.config_type_id;

        let response;
        try{
            if(selectedConfigs && !createNewConfig){
                response = await Config.CompanyConfig.update({
                    id: selectedConfigs.id,
                    company_id: companyId,
                    config: formDataObj,
                    key: key,
                    is_active: +isActive
                })
            }
            else if(createNewConfig){
                response = await Config.CompanyConfig.create({
                    config_type_id: selectedConfigs[0].id,
                    company_id: companyId,
                    config: formDataObj,
                    key: key,
                    is_active: +isActive 
                })
            }
            if(response.status === 200){
                setSuccess(setSuccessToast(createNewConfig ? "Config Created Successfully!" : "Config Edited Successfully!"));
                getCompanyConfigs(companyId);
                setSelectedConfigs();
                handleHide();
            }else if(response.errors) setError(setErrorCatcher(response.errors, false));
            else setError(setErrorCatcher("Unable to Edit Configs", false));
        }catch(ex){
            console.error(ex)
        }
    }

    const convertParams =(response)=>{
        response.forEach((config)=>{
            let entries;
            console.log(config)
            if(config.config) entries = Object.entries(config.config);
            if(config.config_params) entries = Object.entries(config.config_params);
            config.converted_params = entries
        })
        return response
    }

    const handleHide = ()=>{
        setSelectedConfigs();
        setCreateNewConfig(false);
        setView(true);
    }

    // create array of breadcrumbs
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" }
    ];

    // if adminDash.current is true, add the permission dashboard breadcrumb
    if (adminDash.current) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" })
    }

    // add the current page to the breadcrumbs
    breadcrumbs.push({ text: "Configs" });

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} />
            <Card className="content-card">
                <h4 className="section-title config-title-row">
                    <span>
                        Company Configs
                    </span>
                    <span>
                        <button onClick={()=>{setCreateNewConfig(true)}}>New Config</button>
                    </span>
                </h4>
                <div className='company-configs-wrapper'>
                    {!loading && companyConfigs.length > 0 &&
                        <ViewConfigTypes 
                            configTypes={companyConfigs}
                            handleEdit={(configs)=>setSelectedConfigs(configs)}
                        />
                    }
                    <Modal dialogClassName="config-modal" show={selectedConfigs && !createNewConfig ? true : false} onHide={handleHide}>
                        <Modal.Header closeButton>
                            <div>
                                <span>
                                    {view ? "View" : "Edit"} Configs
                                </span>
                                {!deletedConfigType &&
                                    <span>
                                        <button className="view-edit-btn" onClick={()=>setView(!view)}>
                                            {view ? "Edit" : "View"}
                                        </button>
                                    </span>
                                }
                            </div>
                        </Modal.Header>
                        <Modal.Body>
                            {selectedConfigs && !deletedConfigType &&
                                <form onSubmit={handleSave}>
                                    <p className="label-pair">
                                        <span className="config-label">Config Type</span>
                                        <span>{selectedConfigs?.config_type_name}</span>
                                    </p>
                                    <div>
                                        {view ?
                                            <>
                                                <p className="label-pair">
                                                    <span className="config-label">Config Name</span>
                                                    <span>{selectedConfigs?.key}</span>
                                                </p>
                                                <p className="label-pair">
                                                    <span className="config-label">Is Config Active?</span>
                                                    <span>{selectedConfigs?.is_active===1 ? "yes" : "no"}</span>
                                                </p>
                                            </>
                                        :
                                            <>
                                                <p className="label-pair">
                                                    <label htmlFor="key">Config Name</label>
                                                    <input 
                                                        type="text"
                                                        name='key'
                                                        defaultValue={selectedConfigs?.key || ""}
                                                    />        
                                                </p>
                                                <p className="label-pair">
                                                    <label htmlFor="is_active">Is Config Active?</label>
                                                    <select
                                                        name="is_active"
                                                    >
                                                        <option value={1} selected={selectedConfigs?.is_active === 1 ? true : false}>Yes</option>
                                                        <option value={0} selected={selectedConfigs?.is_active === 1 ? false: true}>No</option>
                                                    </select>
                                                </p>
                                            </>
                                        }
                                    </div>
                                    {selectedConfigs?.converted_params?.map((config, i)=>(
                                        <div key={`each-config-param${i}`}>
                                            <ConfigParams 
                                                param={config} //what's being viewed
                                                view={view}  //view or edit
                                                fullConfig={selectedConfigs} 
                                                configType={configTypes.filter((type)=>type.id===selectedConfigs.config_type_id)} 
                                            />
                                        </div>
                                    ))}
                                    {!view && leftoverConfigs?.map(config=>(
                                        <div key={`each-config-param${config[0]}`}>
                                            <ConfigParams 
                                                param={config}
                                                view={false}
                                                fullConfig={selectedConfigs}
                                                configType={configTypes.filter((type)=>type.id===selectedConfigs.config_type_id)}
                                            />
                                        </div>
                                    ))}
                                    {!view && <button className="submit-btn" type="submit">Save Configs</button>}
                                </form>
                            }
                            {selectedConfigs && deletedConfigType &&
                                <div>
                                    The config type for this config cannot be found.
                                </div>
                            }
                        </Modal.Body>
                    </Modal>


                    <Modal dialogClassName="config-modal" show={createNewConfig} onHide={handleHide}>
                        <Modal.Header closeButton>
                            Create New Config
                        </Modal.Header>
                        <Modal.Body>
                            <form onSubmit={handleSave}>
                                <p>
                                    <span className="config-label">
                                        Select Config Type
                                    </span>
                                    <span>
                                        <select name="config_type_id" onChange={(e)=>setSelectedConfigs(configTypes.filter((type)=>type.id===+e.target.value))}>
                                            <option disabled selected>Select Config Type</option>
                                            {configTypes.map((type)=>(
                                                <option key={`config-type-dd-${type.id}`} value={type.id}>{type.name}</option>
                                            ))}
                                        </select>
                                    </span>
                                </p>
                                <p>
                                    {selectedConfigs &&
                                        <div>
                                            <p className="label-pair">
                                                <label htmlFor="key">Config Name</label>
                                                <input 
                                                    type="text"
                                                    name='key'
                                                    defaultValue={selectedConfigs?.key || ""}
                                                />        
                                            </p>
                                            <p className="label-pair">
                                                <label htmlFor="is_active">Is Config Active?</label>
                                                <select
                                                    name="is_active"
                                                    defaultValue={selectedConfigs?.is_active || ""}
                                                >
                                                    <option value={1}>Yes</option>
                                                    <option value={0}>No</option>
                                                </select>
                                            </p>
                                            {selectedConfigs[0]?.converted_params?.map((param)=>(
                                                <div key={`each-config-param${param.id}`}>
                                                    <ConfigParams 
                                                        param={param}
                                                        view={false}
                                                        fullConfig={null}
                                                        configType={selectedConfigs}
                                                    />
                                                </div>
                                            ))}
                                        </div>   
                                    }
                                </p>
                                <p>
                                    <button type="submit" className="submit-btn">Save New Config</button>
                                </p>
                            </form>
                        </Modal.Body>
                    </Modal>
                </div>
            </Card>
        </Container>
    )
}
