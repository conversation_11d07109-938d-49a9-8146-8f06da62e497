import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Table, Row, Col } from 'react-bootstrap';

import Item from '../Properties/Property/Item';
import Line from './Line';
import History from './History';
import AI from './AI';
import Restricted from './Restricted';

import { cmsComponentList } from '../../../../../utils/cms';

/*
import SavePublish from './SavePublish';
import ErrorCatcher from '../../../../../components/common/ErrorCatcher';
import Toast from '../../../../../components/Toast';
*/

import * as actions from '../../../../../store/actions';

export const BasicInfo = (props) => {
    const {save} = props;
    const dispatch = useDispatch();
    const cmsSelector = useSelector(state => state.cms);
    const cmsSelectorElements = useSelector(state => state.cmsElements.present);

    const [error, setError] = useState();
    const [success, setSuccess] = useState();

    const extraProps = useMemo(() => {
        let extra_props=[];
        if (cmsSelector?.currentPageProps?.page_type_name){
            const component_props = cmsComponentList().filter(a=>a.component_name===cmsSelector.currentPageProps.page_type_name)?.[0]?.props || [];
            const element = cmsSelectorElements.elements.filter(a=>a && a?.element_id===cmsSelector.currentPageProps.page_type_name)?.[0] || null;
            if (element) {
                component_props.forEach(component => {
                    let _element = element?.properties?.filter(a=>a.name===component.name)?.[0] || null;
                    if (_element) {
                        _element = JSON.parse(JSON.stringify(_element));
                        extra_props.push(_element);
                    }
                });
            }
        }
        return extra_props;
    }, [cmsSelector?.currentPageProps?.page_type_name, cmsSelectorElements.elements]);

    useEffect(() => {
        return () => {
            setError(null);
            setSuccess(null);
        }
    }, []);

    const changeHandler = (e, type) => {
        let _value = e.target.value;
        if (e.target.type==="checkbox" && !e.target.checked) _value = 0;

        const cms_data = JSON.parse(localStorage.getItem(`cms_${props.pageFactor}`));
        if (cms_data){
            cms_data[type] = _value;
            localStorage.setItem(`cms_${props.pageFactor}`, JSON.stringify(cms_data));
            window.dispatchEvent(new Event("storage"));
            let new_props = JSON.parse(JSON.stringify(cmsSelector.currentPageProps));
            new_props[type] = _value;
            dispatch(actions.CMSSetCurrentPageProps(JSON.parse(JSON.stringify({...new_props}))));
        }
    }

    const saveHandler = useCallback((e, value="", id, type=null) => {
        e.preventDefault();
        e.stopPropagation();

        if (!value && e?.target?.value) value=e.target.value;
        if (type==="json" && value) value=JSON.parse(value);
        if (type==="boolean") value=value?0:1;

        if (cmsSelector?.currentPageProps?.page_type_name){
            let element = cmsSelectorElements.elements.filter(a=>a && a?.element_id===cmsSelector.currentPageProps.page_type_name)?.[0] || null;
            if (element) {
                element = JSON.parse(JSON.stringify(element));
                save(e, value, id, type, element);
            }
        }
    },[save, cmsSelectorElements?.elements, cmsSelector?.currentPageProps?.page_type_name]);

    return (
        <>
            <Table>
                <tbody>
                    {Object.keys(cmsSelector?.currentPageProps?.config?.fields || {}).map((key, i) => {
                        switch(key){
                            case "custom_js":
                                return null;
                            default:
                                if (cmsSelector?.currentPageProps?.config?.fields[key].type==="image"){
                                    // we put the image selection thing here
                                    return null;
                                } else {
                                    return <Line 
                                        key={`basic-info-${i}`}
                                        type={cmsSelector?.currentPageProps?.config?.fields[key].type}
                                        name={cmsSelector?.currentPageProps?.config?.fields[key].display_name}
                                        field_name={key}
                                        tooltip={cmsSelector?.currentPageProps?.config?.fields[key].tooltip}
                                        placeholder={cmsSelector?.currentPageProps?.config?.fields[key].tooltip} 
                                        change={(e)=>changeHandler(e,key)}
                                        condition={cmsSelector?.currentPageProps?.config?.fields[key]?.condition || null}
                                    />
                                }
                        }
                    })}
                    
                    {Object.keys(cmsSelector?.currentPageProps?.config?.fields || {}).includes("custom_js") &&
                        <>
                            {/* WE GOT THIS GUY IN THE THEME BUT KEEPING IT HERE FOR NOW
                            <Line 
                                type="select"
                                type_id="7"
                                name="Custom CSS" 
                                field_name="css"
                                tooltip={`Custom CSS file for this page`}
                                change={(e)=>changeHandler(e,"css")}
                            />
                            */}
                            <Line 
                                type="select"
                                type_id="8"
                                name="Custom JS"
                                field_name="js" 
                                tooltip={`Custom JavaScript file for this page`}
                                change={(e)=>changeHandler(e,"js")}
                            />
                        </>
                    }

                    {/*
                    cmsSelector?.currentPageProps?.page_type === 1 && <Restricted {...props} onChange={changeHandler} />
                    */}

                    {extraProps.map((prop, i) => (
                        <Item key={`extra-field-${i}`} {...prop} save={saveHandler} />
                    ))}
                </tbody>
            </Table>
            {/*
            <Row>
                <Col sm={12} style={{textAlign:"center"}}>
                    <AI type={props.page_type}/>
                </Col>
            </Row>
            {/*
            <Row>
                <Col sm={12} style={{textAlign:"center"}}>
                    <SavePublish {...props} error={err=>setError(<ErrorCatcher error={err} />)} success={succ=>setSuccess(<Toast>{typeName} {succ} successfully!</Toast>)}/>
                </Col>
            </Row>
            */}
            <Row>
                <Col sm={12} className="mt-2 p-4" style={{backgroundColor:"#fff"}}>
                    {<History page_type={props.page_type} page_id={cmsSelector.currentPage} pageFactor={props.pageFactor} setLoading={props.setLoading} />}
                </Col>
            </Row>
            {error}
            {success}
        </>
    );
}