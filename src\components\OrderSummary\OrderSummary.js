import React, {useState, useEffect, useCallback, useRef} from 'react';
import { format } from 'date-fns';

import ErrorCatcher from "../../components/common/ErrorCatcher";
import Pos from '../../api/Pos';

export const OrderSummary = ({ order, showItems=false, showStatusHistory=false, hideDate=false,
    idTitle="Order #:", timeTitle="Time:", userTitle="For User:", itemsTitle="Items:", totalTitle="Total:", statusTitle="Status History:" }) => {

    if (!order) return (<></>);

    return (
        <div className="order-preview">
            <span className="title">{idTitle}</span>
            <span className="tabbed-span">
                {order?.id}
            </span>
            <span className="title">{timeTitle}</span>
            <span>
                {!hideDate && format(new Date(order?.updated_at), "M/d/yy " )}
                {format(new Date(order?.updated_at), "h:mm a" )}
            </span>
            <span className="title">{userTitle}</span>
            <span className="tabbed-span">
                {order?.user_fname} {order?.user_lname}
            </span>
            {showItems &&
                <>
                    <span className="title">{itemsTitle}</span>
                    <div className="item-grid">
                        {order?.items.length > 0 ?
                            <>
                                {order?.items.map((item)=>(
                                    <React.Fragment key={`each-item-${item.id}`}>
                                        <span>${(+item.final_price).toFixed(2)}</span>
                                        <span>{item?.product_name.substring(0, 20)} {item?.product_name.length > 20 ? "..." : null}</span>
                                    </React.Fragment>
                                ))}
                            </>
                            :
                            <span className="tabbed-span">
                                No items in this order.
                            </span>
                        }
                    </div>
                </>
            }
            <span className="title">{totalTitle}</span>
            <span className="tabbed-span total-price">
                ${(+order?.total_price).toFixed(2)}
            </span>
            {showStatusHistory && order?.order_status_history &&
                <>
                    <span className="title">{statusTitle}</span>
                    <span className="tabbed-span">
                        {order?.order_status_history.map((status)=>(
                            <div key={`order-status-${status.id}`}>
                                {status.order_status_name}
                                <span className="at">@</span>
                                {!hideDate && format(new Date(status.logged_at), "M/d/yy " )}
                                {format(new Date(status.logged_at), "h:mm a" )}
                            </div>
                        ))}
                    </span>
                </>
            }
        </div>
    )
}