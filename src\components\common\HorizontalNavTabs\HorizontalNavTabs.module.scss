@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes.scss';

.hor-nav-tabs-wrapper{
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .each-tab{
        &.tab{
            min-width: 85px;
            max-width: 200px;
            border-left: 1px  $primary-color solid;
            border-right: 1px $primary-color solid;
            border-top: 2px  $primary-color solid;
            border-bottom: 2px  $primary-color solid;
            border-radius: 5px 5px 0 0;
            text-align: center;
            cursor: pointer;
        }
        &.button{
            @include basic-button;
            background-color: $secondary-color;
        }
    }
    .active{
        background-color: $primary-color !important;
        color: $primary-inverse-color;
    }
    @media (max-width: 550px){
        .each-tab{
            border-left: 2px $primary-color solid;
            border-right:2px $primary-color solid;
            border-radius: $button-border-radius;
            margin: 4px;
        }
    }
}