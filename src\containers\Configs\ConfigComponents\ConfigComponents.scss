@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/themes.scss';

.create-config-wrapper{
    input, select{
        @include basic-input-select;
    }
    select{
        width: 150px;
    }
    label{
        @include basic-label;
        margin: 8px;
    }
    .label-input-pair{
        @include basic-flex-column;
    }
    .each-param-wrapper{
        @include basic-flex-row;
        flex-wrap: wrap;
        padding: 12px;
        margin-bottom: 2rem;
    }
    .config-params{
        box-shadow: 3px 3px 10px -10px $primary-color;
        &:nth-of-type(odd){
            border-radius: 10px;
            border-left: 3px solid $secondary-color;
            border-right: 3px solid $secondary-color;
        }
        &:nth-of-type(even){
            border-radius: 10px;
            border-left: 3px solid $tertiary-color;
            border-right: 3px solid $tertiary-color;
        }    
    }
    .fake-btn{
        display: flex;
        align-self: center;
        padding: 2px 6px;
        background-color: $error-color;
        border-radius: 5px;
        color: $primary-inverse-color;
    }
    .main-config-wrapper{
        @include basic-flex-row;    
        justify-content: space-evenly;
    }
    button{
        @include basic-button;
    }
    .btn-row{
        @include flex-row-space-between;
        margin-top: 1rem;
    }
    .error-p{
        display: flex;
        justify-content: center;
        color: $error-color;
    }
}
.view-config-table-wrapper{
    table{
        width: 100%;
        tr{
            text-align: center;
        }
    }
}
.view-config-wrapper{
    .two-col{
        @include basic-flex-row;
        p:first-child{
            margin-right: 1rem;
            padding-right: .5rem;
            border-right: 1px solid $divider-color;
            font-weight: 600;
        }
        br{
            margin-bottom: 6px;
        }
    }
    border: 1px solid $divider-color;
    padding: 8px;
    border-radius: 10px;
    margin-bottom: 1rem;
}
.edit-config-wrapper{
    input{
        @include basic-input-select;
    }
    h6{
        @include basic-label;
        margin-top: 1rem;
    }
    i{
        margin-left: .25rem;
    }
}