import React, { useCallback } from 'react';
import { Typeahead } from './Typeahead';
import Products from '../../api/Products';

export const ProductTypeTypeahead = (props) => {

    const getProductTypes=useCallback(async(props)=>{
        let resObj ={
            data: null,
            errors: null,
        }
        try{
            let response = await Products.Types.get();
            if(response && response.data) {
                resObj.data = response.data
            }else if (response && response.error) resObj.errors = response.error
        }catch(ex){
            console.error(ex)
            resObj.errors=ex
        }
        return resObj;
    },[]);

    return(
        <Typeahead
            {...props}
            id="product-type-typeahead"
            makeRequest={getProductTypes}
            paginated={false}
            async={false}
        />
    )

}