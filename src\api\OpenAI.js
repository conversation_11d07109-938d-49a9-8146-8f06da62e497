import axios from 'axios';
const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
    apiKey: process.env.REACT_APP_OPENAI_API_KEY,
});

const openai = new OpenAIApi(configuration);

const interact = async(params) => {
    let response={
        text:["..."],
        usage:{},
        error:null
    }

    const section = params?.type?.toLowerCase() || "about us";
    const company = params?.company || "SiteBoss";
    const keywords = params.keywords || "";

    let heading = "";
    let rules = "";
    switch (section){
        case "create_page":
            /*
            heading = `Create a section for a page in JSON format for a company called ${company}.`;
            rules = `${keywords?`- The output should contain the following sections in the body: ${keywords}`:``}.
            - The output should NOT be in HTML but in JSON format.
            - The output JSON should pass JSON LINT validations.
            - Every key / property name of the output JSON should be wrapped ONLY in double quotes (").
            - Every string value of the output JSON should be wrapped ONLY in double quotes (").
            - The output JSON should NOT contain any line breaks or indentation (\\n, \\s, \\t).
            - The output JSON should NOT contain any comments.
            - The output JSON should NOT contain any HTML tags.
            - The output JSON should NOT contain any custom css classes or styles.
            - The output JSON should NOT contain any custom javascript code.
            - The output JSON be similar to this structure:
            {
                "header": {
                    "logo": "https://www.siteboss.ai/logo.png",
                    "menu": [{
                        "name": "Home",
                        "url": "/"
                    }, {
                        "name": "About Us",
                        "url": "/about-us"
                    }, {
                        "name": "Contact Us",
                        "url": "/contact-us"
                    }]
                },
                "body": [{
                    "hero": {
                        "heading": "Welcome to SiteBoss",
                        "subheading": "We build websites that convert visitors into customers.",
                        "image": "https://www.siteboss.ai/hero.png",
                        "button": {
                            "text": "Get Started",
                            "url": "/get-started"
                        }
                    }
                }, {
                    "container": {
                        "row": [{
                            "column": [{
                                "h1": "Our Services"
                            }, {
                                "span": "We build websites that convert visitors into customers."
                            }]
                        }, {
                            "column": [{
                                "parragraph": "We build websites that convert visitors into customers."
                            }, {
                                "image": "https://www.siteboss.ai/services.png"
                            }]
                        }]
                    }
                }, {
                    "container": {
                        "row": [{
                            "column": [{
                                "h1": "Our work"
                            }, {
                                "span": "We build websites that convert visitors into customers."
                            }]
                        }, {
                            "column": [{
                                "parragraph": "We build websites that convert visitors into customers."
                            }]
                        }]
                    }
                }],
                "footer": {
                    "logo": "https://www.siteboss.ai/logo.png",
                    "copyright": "© 2024 SiteBoss. All rights reserved."
                }
            }`;
            */

            heading = `Build an About Us section given a company and description, but expand on the description to make it longer and more interesting. The response should be JSON in the following format:
            {
            "heading":"Welcome to Company",
            "about_us":"Company provides a location and space for kids of all ages to 'play' - to be social, interact with others, and be active"
            }
            Company: ${company}
            Description: ${keywords}`;
            break;
        case "meta_description":
            heading = `Write a meta description for a website. The company's name is ${company}.`;
            rules = `- The output should be SEO friendly and meet Google's guidelines.`;
            break;
        case "meta_keywords":
            heading = `Write meta keywords for a website, separated by commas. The company's name is ${company}.`;
            rules = `- The output should be SEO friendly and meet Google's guidelines.`;
            break;
        case "headline":
        case "heading":
        case "title":
            heading = `Write a headline for a website.`;
            rules = `- The output should be concise and to the point.
            - The output should be no more than 10 words.
            - The output should not be wrapped in quotes or single quotes.`;
            break;
        default:
            heading = `Write a piece of copy text for a ${section} section of a website. The company's name is ${company}.`;
            if (params.allowHTML){
                rules = `- The output may contain the following html tags: p, b, i, span, u, ul, li, ol, quote.
                - The output should NOT contain custom css classes or styles.`;
            }
            break;
    }
    
    let prompt=`${heading}
    ${keywords?`Use these special keywords: ${keywords}.`: ``}
    ${rules? `Rules: ${rules}`: ``}`.trim();

    try {
        const res = await openai.createChatCompletion({
            model: params?.model || "gpt-3.5-turbo",
            messages: [{role: "user", content: prompt}],
            max_tokens: params?.max_tokens || 2000,
            temperature: section==="create_page" ? 0: 0.8,
            top_p: 1,
            frequency_penalty: 1.9,
            presence_penalty: 1.9,
        });

        if (res?.data?.choices?.[0]?.message){
            //const moderated=await moderate(res.data.choices[0].message.content);
            //if (moderated.trim()==="...") response.text=["..."];
            //else {
                let result=res.data.choices[0].message.content.trim();
                
                if (section==="create_page") result=result.replace(/“/g, '"').replace(/”/g, '"').replace(/‘/g, '"').replace(/’/g, '"').replace(/\n/g, '');
                else {
                    // remove double and single quotes from the beginning and end of the text
                    result=result.replace(/^['"]+/g, '').replace(/['"]+$/g, '');
                }
                response.text=result;
            //}
            response.usage=res.data.usage;
        }
    } catch (error) {
        console.log(error.message)
        if (error.response) response.error=error.response;
        else response.error=error.message;
    }

    return response;
}

const moderate = async(text) => {    
    const res = await axios.post(`https://api.openai.com/v1/moderations`,{input: text,model:"text-moderation-latest"},{
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.REACT_APP_OPENAI_API_KEY}`,
        }
    });
    if (res?.data?.results?.[0]){
        if (res.data.results[0].flagged===false) return text;
    } 

    return "...";
}

const OpenAI = {
    interact, moderate
}
   
export default OpenAI;