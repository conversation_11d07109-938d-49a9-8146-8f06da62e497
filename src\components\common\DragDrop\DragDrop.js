import React,{useState, useEffect} from 'react';
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { useHistory, Link } from 'react-router-dom';
import Button from 'react-bootstrap/Button';

import './DragDrop.scss';

const emptyFunc = () => {};

// data should be an array of objects having an id, a sort_order and name to display
// sortBy has an id of key we are sorting on, and desc is sorting descending (not working yet)
// linkOnClick enables each item being a link using the id, if false no link is created
// referer is the link base in format "/event/type"
// showDragHandle = turns the icon on or off
// onSort = the function passed in to be called every time a sort is done so that sort order can be saved to the db
const DragDrop = ({ data, sortBy={id: "sort_order", desc: false}, linkOnClick=false, referer=null, showDragHandle=true, onSort=emptyFunc, disabled=false}) => {

    const [items, setItems] = useState([]);
    const [isDragDisabled, setIsDragDisabled] = useState(false);

	useEffect(() => {
        setItems(data);
        setIsDragDisabled(linkOnClick);
	}, [data,linkOnClick]);

    // a little function to help us with reordering the result
    const reorder = (list, startIndex, endIndex) => {
        const result = Array.from(list);
        const [removed] = result.splice(startIndex, 1);
        result.splice(endIndex, 0, removed);

        return result;
    };

    function onDragEnd(result) {
        if (!result.destination) {
            return;
        }

        if (result.destination.index === result.source.index) {
            return;
        }

        const itemsNew = reorder(
            items,
            result.source.index,
            result.destination.index
        );

        setItems(itemsNew);
        onSort(itemsNew.map(item => item.id)); // sends an array of item ids in new sort order
    };

    const reorderButtonHandler = () => {
        setIsDragDisabled(!isDragDisabled);
    };

    const contentWithLink = (id, content) => {
        if (referer && linkOnClick)
            return (
                <Link to={`${referer}/${id}`}>
                    {content}
                </Link>
            );
        return content;
    };

    return (
        <div className={`dragdrop-container`}>
            <DragDropContext onDragEnd={onDragEnd}>

                <Droppable droppableId="droppable" isDropDisabled={isDragDisabled}>
                {(provided, snapshot) => (
                    <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className={`droppable ${isDragDisabled ? "" : "enabled"} ${snapshot.isDraggingOver ? "draggingOver" : ""}`}
                    >
                    {items.map((item, index) => (
                        <Draggable key={`drag-${item.id}`} draggableId={`drag-${item.id}`} index={index} isDragDisabled={isDragDisabled}>
                            {(provided, snapshot) => 
                            contentWithLink(item.id, (
                                <div
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    className={`draggable ${snapshot.isDragging ? "dragging" : ""}`}
                                >
                                        <div>
                                            {item.name}
                                        </div>
                                        <i className="far fa-grip-lines"></i>
                                </div>
                            ))
                            }
                        </Draggable>
                    ))}
                    {provided.placeholder}
                    </div>
                )}
                </Droppable>
            </DragDropContext>
            {!disabled &&
                <Button variant="primary" size="sm" onClick={reorderButtonHandler} className={`reorder-button ${linkOnClick ? "" : "hidden"}`}>
                    {isDragDisabled ? "Reorder" : "Stop Reorder"}
                </Button>
            }
        </div>
    );

};

export default DragDrop;