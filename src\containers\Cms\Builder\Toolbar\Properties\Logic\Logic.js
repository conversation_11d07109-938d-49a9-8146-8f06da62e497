import React,{useState, useEffect, useCallback, useMemo} from 'react';
import {Table, Button} from 'react-bootstrap';
import {getFormCustomProps, randomUUID} from '../../../../../../utils/cms';

import Variable from './Variable';

export const Logic = (props) => {
    const {save} = props;
    //const [variables, setVariables] = useState([]);
    const [data, setData] = useState([]);
    const [triggerSave, setTriggerSave] = useState(false);

    const variables = useMemo(() => {
        const _getLogicProps = async () => {
            const els = await getFormCustomProps([], props.pageFactor);
            if (els){
                const _vars = els.reduce((acc,el) => {
                    Object.keys(el.properties).forEach(key => {
                        acc.push({value: el.properties[key], text: `${el.properties?.name || el.display_name}.${key}`, id: el.id, variable: key});
                    });
                    return acc;
                },[]);
                return _vars;
                //setVariables(_vars);
            }
        }
        _getLogicProps();
    }, [props.pageFactor]);


    /*
    useEffect(() => {
        const _getLogicProps = async () => {
            const els = await getFormCustomProps([], props.pageFactor);
            if (els){
                const _vars = els.reduce((acc,el) => {
                    Object.keys(el.properties).forEach(key => {
                        acc.push({value: el.properties[key], text: `${el.properties?.name || el.display_name}.${key}`, id: el.id, variable: key});
                    });
                    return acc;
                },[]);
                setVariables(_vars);
            }
        }
        _getLogicProps();

    }, [props.pageFactor]);
    */

    useEffect(() => {
        return () => {
            setData([]);
            //setVariables([]);
        }
    },[]);

    const moveHandler = useCallback((dragIndex, hoverIndex, sort = true) => {
        setData(prev => {
            const updated = [...prev];
            const draggedItem = updated[dragIndex - 1];
            const hoverItem = updated[hoverIndex - 1];

            if (draggedItem && hoverItem) {
                if (sort === true){
                    const [draggedItem] = updated.splice(dragIndex-1, 1);
                    updated.splice(hoverIndex-1, 0, draggedItem);
                } else {
                    // sort if type is different (sorting a group with a variable)
                    if (draggedItem.type !== hoverItem.type) { 
                        const [draggedItem] = updated.splice(dragIndex-1, 1);
                        updated.splice(hoverIndex-1, 0, draggedItem);
                    } else {

                        // if both items are in the same group create a new group and put both items in it
                        if (draggedItem.parent_id === hoverItem.parent_id && draggedItem.type===1 && hoverItem.type===1){
                            const newGroup = {id: randomUUID(), parent_id: null, type: 0, variable: null, index: (prev?.length || 0) + 1, operator: '=', value: '', andor: 'AND'};
                            updated.splice(hoverIndex-1, 0, newGroup);
                            draggedItem.parent_id = newGroup.id;
                            hoverItem.parent_id = newGroup.id;
                        } else {
                            // if both items are in different groups, put the dragged item in the hover item's group
                            draggedItem.parent_id = hoverItem.type===0 ? hoverItem.id : hoverItem.parent_id;
                        }

                        // sort items by group then reindex
                        updated.sort((a,b) => a.parent_id - b.parent_id).forEach((item, i) => {
                            item.index = i+1;
                        });
                    }
                }
            }
            return updated;
        });
    },[]);

    const saveHandler = useCallback((id, value) => {
        setData(prev=>{
            return prev.map(v => {
                if (v.id === id) {
                    v = {...v, ...value};
                    setTriggerSave(true);
                    return v;
                }
                return v;
            }
        )});
    },[]);

    useEffect(() => {
        if (triggerSave){
            const value = {
                condition: [...data],
                onTrue: "show",
                onFalse: "hide"
            }
            save({
                preventDefault() {},
                stopPropagation() {},
                target: { value: value },
            }, value, props.id);
            setTriggerSave(false);
        }
    }, [triggerSave, data, save, props.id]);


    const deleteHandler = (e,id) => {
        e.preventDefault();
        setData(prev=>prev.filter(v => v.id !== id));
    }
    
    const addConditionHandler = (e) => {
        e.preventDefault();
        // type: 0 = group, 1 = variable
        setData(prev=>[...prev,{id: randomUUID(), parent_id: null, type: 1, variable: null, index: (prev?.length || 0) + 1, operator: '=', value: '', andor: 'AND'}]);
    }

    const _groupByParentId = useCallback((items, parentId = null) => {
        const filteredItems = items.filter(item => item.parent_id === parentId);
        const groupedItems = filteredItems.map((item, i) => {
            const children = _groupByParentId(items, item.id);
            if (children.length) {
                return {...item, children};
            }
            return {...item};
        });
        return groupedItems;
    },[]);

    // recursive function to render groups and variables inside groups
    const _renderGroup = (items) => {
        const _render = (items) => {
            return items.map((item, i) => (
                <Variable key={`data-var-${item.id}`} id={item.id} index={item.index} variables={variables} selected={item} move={moveHandler} update={saveHandler} delete={deleteHandler}>
                    {item?.children && _render(item.children)}
                </Variable>
            ));
        }

        if (!items || !items.length) return null;

        return _render(_groupByParentId([...items]));
    }

    useEffect(() => {
        if (props?.data){
            //console.log(props.data);
            setData(props.data);
        } 
    },[props?.data]);
    
      
    if (!props?.currentElement) return null;

    return (
        <Table>
            <tbody>
                <tr>
                    <td colSpan="2" >
                        <p className="my-3">This element is only visible if the following conditions are met:</p>
                        <div className={`logic-bin`}>
                            {_renderGroup(data)}
                            <Button variant="primary" size="sm" className="d-block mx-auto" onClick={addConditionHandler}>Add Condition</Button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </Table>
    );
}