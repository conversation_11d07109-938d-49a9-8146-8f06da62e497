/*eslint-disable*/
let devQaUserName = Cypress.env('impact_admin_user');
let password = Cypress.env('login_password');
let eventId;
let dateTime = "Sat 05/17/2025 02:00 PM - 02:14 PM";
let where = "Basketball Court 4";
let fee = "$10.00";
let baseUrl = "https://portal-qa.impactathleticsny.com/p/"
let local;

describe("It will make sure everything on the event registration page will load and function properly", {scrollBehavior: "center", testIsolation: false}, ()=>{

    before("It will not visit the login page but insert a link to a direct event", ()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.session([devQaUserName, password], ()=>{
            cy.visit(`${baseUrl}events`);
            cy.wait(1000);
            cy.get('[data-cy="login-username"]').type(devQaUserName);
            cy.get('[data-cy="login-password"]').type(password);
            cy.get('[data-cy="login-submit"]').click();
        })
        cy.wait('@getTheme')
        cy.wait('@getConfig')    
        
    })//end before
    
    beforeEach(()=>{
        cy.viewport(1920, 1080);
    }); 

    it("will find the event id of the proper event",()=>{
        cy.get('#searchTerm')
            .type("Sky Flyers")
        cy.get('tbody > :nth-child(1) > :nth-child(2)')
            .contains("Sky Flyers").parent().within(()=>{
                cy.get(':nth-child(1)')
                    .invoke('text')
                    .then((text)=>{
                        eventId = text;
                    })
            })
    })

    it("will have all the details of a select event",()=>{
        cy.visit(`${baseUrl}event-register?event=${eventId}`);
        //we're getting it in this step because the stored item is slightly different when redirecting from another link
        cy.intercept('POST', '/api/event/type').as('getTypes')
        cy.intercept('GET', `/api/event/${eventId}?event=${eventId}`).as('getSkyFlyers')
        cy.intercept('POST', 'api/product').as('getPrice')
        cy.intercept('GET', '/api/user/user/**').as('getUser')
        cy.wait('@getSkyFlyers');
        cy.wait('@getTypes');
        cy.wait('@getPrice');

        cy.url().should('include', `event-register?event=${eventId}`)
        cy.get('[data-cy="register-event-name"]')
            .invoke('text')
            .should('include', "Sky Flyers")
        cy.get('[data-cy="event-register-top-card"]').within(()=>{
            cy.get('[data-cy="event-when"]')
                .invoke('text')
                .should('include', `When:${dateTime}`)
            cy.get('[data-cy="event-where"]')
                .invoke('text')
                .should('include', `Where:${where}`)
            cy.get('[data-cy="event-fee"]')
                .invoke('text')
                .should('include', `Event Fee:${fee}`)
        })
    }) //end it will have all details of a select event

    it("Will have the user details for the event",()=>{
        local = localStorage.getItem('user');
        cy.log(local)
        cy.get('[data-cy="register-event-lower-card"]')
            .children()
            .get('[data-cy="register-event-title"]')
                .invoke('text')
                .should('include', 'Register for this Event')
            .get('[data-cy="register-event-lower-card"]')
                .invoke('text')
                .should('include', 'Participant')
        cy.get('[data-cy="register-event-participant"]')
            .invoke('text')
            .should('include', (JSON.parse(local)).profile.first_name + " " + JSON.parse(local).profile.last_name + " (Me)")
        cy.window()
            .its('store')
            .invoke('getState')
            .its('family')
            .its('all_family_no_dupes')
            .then(family=>{
                cy.wrap(family).as('family')
            })
        cy.get('@family').then(family=>{
            if(family.length>0){
                cy.get('select')
                    .children()
                    .its('length').then((length)=>{
                        cy.wrap(length).as('childrenLength');
                    });
                cy.get("@family").then(family=>{
                    cy.get('@childrenLength').then(children=>{
                        expect(family.length + 1).to.equal(children)
                    });
                });
            }
        })
    }) //end it will have the user details for the event

    it("Will make sure the event is clickable and won't error",()=>{
        cy.get('button').contains('Register').click()
        cy.get('.fade > div > h1').invoke('text').should('include', "Success")
        cy.get('button').contains('OK').click()

    })//end it will make sure the event is clickable and won't error

    it("Will check that an error from the backend displays properly",()=>{
        cy.intercept('POST', `/api/event`, {fixture: 'Events/error_event.json'}).as('getError')
        cy.visit(`${baseUrl}event-register?event=${eventId}`)
        cy.wait('@getError')
        cy.get('.error').invoke('text').should('contain', "I'm a super secret error from the backend!")
    })
})