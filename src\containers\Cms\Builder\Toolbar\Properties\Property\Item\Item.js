import React, { useMemo, useRef } from 'react';
import { Tooltip, OverlayTrigger } from 'react-bootstrap';

import Element from './Element';
import Extras from './Element/Extras';

import { toNormalCase } from '../../../../../../../utils/cms';

export const Item = (props) => {
    const ref=useRef();

    const _display_text = useMemo(() => {
        let text = props.display_name || toNormalCase(props.name);
        if (props?.icon) text = <><i className={`${props.icon} m-2`} />{text}</>;
        return text;
    },[props?.icon, props?.name, props?.display_name]);
    
    const updateHandler = (e, values) => {
        console.log("updateHandler", values);
    }

    if (props?.hidden) return null;
    if (props?.type==="custom" || props?.type==="wysiwyg" || props?.addItems === true){
        return (
            <>
                <tr className="property-group">
                    <OverlayTrigger placement="bottom" overlay={<Tooltip>{props.description}</Tooltip>}>
                        <th colSpan="2">{_display_text}</th>
                    </OverlayTrigger>
                </tr>
                <tr>
                    <td colSpan="2">
                        <Element {...props} ref={ref}/>
                        <Extras {...props} elementRef={ref} />
                    </td>
                </tr>
            </>
        );
    }

    return (
        <tr>
            <td>
                {(props?.type==="array" || props?.type==="json") && 
                    <OverlayTrigger placement="bottom" overlay={<Tooltip>{props.description+"\nClick to expand"}</Tooltip>}>
                        <a href="#!" onClick={(e)=>props?.click(e,props.name)}>{_display_text}<br/><i className="far fa-expand"/><span>Expand</span></a>
                    </OverlayTrigger>
                }
                {(!(props?.type==="array" || props?.type==="json")) && 
                    <OverlayTrigger placement="bottom" overlay={<Tooltip>{props.description}</Tooltip>}>
                        <span>{_display_text}</span>
                    </OverlayTrigger>
                }
            </td>
            <td>
                <Element {...props} ref={ref} onUpdate={updateHandler} />
                <Extras {...props} elementRef={ref} onUpdate={updateHandler} />
            </td>
        </tr>
    );
}