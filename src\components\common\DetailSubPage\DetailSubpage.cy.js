/*eslint-disable*/
/// <reference types="cypress" />
import store from '../../../redux-store'
import React from 'react';
import { DetailSubpage } from './DetailSubpage';
import { AUTH_SET } from '../../../store/actions/actionTypes';

describe('DetailSubpage Component', {scrollBehavior: "center"}, () => {
    const mockTabs = [
        {
            id: 1,
            displayName: "Profile",
            hash: "profile",
            moduleId: "user.profile",
            component: <div data-cy="profile-content">Profile Content</div>,
            icon: "fas fa-user"
        },
        {
            id: 2,
            displayName: "Settings",
            hash: "settings",
            moduleId: "user.settings",
            component: <div data-cy="settings-content">Settings Content</div>,
            icon: "fas fa-cog"
        },
        {
            id: 3,
            displayName: "External",
            hash: "external",
            moduleId: null,
            link: "/users/:id/external",
            icon: "fas fa-external-link"
        }
    ];

    before(()=>{
        //need to make sure that user is specifically in redux when there are mocked permission calls
        cy.fixture('User/userResponse.json').then((data)=>{
            let localRedux = {user:{profile: data.companyStaffDualRoleUser.data[0]}, logged_in: true}
            cy.get(store.dispatch({type: AUTH_SET, authState: localRedux}))
        });
    })

    beforeEach(() => {
        // Mock the auth function
        cy.intercept(
            'POST', 
            "api/user/module_permission", 
            {data:{"user.settings": true,"user.profile": true}}
        ).as('getPermissions');
        cy.viewport(1920, 1080);
    });

    it('renders loading skeleton when loading prop is true', () => {
        cy.mount(
            <DetailSubpage 
                allTabs={mockTabs}
                loading={true}
                itemId={123}
            />
        );

        cy.get('.react-loading-skeleton').should('exist');
    });

    it('renders tabs with proper permissions', () => {
        cy.mount(
            <DetailSubpage 
                allTabs={[...mockTabs]}
                loading={false}
                itemId={123}
                currentTab={"settings"}
            />
        );
        // cy.wait('@getPermissions')

        // Check if all tabs are rendered
        cy.get('.subpage-subnav').within(() => {
            cy.get('[data-cy="profile"]').should('exist');
            cy.get('[data-cy="settings"]').should('exist');
            cy.get('[data-cy="external"]').should('exist');
        });

        // Verify icons are rendered
        cy.get('.fas.fa-user').should('exist');
        cy.get('.fas.fa-cog').should('exist');
        cy.get('.fas.fa-external-link').should('exist');
        // cy.hash().should('eq', '#profile')
    });

    it('displays correct content when tab is selected', () => {
        cy.mount(
            <DetailSubpage 
                allTabs={mockTabs}
                loading={false}
                itemId={123}
                currentTab="profile"
            />
        );

        // Check if profile content is displayed
        cy.get('[data-cy="profile-content"]').should('be.visible');
        cy.get('.section-title').should('contain', 'Profile');
    });

    it('updates visual state when tab is selected', () => {
        const onTabChange = cy.stub().as('tabChange');
        
        cy.mount(
            <DetailSubpage 
                allTabs={mockTabs}
                loading={false}
                itemId={123}
                currentTab="settings"
                onTabChange={onTabChange}
            />
        );

        cy.get('[data-cy="settings"]')
            .should('have.class', 'active-hash-item');
    });

    it('handles external links correctly', () => {
        cy.mount(
            <DetailSubpage 
                allTabs={mockTabs}
                loading={false}
                itemId={123}
                currentTab="external"
            />
        );

        // Check if the external link is properly formatted
        cy.get('[data-cy="external"]')
            .should('have.attr', 'href')
            .and('include', '/users/123/external');
    });

    it('displays tooltips on hover', () => {
        cy.viewport(350, 600);
        cy.mount(
            <DetailSubpage 
                allTabs={mockTabs}
                loading={false}
                itemId={123}
                currentTab="profile"
            />
        );
        cy.wait('@getPermissions');

        cy.get('[data-cy="profile"] .li-icon')
            .trigger('mouseover', { force: true })
            .trigger('mouseenter', { force: true });  // Try both events
        
        // Add a small wait to allow tooltip to render
        cy.wait(100); // ReactTooltip might need a moment to show
        
        cy.get('[data-cy="profile"] .li-icon')
            .should('have.attr', 'data-tip', 'Profile')
            .should('have.attr', 'data-for', 'Profile-1');
    });

    it('renders first letter when no icon is provided', () => {
        const tabsWithoutIcon = [{
            id: 1,
            displayName: "Test",
            hash: "test",
            moduleId: null,
            component: <div>Test Content</div>
        }];

        cy.mount(
            <DetailSubpage 
                allTabs={tabsWithoutIcon}
                loading={false}
                itemId={123}
                currentTab="test"
            />
        );

        cy.get('.li-icon strong').should('contain', 'T');
    });

    it("doesn't show the tab if the permission doesn't exist", () => {
        // Override the auth stub to deny access
        cy.intercept('POST', "api/user/module_permission", {data:{"user.settings": true,"user.profile": false}}).as('deniedPermission')

        cy.mount(
            <DetailSubpage 
                allTabs={mockTabs}
                loading={false}
                itemId={123}
                currentTab="profile"
            />
        );

        cy.wait('@deniedPermission')

        cy.get('[data-cy="profile"]')
            .should('not.exist');
    });
});