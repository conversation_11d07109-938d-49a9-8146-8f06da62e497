import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { useH<PERSON><PERSON>, <PERSON> } from 'react-router-dom';


/**This requires the following parameters to work:
 * {show, onHide, redirectOneText, redirectOneUrl, redirectTwoText, redirectTwoUrl, keyboard, backdrop, createdWhat}
 * @type {boolean} show - trigger for this modal to show
 * @type {function} onHide - function to fire when the modal is closed
 * @type {string} redirectOneText - descriptive text for the first redirect button and the text saying "would you like to go back to the {____}.toLowerCase()".  Also used as the display text for the button.
 * @type {string} redirectOneUrl - url for the first redirection - done as history.go{_____}
 * @type {string} redirectTwoText - descriptive text for the second redirect button with "or to {____}.toLowerCase()" Also used as the display text for the button
 * @type {string} redirectTwoUrl - url for the second redirection - done as history.go{_____}
 * @type {boolean} keyboard - if you would like the modal to close when the escape key is pressed, true by default
 * @type {boolean || "static"} backdrop - if you would like to include a backdrop, false by default
 * @type {string} modifiedWhat - What was created - "You have {created} a {____} successfully!"
 * @type {string} by default, if left blank says "created".  Will be inserted into a sentence that says "You have {____} a {modifiedWhat} successfully!"
 */
export const RedirectModal = ({
    show, 
    onHide, 
    redirectOneText="", 
    redirectOneUrl, 
    redirectTwoText="", 
    redirectTwoUrl, 
    keyboard=true, 
    backdrop=false,
    modifiedWhat ="",
    typeOfModification="created",
    ...props
}) => {

    const history = useHistory();

    const handleClick=(url)=>{
        history.push(`${url}`);
    }

    return (
        <Modal show={show} onHide={onHide} keyboard={keyboard} backdrop={backdrop}>
            <Modal.Header>Success!</Modal.Header>
            <Modal.Body>
                <div>
                    <p>
                        You have {typeOfModification} {modifiedWhat} successfully! Would you like to go back to the {redirectOneText?.toLowerCase()} or to {redirectTwoText?.toLowerCase()}?
                    </p>
                    <p>
                        <Button onClick={()=>{handleClick(redirectOneUrl)}}>
                            {redirectOneText}
                        </Button>
                        <Button onClick={()=>{handleClick(redirectTwoUrl)}}>
                            {redirectTwoText}
                        </Button>
                    </p>
                </div>
            </Modal.Body>
        </Modal>
    )
}
