import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, ListGroup, Modal, Button, Card } from 'react-bootstrap';
import ReactTooltip from 'react-tooltip';
import { useSelector } from 'react-redux';

import Table from '../../../components/common/Table';
import Events from '../../../api/Events';
import EditUserPropButton from './RoleButton';
import InfoButton from './InfoButton';

//import StatusButton from './StatusButton';
import '../ManageUsers/ManageUsers.scss';
import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';

const ManageUsers = (props) => {
    const [users, setUsers] = useState([]);
    const [eventId, setEventId] = useState();
    const [showModal, setShowModal] = useState(false);
    const [userToRemove, setUserToRemove] = useState();
    const [exporting, setExporting] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);

    const newRole = useSelector(state => state.event.user_event_role);
    const newStatus = useSelector(state => state.event.user_event_status);
    
	useEffect(() => {
        let mounted = true;

        setEventId(props.event_id);

        Events.getDetail({
            id: props.event_id,
            include_users: 1,
        })
        .then(response => {
            if(mounted && response.data.events) {
                setUsers(response.data.events[0].users);
            }
        }).catch(e => console.error(e));

        return () => {
            mounted = false;
        }
	}, [props, newRole, newStatus]);

    const getUsers = useCallback( () => {
        return users;
    },[users])

    const openModal = () => {
        setShowModal(true);
    }

    const closeModal = () => {
        setShowModal(false);
    }

    const removeUserHandler = async (id) => {
        setError(null)
        try{
            let response = await Events.remove_members({event_id: eventId, users: [id]});
            if(response.status === 200){
                setUsers(prevState => prevState.filter(user => user.id !== id));
                setSuccess(setSuccessToast(`User removed from event`));
            }
            else(setError(setErrorCatcher(response.error, false, `Error removing user from event`)))
        }catch(ex){console.error(ex)}
    }

    const columns = React.useMemo(
        () => [{
            id: 'table',
            columns: [
                {
                    Header: 'Name',
                    id: 'name',
                    accessor: d => `${d.first_name} ${d.last_name}`,
                    className: 'align-middle',
                },
                {
                    Header: 'Role',
                    id: 'role',
                    accessor: 'event_role_name',
                    className: 'align-middle',
                },
                {
                    Header: 'Status',
                    id: 'status',
                    accessor: 'user_event_status_name',
                    className: 'align-middle',
                },
                {
                    Header: 'Actions',
                    id: 'controls',
                    accessor: d => "",
                    maxWidth: 10,
                    minWidth: 0,
                    className: 'controls',
                    disableFilters: true,
                    disableSortBy: true,
                    Cell: props =>
                        <ListGroup horizontal>

                            <EditUserPropButton
                                name="Role"
                                user={props.row.original}
                                eventId={eventId}
                                roles={[
                                    { id: 1, name: "Owner" },
                                    { id: 2, name: "Manager" },
                                    { id: 3, name: "Attendee" }
                                ]}
                                btnIcon="far fa-user-tag"
                            />

                            {/*<StatusButton
                                user={props.row.original}
                                statuses={[
                                    { id: 1, name: "Pending" },
                                    { id: 2, name: "Attending" },
                                    { id: 3, name: "Not-Attending" },
                                    { id: 4, name: "Tentative" }
                                ]}
                                btnIcon="far fa-user-clock"
                            />*/}

                            <InfoButton
                                userId={props.row.original.id}
                                eventId={eventId}
                                btnIcon="far fa-info"
                            />

                            <ListGroup.Item className="user-buttons" action href="#RegistrationInfo" onClick={() => {
                                setUserToRemove(props.row.original);
                                openModal();
                            }}>
                                <i className={"far fa-user-times"} data-tip={`Remove from Event`}></i>
                            </ListGroup.Item>
                            
                            <ReactTooltip globalEventOff="click" effect="solid" backgroundColor="#262D33" arrowColor="#262D33" />
                        </ListGroup>,
                },
            ],
    }],[eventId]);

    const exportHandler = async () => {
        setExporting(true);
        Events.export_attendees({id: eventId})
        .then(response => {
            if(response.data) {
                setExporting(false);
                let download = document.createElement('a');
                download.href = response.data[0].uri;
                download.setAttribute('download', '');
                document.body.appendChild(download);
                download.click();
                download.parentNode.removeChild(download);
            } else {
                console.error(response.errors);
                setExporting(false);
            }
        }).catch(e => {
            console.error(e);
            setExporting(false);
        });
    }

    return (
        <>
            <Row>
                {error}
                {success}
                <Col>
                    <Button onClick={exportHandler} className={`${exporting ? " loading":""}`}>Download Attendee List</Button>
                </Col>
            </Row>
            <Row>
                <Col>            
                    <Table className="manage-users" columns={columns} data={getUsers()} />
                </Col>
            </Row>
            <Modal show={showModal} onHide={closeModal} size={"m"} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Remove User From Event</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    Are you sure you want to remove {userToRemove?.first_name + " " + userToRemove?.last_name} from this event?
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeModal}>
                        No
                    </Button>
                    <Button variant="primary" onClick={() => {
                        removeUserHandler(userToRemove.id);
                        closeModal();
                    }}>
                        Yes
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
}

export default ManageUsers;