import React, { useState, useEffect } from 'react';
import { useH<PERSON><PERSON>, Link } from "react-router-dom";
import {Card, Container, Col, Row, Button, Breadcrumb}  from 'react-bootstrap';
import Table from '../../../components/common/Table';
import Stack from '../../../components/common/Stack';
import SubHeader from '../../../components/common/SubHeader';

import './Dashboard.css';

import Companies from '../../../api/Companies';

const Dashboard = (props) => {
    let history = useHistory();

    const newButtonHandler = (event) => {
        history.push("/p/companies/wizard");
    }

    const [loading,setLoading]=useState(true);
    const [companies,setCompanies]=useState([]);
    const adminDash = JSON.parse(localStorage.getItem("adminDash"));

    // first load, get companies from api
    useEffect(() => {

        const _getCompanies = async () => {
            try {
                setLoading(true);
                const res=await Companies.getall();
                if (res.data && mounted) setCompanies(res.data?.map( cmp => cmp));
                setLoading(false);
            } catch (e){
                console.error(e);
            } 
        }

        let mounted = true;

        _getCompanies();

        
        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
            setCompanies([]);
        }        
    },[]);


    const columns = React.useMemo(
        () => [{
            id: 'table',
            columns: [
                {
                    Header: 'Company Name',
                    id: 'name',
                    accessor: 'name',
                    className: "align-middle",
                },
                {
                    Header: 'Owner',
                    id: 'owner_name',
                    accessor: 'owner_name',
                    className: "align-middle",
                },
                {
                    Header: 'Phone',
                    id: 'primary_number',
                    accessor: 'primary_number',
                    className: "align-middle",
                },
                {
                    Header: 'Email',
                    id: 'email',
                    accessor: 'email',
                    className: "align-middle",
                },
                {
                    Header: 'Location',
                    id: 'address_city',
                    className: "align-middle",
                    accessor: d => (
                        <div>
                            {d.address_city}, {d.address_state}. {d.address_postcode}
                        </div>
                    )
                },
                {
                    id: 'id',
                    url:"/companies/:id",
                    show:false,
                },
            ],
        }],[]
    );
        
    return (
        <Container fluid>
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Company Dashboard"},
            ]} />
            <Row className="body">
                <Col>
                    <Card className={`${loading?" loading":""} content-card`}>
                        <Stack direction="horizontal" gap={2}>
                            <h4 className="tm-1 section-title order-2 order-lg-1 ">Company Dashboard</h4>
                            <div className="ms-sm-auto order-1 order-lg-2">
                                <Button variant="primary" onClick={newButtonHandler}>New Company</Button>
                            </div>
                        </Stack>
                        <hr/>
                        <Table columns={columns} data={companies} />
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Dashboard;