import React, { useCallback } from 'react';

import { Typeahead } from '../../../../../components/Typeahead';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import '../../../../../components/Typeahead/Typeahead.scss';

import classes from './classes.json';

/**Basic async typeahead for searching services.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Services back
*/
export const FontAwesomeSelector = (props) => {
    const {selection} = props;

    console.log(classes)

    const makeRequest = useCallback(query => {
        let responseObj = {
            data: classes.filter(c=>c.toLowerCase().includes(query.toLowerCase())) || null,
            errors: null
        }
        return responseObj;
    },[]);

    // each item in responseObj.data is an option
    const formatForLabel = (option) => (
        `<i className='far fa-${option}'/>`
    );

    return (
        <Typeahead
            {...props}
            id="fontawesome-search"
            formatForLabel={formatForLabel}
            makeRequest={makeRequest}
            placeholder={props.placeholder ? props.placeholder : "Enter an icon name..."}
            paginated={true}
        />
    )
}
