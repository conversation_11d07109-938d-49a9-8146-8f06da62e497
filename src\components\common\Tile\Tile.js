import React from 'react';
import Card from 'react-bootstrap/Card';
import Button from 'react-bootstrap/Button';
import Accordion from 'react-bootstrap/Accordion';

import './Tile.css';

const Tile = (props) => {
    
    return (
        <React.Fragment>
            {props.items && props.items.map((item, i) => (
                <Card key={`tile-${i}`}>
                    <Card.Body>
                        <Accordion.Toggle as={Button} variant="link" eventKey={i}>
                            <Card.Title>{item.title}</Card.Title>
                        </Accordion.Toggle>
                        <Accordion.Collapse eventKey={i}>
                            <Card.Text>{item.description}</Card.Text>
                        </Accordion.Collapse>
                    </Card.Body>
                </Card>
            ))}
        </React.Fragment>
    );
}

export default Tile;