{"variables": [{"name": "$fonts", "value": "( 'primary': 'Roboto:400', 'secondary': 'Roboto:400,500,700' )", "mapValue": [{"name": "primary", "value": "Roboto:400", "mapValue": [{"name": "Roboto", "value": "400"}], "compiledValue": ""}, {"name": "secondary", "value": "'Roboto:400", "mapValue": [{"name": "Roboto", "value": "400"}], "compiledValue": ""}], "compiledValue": "(\"primary\": \"Roboto:400\", \"secondary\": \"Roboto:400,500,700\")"}, {"name": "$company-name", "value": "Impact Athletics", "compiledValue": "Impact Athletics"}, {"name": "$background-image", "value": "https://impact-site-images.us-east-1.linodeobjects.com/impact-images/login-bg.png", "compiledValue": ""}, {"name": "$background-image-filter", "value": "none", "compiledValue": "none"}, {"name": "$themeS3Bucket", "value": "https://siteboss.s3.amazonaws.com/themes/3/", "compiledValue": ""}, {"name": "$logoNoText", "value": "$themeS3Bucket + 'logo-short.png'", "compiledValue": "https://siteboss.s3.amazonaws.com/themes/3/logo-short.png"}, {"name": "$logoHeader", "value": "$themeS3Bucket + 'logo.png'", "compiledValue": "https://siteboss.s3.amazonaws.com/themes/3/logo.png"}, {"name": "$logo", "value": "$themeS3Bucket + 'logo-big.png'", "compiledValue": "https://siteboss.s3.amazonaws.com/themes/3/logo-big.png"}, {"name": "$backgroundEvents", "value": "$themeS3Bucket + 'events.png'", "compiledValue": "https://siteboss.s3.amazonaws.com/themes/3/events.png"}, {"name": "$primary-font-family", "value": "'Roboto', sans-serif", "compiledValue": ""}, {"name": "$primary-font-size", "value": "1rem", "compiledValue": "1rem"}, {"name": "$primary-line-height", "value": "1.25rem", "compiledValue": "1.25rem"}, {"name": "$primary-font-color", "value": "#000", "compiledValue": "#000"}, {"name": "$primary-inverse-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$primary-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$secondary-font-family", "value": "'Roboto', sans-serif", "compiledValue": ""}, {"name": "$secondary-font-size", "value": "1.3rem", "compiledValue": "1.3rem"}, {"name": "$secondary-line-height", "value": "1.25rem", "compiledValue": "1.25rem"}, {"name": "$secondary-font-color", "value": "#212121", "compiledValue": "#212121"}, {"name": "$secondary-inverse-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$secondary-font-weight", "value": "600", "compiledValue": "600"}, {"name": "$bold-font-weight", "value": "600", "compiledValue": "600"}, {"name": "$light-font-weight", "value": "300", "compiledValue": "300"}, {"name": "$regular-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$big-font-size", "value": "1.25rem", "compiledValue": "1.25rem"}, {"name": "$big-font-line-height", "value": "1.5rem", "compiledValue": "1.5rem"}, {"name": "$small-font-size", "value": "0.7rem", "compiledValue": "0.7rem"}, {"name": "$small-font-line-height", "value": "0.8rem", "compiledValue": "0.8rem"}, {"name": "$primary-color", "value": "#3e76b6", "compiledValue": "#3e76b6"}, {"name": "$primary-hover-color", "value": "$secondary-color", "compiledValue": "#3b5074"}, {"name": "$primary-light-color", "value": "#C9DCF1", "compiledValue": "#C9DCF1"}, {"name": "$secondary-color", "value": "#3b5074", "compiledValue": "#3b5074"}, {"name": "$secondary-hover-color", "value": "#2f4160", "compiledValue": "#2f4160"}, {"name": "$secondary-light-color", "value": "#adb5c2", "compiledValue": "#adb5c2"}, {"name": "$tertiary-color", "value": "#e66419", "compiledValue": "#e66419"}, {"name": "$tertiary-hover-color", "value": "#af490d", "compiledValue": "#af490d"}, {"name": "$tertiary-light-color", "value": "#e9ae8b", "compiledValue": "#e9ae8b"}, {"name": "$neutral-background-color", "value": "#FFF", "compiledValue": "#FFF"}, {"name": "$neutral-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$neutral-hover-background-color", "value": "$neutral-background-color", "compiledValue": "#FFF"}, {"name": "$neutral-hover-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$neutral-border", "value": "1px solid #afafaf", "compiledValue": "1px solid #afafaf"}, {"name": "$neutral-hover-border", "value": "1px solid $primary-color", "compiledValue": "1px solid #3e76b6"}, {"name": "$shadow-color", "value": "#333", "compiledValue": "#333"}, {"name": "$shadow-elevation-0", "value": "none", "compiledValue": "none"}, {"name": "$shadow-elevation-1", "value": "rgba($shadow-color, .2) 0px 2px 1px -1px, rgba($shadow-color, .14) 0px 1px 1px 0px, rgba($shadow-color, .12) 0px 1px 3px 0px", "compiledValue": ""}, {"name": "$shadow-elevation-2", "value": "rgba($shadow-color, .2) 0px 3px 1px -2px, rgba($shadow-color, .14) 0px 2px 2px 0px, rgba($shadow-color, .12) 0px 1px 5px 0px", "compiledValue": ""}, {"name": "$shadow-elevation-3", "value": "rgba($shadow-color, .2) 0px 3px 3px -2px, rgba($shadow-color, .14) 0px 3px 4px 0px, rgba($shadow-color, .12) 0px 1px 8px 0px", "compiledValue": ""}, {"name": "$error-color", "value": "#b7291f", "compiledValue": "#b7291f"}, {"name": "$error-text-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$success-color", "value": "#4caf50", "compiledValue": "#4caf50"}, {"name": "$warning-color", "value": "#ffe082", "compiledValue": "#ffe082"}, {"name": "$disabled-color", "value": "#bdbdbd", "compiledValue": "#bdbdbd"}, {"name": "$background-color", "value": "#eee", "compiledValue": "#eee"}, {"name": "$divider-color", "value": "#eee", "compiledValue": "#eee"}, {"name": "$divider-border", "value": "1px solid $divider-color", "compiledValue": "1px solid #eee"}, {"name": "$divider-margin", "value": "0.5rem 0 1rem 0", "compiledValue": "0.5rem 0 1rem 0"}, {"name": "$scrollbar-color", "value": "#9e9e9e", "compiledValue": "#9e9e9e"}, {"name": "$scrollbar-background-color", "value": "#e5e5e5", "compiledValue": "#e5e5e5"}, {"name": "$scrollbar-width", "value": "10px", "compiledValue": "10px"}, {"name": "$scrollbar-border-radius", "value": "0px", "compiledValue": "0px"}, {"name": "$main-padding", "value": "2rem", "compiledValue": "2rem"}, {"name": "$content-font-size", "value": "1rem", "compiledValue": "1rem"}, {"name": "$content-padding", "value": "0", "compiledValue": "0"}, {"name": "$small-main-padding", "value": "1rem", "compiledValue": "1rem"}, {"name": "$small-content-padding", "value": "0 $small-main-padding $small-main-padding $small-main-padding", "compiledValue": "0 1rem 1rem 1rem"}, {"name": "$header-background-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$header-font-color", "value": "#000", "compiledValue": "#000"}, {"name": "$header-hover-color", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$header-font-size", "value": ".85rem", "compiledValue": "0.85rem"}, {"name": "$header-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$header-font-family", "value": "$primary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$header-height", "value": "3rem", "compiledValue": "3rem"}, {"name": "$header-padding", "value": "0 1rem", "compiledValue": "0 1rem"}, {"name": "$header-text-decoration", "value": "none", "compiledValue": "none"}, {"name": "$logo-height", "value": "40px", "compiledValue": "40px"}, {"name": "$logo-width", "value": "180px", "compiledValue": "180px"}, {"name": "$logo-url", "value": "url($logo)", "compiledValue": "url(\"https://siteboss.s3.amazonaws.com/themes/3/logo.png\")"}, {"name": "$logo-background-position", "value": "center center", "compiledValue": "center center"}, {"name": "$logo-background-repeat", "value": "no-repeat", "compiledValue": "no-repeat"}, {"name": "$logo-background-size", "value": "contain", "compiledValue": "contain"}, {"name": "$logo-filter", "value": "none", "compiledValue": "none"}, {"name": "$link-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$link-visited-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$link-hover-color", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$link-active-color", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$link-font-size", "value": "1rem", "compiledValue": "1rem"}, {"name": "$link-font-weight", "value": "500", "compiledValue": "500"}, {"name": "$link-text-decoration", "value": "none", "compiledValue": "none"}, {"name": "$link-hover-text-decoration", "value": "underline", "compiledValue": "underline"}, {"name": "$link-padding", "value": "0", "compiledValue": "0"}, {"name": "$link-margin", "value": "0", "compiledValue": "0"}, {"name": "$button-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$button-font-size", "value": "0.9rem", "compiledValue": "0.9rem"}, {"name": "$button-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$button-font-weight", "value": "500", "compiledValue": "500"}, {"name": "$button-border-radius", "value": "8px", "compiledValue": "8px"}, {"name": "$button-border", "value": "0px", "compiledValue": "0px"}, {"name": "$button-border-color", "value": "unset", "compiledValue": "unset"}, {"name": "$button-padding", "value": ".5rem 1.5rem", "compiledValue": "0.5rem 1.5rem"}, {"name": "$button-margin", "value": "0 0.5rem 0.5rem 0", "compiledValue": "0 0.5rem 0.5rem 0"}, {"name": "$button-background-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$button-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$button-shadow", "value": "none", "compiledValue": "none"}, {"name": "$button-hover-background-color", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$button-hover-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$button-hover-border", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$button-active-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$button-active-filter", "value": "brightness(0.9)", "compiledValue": "brightness(0.9)"}, {"name": "$button-disabled-background-color", "value": "$disabled-color", "compiledValue": "#bdbdbd"}, {"name": "$button-disabled-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$button-border-width", "value": "2px", "compiledValue": "2px"}, {"name": "$button-text-transform", "value": "uppercase", "compiledValue": "uppercase"}, {"name": "$button-text-shadow", "value": "none", "compiledValue": "none"}, {"name": "$button-transparent", "value": "rgba($background-color, 0.9)", "compiledValue": "rgba(238, 238, 238, 0.9)"}, {"name": "$button-small-font-size", "value": "0.75rem", "compiledValue": "0.75rem"}, {"name": "$button-small-padding", "value": ".25rem .75rem", "compiledValue": "0.25rem 0.75rem"}, {"name": "$button-round-border-radius", "value": "50%", "compiledValue": "50%"}, {"name": "$button-round-padding", "value": "0.5rem", "compiledValue": "0.5rem"}, {"name": "$button-transparent-background-color", "value": "rgba($header-background-color, 0.9)", "compiledValue": "rgba(255, 255, 255, 0.9)"}, {"name": "$button-transparent-color", "value": "$header-font-color", "compiledValue": "#000"}, {"name": "$button-transparent-hover-color", "value": "$header-hover-color", "compiledValue": "#0a58ca"}, {"name": "$button-icon-margin", "value": "10px", "compiledValue": "10px"}, {"name": "$highlight-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$highlight-hover-color", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$highlight-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$highlight-background-hover-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$highlight-font-weight", "value": "500", "compiledValue": "500"}, {"name": "$breadcrumb-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$breadcrumb-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$breadcrumb-default-color", "value": "#000", "compiledValue": "#000"}, {"name": "$breadcrumb-font-size", "value": ".85rem", "compiledValue": "0.9rem"}, {"name": "$breadcrumb-text-decoration", "value": "none", "compiledValue": "none"}, {"name": "$breadcrumb-hover-color", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$breadcrumb-hover-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$breadcrumb-padding", "value": "1rem 2rem", "compiledValue": "1rem 2rem"}, {"name": "$breadcrumb-margin", "value": "0", "compiledValue": "0"}, {"name": "$breadcrumb-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$chip-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$chip-background-color", "value": "#f5f5f5", "compiledValue": "#f5f5f5"}, {"name": "$chip-padding", "value": "0.3rem 0.5rem", "compiledValue": "0.3rem 0.5rem"}, {"name": "$chip-margin", "value": "0 .5rem .5rem 0", "compiledValue": "0 0.5rem 0.5rem 0"}, {"name": "$chip-font-size", "value": "0.8rem", "compiledValue": "0.8rem"}, {"name": "$chip-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$chip-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$chip-border-radius", "value": "1rem", "compiledValue": "1rem"}, {"name": "$chip-border", "value": "0", "compiledValue": "0"}, {"name": "$chip-color", "value": "#000", "compiledValue": "#000"}, {"name": "$chip-hover-background-color", "value": "#eee", "compiledValue": "#eee"}, {"name": "$chip-hover-color", "value": "#000", "compiledValue": "#000"}, {"name": "$badge-font-family", "value": "$primary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$badge-background-color", "value": "$header-background-color", "compiledValue": "#FFF"}, {"name": "$badge-color", "value": "$header-font-color", "compiledValue": "#000"}, {"name": "$badge-padding", "value": ".3rem .35rem", "compiledValue": "0.3rem 0.35rem"}, {"name": "$badge-margin", "value": "0 .3rem 0 0", "compiledValue": "0 0.3rem 0 0"}, {"name": "$badge-font-size", "value": "1rem", "compiledValue": "1rem"}, {"name": "$badge-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$badge-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$badge-border-radius", "value": "50%", "compiledValue": "50%"}, {"name": "$badge-border", "value": "1px solid #000", "compiledValue": "1px solid #000"}, {"name": "$badge-border-flash-color", "value": "#3e76b6", "compiledValue": "#3e76b6"}, {"name": "$badge-hover-background-color", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$badge-hover-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$badge-size", "value": "1.4rem", "compiledValue": "1.4rem"}, {"name": "$shopping-cart-icon-size", "value": "1.4rem", "compiledValue": "1.4rem"}, {"name": "$modal-border", "value": "0", "compiledValue": "0"}, {"name": "$modal-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$modal-header-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$modal-header-font-size", "value": "1.4rem", "compiledValue": "1.4rem"}, {"name": "$modal-header-border", "value": "0", "compiledValue": "0"}, {"name": "$modal-header-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$modal-header-padding", "value": "0", "compiledValue": "0"}, {"name": "$modal-header-margin", "value": "1rem 1rem 0 1rem", "compiledValue": "1rem 1rem 0 1rem"}, {"name": "$modal-header-content", "value": "", "compiledValue": ""}, {"name": "$modal-body-font-family", "value": "$primary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$modal-body-font-size", "value": "0.985rem", "compiledValue": "0.95rem"}, {"name": "$modal-body-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$modal-body-shadow", "value": "$shadow-elevation-1", "compiledValue": "rgba(0, 0, 0, 0.2) 0px 2px 1px -1px, rgba(0, 0, 0, 0.14) 0px 1px 1px 0px, rgba(0, 0, 0, 0.12) 0px 1px 3px 0px"}, {"name": "$modal-body-border", "value": "0", "compiledValue": "0"}, {"name": "$modal-body-padding", "value": "1rem $main-padding 1rem $main-padding", "compiledValue": "1rem 2rem 1rem 2rem"}, {"name": "$modal-body-background-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$modal-body-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$modal-background-color", "value": "#FFF", "compiledValue": "#FFF"}, {"name": "$modal-close-button-border", "value": "0", "compiledValue": "0"}, {"name": "$modal-close-button-border-radius", "value": "$button-round-border-radius", "compiledValue": "50%"}, {"name": "$modal-close-button-background-color", "value": "#cacaca", "compiledValue": "#FFF"}, {"name": "$modal-close-button-color", "value": "#000", "compiledValue": "#333"}, {"name": "$modal-close-button-padding", "value": "$button-round-padding", "compiledValue": "0.5rem"}, {"name": "$modal-close-button-margin", "value": "0 0 0 auto", "compiledValue": "0 0 0 auto"}, {"name": "$modal-close-button-opacity", "value": "1", "compiledValue": "1"}, {"name": "$modal-close-button-size", "value": "2rem", "compiledValue": "2rem"}, {"name": "$modal-close-button-font-size", "value": "1.9rem", "compiledValue": "1.9rem"}, {"name": "$modal-close-button-shadow", "value": "$shadow-elevation-0", "compiledValue": "none"}, {"name": "$modal-close-button-hover-background-color", "value": "$primary-hover-color", "compiledValue": "#cacaca"}, {"name": "$modal-close-button-hover-color", "value": "#333", "compiledValue": "#333"}, {"name": "$modal-close-button-hover-opacity", "value": "1", "compiledValue": "1"}, {"name": "$profile-image-border-radius", "value": "50%", "compiledValue": "50%"}, {"name": "$profile-image-small-size", "value": "24px", "compiledValue": "24px"}, {"name": "$profile-image-size", "value": "80px", "compiledValue": "80px"}, {"name": "$profile-image-big-size", "value": "120px", "compiledValue": "120px"}, {"name": "$profile-image-huge-size", "value": "325px", "compiledValue": "325px"}, {"name": "$profile-image-padding", "value": "0.5rem", "compiledValue": "0.5rem"}, {"name": "$profile-image-margin", "value": "0 1rem 1rem 0", "compiledValue": "0 1rem 1rem 0"}, {"name": "$card-background-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$card-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$card-border-radius", "value": "5px", "compiledValue": "5px"}, {"name": "$card-border", "value": "0", "compiledValue": "0"}, {"name": "$card-standout-border", "value": "1px solid #afafaf", "compiledValue": "1px solid #afafaf"}, {"name": "$card-padding", "value": "2rem", "compiledValue": "2rem"}, {"name": "$card-margin", "value": "0.25rem 2rem", "compiledValue": "0.25rem 2rem"}, {"name": "$card-font-size", "value": "1rem", "compiledValue": "1rem"}, {"name": "$card-title-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$card-title-font-size", "value": "1.3rem", "compiledValue": "1.3rem"}, {"name": "$card-title-font-weight", "value": "700", "compiledValue": "700"}, {"name": "$card-title-line-height", "value": "2rem", "compiledValue": "2rem"}, {"name": "$card-title-margin", "value": "0", "compiledValue": "0"}, {"name": "$card-title-padding", "value": "0.5rem 0", "compiledValue": "0.5rem 0"}, {"name": "$card-title-color", "value": "#000", "compiledValue": "#000"}, {"name": "$card-subtitle-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$card-subtitle-font-size", "value": "1rem", "compiledValue": "1rem"}, {"name": "$card-subtitle-font-weight", "value": "700", "compiledValue": "700"}, {"name": "$card-subtitle-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$card-subtitle-margin", "value": "0", "compiledValue": "0"}, {"name": "$card-subtitle-padding", "value": "0 0 0.2rem 0", "compiledValue": "0 0 0.2rem 0"}, {"name": "$card-subtitle-color", "value": "#616161", "compiledValue": "#616161"}, {"name": "$card-footer-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$card-footer-border", "value": "0", "compiledValue": "0"}, {"name": "$card-footer-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$card-footer-padding", "value": "default", "compiledValue": "default"}, {"name": "$card-footer-margin", "value": "default", "compiledValue": "default"}, {"name": "$profile-card-background-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$profile-card-padding", "value": "1.25rem", "compiledValue": "1.25rem"}, {"name": "$profile-card-margin", "value": "1rem 0", "compiledValue": "1rem 0"}, {"name": "$profile-card-nav-link-color", "value": "$444", "compiledValue": "#444"}, {"name": "$profile-card-image-width", "value": "100%", "compiledValue": "100%"}, {"name": "$profile-card-image-height", "value": "250px", "compiledValue": "250px"}, {"name": "$profile-card-image-border-radius", "value": "0.625rem", "compiledValue": "0.625rem"}, {"name": "$profile-card-image-border", "value": "1px solid #000", "compiledValue": "1px solid #000"}, {"name": "$profile-card-image-margin", "value": "0 0 0.625rem", "compiledValue": "0 0 0.625rem"}, {"name": "$form-control-label-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$form-control-label-font-size", "value": "0.9rem", "compiledValue": "0.9rem"}, {"name": "$form-control-label-font-weight", "value": "700", "compiledValue": "700"}, {"name": "$form-control-label-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$form-control-label-margin", "value": "0.5rem 0", "compiledValue": "0.5rem 0"}, {"name": "$form-control-label-color", "value": "#757575", "compiledValue": "#757575"}, {"name": "$form-control-font-family", "value": "$primary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$form-control-font-size", "value": "1rem", "compiledValue": "1rem"}, {"name": "$form-control-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$form-control-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$form-control-color", "value": "#000", "compiledValue": "#000"}, {"name": "$form-control-background-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$form-control-border-color", "value": "$divider-color", "compiledValue": "#eee"}, {"name": "$form-control-border", "value": "1px solid $form-control-border-color", "compiledValue": "1px solid #eee"}, {"name": "$form-control-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$form-control-padding", "value": "0.5rem 0.75rem", "compiledValue": "0.5rem 0.75rem"}, {"name": "$form-control-margin", "value": "0 0 1rem 0", "compiledValue": "0 0 1rem 0"}, {"name": "$form-control-placeholder-color", "value": "#757575", "compiledValue": "#757575"}, {"name": "$form-control-placeholder-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$form-control-placeholder-font-size", "value": "1rem", "compiledValue": "1rem"}, {"name": "$form-control-placeholder-line-height", "value": "1.5rem", "compiledValue": "1.5rem"}, {"name": "$form-control-switch-border-radius", "value": "1rem", "compiledValue": "1rem"}, {"name": "$form-control-switch-padding", "value": "0 0 0 1rem", "compiledValue": "0 0 0 1rem"}, {"name": "$dropdown-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$dropdown-background-color", "value": "#fafafa", "compiledValue": "#fafafa"}, {"name": "$dropdown-border", "value": "0", "compiledValue": "0"}, {"name": "$dropdown-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$dropdown-padding", "value": "0", "compiledValue": "0"}, {"name": "$dropdown-margin", "value": "0", "compiledValue": "0"}, {"name": "$dropdown-font-size", "value": "0.85rem", "compiledValue": "0.85rem"}, {"name": "$dropdown-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$dropdown-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$dropdown-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$dropdown-shadow", "value": "$shadow-elevation-1", "compiledValue": "rgba(0, 0, 0, 0.2) 0px 2px 1px -1px, rgba(0, 0, 0, 0.14) 0px 1px 1px 0px, rgba(0, 0, 0, 0.12) 0px 1px 3px 0px"}, {"name": "$dropdown-item-font-family", "value": "$dropdown-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$dropdown-item-background-color", "value": "$dropdown-background-color", "compiledValue": "#fafafa"}, {"name": "$dropdown-item-border", "value": "0", "compiledValue": "0"}, {"name": "$dropdown-item-border-radius", "value": "$dropdown-border-radius", "compiledValue": "0"}, {"name": "$dropdown-item-padding", "value": "0.75rem 1rem", "compiledValue": "0.75rem 1rem"}, {"name": "$dropdown-item-margin", "value": "0", "compiledValue": "0"}, {"name": "$dropdown-item-font-size", "value": "$dropdown-font-size", "compiledValue": "0.85rem"}, {"name": "$dropdown-item-font-weight", "value": "$dropdown-font-weight", "compiledValue": "400"}, {"name": "$dropdown-item-line-height", "value": "$dropdown-line-height", "compiledValue": "1rem"}, {"name": "$dropdown-item-color", "value": "$dropdown-color", "compiledValue": "#000"}, {"name": "$dropdown-item-hover-background-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$dropdown-item-hover-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$dropdown-item-hover-font-weight", "value": "$dropdown-font-weight", "compiledValue": "400"}, {"name": "$dropdown-item-hover-text-decoration", "value": "none", "compiledValue": "none"}, {"name": "$dropdown-item-active-background-color", "value": "$primary-color", "compiledValue": "#3e76b6"}, {"name": "$dropdown-item-active-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$dropdown-item-active-font-weight", "value": "$dropdown-font-weight", "compiledValue": "400"}, {"name": "$dropdown-item-disabled-background-color", "value": "$neutral-background-color", "compiledValue": "#e0e0e0"}, {"name": "$dropdown-item-disabled-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$dropdown-item-disabled-font-weight", "value": "$dropdown-font-weight", "compiledValue": "400"}, {"name": "$table-margin", "value": "0.5rem 0", "compiledValue": "0.5rem 0"}, {"name": "$table-header-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$table-header-font-size", "value": "0.8rem", "compiledValue": "0.8rem"}, {"name": "$table-header-font-weight", "value": "700", "compiledValue": "700"}, {"name": "$table-header-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$table-header-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$table-header-padding", "value": "0.5rem", "compiledValue": "0.5rem"}, {"name": "$table-header-margin", "value": "0", "compiledValue": "0"}, {"name": "$table-header-border", "value": "1px solid $divider-color", "compiledValue": "1px solid #eee"}, {"name": "$table-header-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$table-header-background-color", "value": "#fafafa", "compiledValue": "#fafafa"}, {"name": "$table-header-text-align", "value": "left", "compiledValue": "left"}, {"name": "$table-header-shadow", "value": "$shadow-elevation-0", "compiledValue": "none"}, {"name": "$table-header-text-transform", "value": "uppercase", "compiledValue": "uppercase"}, {"name": "$table-row-font-family", "value": "$primary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$table-row-font-size", "value": "0.85rem", "compiledValue": "1rem"}, {"name": "$table-row-font-weight", "value": "400", "compiledValue": "400"}, {"name": "$table-row-line-height", "value": "1.25rem", "compiledValue": "1.25rem"}, {"name": "$table-row-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$table-row-padding", "value": "0.5rem", "compiledValue": "0.5rem"}, {"name": "$table-row-margin", "value": "0", "compiledValue": "0"}, {"name": "$table-row-border", "value": "0", "compiledValue": "0"}, {"name": "$table-row-border-bottom", "value": "1px solid #fafafa", "compiledValue": "1px solid #fafafa"}, {"name": "$table-row-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$table-row-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$table-row-text-align", "value": "left", "compiledValue": "left"}, {"name": "$table-row-shadow", "value": "$shadow-elevation-0", "compiledValue": "none"}, {"name": "$table-row-text-transform", "value": "default", "compiledValue": "default"}, {"name": "$table-row-background-color-odd", "value": "transparent", "compiledValue": "transparent"}, {"name": "$table-row-hover-background-color", "value": "#fafafa", "compiledValue": "#fafafa"}, {"name": "$table-row-hover-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$pagination-container-margin", "value": "0 0 0 auto", "compiledValue": "0 0 0 auto"}, {"name": "$pagination-container-justify", "value": "flex-end", "compiledValue": "flex-end"}, {"name": "$pagination-height", "value": "2.25rem", "compiledValue": "2.25rem"}, {"name": "$pagination-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$pagination-font-size", "value": "0.7rem", "compiledValue": "0.7rem"}, {"name": "$pagination-font-weight", "value": "700", "compiledValue": "700"}, {"name": "$pagination-line-height", "value": "1rem", "compiledValue": "1rem"}, {"name": "$pagination-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$pagination-padding", "value": "0.5rem 0.75rem", "compiledValue": "0.5rem 0.75rem"}, {"name": "$pagination-margin", "value": "0 0 1rem 0", "compiledValue": "0 0 1rem 0"}, {"name": "$pagination-border", "value": "2px solid transparent", "compiledValue": "2px solid transparent"}, {"name": "$pagination-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$pagination-background-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$pagination-text-align", "value": "center", "compiledValue": "center"}, {"name": "$pagination-shadow", "value": "$shadow-elevation-0", "compiledValue": "none"}, {"name": "$pagination-text-transform", "value": "uppercase", "compiledValue": "uppercase"}, {"name": "$pagination-hover-background-color", "value": "#fafafa", "compiledValue": "#fafafa"}, {"name": "$pagination-hover-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$pagination-hover-text-decoration", "value": "none", "compiledValue": "none"}, {"name": "$pagination-active-background-color", "value": "$pagination-background-color", "compiledValue": "#fff"}, {"name": "$pagination-active-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$pagination-active-border", "value": "2px solid $primary-color", "compiledValue": "2px solid #3e76b6"}, {"name": "$pagination-active-shadow", "value": "$shadow-elevation-1", "compiledValue": "rgba(0, 0, 0, 0.2) 0px 2px 1px -1px, rgba(0, 0, 0, 0.14) 0px 1px 1px 0px, rgba(0, 0, 0, 0.12) 0px 1px 3px 0px"}, {"name": "$pagination-disabled-background-color", "value": "$background-color", "compiledValue": "#eee"}, {"name": "$pagination-disabled-color", "value": "$divider-color", "compiledValue": "#eee"}, {"name": "$pagination-disabled-border", "value": "2px solid transparent", "compiledValue": "2px solid transparent"}, {"name": "$tabs-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$tabs-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$tabs-font-size", "value": "0.85rem", "compiledValue": "0.85rem"}, {"name": "$tabs-font-weight", "value": "500", "compiledValue": "500"}, {"name": "$tabs-line-height", "value": "2rem", "compiledValue": "2rem"}, {"name": "$tabs-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$tabs-border-top", "value": "1px solid transparent", "compiledValue": "1px solid transparent"}, {"name": "$tabs-border-bottom", "value": "1px solid $divider-color", "compiledValue": "1px solid #eee"}, {"name": "$tabs-border-left", "value": "0", "compiledValue": "0"}, {"name": "$tabs-border-right", "value": "0", "compiledValue": "0"}, {"name": "$tabs-padding", "value": "0", "compiledValue": "0"}, {"name": "$tabs-margin", "value": "0 0 1rem 0", "compiledValue": "0 0 1rem 0"}, {"name": "$tab-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$tab-border-top", "value": "0", "compiledValue": "0"}, {"name": "$tab-border-right", "value": "0", "compiledValue": "0"}, {"name": "$tab-border-bottom", "value": "2px solid transparent", "compiledValue": "2px solid transparent"}, {"name": "$tab-border-left", "value": "0", "compiledValue": "0"}, {"name": "$tab-padding", "value": "0", "compiledValue": "0"}, {"name": "$tab-margin", "value": "0 1rem 0 0", "compiledValue": "0 1rem 0 0"}, {"name": "$tab-border-color", "value": "$divider-color", "compiledValue": "#eee"}, {"name": "$tab-active-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$tab-active-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$tab-active-border-color", "value": "$primary-color", "compiledValue": "#4e8bd1"}, {"name": "$tab-active-border-top", "value": "0", "compiledValue": "0"}, {"name": "$tab-active-border-right", "value": "0", "compiledValue": "0"}, {"name": "$tab-active-border-bottom", "value": "2px solid $primary-color", "compiledValue": "2px solid #4e8bd1"}, {"name": "$tab-active-border-left", "value": "0", "compiledValue": "0"}, {"name": "$tab-active-font-weight", "value": "500", "compiledValue": "500"}, {"name": "$tab-hover-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$tab-hover-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$tab-hover-border-color", "value": "$divider-color", "compiledValue": "#eee"}, {"name": "$tab-hover-border-top", "value": "0", "compiledValue": "0"}, {"name": "$tab-hover-border-right", "value": "0", "compiledValue": "0"}, {"name": "$tab-hover-border-bottom", "value": "2px solid $divider-color", "compiledValue": "2px solid #eee"}, {"name": "$tab-hover-border-left", "value": "0", "compiledValue": "0"}, {"name": "$tab-hover-font-weight", "value": "500", "compiledValue": "500"}, {"name": "$menu-font-family", "value": "$secondary-font-family", "compiledValue": "\"Roboto\", sans-serif"}, {"name": "$menu-background-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$menu-width", "value": "250px", "compiledValue": "250px"}, {"name": "$menu-box-shadow", "value": "$shadow-elevation-3", "compiledValue": "rgba(0, 0, 0, 0.2) 0px 3px 3px -2px, rgba(0, 0, 0, 0.14) 0px 3px 4px 0px, rgba(0, 0, 0, 0.12) 0px 1px 8px 0px"}, {"name": "$menu-hover-background-color", "value": "$secondary-color", "compiledValue": "#3b5074"}, {"name": "$menu-hover-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$menu-active-background-color", "value": "#142138", "compiledValue": "#142138"}, {"name": "$menu-active-color", "value": "$primary-inverse-color", "compiledValue": "#fff"}, {"name": "$menu-active-hover-background-color", "value": "$primary-hover-color", "compiledValue": "#3b5074"}, {"name": "$menu-item-background-color", "value": "#b1c8f0", "compiledValue": "#b1c8f0"}, {"name": "$menu-item-color", "value": "$primary-font-color", "compiledValue": "#000"}, {"name": "$menu-item-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$menu-divider-color", "value": "#eee", "compiledValue": "#eee"}, {"name": "$menu-scrollbar-color", "value": "$scrollbar-color", "compiledValue": "#9e9e9e"}, {"name": "$menu-scrollbar-background-color", "value": "$scrollbar-background-color", "compiledValue": "#e5e5e5"}, {"name": "$menu-scrollbar-width", "value": "5px", "compiledValue": "5px"}, {"name": "$menu-scrollbar-border-radius", "value": "$scrollbar-border-radius", "compiledValue": "0px"}, {"name": "$date-picker-day-border-radius", "value": "0", "compiledValue": "0"}, {"name": "$alert-background-color", "value": "#000", "compiledValue": "#000"}, {"name": "$alert-font-color", "value": "#fff", "compiledValue": "#fff"}, {"name": "$pos-menu-width", "value": "300px", "compiledValue": "300px"}, {"name": "$pos-secondary-color", "value": "#757575", "compiledValue": "#757575"}, {"name": "$pos-column-color", "value": "$background-color", "compiledValue": "#eee"}, {"name": "$pos-header-button-margin", "value": "0 5px", "compiledValue": "0 5px"}, {"name": "$pos-header-button-border", "value": "3px solid #d6d6d6", "compiledValue": "3px solid #d6d6d6"}, {"name": "$pos-header-button-hover-border", "value": "3px solid #b5b5b5", "compiledValue": "3px solid #b5b5b5"}, {"name": "$pos-header-background-color", "value": "#212529", "compiledValue": "#212529"}, {"name": "$pos-header-font-color", "value": "#e5e5e5", "compiledValue": "#e5e5e5"}, {"name": "$pos-table-header-font-size", "value": "0.9rem", "compiledValue": "0.9rem"}, {"name": "$pos-table-header-font-weight", "value": "500", "compiledValue": "500"}, {"name": "$pos-table-header-color", "value": "#5c5c5c", "compiledValue": "#5c5c5c"}, {"name": "$pos-table-header-background-color", "value": "transparent", "compiledValue": "transparent"}, {"name": "$pos-table-header-border", "value": "0", "compiledValue": "0"}, {"name": "$cms-drop-ready-background-color", "value": "inherit", "compiledValue": "inherit"}, {"name": "$cms-drop-background-color", "value": "inherit", "compiledValue": "inherit"}, {"name": "$water-mark-filter", "value": "grayscale(100%)", "compiledValue": "grayscale(100%)"}]}