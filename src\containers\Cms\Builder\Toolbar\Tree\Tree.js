import React,{useState, useEffect} from 'react';
import {TreeView, TreeItem }from '@material-ui/lab';

export const Tree = (props) => {
    const [expanded, setExpanded] = useState(['root']);
    const [selected, setSelected] = useState([]);

    useEffect(() => {

        // recursively get all node ids
        const _getIds = (node) => {
            let ids = [`${node.id}`];
            node?.children?.forEach(a => {
                ids = [...ids, ..._getIds(a)];
            });
            return ids;
        }

        if (props.data) {
            const ids = _getIds(props.data);
            setExpanded(ids);
        }
    }, [props.data]);

    useEffect(() => {
        return () => {
            setExpanded([]);
            setSelected([]);
        }
    }, []);
    
    const toggleHandler = (e, nodeIds) => {
      setExpanded(nodeIds);
    };
  
    const selectHandler = (e, nodeIds) => {
      setSelected(nodeIds);
      props.select(nodeIds);
    };    
    const renderTree = (node) => {
        if (node){
            return (
                <TreeItem key={`tree-item-${node.id}-${node.name}`} nodeId={`${node.id}`} label={node.name}>
                    {node?.children?.map(_branch=>renderTree(_branch))}
                </TreeItem>
            );
        }
    }    
    
    return (
        <TreeView className={`treeview`} 
            expanded={expanded} 
            selected={selected}
            onNodeToggle={toggleHandler}
            onNodeSelect={selectHandler}            
            defaultCollapseIcon={<i className="far fa-minus-square" />} 
            defaultExpandIcon={<i className="far fa-plus-square" />}
        >
            {!props.data && <li className="nodata">No element selected.</li>}
            {props.data && renderTree(props.data)}
        </TreeView>
    );
}