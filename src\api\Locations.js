import Request from './Api';

const get = async(props) => {

    // remove this when the tables are mapped on the live server
    if (props?.tables){
        return (
            Request({
                url: "/location" + (!props ? "": "/"+props.id),
                data: {...props}
            })
        );
    }

    return (
        Request({
            url: "/location",
            data: {...props},
            method: "POST"
        })
    );
}


const getStructured = async(props) => {
    let structuredArray = { errors: null, data: [] };
    let unplacedLocations = [];
    let availabilities = getGenericAvailabilities();

    const search = (location, parent) => {
        if(parent.id === location.parent_id) {
            //parent found, add to children and remove from unplacedLocations
            location.general_availabilities = availabilities;
            parent.children = [...parent.children, {...location, children: []}];
            unplacedLocations = unplacedLocations.filter( item => item.id !== location.id);
        } else if(parent.children.length > 1) {
            //no match, search deeper
            parent.children.map( child => search(location, child) );
        }
    }

    await get(props)
    .then(response => {
        let locations = response.data;
        let i = 0;
        unplacedLocations = locations;
        while(i < locations.length && unplacedLocations.length > 0) {
            for(let j = 0; j < [...unplacedLocations].length; j++) {
                let location = [...unplacedLocations][j];
                if( !location.parent_id || location.id === 1 ) {
                    //place top-level locations with no parents
                    //id check is temp fix for seed error: main building should have parent_id null
                    location.general_availabilities = availabilities;
                    structuredArray.data = [...structuredArray.data, {...location, children: []}];
                    unplacedLocations = unplacedLocations.filter( item => item.id !== location.id);
                } else {
                    //has a parent location, need to find
                    structuredArray.data.map( parent => search(location, parent) );
                }
                return null;
            }
            i += 1;
        }
    })
    .catch(e => console.error(e));

    return structuredArray;
}

const getWithAvailabilities = async (props) => {
    // Locations.getEvents({id: props.location, start_datetime: date_start, end_datetime: date_end, include_sublocations: 0})
    let availabilities = getGenericAvailabilities();
    let returnData = { errors: null, data: {} };

    try {
        await get(props)
        .then(response => {
            returnData.data = response.data[0];
            returnData.data.general_availabilities = availabilities;
        });
        await getEvents(props)
        .then(response => {
            returnData.data.events = response.data?.filter( event => //other events at this location
                event.type_id !== 5 //not meta
                && event.status_id !== 3 //not postponed
                && event.status_id !== 4 //not canceled
            );
        });
    } catch (e) {
        console.error(e);
    }

    return returnData;
}

// create location
const create = async (props) => {
    
    return (
        Request({
            url: "/location/create",
            data: props,
            method: "POST"
        })
    );
}

// update location
const update = async (props) => {

    
    return (
        Request({
            url: "/location/edit",
            data: props,
            method: "POST"
        })
    );
}

// Location Types
const Types = {

    // get status
    get: async(props)=>{
        
        return (
            Request({
                url: "/location/type",
                data: {props},
            })
        );
    },
}


// Location Shapes
const Shapes = {

    // ge status
    get: async(props)=>{
        
        return (
            Request({
                url: "/location/shape",
                data: props
            })
        );
    },
}


// Event Types 
const Events = {
    Types: {
        // ge status
        get: async(props)=>{
            
            return (
                Request({
                    url: "/location/event/type",
                    data: {props}
                })
            );
        },
    }
}



const getGenericAvailabilities=()=>{
    //const d=new Date();

    let slots = [];
    let slotsPerHour = 4;

    for(let day = 2; day <= 6; day += 1) { // Monday through Friday
        for(let hour = 6; hour < 22; hour += 1) {  // open 06:00 until 22:00
            for(let slotNum = 1; slotNum <= slotsPerHour; slotNum += 1) {

                let slotLength = 60 / slotsPerHour;
                let timestamp_hour = hour < 10 ? "0" + hour : hour;
                let timestamp_start_minutes = slotLength * ( slotNum - 1 ); //end of previous slot
                if (timestamp_start_minutes < 10) timestamp_start_minutes = "0" + timestamp_start_minutes;
                let timestamp_end_minutes = slotLength * slotNum - 1; //one minute before next slot
                if (timestamp_end_minutes < 10) timestamp_end_minutes = "0" + timestamp_end_minutes;

                slots = [ ...slots, {
                    day_of_week: day,
                    start_time: timestamp_hour + ":" + timestamp_start_minutes,
                    end_time: timestamp_hour + ":" + timestamp_end_minutes
                } ]
            }
        }
    }

    for(let day = 1; day <= 7; day += 6) { // Saturday and Sunday
        for(let hour = 7; hour < 22; hour += 1) {  // open 07:00 until 22:00
            for(let slotNum = 1; slotNum <= slotsPerHour; slotNum += 1) {

                let slotLength = 60 / slotsPerHour;
                let timestamp_hour = hour < 10 ? "0" + hour : hour;
                let timestamp_start_minutes = slotLength * ( slotNum - 1 ); //end of previous slot
                if (timestamp_start_minutes < 10) timestamp_start_minutes = "0" + timestamp_start_minutes;
                let timestamp_end_minutes = slotLength * slotNum - 1; //one minute before next slot
                if (timestamp_end_minutes < 10) timestamp_end_minutes = "0" + timestamp_end_minutes;

                slots = [ ...slots, {
                    day_of_week: day,
                    start_time: timestamp_hour + ":" + timestamp_start_minutes,
                    end_time: timestamp_hour + ":" + timestamp_end_minutes
                } ]
            }
        }
    }

    /*let filled=[];
    let general_availabilities=[];    
    for (let j=0;j<7;j++){
        for (let k=0;k<24;k++){

            for (let l=0;l<4;l++){
                general_availabilities.push(
                    {
                        day_of_week:j,
                        start_time:`${k}:${l*15}`,
                        end_time:`${k}:${l*15+14}`
                    },        
                );
            }

            /*
            let item = slots[Math.floor(Math.random() * slots.length)];
            const ast=item["start_time"].split(":");
            const aet=item["end_time"].split(":");
            if (!filled[ast[0]]){
                availability.push({...item,day_of_week:j});
                for (let i=ast[0];i<=aet[0];i++){
                    filled[i]=i;
                }
            }
        }
    }

    general_availabilities.sort(function(a, b) {
        const time_a=a.start_time.split(":");
        const time_b=b.start_time.split(":");
        const key_a = new Date(d.getDate(),d.getMonth(),d.getFullYear(),time_a[0],time_a[1]);
        const key_b = new Date(d.getDate(),d.getMonth(),d.getFullYear(),time_b[0],time_b[1]);
        if (key_a < key_b) return -1;
        if (key_a > key_b) return 1;
        return 0;
    });*/

    return slots;
}

const getEvents = props => {
    return (
        Request({
            url: "/location/events",
            data: props,
            method: "POST"
        })
    );
}


// Print Location
const printLocation = {
    getNext: props =>{
        return (
            Request({
                url: "/print_location",
                method: "POST",
                data: props,
            })
        );    
    },
    complete: props =>{
        return (
            Request({
                url: "/print_location/complete",
                method: "POST",
                data: props,
            })
        );    
    },
    addToQueue: props =>{
        return (
            Request({
                url: "/print_location/create",
                method: "POST",
                data: props,
            })
        );
    },
    getLocations: props =>{
        return (
            Request({
                url: "/print_location/location",
                method: "POST",
                data: props,
            })
        );
    },
};


const Locations = {
	get, getStructured, getWithAvailabilities, create, update, getEvents, Types, Shapes, Events, printLocation //, delete, etc. ...
}
  
export default Locations;