/*eslint-disable*/

let baseUrl = "https://portal-qa.impactathleticsny.com/p/";
let staffUserName= Cypress.env("impact_staff_user");
let password = Cypress.env("login_password");
let giftCard = Cypress.env('gift_card_qa_one');
// let giftCard = Cypress.env('gift_card_dev_two');

describe("it will check that a transaction can be completed with use of a gift card",{testIsolation: false, scrollBehavior: "center"}, ()=>{
    let orderNumber;
    let local;
    let subtotal = ".97";
    let tax = ".07"
    
    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, staffUserName, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach(()=>{
        cy.viewport(1920, 1080);
    });

    context("it will create the transaction in the POS", ()=>{
        
        it("will visit the POS",()=>{
            cy.visit(`${baseUrl}pos/1`);
            cy.wait(5000);
        })
    
        it("will add items to the cart",()=>{
            cy.get('[data-cy="product-card"]')
                .contains('Snacks')
                .click();
            cy.get('.item-name')
                .invoke('text')
                .should('include', 'Snacks')
            cy.get('[data-cy="preview-item"] > .item-price')
                .invoke('text')
                .should('include', subtotal)
            cy.get('[data-cy="order-number"]')
                .invoke('text').as('orderNumber')
            cy.get('@orderNumber').then((text)=>{
                orderNumber = text
            })
            cy.get('.item-name')
                .children()
                .should('have.length', 1);
        });
    
        it("will checkout with a giftcard in a single transaction", ()=>{
            cy.get('[data-cy="pos-button-checkout"]')
                .click();
            cy.get('[data-cy="payment-type-4"]')
                .click()
            cy.get('[data-cy="gift-card-code"]')
                .should('not.exist')
            cy.get('[data-cy="gift-card-balance"]')
                .should('not.exist');
            cy.get('[data-cy="gift-card-remove-btn"]')
                .should('not.exist');
            cy.get('[data-cy="checkout-totals-payment-0"]')
                .should('not.exist')
            cy.get('#card_code')
                .click()
                .type(giftCard);
            cy.get('.input-group-append')
                .click();
            cy.get('#memo')
                .click()
                .type("Cypress is cooooool")
            cy.get('[data-cy="gift-card-code"]')
                .should('exist')
            cy.get('[data-cy="gift-card-balance"]')
                .should('exist');
            cy.get('[data-cy="gift-card-remove-btn"]')
                .should('exist');
            cy.get('[data-cy="checkout-totals-payment-0"]')
                .should('exist');
            cy.get('[data-cy="details-add-to-cart"]')
                .click()
        });

        it("will make sure there are successful details",()=>{
            cy.get('[data-cy="success-title"]')
                .should('exist');
            cy.get('[data-cy="success-title"]')
                .invoke('text')
                .should('include', "Payment Successful");

            cy.get('.col-lg-8 > :nth-child(5)')
                .invoke('text')
                .should('include', "Receipt");
            cy.get('.col-lg-8 > :nth-child(6)')
                .invoke('text')
                .should('include', "Invoice");
            cy.get('[data-cy="payment-cancel-button"]')
                .click();
            cy.stubPrint();
            cy.wait(1000);
            cy.get('[data-cy="order-number"]')
                .should('not.exist');
        });
    });

    context("it should go to the order page in the POS to check the details", ()=>{

        it("will visit the order page and check the amounts and order number",()=>{
            cy.visit(`${baseUrl}order/${orderNumber}`);
            cy.wait(1000)
            cy.get('.section-title')
                .invoke('text')
                .should('include', orderNumber);
            cy.get('[data-cy="order-subtotal"]')
                .invoke('text')
                .should('include', subtotal);
            cy.get('[data-cy="order-tax-total"]')
                .invoke('text')
                .should('include', tax);
        });

        it("will check the transaction", ()=>{
            cy.get('[data-cy="transaction-histories"]')
                .children()
                .should('have.length', 2); //the collapse of the accordion also counts as a child
            cy.get('.transaction-pair-selectable')
                .click();
            cy.get(':nth-child(1) > .collapse > .selected-transaction')
                .should('be.visible');
            cy.get('[data-cy="transaction-type"]')
                .should('be.visible');
            cy.get('[data-cy="transaction-payment-type"]')
                .invoke('text')
                .should('include', "Gift Card");
        })
    })

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
})