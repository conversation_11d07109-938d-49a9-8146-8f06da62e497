/**
 * @license
 *
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * @fileoverview Blockly React Component.
 * <AUTHOR> (Sam <PERSON>)
 */

 import React from 'react';
 import './BlocklyComponent.css';
 
 import Blockly from 'blockly/core';
 import locale from 'blockly/msg/en';
 import 'blockly/blocks';
 
 Blockly.setLocale(locale);
 
 class BlocklyComponent extends React.Component {
     constructor(props) {
         super(props);
         this.blocklyDiv = React.createRef();
         this.toolbox = React.createRef();
     }
 
     componentDidMount() {
         const { initialXml, children, ...rest } = this.props;
         this.primaryWorkspace = Blockly.inject(
             this.blocklyDiv.current,
             {
                 toolbox: this.toolbox.current,
                 ...rest
             },
         );
 
         if (initialXml) {
             Blockly.Xml.domToWorkspace(Blockly.Xml.textToDom(initialXml), this.primaryWorkspace);
         }
     }
 
     get workspace() {
         return this.primaryWorkspace;
     }
 
     setXml(xml) {
         Blockly.Xml.domToWorkspace(Blockly.Xml.textToDom(xml), this.primaryWorkspace);
     }
 
     render() {
         const { children } = this.props;
 
         return <React.Fragment>
             <div ref={this.blocklyDiv} id="blocklyDiv" />
             <xml xmlns="https://developers.google.com/blockly/xml" is="blockly" style={{ display: 'none' }} ref={this.toolbox}>
                 {children}
             </xml>
         </React.Fragment>;
     }
 }
 
 export default BlocklyComponent;