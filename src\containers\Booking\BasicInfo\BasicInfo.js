import React, { useState, useEffect } from 'react';
import { useSelector,useDispatch } from 'react-redux';
import { Container, Col, Form, Button } from 'react-bootstrap';
import { format, parse } from 'date-fns';

import Toast from '../../../components/Toast';
import * as actions from '../../../store/actions';

import Events from '../../../api/Events';

export const BasicInfo = (props) => {
    const closeHandler = props.closeHandler;
    const dispatch = useDispatch();
    const values = useSelector(state => state.map.selected_items.filter(item=>item.id===props.location.id)?.[0].booking)

    const [event, setEvent] = useState({});
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [success, setSuccess] = useState();
    const [parentId, setParentId] = useState(null);
    const [parentEvents, setParentEvents] = useState([]);
    const [requiresRegistration, setRequiresRegistration] = useState(1);

	useEffect(() => {
        try{
            if (props.event) setEvent(props.event);
            else {
                setEvent(values);
            }
        } catch(error){}

	}, [props.event,values]);

    useEffect(() => {
        if (success && props.modal){
            const timer = setTimeout(() => closeHandler(), 3000);
            return () => clearTimeout(timer);
        }
    }, [success,closeHandler,props.modal]);

    useEffect(() => {
        let mounted = true;

        Events.getSimple()
        .then( response => {
            if(mounted) {
                let events = [];
                response.data?.events.map( event => //add to list of possible parents if occurring at same time
                    (new Date(event.start_datetime) <= props.slot.start_time && new Date(event.end_datetime) >= props.slot.end_time) ? events = [...events, {"id": event.id, "name": event.name}] : null
                );
                setParentEvents(events);
            }
        }).catch(e => console.error(e));

        return () => mounted = false;
    },[props.slot])

    const parentHandler = (e) => {
        setParentId(e.currentTarget.value);
    }

    const toggleRequiresRegistration = () => {
        requiresRegistration === 1 ? setRequiresRegistration(0) : setRequiresRegistration(1);
    }

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            if (props.location.id) formData.append("location_id", props.location.id);
            if (props.slot.start_time) formData.append("start_datetime", props.slot.start_time);
            formData.append("requires_registration", requiresRegistration.toString())
            
            let json_data = {};
            formData.forEach((value, key) => {
                switch(key){
                    case "end_datetime":
                        json_data[key] = format(new Date(value), "yyyy-MM-dd HH:mm:ss");
                        break;
                    case "start_datetime":
                        json_data[key] = format(new Date(value), "yyyy-MM-dd HH:mm:ss");
                        break;
                    default:
                        json_data[key] = value;
                        break;
                }
            });

            dispatch(actions.saveSpot(json_data));

            setSuccess(<Toast>Event details set successfully!</Toast>);
            setSubmitting(false);
        } else setSubmitting(false);
    };


    // get all end times since the start of the date range so we can select multiple slots
    let end_date_el;
    if (props.slots){
        end_date_el=props.slots.map(slots=>{
            return slots.map((slot,y)=>{
                if (props.slot.start_time<=slot.start_time){
                    return (<option key={`eotopy-${y}`} value={format(slot.end_time, "yyyy-MM-dd HH:mm:ss")}>{format(slot.end_time, "hh:mm aa")}</option>);
                } else return null;
            })            
        });
        end_date_el=<Form.Control required custom as="select" name="end_datetime" defaultValue={format(props.slot.end_time, "yyyy-MM-dd HH:mm:ss") || ""}>{end_date_el}</Form.Control>
    } else {
        end_date_el=(
            <p>
                {format(new Date(props.slot.end_time || event.end_datetime), "hh:mm aa")}
            </p>
        );
    }

    return (
        <Container fluid>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Form.Row>
                    <Col sm="12">
                        <Form.Group className="mt-3">
                            <h5 className="title">{props.location && props.location.name}</h5>
                        </Form.Group>
                    </Col>
                    <Col sm="12">
                        <Form.Group>
                            <Form.Label>Start</Form.Label>
                            <p>
                                {format(new Date(props.slot.start_time || event.start_datetime), "eee, MM/dd/yyyy hh:mm aa")}    
                            </p>
                        </Form.Group>
                    </Col>
                    <Col sm="12">
                        <Form.Group>
                            <Form.Label>End</Form.Label>
                            {end_date_el}
                        </Form.Group>
                    </Col>
                </Form.Row>
                <Form.Row>
                    <Col sm="12">
                        <Form.Group controlId="event_name">
                            <Form.Label>Event Name</Form.Label>
                            <Form.Control required type="text" name="event_name" defaultValue={event.event_name || ""} autocomplete="off" />
                        </Form.Group>
                    </Col>
                    <Col sm="12">
                        <Form.Group controlId="event_description">
                            <Form.Label>Description</Form.Label>
                            <Form.Control required as="textarea" name="event_description" defaultValue={event.event_description || ""} />
                        </Form.Group>
                    </Col>
                </Form.Row>
                <Form.Row>
                    <Form.Group controlId="event_parent">
                        <Form.Label>Parent Event</Form.Label>
                        <Form.Text className="text-muted">An event must occur within the duration of its parent. Only valid parents are shown.</Form.Text>
                        <Form.Control required custom as="select" name="parent_id" value={parentId} onChange={parentHandler}>
                            <option key="default_option" value={"0"}>None</option>
                            {parentEvents.map( (parent, i) => 
                                <option key={`option_${i}`} value={parent.id}>{parent.name}</option>
                            )}
                        </Form.Control>
                    </Form.Group>
                </Form.Row>
                <Form.Row>
                <Form.Group controlId="event_requires_registration">
                    <Form.Switch name="requires_registration" checked={requiresRegistration === 1} onChange={toggleRequiresRegistration} label="Requires Registration" />
                </Form.Group>
                </Form.Row>
                <Form.Row>
                    <Col sm="12" lg={props.modal?"8":"4"} className="mt-4 mb-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={`w-${props.modal?"50":"100"}${submitting?" submitting":""}`}>Save</Button>
                        {props.modal &&
                            <Button variant="light" type="button" disabled={submitting} className={`${submitting?" submitting":""}`} onClick={props.closeHandler}>Close</Button>
                        }
                    </Col>
                </Form.Row>
            </Form>
        </Container>
    );
}