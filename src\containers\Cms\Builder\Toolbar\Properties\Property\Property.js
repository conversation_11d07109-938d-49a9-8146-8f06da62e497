import React, {useState, useEffect, useMemo} from 'react';
import {Table, Form} from 'react-bootstrap';
import {default as _cssProperties} from '../../../cssProperties';
import Item from './Item';

export const Property = (props) => {
    const [filteredData, setFilteredData] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(false);

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
    }

    const data = useMemo(() => {
        setLoading(true);
        if (props?.currentElement?.properties){
            let _cssprops = JSON.parse(JSON.stringify(_cssProperties));
            let _elemprops = JSON.parse(JSON.stringify(props.currentElement.properties));
            switch(props.type){
                case 'content':
                    _cssprops = _cssprops.filter(a=>(!a.prop_type || a.prop_type==='value') && a.name!=='logic');
                    _elemprops = _elemprops.filter(a=>(!a.prop_type || a.prop_type==='value') && a.name!=='logic');
                    //return [..._cssprops.filter(a=>(!a.prop_type || a.prop_type==='value') && a.name!=='logic'), ..._elemprops.filter(a=>(!a.prop_type || a.prop_type==='value') && a.name!=='logic')];
                    break;
                case 'basic':
                    _cssprops = _cssprops.filter(a=>a.prop_type && a.prop_type!=='value' && a.prop_type==='basic');
                    _elemprops = _elemprops.filter(a=>a.prop_type && a.prop_type!=='value' && a.prop_type==='basic');
                    //return [..._cssprops.filter(a=>a.prop_type && a.prop_type!=='value' && a.prop_type==='basic'), ..._elemprops.filter(a=>a.prop_type && a.prop_type!=='value' && a.prop_type==='basic')];
                    break;
                case 'advanced':
                default:
                    _cssprops = _cssprops.filter(a=>a.prop_type && a.prop_type!=='value' && a.prop_type!=='basic');
                    _elemprops = _elemprops.filter(a=>a.prop_type && a.prop_type!=='value' && a.prop_type!=='basic');
                    //return [..._cssprops.filter(a=>a.prop_type && a.prop_type!=='value' && a.prop_type!=='basic'), ..._elemprops.filter(a=>a.prop_type && a.prop_type!=='value' && a.prop_type!=='basic')];
                    break;
            }
            
            const elempropsMap = new Map(_elemprops.map(item => [item.id, item]));
            const mergedProps = _cssprops.map(cssProp => {
                if (elempropsMap.has(cssProp.id)) return { ...cssProp, ...elempropsMap.get(cssProp.id) };
                else return cssProp;
            });
            _elemprops.forEach(elemProp => {
                if (!mergedProps.some(prop => prop.id === elemProp.id)) {
                    mergedProps.push(elemProp);
                }
            });
            return mergedProps;
        }
        setLoading(false);
    },[props?.currentElement, props.type]);

    
    useEffect(() => {
        setLoading(true);
        if (!searchTerm) setFilteredData(JSON.parse(JSON.stringify(data)));
        else {
            const _filteredData = [...data].filter(a => a.name.toLowerCase().includes(searchTerm.toLowerCase()) || a.display_name.toLowerCase().includes(searchTerm.toLowerCase()) || (typeof a.value==="string" && a.value.toLowerCase().includes(searchTerm.toLowerCase())));
            setFilteredData(JSON.parse(JSON.stringify(_filteredData)));
        }
        setLoading(false);
    }, [searchTerm, data]);
    

    useEffect(() => {
        return () => {
            setFilteredData([]);
            setSearchTerm('');
            setLoading(false);
        }
    }, []);


    if (!props?.currentElement || loading) return null;

    return (
        <>
        <Form.Control id={`search-${props.type}-${props.currentElement.id}`} type="text" placeholder="Search" value={searchTerm} onChange={handleSearch} />
        <Table>
            <tbody>
                <>
                {props.type==="content" && 
                    <tr>
                        <td>ID</td>
                        <td><small style = {{display: "block", padding: "1rem 0"}}>{props.currentElement.id}</small></td>
                    </tr>
                }
                {filteredData?.map((prop, i) =>{
                    let elems=[];
                    // check any conditions for this property
                    if (prop?.condition){
                        const _cond = data.filter(a=>{
                            if (a.name===prop.condition.name) {
                                switch (prop.condition.value){
                                    case "{not_empty}":
                                        return a.value ? true : false;
                                    case "{empty}":
                                        return a.value ? false : true;
                                    case "{length}":
                                        let _value = a.value;
                                        if (!Array.isArray(_value)) _value = _value.split(',');
                                        if (props.condition?.extra){
                                            switch(props.condition.extra.operator){
                                                case ">":
                                                    return _value.length>props.condition.extra.value;
                                                case "<":
                                                    return _value.length<props.condition.extra.value;
                                                case ">=":
                                                    return _value.length>=props.condition.extra.value;
                                                case "<=":
                                                    return _value.length<=props.condition.extra.value;
                                                case "=":
                                                    return _value.length===props.condition.extra.value;
                                                case "!=":
                                                    return _value.length!==props.condition.extra.value;
                                                default:
                                                    return true;
                                            }
                                        }
                                        return a.value.length>0;
                                    default:
                                        let _cond = prop.condition.value;
                                        if (!Array.isArray(_cond)) _cond = `${_cond}`;
                                        return _cond.includes(a.value) || false;
                                }
                            }
                            return false;
                            //a.name===prop.condition.name && (prop.condition.value==="{not_empty}"?a.value:prop.condition.value.includes(a.value))
                        });
                        if (!_cond || _cond.length===0) return elems;
                    }

                    // add the group name if it exists and its different from the previous group
                    if (prop.group && (!data[i-1] || prop.group !== data[i-1].group)){
                        elems.push(<tr key={`property-group-${props.currentElement}-${i}`} className="property-group"><th colSpan="2">{prop.group}</th></tr>);
                    }
                    // add the property
                    elems.push(<Item key={`property-${props.currentElement}-${i}`} {...prop} currentElementId={props.currentElement.id} selectedItem={props.selectedItem} save={props.save} click={props.click} />);
                    return elems;
                })}
                </>
            </tbody>
        </Table>
        </>
    );
}