import Request from './Api';

const get = async (props) => {
    // gets mock data for testing - will be removed when api is ready
    //let tasks = await test();

    return (
        Request({
            url: "/task/list",
            data: {
                user_id: props?.user_id,
				date_start: props?.date_start,
				date_end: props?.date_end,
            },
            //test: tasks // send mock data to simulate an api call
        })
    );
}


// create task
const create = async (props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Title is required"]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/task/create",
            data: {
                props
            },
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

// update task
const update = async(props) => {

    // sets mock data for testing - will be removed when api is ready
    //let mockdata = {data:null,errors:["Title is required"]};
    //let mockdata = {data:1,errors:null};
    
    return (
        Request({
            url: "/user/update",
            data: {
                props
            },
            //test: mockdata // send mock data to simulate an api call
        })
    );
}

/*
// sets up mock data
const test = async() => {
	
	const getRandomDay = () => {
		return Math.floor(Math.random() * 27) + 1;
	}

    return new Promise((resolve, reject) => {
		const current_month=new Date().getMonth()+1;
		const current_year=new Date().getFullYear();
		
		let tasks=[];

		tasks["everyday"]=[
			{
				id: 1,
				type:1,
				time_start:"07:00:00",
				time_end:"08:00:00",
				title:"Workout",
				description:"I'm baby readymade occupy af tousled, pitchfork ugh lyft food truck poke asymmetrical."
			},
		];

		tasks[`${current_year}/${("0"+current_month).slice(-2)}/15`]=[
			{
				id: 121,
				type:0,
				day:15,
				month:current_month,
				year:current_year,
				time_start:"07:25:00",
				time_end:"12:00:00",
				title:"TEST",
				description:"Next level cliche taxidermy, air plant everyday carry hot chicken palo santo yuccie normcore knausgaard sustainable four dollar toast organic kogi drinking vinegar."
			},
		];	

		let day=getRandomDay();
		tasks[`${current_year}/${("0"+current_month).slice(-2)}/${("0"+day).slice(-2)}`]=[
			{
				id: 11,
				type:0,
				day:day,
				month:current_month,
				year:current_year,
				time_start:"11:25:00",
				time_end:"12:00:00",
				title:"Lunch with family",
				description:"Next level cliche taxidermy, air plant everyday carry hot chicken palo santo yuccie normcore knausgaard sustainable four dollar toast organic kogi drinking vinegar."
			},
			{
				id: 55,
				type:0,
				day:day,
				month:current_month,
				year:current_year,
				time_start:"20:25:44",
				time_end:"20:40:00",
				title:"Read a physics book",
				description:"Paleo godard marfa mixtape butcher venmo ethical slow-carb sustainable taiyaki truffaut jean shorts single-origin coffee fanny pack four dollar toast."
			},
		];

		day=getRandomDay();
		tasks[`${current_year}/${("0"+current_month).slice(-2)}/${("0"+day).slice(-2)}`]=[
			{
				id: 96,
				type:0,
				day:day,
				month:current_month,
				year:current_year,
				time_start:"09:00:00",
				time_end:"20:00:00",
				title:"Work on personal project",
				description:"Man braid cred ramps listicle lyft fixie."
			},
		];

        resolve(tasks);
    });
}/**/

const Tasks = {
	get, create, update //, delete, etc. ...
}
  
export default Tasks;