import React, { useCallback } from 'react'

import { Typeahead } from './Typeahead'
import Products from '../../api/Products';

const ProductVariantTypeahead = ({parameters,variants=false, ...props}) => {

    //in the future, we will likely want a POST for the product variants rather than filtering it here.      
    const makeRequest = useCallback(async (query, perPage, page=1)=>{
        const filters={
            max_records: perPage,
            page_no: page,
            ...parameters
        }
        if(query) filters.search = query
        let response = await Products.get(filters)
        let newList=response.data.products;
        if(response.data && variants) newList = variantsToOptions(response);
        let responseObj = {
            data: newList || null,
            errors: response.errors || null
        }
        return responseObj;
    },[parameters, variants]);

    const formatForLabel = (option) =>(
        `${option?.productName} - ${option?.variantName}`
    )
    
    const variantsToOptions = (response)=>{
        let newList = []
        let products = response.data.products;
        for(let i = 0; i < products.length; i++){
            products[i].product_variants.forEach((variant)=>{
                newList.push({
                    id: variant.id,
                    productName:  products[i].name,
                    variantName: variant.name
                })
            })
        }
        return newList
    }

    return (
        <Typeahead
            {...props}
            async={props.async || false}
            id={"variant-search"}
            formatForLabel={variants ? formatForLabel : (option) => (`${option?.name}`)}
            makeRequest={makeRequest}
            paginated={props.paginated || false}
            placeholder={"Start typing to search..."}
        />
    )
}

export default ProductVariantTypeahead