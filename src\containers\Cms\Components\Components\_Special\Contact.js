import React, {useState, useEffect} from 'react';
import {Row, Col, Form, Button} from 'react-bootstrap';
import ContactForm from './ContactForm';
import Map from '../Map';

const Contact = (props) => {
    // this should be in every component, its used to forward the click event to the builder if in preview mode
    let preview_click=null;
    if (props.is_preview && props.onClick){
        preview_click = props.onClick;
    }   

    return (
        <div className={`contact_${props.page_id} ${props.className || ""}`} style={props?.style || null} onClick={preview_click}>
            <Row className={`row_${props.page_id}`}>
                <Col sm="12" className={`col_${props.page_id}`}>
                    <h1>{props.title}</h1>
                    {props?.subtitle && <p>{props.subtitle}</p>}
                </Col>
            </Row>
            <Row className={`row_${props.page_id}`}>
                <Col sm={12} lg={6} className={`col_${props.page_id}`}>
                    <ContactForm style={props?.style || null} page_id={props.page_id} className={props.className} fields={props.fields} send_to={props.send_to} />
                </Col>
                <Col sm={12} lg={6} className={`col_${props.page_id}`}>
                    {/**/
                    <Map type={props.map_type} lat={props.lat} page_id={props.page_id} lng={props.lng} zoom={props.zoom} api_key={props.api_key} />
                    /**/}
                </Col>
            </Row>
        </div>
    );
}

export default Contact;