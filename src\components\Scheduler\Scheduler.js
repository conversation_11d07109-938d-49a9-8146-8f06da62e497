import React,{ useEffect, useState } from 'react';
import {SchedulerHeader, getWeekNumber} from './Common.js';
import Year from './Year';
import Month from './Month';
import Week from './Week';
import Day from './Day';
import Schedule from './Schedule';

import Tasks from '../../api/Tasks';

export const Scheduler = (props) => {

    // date picker change events
    const [currentDate,setCurrentDate]=useState(props.date || new Date().toLocaleDateString("en-US",{year:"numeric", month:"2-digit", day:"2-digit"}));
    const changeDateHandler = (e) =>{
        setCurrentDate(e.toLocaleDateString("en-US",{year:"numeric", month:"2-digit", day:"2-digit"}));
    }

    // month change events
    const [currentMonth,setCurrentMonth]=useState(props.month || new Date().getMonth()+1);
    const changeMonthHandler = (e) =>{
        setCurrentMonth(e.target.hash.substr(1));
    }

    // week change events
    const [currentWeek,setCurrentWeek]=useState(getWeekNumber(props.date || new Date()).value);
    const changeWeekHandler = (e) =>{
        setCurrentWeek(e.target.hash.substr(1));
    }

    // year change events
    const [currentYear,setCurrentYear]=useState(props.year || new Date().getFullYear());
    const changeYearHandler = (e) =>{
        setCurrentYear(e.target.text);
    }

    // go to today event
    const showTodayHandler = () =>{
        setCurrentDate(new Date().toLocaleDateString("en-US",{year:"numeric", month:"2-digit", day:"2-digit"}));
        setCurrentWeek(getWeekNumber(new Date()).value);
        setCurrentYear(new Date().getFullYear());
        setCurrentMonth(new Date().getMonth()+1);
        setCurrentYear(new Date().getFullYear());
    }

    const [schedulerType,setschedulerType]=useState("schedule"); // type of scheduler to display: day, week, month, year, schedule
    const [body,setBody]=useState(); // calendar body
    const [header,setHeader]=useState(); // calendar header (each type has a different selector)
    //const [tasks,setTasks]=useState(); // tasks

    // change to month view
    const viewMonthHandler = () => {
        setLoading(true);
        setschedulerType("month");
    }

    // change to year view
    const viewYearHandler = () =>{
        setLoading(true);
        setschedulerType("year");
    }

    // change to week view
    const viewWeekHandler = () =>{
        setLoading(true);
        setschedulerType("week");
    }

    // change to day view
    const viewDayHandler = () =>{
        setLoading(true);
        setschedulerType("day");
    }

    // change to scheduler view
    const viewScheduleHandler = () =>{
        setLoading(true);
        setschedulerType("schedule");
    }

    // add new event
    const addNewHandler = () =>{

    }

    // show loading overlay
    const [loading,setLoading]=useState(false);

    
    // first load, get tasks for the month
    useEffect(() => {
        /*setLoading(true);
        Tasks.get().then(response => {
            setTasks(response);
            setLoading(false);
        });

        // cancel stuff when component unmounts
        return () => { 
            setLoading(false);
            setTasks(false);
        }*/
    },[]);


    // when schedule type, year, month change, re-render scheduler
    useEffect(() => {
        let mounted = true;

        setLoading(true);
        switch(schedulerType){
            case "year":
                setHeader(<SchedulerHeader
                    {...props} 
                    
                    todayClick={showTodayHandler} 
                    newClick={addNewHandler} 
                    yearViewClick={schedulerType!=="year" ? viewYearHandler : null }            
                    monthViewClick={schedulerType!=="month" ? viewMonthHandler : null}
                    weekViewClick={schedulerType!=="week" ? viewWeekHandler : null }
                    dayViewClick={schedulerType!=="day" ? viewDayHandler : null }
                    scheduleViewClick={schedulerType!=="schedule" ? viewScheduleHandler : null }

                    currentYear={currentYear}
                    yearClick={changeYearHandler}
                />);

                // fetch tasks for the year
                Tasks.get({
                    date_start:`${currentYear}/01/01`,
                    date_end:`${currentYear}/31/12`
                }).then(response=>{
                    if(mounted) {
                        setBody(<Year {...props} year={currentYear} tasks={response} />);
                        setLoading(false);
                    }
                }).catch(e => console.error(e));
                break;
            case "month":
                setHeader(<SchedulerHeader
                    {...props} 
                    
                    todayClick={showTodayHandler} 
                    newClick={addNewHandler} 
                    yearViewClick={schedulerType!=="year" ? viewYearHandler : null }            
                    monthViewClick={schedulerType!=="month" ? viewMonthHandler : null}
                    weekViewClick={schedulerType!=="week" ? viewWeekHandler : null }
                    dayViewClick={schedulerType!=="day" ? viewDayHandler : null }
                    scheduleViewClick={schedulerType!=="schedule" ? viewScheduleHandler : null }

                    currentYear={currentYear}
                    currentMonth={currentMonth}

                    yearClick={changeYearHandler}
                    monthClick={changeMonthHandler}                        
                />);

                // fetch tasks for the month
                const last_day=("0"+new Date(currentYear,currentMonth,0).getDate()).slice(-2);
                Tasks.get({
                    date_start:`${currentYear}/${currentMonth-1}/01`,
                    date_end:`${currentYear}/${currentMonth}/${last_day}`
                }).then(response=>{
                    if(mounted) {
                        setBody(<Month {...props} year={currentYear} month={currentMonth} tasks={response} />);
                        setLoading(false);
                    }
                }).catch(e => console.error(e));
                break;
            case "week":
                setHeader(<SchedulerHeader
                    {...props} 
                    
                    todayClick={showTodayHandler} 
                    newClick={addNewHandler} 
                    yearViewClick={schedulerType!=="year" ? viewYearHandler : null }            
                    monthViewClick={schedulerType!=="month" ? viewMonthHandler : null}
                    weekViewClick={schedulerType!=="week" ? viewWeekHandler : null }
                    dayViewClick={schedulerType!=="day" ? viewDayHandler : null }
                    scheduleViewClick={schedulerType!=="schedule" ? viewScheduleHandler : null }

                    currentWeek={currentWeek}
                    weekClick={changeWeekHandler}
                />);

                // fetch tasks for the week
                const weekDate = new Date(currentDate);
                const weekStart = new Date(weekDate.setDate(weekDate.getDate() - weekDate.getDay())) // first day of week
                const weekEnd = new Date(weekDate.setDate(weekDate.getDate() - weekDate.getDay() +6)); // last day of week
                Tasks.get({
                    date_start:`${weekStart.getFullYear()}/${weekStart.getMonth()}/${weekStart.getDate()}`,
                    date_end:`${weekEnd.getFullYear()}/${weekEnd.getMonth()}/${weekEnd.getDate()}`
                }).then(response=>{
                    if(mounted) {
                        setBody(<Week {...props} week={currentWeek} tasks={response} />);
                        setLoading(false);
                    }
                }).catch(e => console.error(e));
                break;
            case "day":
                setHeader(<SchedulerHeader
                    {...props} 
                    
                    todayClick={showTodayHandler} 
                    newClick={addNewHandler} 
                    yearViewClick={schedulerType!=="year" ? viewYearHandler : null }            
                    monthViewClick={schedulerType!=="month" ? viewMonthHandler : null}
                    weekViewClick={schedulerType!=="week" ? viewWeekHandler : null }
                    dayViewClick={schedulerType!=="day" ? viewDayHandler : null }
                    scheduleViewClick={schedulerType!=="schedule" ? viewScheduleHandler : null }

                    currentDate={currentDate}
                    dateClick={changeDateHandler}
                />);

                // fetch tasks for the day
                const dayDate = new Date(currentDate);
                Tasks.get({
                    date_start:`${dayDate.getFullYear()}/${dayDate.getMonth()}/${dayDate.getDate()}`,
                    date_end:`${dayDate.getFullYear()}/${dayDate.getMonth()}/${dayDate.getDate()}`
                }).then(response=>{
                    if(mounted) {
                        setBody(<Day {...props} date={dayDate} tasks={response} />);
                        setLoading(false);
                    }
                }).catch(e => console.error(e));
                break;
            case "schedule":
            default:
                setHeader(<SchedulerHeader
                    {...props} 
                    
                    todayClick={showTodayHandler} 
                    newClick={addNewHandler} 
                    yearViewClick={schedulerType!=="year" ? viewYearHandler : null }            
                    monthViewClick={schedulerType!=="month" ? viewMonthHandler : null}
                    weekViewClick={schedulerType!=="week" ? viewWeekHandler : null }
                    dayViewClick={schedulerType!=="day" ? viewDayHandler : null }
                    scheduleViewClick={schedulerType!=="schedule" ? viewScheduleHandler : null }

                    currentDate={currentDate}
                    dateClick={changeDateHandler}
                />);

                // fetch tasks for the day
                const scheduleDate = new Date(currentDate);
                Tasks.get({
                    date_start:`${scheduleDate.getFullYear()}/${scheduleDate.getMonth()}/${scheduleDate.getDate()}`,
                    date_end:`${scheduleDate.getFullYear()}/${scheduleDate.getMonth()}/${scheduleDate.getDate()}`
                }).then(response=>{
                    if(mounted) {
                        setBody(<Schedule {...props} date={scheduleDate} tasks={response} />);
                        setLoading(false);
                    }
                }).catch(e => console.error(e));
                break;
        }
	}, [schedulerType, currentYear, currentMonth, currentWeek, currentDate, props]);    


    // scheduler
    return (
        <div className={`scheduler-container${loading?" scheduler-loading":""}`}>
            {header}
            {body}
        </div>
    );
}