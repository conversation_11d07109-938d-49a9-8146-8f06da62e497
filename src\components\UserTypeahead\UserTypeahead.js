import React, { useState, useEffect, useCallback, useRef } from 'react';
import { AsyncTypeahead, Token } from 'react-bootstrap-typeahead';

import ErrorCatcher from '../common/ErrorCatcher';

import UsersAPI from '../../api/Users';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import './UserTypeahead.scss';

/**Basic async typeahead for searching users.  After a selection is made, prop function will pass the data back up to the parent component.
 * @param {()} props.passUserSelection to pass the selection
 * @param props.role to search by a particular role
 * @param props.userSelection a flat array of user ids to pre-populate the typeahead results with
*/
export const UserTypeahead = (props) => {
    const passUserSelection=props.passUserSelection;

    const multiple = props.multiple || false;

    const ref=useRef(null);
    const [error, setError]=useState();
    const [loading, setLoading]=useState(false);
    const [selectedResult, setSelectedResult] = useState([]);
    const [preselectedUsers, setPreselectedUsers] = useState([]);
    const [users, setUsers]=useState([]);

    const filterBy = () => true;

    const _getUsers = useCallback(async(query, filter) => {
        if (!filter){
            filter={
                user_roles:props.role || null,
                search_words:query || null
            };
        }
        setLoading(true);
        try{
            let response=await UsersAPI.list({
                filters: filter,
                max_records:25,
                page_no:1,
                sort_col:"username",
                sort_direction:"ASC"
            });
            setLoading(false);
            return response;
        }catch(ex){
            console.error(ex)
            setLoading(false);
        }
        return null;
    },[props.role]);


    const searchHandle = useCallback(async(query, filter) => {
        setLoading(true);
        const response = await _getUsers(query, filter);
        if(!response.errors && response.status===200){
            setUsers(response.data.users);
            setLoading(false);
        } 
        else if(response.errors) setError(<ErrorCatcher error={response.errors} />)
        setLoading(false);
    },[_getUsers]);

    const formatForLabel = (option) => (
        `${option?.first_name} ${option?.last_name} (${option?.username})`
    );

    useEffect(()=>{
        if(selectedResult) passUserSelection(selectedResult);
    },[selectedResult,passUserSelection]);

    useEffect(()=>{
        if (props?.userSelection?.length){
            _getUsers(null,{user_ids:props.userSelection}).then(res=>{
                if (!res.errors) setPreselectedUsers(res.data.users);
            });
        }
    },[props.userSelection, _getUsers]);

    useEffect(()=>{
        return () => {
            setUsers([]);
            setSelectedResult([]);
            setPreselectedUsers([]);
        }
    },[]);

    const renderInput = useCallback(({ inputClassName, inputRef, referenceElementRef, ...props },{ onRemove, selected }) => (
        <>
            <input
                {...props}
                className="form-control"
                ref={input => {
                    referenceElementRef(input);
                    inputRef(input);
                }}
                type="text"
            />
            <div style={{ marginTop: '10px' }}>
                {[...selectedResult,...preselectedUsers].map((option, i) => {
                    if (!option) return null;
                    return (
                        <Token key={`tkn-${i}`} onRemove={() => {
                            const selected = [...selectedResult,...preselectedUsers].filter((o) => o.id !== option.id);
                            setPreselectedUsers(selected);
                            setSelectedResult(selected);
                            return onRemove(option);
                        }}>
                            {formatForLabel(option)}
                        </Token>
                )})}
            </div>
        </>
    ),[selectedResult, preselectedUsers]);

    let additionalProps = {};
    if (multiple) additionalProps.renderInput = renderInput

    return (
        <div className="general-typeahead">
            {users &&
                <AsyncTypeahead
                    filterBy={filterBy}
                    id="user_autocomplete"
                    isLoading={loading}
                    minLength={3}
                    onSearch={searchHandle}
                    onChange={item=>{
                        let _v;
                        if (preselectedUsers.length) _v=[...item,...preselectedUsers];
                        else _v=item;
                        setSelectedResult(_v);
                    }}
                    labelKey={formatForLabel}
                    options={users}
                    placeholder={props.placeholder ? props.placeholder : "Enter a user name..."}
                    // selected={selectedResult}
                    ref={ref}
                    multiple={multiple}
                    clearButton={true}
                    renderInput={multiple ? renderInput : null}
                />
            }
            {error}
        </div>
    )
}
