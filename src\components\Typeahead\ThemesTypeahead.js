import React, { useCallback } from 'react';

import { Typeahead } from './Typeahead';
import CMS from '../../api/Cms';

export const ThemesTypeahead = (props) =>{

    const getThemes = useCallback(async()=>{
        let responseObj = {
            data: null,
            errors: null
        }

        try{
            let response = await CMS.themes.get();
            if(response.status === 200 && response.data) responseObj.data = response.data;
            else if(response.errors) responseObj.errors = response.errors;
        }catch(ex){
            console.error(ex)
            responseObj.errors = ex;
        }

        return responseObj;
    },[])

    return(
        <Typeahead
            {...props}
            id="themes-search"
            makeRequest={getThemes}
            placeholder={props.placeholder || "Search Themes"}
            paginated = {false}
        />
    )
}
