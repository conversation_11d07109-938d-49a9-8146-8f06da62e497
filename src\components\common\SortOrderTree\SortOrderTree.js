import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from 'react-bootstrap';
import { UncontrolledTreeEnvironment, StaticTreeDataProvider, Tree } from 'react-complex-tree';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import "react-complex-tree/lib/style-modern.css";

import './SortOrderTree.scss';
import usePrevious from '../CustomHooks';

//tree reference link:
//https://rct.lukasbach.com/docs/guides/uncontrolled-environment

//NOTE: when working with the tree, it doesn't like hot reloads.  It loses the index from the parent, I think? Best to refresh after saves

//used in
//src\containers\Permissions\Features\SortFeatures\SortFeatures.js
//src\containers\MenuItems\MenuItems.js

/**each item in an array that comes in must have a name, a sort_order, and index.  Indexes have to be unique and have to be in order
 * @param {{}} data - a list of data such as [{name: "hi", sort_order: 1, index: 1}, {name: "bye", sort_order: 2, index: 2}]
 * @param {{}} passState - a function to pass the current order up to the parent
 * Will be passed as an array of objects with the id and sort order: [{id: 15, sort_order: 1}, {id: 3, sort_order: 2}, etc, etc]
 * @param {{}} optionalDetails - an array of what optional details will be displayed, since all the data coming in can be different.  
 * For example, you want to display the objects name, sort order, and description when an item is clicked, pass in an object like so:
 * [title: "Stuff stuff Details", requiresActive: true, type: "info", data:{name: "name", label: "Name"}, {name: "sort_order", label: "Original Sort Order"}, {name: "description", label: "Description"}}]
 * If you want to display HTML instead that always shows, pass in an object like this:
 * [title: "Click Stuff", requiresActive: false, type: "html", data: [<Button onClick()=>{do stuff} />, <p>Read me</p>]}
 * by default, this is left as [] and will display nothing if not passed in.
 * @param {{}} reset - a boolean that will reset the tree to its original state
 * @param {{}} setReset - a function to set the reset state to false so it doesn't retrigger
 * @param {{}} clearSelected - to clear the active item
 * @param {{}} setClearSelected - a function to set the clearSelected state to false so it doesn't retrigger
 * @param {{}} passActive - a function to send the active item back up to the parent
 * @param {{}} treeClearBtn - a boolean to show/hide the clear button at the top of the tree
 * @param {{}} treeSettings - an object that can contain optional tree settings.  Default is for basic sort/reorder (no parents, can drag)
 */
export const SortOrderTree = ({
    data, 
    passState=()=>{console.log("No passState function from the parent")}, 
    optionalDetails=[],
    reset=false, 
    setReset,
    clearSelected=false,
    setClearSelected=()=>{console.log("No setClearSelected function passed in from the parent")},
    passActive=()=>{console.log("No passActive function sent in from the parent")},
    treeClearBtn = true,
    treeSettings={
        canDragAndDrop: true,
        canDropOnFolder: false,
        canReorderItems: true,
        canSearchByStartingTyping: true,
        canDropOnNonFolder: false,
        treeId: "sort-features"
    }, 
    ...props
    }) => {
    
    const mountedRef = useRef(false);
    const environment = useRef();
    const tree = useRef();
    const [ loading, setLoading ]=useState(true);
    const [ dataProvider, setDataProvider ]=useState();
    const [ activeItem, setActiveItem ]=useState();
    const oldActiveItem = usePrevious(activeItem);

    const getFocus=()=> environment.current.viewState[treeSettings.treeId].selectedItems = []

    const sortDataToDataProvider=useCallback((localData)=>{
        let tempData = {}
        localData.forEach((item)=>{
            item.isFolder = item?.children?.length > 0 || item?.module?.module_type_id === 4 ? true : false;
            item.children = item?.children?.length > 0 ? item.children.map((item)=>item?.index ? item.index : item) : [];
            tempData[item.index] = (item)
        });
        tempData.root={
            index: "root",
            isFolder: true,
            children: localData.filter((item)=>!item.parent_id).map((item)=>item.index),
            data: "Root Item"
        }
        setDataProvider(new StaticTreeDataProvider(tempData));
    },[])

    useEffect(()=>{
        mountedRef.current = true;

        return ()=>{
            mountedRef.current = false;
        }
    },[]);

    //check the disabled items and add a className accordingly.  If data does not have a disabled state, false is added to all items
    // useEffect(()=>{
    //     if(disabled.length > 0 && !loading && environment.current.items && mountedRef.current){
    //         for(let i = 0; i <disabled.length; i++){
    //             let disabledItem = document.querySelectorAll(`button[data-rct-item-id="${disabled[i]}"]`);
    //             // disabledItem.classList.add('tree-disabled')
    //             console.log(disabledItem)
    //             if(disabledItem.length > 0) disabledItem[0].classList.add('tree-disabled')
    //         }
    //     }
    // },[disabled])

    //if the parent tells the tree to clear the selected item
    useEffect(()=>{
        if(clearSelected && mountedRef.current){
            setClearSelected(false);
            setActiveItem(null);
            if(activeItem) tree.current.toggleItemSelectStatus(getFocus());
        }
    //don't want it triggering on activeItem, just want it to do something IF it's there
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[clearSelected, setClearSelected])

    //format to the liking of the tree
    useEffect(()=>{
        if(data && mountedRef.current){
            sortDataToDataProvider(data);
        }
    },[data, sortDataToDataProvider]);

    useEffect(()=>{
        if(dataProvider && mountedRef.current) setLoading(false);
    },[dataProvider]);

    useEffect(()=>{
        if(reset && mountedRef.current){
            setLoading(true);
            setActiveItem(null);
            let sorted = data.sort((a,b)=>(a.sort_order < b.sort_order ? -1 : a.sort_order > b.sort_order))
            sortDataToDataProvider(sorted);
            setReset(false)
        }
    },[reset, data, sortDataToDataProvider, setReset]);

    useEffect(()=>{
        if(activeItem !== oldActiveItem && mountedRef.current){
            passActive(activeItem)
        }
    },[activeItem, oldActiveItem, passActive]);

    const onDataChange=()=>{
        if(environment?.current?.items && mountedRef.current){
            const newDataOrder=[];
            let items = environment.current.items
            if(treeSettings.canDropOnNonFolder || treeSettings.canDropOnFolder){
                let objectArray = Object.entries(items)
                for(let i = 0; i< objectArray.length; i++){
                    objectArray[i][1].parent_id = null;
                    if(objectArray[i][1].children.length > 0) objectArray[i][1].isFolder = true;
                    else if(objectArray[i][1]?.module?.module_type_id === 4) objectArray[i][1].isFolder = true;
                    else objectArray[i][1].isFolder=false
                }
                items = Object.fromEntries(objectArray)
            }
            //order the root's children (so all the top folders)
            let order = items?.root?.children;
            for(let i = 0; i < order.length; i++){
                let matchedItem = items[order[i]].id
                newDataOrder.push({
                    id: matchedItem,
                    sort_order: i+1,
                    children: items[order[i]].children,
                    wholeItem: items[order[i]] //anywhere the tree is used, it will have different item structures.  
                    //We pass the three things we need for all sorting (id, order, child)
                    //Everything else can be passed this way and the parent can use it as needed.
                })
            }
            //children will be handled after they're passed back up.  Util file has helper functions
            passState(newDataOrder)
        }
    }

    const handleActiveItem=(itemId)=>{
        const match = data[(itemId-=1)];
        setActiveItem(match);
    }

    const handleClear=()=>{
        setActiveItem(null);
        tree.current.toggleItemSelectStatus(getFocus());
    }

    return (
        <div className="sort-order-tree">
            <div className="tree">
                {loading ? 
                    <SkeletonTheme color="#e0e0e0">
                        <div className="mt-3 text-center skeleton" data-cy="sort-tree-skeleton">
                            <Skeleton height={16} count={8} width={300} />
                        </div>
                    </SkeletonTheme>
                :
                    <>
                        {dataProvider &&
                            <>
                                <UncontrolledTreeEnvironment 
                                    ref={environment}
                                    dataProvider={dataProvider}
                                    getItemTitle={item => item.name}
                                    canDragAndDrop={treeSettings.canDragAndDrop}
                                    canDropOnFolder={treeSettings.canDropOnFolder}
                                    canDropOnNonFolder={treeSettings.canDropOnNonFolder}
                                    canReorderItems={treeSettings.canReorderItems}
                                    canSearchByStartingTyping={treeSettings.canSearchByStartingTyping}
                                    onSelectItems={itemId => handleActiveItem(itemId)}
                                    onDrop={onDataChange}
                                    viewState={{}}
                                >
                                    {treeClearBtn && <Button onClick={()=>handleClear()} data-cy="clear-btn">Clear Selected</Button>}
                                    <Tree 
                                        ref={tree} 
                                        treeId={treeSettings.treeId}
                                        rootItem="root" 
                                        treeLabel="Name"
                                    />
                                </UncontrolledTreeEnvironment>
                            </>
                        }
                    </>    
                }
            </div>
            {optionalDetails && ((optionalDetails.requiresActive && activeItem) || !optionalDetails.requiresActive) &&
                <div className="details" data-cy="tree-optional-details">
                    <h6 data-cy="tree-detail-title">
                        {optionalDetails?.title}
                    </h6>
                    {optionalDetails?.type ==="info" &&
                        <>
                            {optionalDetails?.data?.map((detail, i)=>(
                                <div key={`detail-menu-${i}`} className="stack" data-cy="tree-details-active-item">
                                    <label htmlFor={`detail-${i}-${detail?.name}`}>
                                        {detail?.label}:
                                    </label>
                                    <span name={`detail-${i}-${detail?.name}`}>
                                        {activeItem[detail?.name] ?
                                            activeItem[detail?.name]
                                        :
                                            "--"
                                        }
                                    </span>
                                </div>
                            ))}
                        </>
                    }
                    {optionalDetails?.type==="html" &&
                        <div className="stack" data-cy="tree-details-html">
                            {optionalDetails?.data?.map((detail, i)=>(
                                <React.Fragment key={`imported-detail-${i}`}>
                                    {detail}
                                </React.Fragment>
                            ))}
                        </div>
                    }
                </div>
            }
        </div>
    )
}
