import React, {useState, useEffect, useCallback} from 'react';
import {useSelector} from 'react-redux';
import {Form} from 'react-bootstrap';

import APIEvents from '../../../../../../api/Events';

export const Step4 = props => {
    const {click, saveStepValues, disableNext} = props;
    const user = useSelector(state => state.auth.user.profile);

    const [loading, setLoading] = useState(true);
    const [familyMembers, setFamilyMembers] = useState([]);
    const [customFields, setCustomFields] = useState([]);
    const [fieldValues, setFieldValues] = useState();
    const [dataLoaded, setDataLoaded] = useState(false);

    const changeHandler = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        const {name, value} = e.target;
        const dataUserId = e.target.getAttribute('data-user-id');
        const required = e.target?.required || false;

        const newFieldValues = {...fieldValues};
        if (dataUserId){
            const _field = {
                id: dataUserId,
                custom_field_id: name.replace('custom_', ''),
                custom_field_label: e.target.getAttribute('placeholder'),
                value: value,
                label: e.target?.options?.[e.target?.selectedIndex]?.text || value,
                required: required,
            }

            if (!newFieldValues[dataUserId]) newFieldValues[dataUserId] = [];
            const _idx =newFieldValues[dataUserId].findIndex(a=>a.custom_field_id===_field.custom_field_id)
            if (_idx>=0) newFieldValues[dataUserId][_idx] = _field;
            else newFieldValues[dataUserId].push(_field);
        }
        setFieldValues(newFieldValues);
        saveStepValues({customFields: fieldValues});
    }, [fieldValues, saveStepValues]);

    useEffect(() => {
        if (dataLoaded && !customFields.length>0){
            click({
                preventDefault: () => {},
                stopPropagation: () => {},
            }, props.referrerStep === "back" ? 3 : 5);
        }
    }, [dataLoaded, customFields, click, props.referrerStep]);

    useEffect(() => {
        if (user.id){
            if (props.stepValues?.selectedUsers) setFamilyMembers(props.stepValues.selectedUsers);
        }
    }, [user.id, props.stepValues?.selectedUsers]);


    useEffect(() => {        
        const _loadCustomFields = async (event_id) => {
            setLoading(true);
            if (event_id){
                const response2 = await APIEvents.get_custom_fields({event_id: event_id});
                if (response2?.data){
                    setCustomFields(response2.data?.map( (item, i) => (
                        <Form.Group controlId={item.name} key={`custom-field-group-${event_id}-${item.id}-${i}`}>
                            <Form.Label>{item.placeholder_text}</Form.Label>
                            {item.custom_field_type === "select" &&
                                <Form.Control as="select" custom name={`custom_${item.name}`} placeholder={item.placeholder_text} required={item.required?true:undefined} onChange={changeHandler}>
                                    <option key={`custom-select-${item.id}-x`} value=""></option>
                                    {item.options.map((option, i) => (
                                        <option key={`custom-select-${item.id}-${i}`} value={option.value}>{option.text}</option>
                                    ))}
                                </Form.Control>
                            }
                            {item.custom_field_type === "input" &&
                                <Form.Control type="text" required={item.required?true:undefined} name={`custom_${item.name}`} placeholder={item.placeholder_text} defaultValue={item.default_value || ""} onBlur={changeHandler}/>
                            }
                        </Form.Group>
                    )));
                }
                setDataLoaded(true);
            }
            setLoading(false);
        }

        _loadCustomFields(props?.id);
    }, [props?.id, changeHandler]);

    useEffect(() => {
        let _disabledNext = false;
        if (customFields.length>0 && familyMembers.length>0){
            familyMembers.forEach(member => {
                customFields.filter(a=>a?.props?.children.filter(b=>b.required) || null).forEach(item =>{
                    if (item){
                        if (!fieldValues?.[member.id]) {
                            _disabledNext=true;
                            return false;
                        } else {
                            if (fieldValues[member.id].filter(a=>`${a.custom_field_id}`===`${item.props.controlId}` && a.value).length===0) {
                                _disabledNext=true;
                                return false;
                            }
                        }
                    }
                });
            });
        }
        disableNext(_disabledNext);
    }, [familyMembers, fieldValues, customFields, disableNext]);

    useEffect(() => {
        return () => {
            setCustomFields([]);
            setFamilyMembers([]);
            setLoading(false);
            setFieldValues(null);
            setDataLoaded(false);
        }
    }, []);

    if (familyMembers.length===0) return null;
    if (loading) return (<p>Loading...</p>);

    return (
        <div>
            <p>
                <label className="form-label">We need a little extra information from you.</label>
            </p>
            {familyMembers.map((member, i) => (
                <div key={`custom-fields-member-${i}`}>
                    <p><span className="bold">{member.first_name}</span> ({member.group_member_role}):</p>
                    {customFields.length>0 &&
                        <div>
                            {customFields.map((field, i) =>{
                                const newField = React.cloneElement(field, {key:`field-member-${i}-${member.id}`},
                                    React.Children.map(field.props.children, (child) => {
                                        if (!child) return null;
                                        if (child.props?.type === 'text' || child.props?.as === 'select') {
                                            return React.cloneElement(child, {
                                                'data-user-id': member.id, 
                                                defaultValue: fieldValues?.[member.id]?.find(f => `${f.custom_field_id}` === `${field.props.controlId}`)?.value || ""
                                            });
                                        } else {
                                            return child;
                                        }
                                    })
                                );
                                return newField;
                            })}
                        </div>
                    }
                    <hr className='my-3' />
                </div>
            ))}
        </div>
    );
}