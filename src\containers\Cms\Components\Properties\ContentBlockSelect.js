import React, {useState, useEffect} from 'react';
import { useSelector } from 'react-redux';
import { Form } from 'react-bootstrap';
//import APICms from '../../../api/Cms';
import { getPageById } from '../../../../utils/cms';

const ContentBlockSelect = React.forwardRef((props, _) => {
    const {selection} = props;

    const cmsSelector = useSelector(state => state.cms);
    const [options, setOptions] = useState([]);

    useEffect(() => {
        const _getPageById = async () => {
            const res=await getPageById({website_id:cmsSelector.currentWebsite,page_type_id:props.page_type || 2});
            if (res) setOptions(res);
        }
        _getPageById();
    },[props.page_type,cmsSelector.currentWebsite]);

    useEffect(() => {
        return () => {
            setOptions([]);
        }
    }, []);

    return (
        <Form.Control as="select" custom onChange={(e)=>selection(e.target.value)} value={props.value || undefined}>
            <option></option>
            {options?.map((option, i) => {
                return <option key={`select-${props.name}-${i}`} value={option.id}>{option.title}</option>
            })}
        </Form.Control>
    );
});

export default ContentBlockSelect;