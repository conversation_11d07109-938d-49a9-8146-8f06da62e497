import React, { useCallback } from 'react';
import { Form } from 'react-bootstrap';
import { Typeahead, Menu, MenuItem } from 'react-bootstrap-typeahead';

import { randomUUID } from '../../../../../../../utils/cms';

import 'react-bootstrap-typeahead/css/Typeahead.css';

export const Element = (props) => {
    const {update} = props;
    
	const changeHandler = useCallback((e) => {
		e.preventDefault();
		if (props.type === "checkbox") update(props.element_id,{...props.item, checked: e.target.checked});	
		else update(props.element_id,{...props.item, value: e.target.value});
	},[update, props.type, props.element_id, props.item]);

    if (!props.item || !props.element_id) return null;

    switch(props.item.type){
        case 'select':
            return (
                <div>
                    {props.item.display_name} #{props.element_id}
                    <Form.Control as="select" custom onChange={changeHandler} value={props.item?.value || ""} required={props.item?.required || false}>
                        <option value="" disabled>{props.item.display_name}</option>
                        {props.item?.options?.map((option, j) => {
                            return (
                                <option key={`item-${randomUUID()}-option-${j}`} value={option.value}>{option.text}</option>
                            );
                        })}
                    </Form.Control>
                </div>
            );
        case 'checkbox':
            return (
                <div style={{alignSelf:"flex-end"}}>
                    <Form.Check
                        key={randomUUID()}
                        className="form-switch"
                        type="checkbox"
                        label={props.item.display_name}
                        checked={props.item?.checked || false}
                        value={props.item?.value || props.element_id}
                        onChange={changeHandler}
                    />
                </div>
            );
        case 'autocomplete':
            //if (props.value) setAutocompleteSelected(prev=>[...prev,{item: i, data:[{text: props.value, value: props.value}]}]);
            return (
                <div>
                    {props.item.display_name} #{props.element_id}
                    <Typeahead
                        id={`typeahead-${randomUUID()}`}
                        labelKey="value"
                        onChange={(selected)=>{
                            changeHandler({
                                preventDefault:()=>{},
                                target: {
                                    type: "text",
                                    value: selected?.[0]?.value || ""
                                }
                            });
                        }}
                        options={props.item?.values || []}
                        placeholder={`${props.item.display_name} #${props.element_id}`}
                        defaultInputValue={props.item?.value || ""}
                        renderMenu={(results, menuProps) => (
                            <Menu {...menuProps}>
                                {results.map((result, index) => (
                                    <MenuItem option={result} position={index} key={`typeahead-item-${randomUUID()}-${result?.text || ""}`}>
                                        {result?.icon && <i className={`${result.icon}`} style={{marginRight:"0.5rem"}}/>}
                                        {result?.text || ""}
                                    </MenuItem>
                                ))}
                            </Menu>
                        )}
                    />
                    {/*
                    <Form.Control as="select" custom onChange={(e)=>changeHandler(e, item)} value={item?.value || ""}>
                        <option value="" disabled></option>
                        {item?.values?.map((option, j) => (
                            <option key={`item-${i}-option-${j}`} value={option.value}>{option.text}</option>
                        ))}
                    </Form.Control>
                    */}
                </div>
            );
        default:
            return (
                <div>
                    {props.item.display_name} #{props.element_id}
                    <Form.Control onBlur={changeHandler} defaultValue={props.item?.value || ""} required={props.item?.required || false} />
                </div>
            );
    }
}