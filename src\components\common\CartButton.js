import React, { useRef, useState } from 'react';
import { useEffect } from 'react';
import { Button } from 'react-bootstrap';
import { useDispatch, useSelector } from 'react-redux';

import Products from '../../api/Products';
import ErrorCatcher from './ErrorCatcher';
import { addToCart, saveLogging } from '../../utils/thunks';

/**Takes a product id and a setErrors for the parent component and adds the product/first variant to the cart 
 * 
 * @param {number} productId
 * @param {function} setErrors
*/
const CartButton = (props) => {

    const defaultPatronRegisterId = useSelector(state => state.pos.register)
    const {productId, setErrors} = props;
    const mountedRef=useRef(false);
    const dispatch = useDispatch();
    const [product, setProduct]=useState(null);

    useEffect(()=>{
        mountedRef.current = true

        return () => mountedRef.current = false
    },[])

    useEffect(()=>{
        const getProduct=async(productId)=>{
            try{
                let response = await Products.getSingle({id: productId})
                if(!response.errors && response.data && mountedRef.current) setProduct(response.data[0])
                else if(response.errors) setErrors(<ErrorCatcher error={response.errors} />)
            }catch(ex){console.error(ex)}
        }

        getProduct(productId)

    },[productId, setErrors])

    const addProductToCart=()=>{
        if(product){
            //console.log("dispatch addToCart CartButton 44");
            dispatch(saveLogging(`dispatch addToCart CartButton 44`));
            dispatch(addToCart([
                {
                    category_id: product.categories[0].id,
                    discounts : 0,
                    hash: null,
                    id: product.id,
                    product_type_id: product.product_type_id,
                    is_taxable: product.is_taxable,
                    original_price: +product.product_variants[0].price,
                    parent_id: null,
                    product_name: product.name,
                    qty: 1,
                    type: 1,
                    variant_id: product.product_variants[0].id,
                    variant_name: product.product_variants[0].name,
                }
            ], false, defaultPatronRegisterId
            ))
        }
    }

    return (
        <>
            <Button variant="primary" disabled={product ? false : true} onClick={addProductToCart}>Purchase</Button>
        </>
    )
}

export default CartButton