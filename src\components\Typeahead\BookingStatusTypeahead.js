import React, { useCallback } from 'react';

import { Typeahead } from './Typeahead';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import './Typeahead.scss';

/**Basic async typeahead for searching locations.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Services back
*/
export const BookingStatusTypeahead = (props) => {

    const makeRequest = useCallback(async (query, perPage, page=1) => {
        return {
            data: [
                {
                    id: 1,
                    name: "Canceled",
                },
                {
                    id: 2,
                    name: "Confirmed: No Requests",
                },
                {
                    id: 3,
                    name: "Confirmed: Cancel Requested",
                }
            ],
            errors: null
        };
    },[]);

    // each item in responseObj.data is an option
    const formatForLabel = (option) => (
        `${option?.name}`
    );

    return (
        <Typeahead
            {...props}
            id="location-search"
            formatForLabel={formatForLabel}
            makeRequest={makeRequest}
            async={false}
            paginated={false}
            placeholder={props.placeholder ? props.placeholder : "Enter a booking status..."}
        />
    )
}
