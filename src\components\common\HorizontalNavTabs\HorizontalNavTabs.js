import React from 'react';
import styles from './HorizontalNavTabs.module.scss'

export const HorizontalNavTabs=({
    data,
    activeTab,
    tab=true,
    ...props
})=>{

    const eachTab=(each)=>{
        return(
            <span 
                onClick={each.onClick} 
                className={
                    `${tab ? styles["tab"] : styles["button"]}
                    
                    ${activeTab === each.name ? 
                        `${styles["each-tab"]} ${styles["active"]}`
                    : 
                        `${styles["each-tab"]}`
                    }`
                }
            >
                <span>{each?.icon}</span>
                <span>{each?.text}</span>
            </span>
        )
    }

    return(
        <div className={styles['hor-nav-tabs-wrapper']}>
            {data?.map((each, i)=>(
                <React.Fragment key={`each-${i}`}>
                    {eachTab(each)}
                </React.Fragment>
            ))}
        </div>
    )

}

//.hor-nav-tabs-wrapper