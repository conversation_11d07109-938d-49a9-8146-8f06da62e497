import React, { useRef, useState, useEffect, useContext, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Button from 'react-bootstrap/Button';
import Card from 'react-bootstrap/Card';

import Subscriptions from '../../containers/User/Profile/Subscriptions';
import OutstandingCharges from '../../containers/User/Profile/OutstandingCharges';
import Groups from '../../containers/User/Profile/Groups'
import { Families } from '../../containers/Dashboard/FamilyGroup/Tabs/Families';
import { FileURLContext } from '../../contexts/FileURLContext';
import UsersAPI from '../../api/Users';

import * as actions from '../../store/actions';
import { setUser } from '../../store/actions/pos';

//used in src\containers\CheckIn\CheckIn.js
//items commented out make it easy to turn on the actual components for these pieces of information if we need/want them in the future

export const UserSidebar = ({ userID}) => {
    const dispatch = useDispatch();
    const ref = React.createRef();
    const mountedRef = useRef(false)

    const imageURL = useContext(FileURLContext);

    const [ profileImgPath, setProfileImgPath ] = useState(null);
    const [ signedWaiver, setSignedWaiver ] = useState(null);
    const [ user, setUser ] = useState(null);
    const [ amountOfOutstanding, setAmountOfOutstanding]=useState(0);
    const [ familyMembers, setFamilyMembers]=useState([]);
    // const [ clickedType, setClickedType]=useState("Outstanding");

    const getCharges = useCallback(async()=>{
        let userCount=0;
        let familyCount=0;
        let tempFamily=[];
        try{
            let response = await UsersAPI.getOutstandingCharges({id: userID});
            if(response.data && response.status === 200){
                if(response.data.family.length > 0){
                    let family = response.data.family
                    for(let i = 0; i < family.length; i++){
                        if(family[i].outstanding_charges.length > 0){
                            tempFamily.push(family[i].first_name + " " + family[i].last_name)
                            familyCount += family[i].outstanding_charges.length;
                        }
                    }
                }
                if(response.data.outstanding_charges.length > 0) userCount += response.data.outstanding_charges.length;
            }
            setAmountOfOutstanding({family: familyCount, user: userCount});
            setFamilyMembers(tempFamily);
        }
        catch(ex){
            console.error(ex)
        }
    },[userID])

    useEffect(()=>{
        mountedRef.current = true
        if (userID){
            setProfileImgPath(null)
            UsersAPI.get({id: userID})
            .then( response =>{
                if(mountedRef.current){
                    setUser(response?.data[0]);
                    setProfileImgPath(response.data[0].profile_img_path);
                    let signed = response.data[0].hasOwnProperty("has_signed_waiver") ? response.data[0].has_signed_waiver : false;
                    setSignedWaiver(!!signed);
                }
            }).catch ( ex => console.log(ex))
        } else setUser(null);

        return()=>mountedRef.current = false
    },[userID]);

    //counts the number of outstanding charges from the user and family
    useEffect(()=>{
        if(user){
            getCharges();
        }
    },[user, getCharges])

    
    if (!user) return null;

    return (
        <React.Fragment> 
            <Row>
                <Col sm="12" className="user-info middle-column">
                    <div className="user-pic" style={{backgroundImage:`url(${profileImgPath || imageURL.noPic})`}} />
                    <h4 className="current-user">{user.first_name+" "+user.last_name}</h4>
                    <Card>
                        <Button data-cy="user-waiver" className={`sign-waiver-btn ${signedWaiver ? 'signed' : ''}`}>{signedWaiver ? "Signed Waiver on File" : "Unsigned Waiver"}</Button>
                    </Card>
                </Col>
                <Col sm="12" className="checkin-cards">
                    <Row className="d-flex justify-content-center">
                        <Card className="click-card">
                            <Card.Subtitle>
                                Pending Charges
                            </Card.Subtitle>
                            <Card.Text>
                                {amountOfOutstanding.family > 0 || amountOfOutstanding.user >0 ? 
                                    <span className="error-text">
                                        {user.first_name}: {amountOfOutstanding.user} charges
                                        <br />
                                        {amountOfOutstanding.family > 0 &&
                                            <>
                                                Family: {amountOfOutstanding.family} charges
                                            </> 
                                        }
                                    </span>
                                    :
                                    <span>
                                        None
                                    </span>
                                }
                            </Card.Text>
                        </Card>
                        <Card className="click-card">
                            <Card.Subtitle className="my-3">
                                Subscriptions
                            </Card.Subtitle>
                            {user.subscriptions.length > 0 ?
                                <>
                                    {user.subscriptions.map((subscription)=>(
                                        <Card.Text className="profile-each-sub-wrapper" key={`each-sub-profile-${subscription.id}`}>
                                            <span className={
                                                    (subscription.subscription_status==="Active"&& "sub-active") 
                                                    || (subscription.subscription_status==="Expired" && "sub-expired")
                                                    || (subscription.subscription_status==="Cancelled" && "sub-cancelled")
                                                    || (subscription.subscription_status==="Suspended" && "sub-suspended")
                                                }>
                                                {subscription.subscription_status==="Active" ?
                                                    <i className="far fa-lightbulb" style={{paddingLeft:"5px", paddingRight:"4px"}}/>
                                                    :
                                                    <i className="far fa-lightbulb-slash" />
                                                }
                                                {" "}{subscription.subscription_status}
                                            </span>
                                            {" "}{subscription.product_name} 
                                            {subscription?.subscription_type_name ?
                                                <>
                                                    {" "}- <span className="bold">{subscription.subscription_type_name}</span>
                                                </>
                                            : null }
                                        </Card.Text>
                                    ))}    
                                </>
                            :
                                <span>
                                    None
                                </span>
                            }
                        </Card>
                        <Card className="click-card">
                            <Card.Subtitle>Family Members</Card.Subtitle>
                            <Card.Text>
                                {familyMembers?.length > 0 ?
                                    <>
                                        {familyMembers.map((member, i)=>(
                                            <p className="family-member" key={`each-member-profile-${i}`}>
                                                {member}
                                            </p>
                                        ))}
                                    </>
                                :
                                    <p>None</p>
                                }
                            </Card.Text>
                        </Card>
                    </Row>
                    {/* <Row className="d-flex justify-content-center">
                        {clickedType === "Outstanding" &&
                            <OutstandingCharges user_id={userID} user={user} />
                        }
                        {clickedType==="Subscriptions" &&
                            <Subscriptions user_id={userID} searchBar={false} />
                        }
                        {clickedType==="Groups" &&
                            <>
                                <Families user_id={userID} data={user.family} />
                                <br />
                                <Groups user_id={userID} searchBar={false} user={user} />
                            </>
                        }
                    </Row> */}
                </Col>
            </Row>
        </React.Fragment>
    );
}