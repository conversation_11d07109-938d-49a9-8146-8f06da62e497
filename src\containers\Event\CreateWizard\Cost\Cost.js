import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, Button, InputGroup } from 'react-bootstrap';

import Recurring from './Recurring';

import { selectCurrentEvent, selectParentFees } from '../../../../store/selectors';
import * as actions from '../../../../store/actions';

const Cost = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const currentEvent = useSelector(selectCurrentEvent);
    const parentFees = useSelector(selectParentFees);
    const errors = useSelector(state => state.eventwizard.errors);
    const [pagePartFees, setPagePartFees] = useState();
    const [variants, setVariants] = useState(currentEvent.variants);

    const addVariantHandler = useCallback(() => {
        setVariants(prev => [...prev, {
            name: "",
            price: 0,
            activation_fee: 0,
            bill_interval: "",
            bill_num_times: 0,
            interval_quantity: 0,
        }]);
    }, []);

    const removeVariantHandler = (index) => {
        let newVariants = JSON.parse(JSON.stringify(currentEvent.variants));
        newVariants.splice(index, 1);
        dispatch(actions.setCurrentEventWizard({ variants: newVariants }));
    }

    const saveVariantHandler = useCallback((variant, variant_idx) => {
        setVariants(prev => prev.map((v, i) => {
            if ((variant.id && v.id === variant.id) || (!variant.id && i === variant_idx)) {
                return {...v, ...variant};
            }
            return v;
        }));
    }, [setVariants]);

    useEffect(() => {
        dispatch(actions.setCurrentEventWizard({ variants: variants }));
    }, [variants, dispatch]);

    useEffect(() => {
        if (parentFees.length>0) {
            setPagePartFees(
                <div className={`form-row stacked`}>
                    This will be in addition to the
                    {parentFees.map((fee, index) =>
                        <React.Fragment key={`event-fee-${index}`}>
                            {index>0 ? ` and` : ''}
                            {` $${fee.price.toFixed(2)} being charged for ${fee.name}`}
                        </React.Fragment>
                    )}
                </div>
            );
        } else {
            setPagePartFees();
        }
    },[parentFees]);

    return (
        <>
        <Row>
            <Col className="wizard">
                <span className="title">Does it require a fee?</span>
                <Form.Row className="centered">
                    {pagePartFees}
                    <Form.Check 
                        type="radio"
                        label="No"
                        id={`has_fee_0`}
                        name="has_fee"
                        value={0}
                        checked={currentEvent.has_fee===0}
                        onChange={onChangeInput}
                        isInvalid={!!errors.has_fee}
                        className="form-radio sm"
                    />
                    <Form.Check 
                        type="radio"
                        label="Yes"
                        id={`has_fee_1`}
                        name="has_fee"
                        value={1}
                        checked={currentEvent.has_fee===1}
                        onChange={onChangeInput}
                        isInvalid={!!errors.has_fee}
                        className="form-radio sm"
                    />
                </Form.Row>
                {currentEvent.has_fee===1 &&
                    <>
                        <Form.Row className="centered">
                            <Form.Group controlId="fee" className={`fee-input`}>
                                <Form.Label>Full Fee</Form.Label>
                                <InputGroup>
                                    <InputGroup.Prepend>
                                        <InputGroup.Text>$</InputGroup.Text>
                                    </InputGroup.Prepend>
                                    <Form.Control
                                        type="number"
                                        name="price"
                                        onChange={onChangeInput}
                                        defaultValue={currentEvent.price}
                                        isInvalid={!!errors.price}
                                        //defaultValue={currentEvent.price}
                                    />
                                </InputGroup>
                            </Form.Group>
                        </Form.Row>
                        <div className={`err ${!!errors.has_fee || !!errors.price ? "" : "hidden"}`}>
                            {errors?.has_fee}
                            {errors?.price}
                        </div>

                        <span className="title my-4">Payment Options</span>

                        {currentEvent.variants.length===0 &&
                            <Recurring variant_idx={0} onSave={saveVariantHandler} onRemove={removeVariantHandler} />
                        }
                        {currentEvent.variants.length>0 && currentEvent.variants.map((variant, i) => (
                            <Recurring key={`recurring-${i}`} variant_idx={i} variant={variant} onSave={saveVariantHandler} onRemove={removeVariantHandler} />
                        ))}
                        <Button variant="light" onClick={addVariantHandler}>Add another payment option</Button>
                    </>
                }
            </Col>
        </Row>
        </>
    );
}

export default Cost;