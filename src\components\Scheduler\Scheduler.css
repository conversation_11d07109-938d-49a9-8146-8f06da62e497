  .scheduler {
    display: grid;
    width: 100%;
    grid-template-columns: repeat(7, minmax(50px, 1fr));
    grid-template-rows: 50px;
    grid-auto-rows: minmax(120px, auto);
    overflow: auto;
    /*border:1px solid #eee;*/
  }

  .scheduler-year-table {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: initial;
    grid-auto-rows: minmax(200px, auto);
  }

  .scheduler-day-table {
    grid-template-columns: repeat(1, minmax(50px, 1fr));
    grid-template-rows: 50px;
    grid-auto-rows: minmax(50px, auto);
  }

  .scheduler-week-table {
    grid-template-columns: 80px repeat(7, minmax(50px, 1fr));
    grid-template-rows: 50px;
    grid-auto-rows: minmax(50px, auto);
  }
  
  .scheduler-week-table > div,
  .scheduler-day-table > div {
      position:relative;
      display:flex;
      
  }
  .scheduler-week-table > div > div{
    max-width: 100%;
  }

  .scheduler-week-table > div:nth-child(1n + 1):not(:nth-last-child(-n+8)):after,
  .scheduler-day-table > div:nth-child(1n + 1):not(:last-child):after {
    content:"";
    position:absolute;
    bottom:0px;
    left:0;
    width:100%;
    height:1px;
    background:#eee;
  }

  .scheduler-time{
      border-right:1px solid #eee;
      width:80px;
      padding:1rem;
      font-size: .75rem;
  }

  .scheduler-time ~ div{
    width:calc(100% - 80px);
    overflow: hidden;
    min-width: 0;
  }

  .scheduler-loading:before{
      content:'';
      z-index:998;
      position:absolute;
      top:0;
      left:0;
      width:100%;
      height:100%;
      background:rgba(255,255,255,.6);
  }
  .scheduler-loading:after{
    font-family: 'Font Awesome 5 Pro';
    font-weight:300;
    content: '\f1ce';
    position:absolute;
    top:2.5rem;
    left:calc(50% - 1rem);
    font-size:2rem;
    animation: fa-spin 2s linear infinite;  
    color:#536dfe;
    z-index:999;
  }

  .scheduler-container {
    position:relative;
    width: 100%;
    margin: auto;
    overflow: hidden;
    max-width: 100%;
    min-height:80px;
  }

  .scheduler-container .container{
    min-height:350px;
  }

  .scheduler-header {
    padding: .5rem 0 1rem 0;
  }
  .scheduler-header h1 {
    margin: 0;
    font-size: 1rem;
  }
  .scheduler-header p {
    margin: 5px 0 0 0;
    font-size: 1rem;
    font-weight: 500;
    color: #bdbdbd;
  }

  .scheduler-header .dropdown .btn {
      border:0;
      font-weight: 500;
  }

  .scheduler-header .dropdown-toggle::after{
      margin-left:1rem;
  }

  .day {
    border-bottom: 1px solid #eee;
    border-right: 1px solid #eee;
    letter-spacing: 1px;
    text-align: right;
    padding:.7rem .5rem;
    font-size: .7rem;
    box-sizing: border-box;
    color: #424242;
    position: relative;
    overflow:hidden;
  }
  .scheduler:not(.scheduler-year-table) .day:nth-of-type(7n + 7) {
    border-right: 0;
  }
  .day:nth-of-type(n + 1):nth-of-type(-n + 7) {
    grid-row: 2;
  }
  .day:nth-of-type(n + 8):nth-of-type(-n + 14) {
    grid-row: 3;
  }
  .day:nth-of-type(n + 15):nth-of-type(-n + 21) {
    grid-row: 4;
  }
  .day:nth-of-type(n + 22):nth-of-type(-n + 28) {
    grid-row: 5;
  }
  .day:nth-of-type(n + 29):nth-of-type(-n + 35) {
    grid-row: 6;
  }
  .day:nth-of-type(7n + 1) {
    grid-column: 1/1;
  }
  .day:nth-of-type(7n + 2) {
    grid-column: 2/2;
  }
  .day:nth-of-type(7n + 3) {
    grid-column: 3/3;
  }
  .day:nth-of-type(7n + 4) {
    grid-column: 4/4;
  }
  .day:nth-of-type(7n + 5) {
    grid-column: 5/5;
  }
  .day:nth-of-type(7n + 6) {
    grid-column: 6/6;
  }
  .day:nth-of-type(7n + 7) {
    grid-column: 7/7;
  }

  .scheduler-year-table .day{
    grid-row:initial !important;
    grid-column: initial !important;
  }
  .scheduler-year-table .day:nth-of-type(3n + 3) {
    border-right: 0;
  }

  .scheduler-year-table .day:nth-child(n + 10) {
    border-bottom: 0;
  }


  .day-name {
    width:100%;
    display:flex;
    flex-direction: column;
    font-size: .75rem;
    text-transform: uppercase;
    background-color: #fafafa;
    color: #424242;
    text-align: center;
    border-bottom: 1px solid #eee;
    line-height: 50px;
    font-weight: 500;
  }
  .day-date {
    font-size: .7rem;
    text-transform:capitalize;
    background-color: #fafafa;
    color: #424242;
    text-align: center;
    font-weight: 600;
    line-height: 26px;
    border:0;
  }

  .scheduler-week-table .day-name {
    line-height: 24px;
  }

  .day-disabled {
    color: #bdbdbd;
    background-color: #fafafa;
    cursor: not-allowed;
    pointer-events: none;
  }

  .day-today {
    /*box-shadow: inset 0 0 0 0.2rem #e8eaf6;*/
    background:#f0f3ff;
  }
  
  .scheduler .task {
    position:relative;
    text-align: left;
    padding: .55rem;
    margin: .5rem 0;
    font-size: .65rem;
    white-space: nowrap;
    max-width: 100%;
    background-color: #b3e5fc;
    align-self: center;
    border-radius:5px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left:2px solid #03a9f4;
    color:#0288d1;
    overflow:hidden;
  }
  .scheduler .task:hover:after {
      content:'';
      background:rgba(255,255,255,.2);
      width:100%;
      height:100%;
      position:absolute;
      top:0;
      left:0;
  }  
  .scheduler .task a{
      color:inherit;
      cursor:default;
  }

  .scheduler .task-multiday-right{
      margin-right:0;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
  }
  .scheduler .task-multiday-left{
    margin-left:0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.scheduler .task-everyday {
    background: #c8e6c9;
    border-color:#66bb6a;
    color:#388e3c;
  }

  .datepicker-calendar .dropdown-item{
      padding:0;
  }
  .datepicker-calendar .dropdown-item:hover{
    background: inherit;
  }

  .week-dropdown .dropdown-menu{
    display: flex !important;
    flex-direction: column;
    flex-wrap: wrap;
    width:42rem;
    height: 400px;
    overflow:hidden;    
  }
  .week-dropdown .dropdown-menu .dropdown-item{
      width:auto;
      font-size:.85rem;
      font-weight: 500;
      padding:.35rem .75rem;
  }

  .schedule{
    display:flex;
    flex-direction: row;
    align-items: flex-start;
  }

  .schedule ul > li{
    line-height: 2rem;
  }
  .schedule ul:first-child{
    width:170px;
  }

  .schedule ul > li .task {
    display:inline-block;
    background-color:#03a9f4;
    height:.5rem;
    width:.5rem;
    border-radius: 50%;
    margin-right:.65rem;
  }
  .schedule ul > li .task-everyday {
    background-color:#4caf50;
  }
  
  .schedule-date{
    line-height: 2rem;
    font-weight: 500;
  }

  @media (max-width: 991px){
    .week-dropdown .dropdown-menu{
        width:30rem;
      }
      .week-dropdown .dropdown-menu .dropdown-item{
          font-size:.7rem;
      }
  }
  