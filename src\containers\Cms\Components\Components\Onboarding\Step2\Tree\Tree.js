import React, {useState, useEffect, useCallback} from 'react';
import {TreeView, TreeItem }from '@material-ui/lab';
import {Form} from 'react-bootstrap';

import industries from './industries';
import styles from './Tree.module.scss';

export const Tree = (props) => {
    const {onSelect} = props;
    const [selected, setSelected] = useState([]);
    const [selectedNodes, setSelectedNodes] = useState([]); // [node, node, node]
    const [expanded, setExpanded] = useState([]);
    const [data, setData] = useState(industries);

    useEffect(() => {
        setSelected(props.selected);
    }, [props.selected]);

    useEffect(() => {
        if (onSelect) onSelect(selectedNodes); // pass selected nodes to parent
    }, [selectedNodes, onSelect]);

    useEffect(() => {
        return () => {
            setSelected([]);
            setSelectedNodes([]);
            setExpanded([]);
            setData([]);
        }
    }, []);

    const selectHandler = useCallback((e, node) => {
        e.preventDefault();
        e.stopPropagation();
        if (node){
            if (e.target.checked){
                if (!selected.includes(node.id)) {
                    setSelected(prev=>[...prev, node.id]);
                    setSelectedNodes(prev=>[...prev, node]);
                }
            } else {
                setSelected(selected.filter(a => +a !== +node.id));
                setSelectedNodes(selectedNodes.filter(a => +a.id !== +node.id));
            }
        }
    }, [selected, selectedNodes]);

    const toggleHandler = (e, nodeIds) => {
        setExpanded(nodeIds);
    };
  
    const renderTree = useCallback(node => {
        return node.map(n => (
            <TreeItem key={`tree-item-${n.id}`} nodeId={`${n.id}`} classes={{iconContainer:styles["tree-item"]}} label={
                <Form.Check
                    key={`tree-item-${n.id}-checkbox`}
                    type="checkbox"
                    label={n.name}
                    value={n.id}
                    checked={selected.includes(n.id) || false}
                    onChange={(e)=>selectHandler(e, n)}
                    className="px-0"
                />
            }>
                {n?.children && renderTree(n.children)}
            </TreeItem>
        ));
    }, [selected, selectHandler]);
    
    return (
        <TreeView className={`treeview`} 
            expanded={expanded} 
            selected={selected}
            onNodeToggle={toggleHandler}
            defaultCollapseIcon={<i className="far fa-chevron-down" />} 
            defaultExpandIcon={<i className="far fa-chevron-right" />}
        >
            {data && renderTree(data)}
        </TreeView>
    );
}