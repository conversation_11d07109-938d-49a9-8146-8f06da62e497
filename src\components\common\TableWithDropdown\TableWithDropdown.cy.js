/*eslint-disable*/
///<reference types="cypress" />
import { TableWithDropdown } from './TableWithDropdown';
import './TableWithDropdown.module.scss';

describe('TableWithDropdown Component', { scrollBehavior: "center" }, () => {
    const mockTableHeadings = [
        { key: 'id', label: 'ID', sortHeader: 'id' },
        { key: 'name', label: 'Name', sortHeader: 'name' },
        { key: 'created_at', label: 'Created Date' },
        { key: 'multiple-fullName', label: 'Full Name', multiple: ['firstName', 'lastName'] }
    ];

    const mockData = [
        { 
            id: 1, 
            name: 'Test Item 1', 
            created_at: '2023-01-01T12:00:00Z',
            firstName: 'John',
            lastName: 'Doe',
            detail1: 'Detail 1',
            detail2: 'Detail 2'
        },
        { 
            id: 2, 
            name: 'Test Item 2', 
            created_at: '2023-01-02T12:00:00Z',
            firstName: '<PERSON>',
            lastName: '<PERSON>'
        }
    ];

    const defaultProps = {
        title: 'Test Table',
        tableHeadings: mockTableHeadings,
        data: mockData,
        name: 'test-table'
    };

    it('renders with basic props', () => {
        cy.mount(<TableWithDropdown {...defaultProps} />);
        
        // Check title
        cy.get('.section-title').should('contain', 'Test Table');
        
        // Check table exists
        cy.get('[data-cy="test-table-table"]').should('exist');
        
        // Check headers
        cy.get('thead tr td').should('have.length', mockTableHeadings.length);
        cy.get('thead tr td').first().should('contain', 'ID');
    });

    it('renders loading skeleton when loading prop is true', () => {
        cy.mount(<TableWithDropdown {...defaultProps} loading={true} skeletonRows={5} />);
        
        cy.get('[data-cy="table-loading-skeleton"]')
            .should('exist');
        // Count skeleton rows
        cy.get('tbody tr')
            .should('have.length', 5);
    });

    it('handles date formatting correctly', () => {
        cy.mount(<TableWithDropdown {...defaultProps} dateFormat="MM/dd/yy" />);
        
        // Check if the date is formatted correctly
        cy.get('tbody tr')
            .first()
            .find('td')
            .eq(2)
            .should('contain', '01/01/23');
    });

    it('handles sorting functionality', () => {
        const sortSpy = cy.spy().as('sortSpy');
        cy.mount(<TableWithDropdown {...defaultProps} sortOnClick={sortSpy} />);
        
        // Click sort header
        cy.get('[data-cy="header-sort-td-id"]')
            .click();
        
        cy.get('@sortSpy')
            .should('have.been.calledWith', 'id', 'ASC');
        
        // Check sort icon
        cy.get('[data-cy="header-sort-td-id"] i')
            .should('have.class', 'fa-sort-up');
    });

    it('handles expanded rows correctly', () => {
        cy.viewport(1200, 1080);
        const expandedHeadings = [
            { key: 'detail1', label: 'Detail 1' },
            { key: 'detail2', label: 'Detail 2' }
        ];

        cy.mount(
            <TableWithDropdown 
                {...defaultProps}
                expandedRow={true}
                expandedText="View Details"
                expandedHeadings={expandedHeadings}
            />
        );

        // Check for expanded column
        cy.get('[data-cy="handle-expanded-td"]')
            .should('exist')
            .and('contain', 'View Details');

        // Click to expand
        cy.get('[data-cy="handle-expanded-td"]')
            .first()
            .click();

        // Verify expanded content is visible
        cy.get('[data-cy="expanded-row-1"]')
            .should('exist');
    });

    it('handles row click events', () => {
        const rowClickSpy = cy.spy().as('rowClickSpy');
        cy.mount(<TableWithDropdown {...defaultProps} rowOnClick={rowClickSpy} />);

        cy.get('tbody tr')
            .first()
            .click();
        cy.get('@rowClickSpy')
            .should('have.been.calledWith', mockData[0]);
    });

    it('handles multiple field columns correctly', () => {
        cy.mount(<TableWithDropdown {...defaultProps} />);

        // Check if multiple fields are rendered correctly
        cy.get('tbody tr').first()
            .find('td').last()
            .should('contain', 'John')
            .and('contain', 'Doe');
    });

    it('handles click cell functionality', () => {
        const clickCellSpy = cy.spy().as('clickCellSpy');
        cy.mount(
            <TableWithDropdown 
                {...defaultProps}
                clickCell={true}
                clickCellContent="Click Me"
                clickCellFunction={clickCellSpy}
            />
        );

        cy.get('[data-cy="handle-detail-click-td"]')
            .should('exist')
            .and('contain', 'Click Me')
            .first()
            .click();

        cy.get('@clickCellSpy')
            .should('have.been.calledWith', mockData[0]);
    });

    it('handles ellipsis styling', () => {
        let longMock=[{ 
            id: 3, 
            name: 'Test Item 3 that has a long naaaame', 
            created_at: '2023-01-02T12:00:00Z',
            firstName: 'Jebediahberg',
            lastName: 'Wigglysyndromic'
        }]
        cy.mount(<TableWithDropdown {...defaultProps} data={[...mockData, ...longMock]} ellipsis={true} />);
        
        cy.get('[data-cy="test-table-table"]')
            .invoke('attr', 'class').should('contain', 'ellipsis');
    });
});