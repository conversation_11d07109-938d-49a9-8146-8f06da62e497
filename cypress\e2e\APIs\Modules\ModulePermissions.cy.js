/*eslint-disable*/

import moduleData from "./ModuleDataImpact.json";

let baseURL = Cypress.env('qa_api_url');
let header;

let userSB = Cypress.env('impact_sb_user');
let userAdmin = Cypress.env('impact_admin_user');
let userStaff = Cypress.env('impact_staff_user');
let userPatron = Cypress.env('impact_patron_user');
let password = Cypress.env('login_password');

const requiredModuleIds = moduleData.requiredModuleIds;
const userProfileModuleIds = moduleData.userProfileModuleIds;
const servicesModuleIds = moduleData.serviceModuleIds;
const groupModuleIds = moduleData.groupModuleIds;

const login = (user, password)=>{
    cy.loginApi(user, password)
        .then((response)=>{
            header={
                Authorization: response.body.data.token,
                "Content-Type": "application/json"
            }
        })
}

const moduleForEach=(module, status, bool)=>{
    cy.request({
        method: "POST",
        url: `${baseURL}/user/module_permission`,
        headers: header,
        body: {
            "module_ids": [module.id]
        }
    }).then((response)=>{
        expect(response.status).to.eq(status);
        if(bool) expect(response.body.data[module.id]).to.be.true;
        else expect(response.body.data[module.id]).to.be.false;
    })
}

/** Will check the required feature */
describe("It will check the modules in the required feature permissions", {scrollBehavior: "center", testIsolation:false}, ()=>{

    context("It will make sure they all come back properly for a SB user",()=>{

        it("will login to SB account",()=>{
            login(userSB, password)
        });

        it("will make sure each endpoint has a 200 status and true response",()=>{
            
            requiredModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact SB User`)
                moduleForEach(module, 200, module.permissions["sb"].access)
            })
        })
    })

    context("will make sure the required modules all come back status 200 and true for SB admin",()=>{
        
        it("will log into an admin account",()=>{
            login(userAdmin, password)
        });
        
        it("will make sure they are status 200 and true",()=>{
            requiredModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Admin User`)
                moduleForEach(module, 200, module.permissions["admin"].access)
            })
        })
    });

    context("will make sure that the required modules all come back status 200",()=>{
        it("will log into a staff account", ()=>{
            login(userStaff, password)
        });
        it("will check that the required modules are 200 and true",()=>{
            requiredModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Staff User`)
                moduleForEach(module, 200, module.permissions["staff"].access)
            })
        })
    })

    context("it will make sure that a patron user also has access to the required modules",()=>{
        it("will log into a patron account",()=>{
            login(userPatron, password)
        })

        it('will check that the required modules are 200 and true',()=>{
            requiredModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Patron User`)
                moduleForEach(module, 200, module.permissions["patron"].access)
            })
        })
    })

});

/**Will check the 'User Profile' feature */
describe("It will check the feature 'User Profile'",()=>{
    context("It will make sure a SB user gets a 200 and true for each module",()=>{
        it("will log into a SB account",()=>{
            login(userSB, password)
        });

        it("will make sure each endpoint has a 200 status and true response",()=>{
            userProfileModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact SB User`)
                moduleForEach(module, 200, module.permissions["sb"].access)
            })
        })
    });

    context("It will make sure that an admin user gets the proper access returned",()=>{
        it("will log into an admin account",()=>{
            login(userAdmin, password)
        });

        it("will make sure each endpoint has the right access",()=>{
            userProfileModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Admin User`)
                moduleForEach(module, 200, module.permissions["admin"].access)
            })
        })
    })

    context("It will make sure that a staff has the proper access",()=>{
        it("will log into a staff account",()=>{
            login(userStaff, password)
        });

        it("will make sure each endpoint has the right access",()=>{
            userProfileModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Staff User`)
                moduleForEach(module, 200, module.permissions["staff"].access)
            })
        })
    })

    context("It will make sure that a patron has proper access",()=>{
        it("will log into a patron account",()=>{
            login(userPatron, password)
        });

        it("will make sure each endpoint has the right access",()=>{
            userProfileModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Patron User`)
                moduleForEach(module, 200, module.permissions["patron"].access)
            })
        })
    })
});

/**Service Feature */
describe("It will check the feature 'Services'",()=>{
    context("It will check for a SB user",()=>{
        it("will log into a SB account",()=>{
            login(userSB, password)
        });

        it("will make sure each endpoint has a 200 status and true response",()=>{
            servicesModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact SB User`)
                moduleForEach(module, 200, module.permissions["sb"].access)
            })
        })
    });

    context("It will check for an admin user",()=>{
        it("will log into an admin account",()=>{
            login(userAdmin, password)
        });

        it("will make sure each endpoint has a 200 status and proper response",()=>{
            servicesModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Admin User`)
                moduleForEach(module, 200, module.permissions["admin"].access)
            })
        })
    });

    context("It will check for a staff user",()=>{
        it("will log into a staff account",()=>{
            login(userStaff, password)
        });

        it("will make sure each endpoint has a 200 status and proper response",()=>{
            servicesModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Staff User`)
                moduleForEach(module, 200, module.permissions["staff"].access)
            })
        })
    });

    context("It will check for a patron user",()=>{
        it("will log into a patron account",()=>{
            login(userPatron, password)
        });

        it("will make sure each endpoint has a 200 status and proper response",()=>{
            servicesModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Patron User`)
                moduleForEach(module, 200, module.permissions["patron"].access)
            })
        })
    })
})

/**Group Feature */
describe("It will check the feature 'Groups'", ()=>{
    context("It will check for a SB user",()=>{
        it("will log into a SB account",()=>{
            login(userSB, password)
        });

        it("will make sure each endpoint has a 200 status and proper response",()=>{
            groupModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact SB User`)
                moduleForEach(module, 200, module.permissions["sb"].access)
            })
        })
    });

    context("It will check for an admin user",()=>{
        it("will log into an admin account",()=>{
            login(userAdmin, password)
        });

        it("will make sure each endpoint has a 200 status and proper response",()=>{
            groupModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Admin User`)
                moduleForEach(module, 200, module.permissions["admin"].access)
            })
        })
    });

    context("It will check for a staff user",()=>{
        it("will log into a staff account",()=>{
            login(userStaff, password)
        });

        it("will make sure each endpoint has a 200 status and proper response",()=>{
            groupModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Staff User`)
                moduleForEach(module, 200, module.permissions["staff"].access)
            })
        })
    });

    context("It will check for a patron user",()=>{
        it("will log into a patron account",()=>{
            login(userPatron, password)
        });

        it("will make sure each endpoint has a 200 status and proper response",()=>{
            groupModuleIds.forEach((module)=>{
                cy.log(`${module.name} - Impact Patron User`)
                moduleForEach(module, 200, module.permissions["patron"].access)
            })
        })
    })
})