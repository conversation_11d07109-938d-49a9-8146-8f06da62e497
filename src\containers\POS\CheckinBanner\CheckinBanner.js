import React, { useState, useEffect } from 'react';
import BottomBanner from '../../../components/common/BottomBanner';
import CheckinUserList from '../../../components/common/CheckinUserList';

import './CheckinBanner.scss';

export const CheckinBanner = ({ register_id }) => {

    const [ isTablet, setIsTablet ] = useState(false);

    useEffect(()=>{
        if(window.innerWidth < 1350) setIsTablet(true);
    }, [])

    return (
        <div className="checkin-banner-container">
            <BottomBanner
                title="Recent Check-ins"
                detailBtn={false}
            />
            <CheckinUserList 
                numShown={isTablet ? 4 : 6}
                todayOnly={true}
                dateTimeFormat="h:mm a"
                refreshMilliseconds={30000}
                register_id={register_id}
                showAllButton={true}
            />
        </div>
    );
};

export default CheckinBanner;