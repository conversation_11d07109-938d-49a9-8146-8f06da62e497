/*eslint-disable*/

import {
    checkFamilyAllowedByRole,
    checkAllowedAge,
    checkIsRegistered,
    eachFamilyMember,
    checkFamilyStatuses
} from './EventHelperFunctions';

describe('EventHelperFunctions Unit Tests', {scrollBehavior: "center", testIsolation: true}, () => {

    describe('checkAllowedAge function', () => {
        const mockUser = { dob: '2010-01-01' }; 
        it('should allow user within age range', () => {
            const result = checkAllowedAge(mockUser, 10, 20);

            expect(result.isAllowed).to.be.true;
            expect(result.notAllowedReason).to.equal("");
        });

        it('should reject user below minimum age', () => { 
            const result = checkAllowedAge(mockUser, 18, 25);

            expect(result.isAllowed).to.be.false;
            expect(result.notAllowedReason).to.equal("Participant is below the age limit");
        });

        it('should reject user above maximum age', () => {
            const result = checkAllowedAge(mockUser, 5, 10);

            expect(result.isAllowed).to.be.false;
            expect(result.notAllowedReason).to.equal("Participant is above the age limit");
        });

        it('should allow user when only minimum age set and user meets it', () => {
            const result = checkAllowedAge(mockUser, 10);

            expect(result.isAllowed).to.be.true;
            expect(result.notAllowedReason).to.equal("");
        });

        it('should allow user when only maximum age set and user meets it', () => {
            const result = checkAllowedAge(mockUser, null, 20);

            expect(result.isAllowed).to.be.true;
            expect(result.notAllowedReason).to.equal("");
        });

        it('should allow user when no age restrictions', () => {
            const result = checkAllowedAge(mockUser);

            expect(result.isAllowed).to.be.true;
            expect(result.notAllowedReason).to.equal("");
        });
    });

    describe('checkFamilyAllowedByRole function', () => {
        it('should return true when user is admin in their group', () => {
            const mockUser = { id: 123 };
            const mockFamily = {
                group_members: [{ user_id: 123, group_member_role_id: 10 }],
                group_member_roles: [{ id: 10, is_admin: 1 }]
            };

            cy.then(async () => {
                const result = await checkFamilyAllowedByRole(mockFamily, mockUser);
                expect(result).to.be.true;
            });
        });

        it('should return false when user is not admin', () => {
            const mockUser = { id: 123 };
            const mockFamily = {
                group_members: [{ user_id: 123, group_member_role_id: 5 }],
                group_member_roles: [{ id: 5, is_admin: 0 }]
            };

            cy.then(async () => {
                const result = await checkFamilyAllowedByRole(mockFamily, mockUser);
                expect(result).to.be.false;
            });
        });

        it('should return false when user not found in family', () => {
            const mockUser = { id: 123 };
            const mockFamily = {
                group_members: [{ user_id: 456, group_member_role_id: 10 }],
                group_member_roles: [{ id: 10, is_admin: 1 }]
            };

            cy.then(async () => {
                const result = await checkFamilyAllowedByRole(mockFamily, mockUser);
                expect(result).to.be.false;
            });
        });

        it('should not error when missing family, just return false', () => {
            const mockUser = { id: 123 };
            const mockFamily = {};

            cy.then(async () => {
                const result = await checkFamilyAllowedByRole(mockFamily, mockUser);
                expect(result).to.be.false;
            });
        });
    });

    describe('checkIsRegistered function', () => {
        it('should return true when user is registered', () => {
            const mockUser = { user_id: 123 };
            const mockEventDetails = {
                advancedDetails: {
                    users: [{ id: 123 }, { id: 456 }]
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);
            expect(result).to.be.true;
        });

        it('should return false when user is not registered', () => {
            const mockUser = { user_id: 123 };
            const mockEventDetails = {
                advancedDetails: {
                    users: [{ id: 456 }, { id: 789 }]
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);
            expect(result).to.be.false;
        });

        it('should return false when no users in event', () => {
            const mockUser = { user_id: 123 };
            const mockEventDetails = {
                advancedDetails: {
                    users: []
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);
            expect(result).to.be.false;
        });

        it('should handle missing event details and not error', () => {
            const mockUser = { user_id: 123 };
            const result = checkIsRegistered(mockUser, {});
            expect(result).to.be.false;
        });
    });

    describe('eachFamilyMember function', () => {
        it('should process family members correctly', () => {
            const mockEventDetails = { min_age: 10, max_age: 20 };
            const mockFamily = {
                group_members: [
                    {
                        user_id: 123,
                        first_name: 'John',
                        last_name: 'Doe',
                        group_member_role_name: 'Child',
                        dob: '2010-01-01'
                    },
                    {
                        user_id: 456,
                        first_name: 'Jane',
                        last_name: 'Doe',
                        group_member_role_name: 'Parent',
                        dob: '1985-01-01'
                    }
                ]
            };

            cy.then(async () => {
                const result = await eachFamilyMember(mockFamily, 0, mockEventDetails);

                expect(result).to.have.length(2);
                expect(result[0]).to.include({
                    key: 2,
                    id: 123,
                    firstName: 'John',
                    lastName: 'Doe',
                    name: 'John Doe',
                    isSelf: false,
                    roleName: 'Child'
                });
                expect(result[1]).to.include({
                    key: 3,
                    id: 456,
                    firstName: 'Jane',
                    lastName: 'Doe',
                    name: 'Jane Doe',
                    isSelf: false,
                    roleName: 'Parent'
                })
            });
        });

        it('should handle empty family members', () => {
            const mockEventDetails = { min_age: 10, max_age: 20 };
            const mockFamily = { group_members: [] };

            cy.then(async () => {
                const result = await eachFamilyMember(mockFamily, 0, mockEventDetails);
                expect(result).to.have.length(0);
            });
        });
    });

    describe('checkFamilyStatuses function', () => {

        it('should validate complete family member processing workflow', () => {
            const mockUser = {
                id: 123,
                first_name: 'John',
                last_name: 'Doe',
                dob: '1990-01-01'
            };
            const mockEventDetails = { min_age: 10, max_age: 50 };
            const mockFamily = [{
                group_members: [
                    {
                        user_id: 456,
                        first_name: 'Jane',
                        last_name: 'Doe',
                        group_member_role_name: 'Spouse',
                        dob: '1992-01-01'
                    }
                ]
            }];
            const mockLoadingStates = { current: { family: false } };

            cy.then(async () => {
                const result = await checkFamilyStatuses(mockLoadingStates, mockUser, mockFamily, mockEventDetails);

                expect(result).to.have.length(2);
                expect(result[0]).to.include({
                    id: 123,
                    firstName: 'John',
                    lastName: 'Doe',
                    isSelf: true
                });
                expect(result[1]).to.include({
                    id: 456,
                    firstName: 'Jane',
                    lastName: 'Doe',
                    isSelf: false
                });
                expect(mockLoadingStates.current.family).to.be.false;
            });
        });

        it('should filter out duplicate family members correctly', () => {
            const mockUser = {
                id: 123,
                first_name: 'John',
                last_name: 'Doe',
                dob: '1990-01-01'
            };
            const mockEventDetails = { min_age: 10, max_age: 50 };
            const mockFamily = [
                {
                    group_members: [
                        {
                            user_id: 456,
                            first_name: 'Jane',
                            last_name: 'Doe',
                            group_member_role_name: 'Spouse',
                            dob: '1992-01-01'
                        }
                    ]
                },
                {
                    group_members: [
                        {
                            user_id: 456, // Same user in different family group
                            first_name: 'Jane',
                            last_name: 'Doe',
                            group_member_role_name: 'Parent',
                            dob: '1992-01-01'
                        }
                    ]
                }
            ];
            const mockLoadingStates = { current: { family: false } };

            cy.then(async () => {
                const result = await checkFamilyStatuses(mockLoadingStates, mockUser, mockFamily, mockEventDetails);

                expect(result).to.have.length(2); // Should only have user + 1 unique family member
                const janeEntries = result.filter(member => member.id === 456);
                expect(janeEntries).to.have.length(1);
            });
        });
    });


});
