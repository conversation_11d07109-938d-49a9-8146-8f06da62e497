/*eslint-disable*/

import {
    checkFamilyAllowedByRole,
    checkAllowedAge,
    checkIsRegistered,
    eachFamilyMember,
    checkFamilyStatuses
} from './EventHelperFunctions';

describe('EventHelperFunctions Unit Tests', {scrollBehavior: "center", testIsolation: true}, () => {

    describe('checkAllowedAge function', () => {
        const mockUser = { dob: '2010-01-01' }; 
        it('should allow user within age range', () => {
            const result = checkAllowedAge(mockUser, 10, 20);

            expect(result.isAllowed).to.be.true;
            expect(result.notAllowedReason).to.equal("");
        });

        it('should reject user below minimum age', () => { 
            const result = checkAllowedAge(mockUser, 18, 25);

            expect(result.isAllowed).to.be.false;
            expect(result.notAllowedReason).to.equal("Participant is below the age limit");
        });

        it('should reject user above maximum age', () => {
            const result = checkAllowedAge(mockUser, 5, 10);

            expect(result.isAllowed).to.be.false;
            expect(result.notAllowedReason).to.equal("Participant is above the age limit");
        });

        it('should allow user when only minimum age set and user meets it', () => {
            const result = checkAllowedAge(mockUser, 10);

            expect(result.isAllowed).to.be.true;
            expect(result.notAllowedReason).to.equal("");
        });

        it('should allow user when only maximum age set and user meets it', () => {
            const result = checkAllowedAge(mockUser, null, 20);

            expect(result.isAllowed).to.be.true;
            expect(result.notAllowedReason).to.equal("");
        });

        it('should allow user when no age restrictions', () => {
            const result = checkAllowedAge(mockUser);

            expect(result.isAllowed).to.be.true;
            expect(result.notAllowedReason).to.equal("");
        });
    });

    describe('checkFamilyAllowedByRole function', () => {
        it('should return true when user is admin in their group', () => {
            const mockUser = { id: 123 };
            const mockFamily = {
                group_members: [{ user_id: 123, group_member_role_id: 10 }],
                group_member_roles: [{ id: 10, is_admin: 1 }]
            };

            cy.then(async () => {
                const result = await checkFamilyAllowedByRole(mockFamily, mockUser);
                expect(result).to.be.true;
            });
        });

        it('should return false when user is not admin', () => {
            const mockUser = { id: 123 };
            const mockFamily = {
                group_members: [{ user_id: 123, group_member_role_id: 5 }],
                group_member_roles: [{ id: 5, is_admin: 0 }]
            };

            cy.then(async () => {
                const result = await checkFamilyAllowedByRole(mockFamily, mockUser);
                expect(result).to.be.false;
            });
        });

        it('should return false when user not found in family', () => {
            const mockUser = { id: 123 };
            const mockFamily = {
                group_members: [{ user_id: 456, group_member_role_id: 10 }],
                group_member_roles: [{ id: 10, is_admin: 1 }]
            };

            cy.then(async () => {
                const result = await checkFamilyAllowedByRole(mockFamily, mockUser);
                expect(result).to.be.false;
            });
        });

        it('should not error when missing family, just return false', () => {
            const mockUser = { id: 123 };
            const mockFamily = {};

            cy.then(async () => {
                const result = await checkFamilyAllowedByRole(mockFamily, mockUser);
                expect(result).to.be.false;
            });
        });
    });

    describe('checkIsRegistered function', () => {
        it('should return true when user is registered', () => {
            const mockUser = { user_id: 123 };
            const mockEventDetails = {
                advancedDetails: {
                    users: [{ id: 123 }, { id: 456 }]
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);
            expect(result).to.be.true;
        });

        it('should return false when user is not registered', () => {
            const mockUser = { user_id: 123 };
            const mockEventDetails = {
                advancedDetails: {
                    users: [{ id: 456 }, { id: 789 }]
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);
            expect(result).to.be.false;
        });

        it('should return false when no users in event', () => {
            const mockUser = { user_id: 123 };
            const mockEventDetails = {
                advancedDetails: {
                    users: []
                }
            };

            const result = checkIsRegistered(mockUser, mockEventDetails);
            expect(result).to.be.false;
        });

        it('should handle missing event details and not error', () => {
            const mockUser = { user_id: 123 };
            const result = checkIsRegistered(mockUser, {});
            expect(result).to.be.false;
        });
    });

    describe('eachFamilyMember function', () => {
        it('should process family members correctly', () => {
            const mockEventDetails = { min_age: 10, max_age: 20 };
            const mockFamily = {
                group_members: [
                    {
                        user_id: 123,
                        first_name: 'John',
                        last_name: 'Doe',
                        group_member_role_name: 'Child',
                        dob: '2010-01-01'
                    },
                    {
                        user_id: 456,
                        first_name: 'Jane',
                        last_name: 'Doe',
                        group_member_role_name: 'Parent',
                        dob: '1985-01-01'
                    }
                ]
            };

            cy.then(async () => {
                const result = await eachFamilyMember(mockFamily, 0, mockEventDetails);

                expect(result).to.have.length(2);
                expect(result[0]).to.include({
                    key: 2,
                    id: 123,
                    firstName: 'John',
                    lastName: 'Doe',
                    name: 'John Doe',
                    isSelf: false,
                    roleName: 'Child'
                });
                expect(result[1]).to.include({
                    key: 3,
                    id: 456,
                    firstName: 'Jane',
                    lastName: 'Doe',
                    name: 'Jane Doe',
                    isSelf: false,
                    roleName: 'Parent'
                })
            });
        });

        it('should handle empty family members', () => {
            const mockEventDetails = { min_age: 10, max_age: 20 };
            const mockFamily = { group_members: [] };

            cy.then(async () => {
                const result = await eachFamilyMember(mockFamily, 0, mockEventDetails);
                expect(result).to.have.length(0);
            });
        });
    });

    describe('checkFamilyStatuses function', () => {

        it('should validate complete family member processing workflow', () => {
            const mockUser = {
                id: 123,
                first_name: 'John',
                last_name: 'Doe',
                dob: '1990-01-01'
            };
            const mockEventDetails = { min_age: 10, max_age: 50 };
            const mockFamily = [{
                group_members: [
                    {
                        user_id: 456,
                        first_name: 'Jane',
                        last_name: 'Doe',
                        group_member_role_name: 'Spouse',
                        dob: '1992-01-01'
                    }
                ]
            }];
            const mockLoadingStates = { current: { family: false } };

            cy.then(async () => {
                const result = await checkFamilyStatuses(mockLoadingStates, mockUser, mockFamily, mockEventDetails);

                expect(result).to.have.length(2);
                expect(result[0]).to.include({
                    id: 123,
                    firstName: 'John',
                    lastName: 'Doe',
                    isSelf: true
                });
                expect(result[1]).to.include({
                    id: 456,
                    firstName: 'Jane',
                    lastName: 'Doe',
                    isSelf: false
                });
                expect(mockLoadingStates.current.family).to.be.false;
            });
        });

        it('should filter out duplicate family members correctly', () => {
            const mockUser = {
                id: 123,
                first_name: 'John',
                last_name: 'Doe',
                dob: '1990-01-01'
            };
            const mockEventDetails = { min_age: 10, max_age: 50 };
            const mockFamily = [
                {
                    group_members: [
                        {
                            user_id: 456,
                            first_name: 'Jane',
                            last_name: 'Doe',
                            group_member_role_name: 'Spouse',
                            dob: '1992-01-01'
                        }
                    ]
                },
                {
                    group_members: [
                        {
                            user_id: 456, // Same user in different family group
                            first_name: 'Jane',
                            last_name: 'Doe',
                            group_member_role_name: 'Parent',
                            dob: '1992-01-01'
                        }
                    ]
                }
            ];
            const mockLoadingStates = { current: { family: false } };

            cy.then(async () => {
                const result = await checkFamilyStatuses(mockLoadingStates, mockUser, mockFamily, mockEventDetails);

                expect(result).to.have.length(2); // Should only have user + 1 unique family member
                const janeEntries = result.filter(member => member.id === 456);
                expect(janeEntries).to.have.length(1);
            });
        });
    });

    // EventDetails Component for testing
    const EventDetails = ({eventId, eventDetails, setEventDetails, loadingStates, ...props}) => {
        const adjustTimeString = React.useCallback((event) => {
            let startFormatted = format(new Date(event?.start_datetime), "ccc MM/dd/yyyy");
            let endFormatted = format(new Date(event?.end_datetime), "ccc MM/dd/yyyy");
            if(startFormatted === endFormatted) {
                startFormatted = format(new Date(event?.start_datetime), "ccc MM/dd/yyyy h:mm aa");
                endFormatted = format(new Date(event?.end_datetime), "h:mm aa");
                return `${startFormatted} - ${endFormatted}`;
            } else {
                startFormatted = format(new Date(event?.start_datetime), "ccc MM/dd/yyyy h:mm aa");
                endFormatted = format(new Date(event?.end_datetime), "ccc MM/dd/yyyy h:mm aa");
                return `${startFormatted} - ${endFormatted}`;
            }
        }, []);

        const variantPriceArray = React.useCallback((event) => {
            let priceArray = [];
            if(event?.variants?.length > 0) {
                event.variants.forEach((variant) => {
                    priceArray.push(parseFloat(variant.price));
                });
                priceArray.sort((a, b) => a - b);
            }
            return priceArray;
        }, []);

        React.useEffect(() => {
            const getEventBasics = async() => {
                loadingStates.current.eventDetails = true;
                try {
                    let response = await Events.publicGet({id: eventId});
                    if(response?.data?.events) {
                        let event = response?.data?.events?.filter((event) => event.id === eventId)[0];
                        if(event.images.length > 0) event.images.forEach((image) => image.url = image.preview_url);
                        event.timeString = adjustTimeString(event);
                        event.eventPricesOrder = variantPriceArray(event);
                        event.advancedDetails = await getAdvancedEventDetails();
                        setEventDetails(event);
                        loadingStates.current.eventDetails = false;
                    }
                } catch(ex) {
                    console.error(ex);
                    loadingStates.current.eventDetails = "ERROR";
                }
            };

            const getAdvancedEventDetails = async() => {
                try {
                    let response = await Events.getSingle({id: eventId});
                    if(response?.data) {
                        let neededDetails = {
                            users: response.data[0].users,
                            custom_fields: response.data[0].custom_fields
                        };
                        return neededDetails;
                    }
                } catch(ex) {
                    console.error(ex);
                    loadingStates.current.eventDetails = "ERROR";
                }
            };

            if(loadingStates.current.eventDetails === false) getEventBasics();
        }, [eventId, adjustTimeString, variantPriceArray, setEventDetails]);

        return (
            <div className={styles["event-details-wrapper"]}>
                {eventDetails &&
                    <div>
                        <div className={styles["detail-pair"]} data-cy="event-status-details">
                            {(eventDetails?.event_status_id === 1 || eventDetails?.event_status_id === 3 || eventDetails?.event_status_id === 4 || eventDetails?.event_status_id === 6 || eventDetails?.event_status_id===8) &&
                                <h6>At this time, registration for this event is not available due to its {eventDetails?.event_status_name?.toLowerCase()} status.  If you have any questions, don't hesitate to reach out.</h6>
                            }
                            <h2 data-cy="register-event-name">{eventDetails?.name}</h2>
                            {eventDetails?.event_status_id === 5 &&
                                <h6 data-cy="event-details-private">This is a private event
                                    {eventDetails.requires_registration ===1 &&
                                        <span>
                                            and can only be registered for via a direct invitation.
                                        </span>
                                    }
                                </h6>
                            }
                        </div>
                        <div className={styles["detail-pair"]} data-cy="event-image-viewer">
                            <ImageViewer
                                images={eventDetails?.images}
                                largeImgMax={"200px"}
                                thumbMax={"50px"}
                                thumbLimit={4}
                            />
                        </div>
                        <div className={styles["detail-pair"]} data-cy="event-details-when">
                            <label>When</label>
                            {eventDetails.start_datetime &&
                                <p>{eventDetails.timeString}</p>
                            }
                        </div>
                        <div className={styles["detail-pair"]} data-cy="event-details-where">
                            <label>Where</label>
                            <p>{eventDetails.location_name}</p>
                        </div>
                        <div className={styles["detail-pair"]} data-cy="event-details-age-requirement">
                            <label>Age Requirement</label>
                            <p data-cy="event-details-ages">
                                {eventDetails.min_age > 0 && eventDetails.max_age > 0 &&
                                    `Participant must be between ${eventDetails?.min_age} - ${eventDetails?.max_age} years old to register`
                                }{eventDetails.min_age > 0  && !eventDetails.max_age &&
                                    `Participant must be at least ${eventDetails?.min_age} years old to register`
                                }{!eventDetails.min_age && eventDetails.max_age > 0 &&
                                    `Participant must be no more than ${eventDetails?.max_age} years old to register`
                                }{!eventDetails.min_age && !eventDetails.max_age &&
                                    `There is no age requirement.`
                                }
                            </p>
                        </div>
                        {eventDetails.requires_membership === 1 &&
                            <div className={styles["detail-pair"]} data-cy="event-details-membership">
                                <label>Requires Membership</label>
                                <p>A membership is required to register for this event.</p>
                            </div>
                        }
                        <div className={styles["detail-pair"]} data-cy="event-details-registration">
                            <label>Requires Registration</label>
                            {eventDetails.requires_registration ===1 ?
                                <p>Registration is required for this event</p>
                                :
                                <p>Registration is not required for this event.</p>
                            }
                        </div>
                        <div className={styles["detail-pair"]} data-cy="event-details-description">
                            <label>What</label>
                            <div dangerouslySetInnerHTML={{__html: eventDetails?.description}} />
                        </div>
                        <div className={styles["detail-pair"]} data-cy="event-details-price">
                            <label>Price</label>
                            <p>
                                {eventDetails?.eventPricesOrder?.length > 1 ?
                                    <span>From ${eventDetails?.eventPricesOrder[0]} to ${eventDetails?.eventPricesOrder[eventDetails?.eventPricesOrder?.length-1]}</span>
                                    :
                                    <span>${eventDetails?.eventPricesOrder[0]}</span>
                                }
                            </p>
                        </div>
                    </div>
                }
            </div>
        );
    };

    describe('EventDetails Component Tests', () => {
        let eventFixtures;

        before(() => {
            cy.fixture('Events/event_registration_conditions.json').then((data) => {
                eventFixtures = data;
            });
        });

        beforeEach(() => {
            // Mock user in localStorage for component mounting
            window.localStorage.setItem('user', JSON.stringify({
                menu: [],
                roles: [],
                profile: { id: 123, first_name: 'Test', last_name: 'User' },
                token: "bearer test-token"
            }));
            cy.viewport(1200, 800);
        });

        it('should render EventDetails with variant event data correctly', () => {
            const variantEvent = eventFixtures.event_with_variants;

            // Intercept API calls with fixture data
            cy.intercept('POST', '/api/public/event', variantEvent.publicGet).as('getPublicEvent');
            cy.intercept('GET', '/api/event/7107', variantEvent.getSingle).as('getSingleEvent');

            const mockLoadingStates = { current: { eventDetails: false } };
            const mockSetEventDetails = cy.stub();

            cy.mount(
                <EventDetails
                    eventId={7107}
                    setEventDetails={mockSetEventDetails}
                    eventDetails={null}
                    loadingStates={mockLoadingStates}
                />
            );

            cy.wait('@getPublicEvent');
            cy.wait('@getSingleEvent');

            // Test event name
            cy.get('[data-cy="register-event-name"]')
                .should('contain', 'Variant Event');

            // Test event status details
            cy.get('[data-cy="event-status-details"]')
                .should('exist');

            // Test image viewer
            cy.get('[data-cy="event-image-viewer"]')
                .should('exist');

            // Test when section
            cy.get('[data-cy="event-details-when"]')
                .should('exist')
                .within(() => {
                    cy.get('label').should('contain', 'When');
                    cy.get('p').should('exist');
                });

            // Test where section
            cy.get('[data-cy="event-details-where"]')
                .should('exist')
                .within(() => {
                    cy.get('label').should('contain', 'Where');
                    cy.get('p').should('contain', 'Arcade');
                });

            // Test age requirement section
            cy.get('[data-cy="event-details-age-requirement"]')
                .should('exist')
                .within(() => {
                    cy.get('label').should('contain', 'Age Requirement');
                    cy.get('[data-cy="event-details-ages"]')
                        .should('contain', 'There is no age requirement');
                });

            // Test registration requirement
            cy.get('[data-cy="event-details-registration"]')
                .should('exist')
                .within(() => {
                    cy.get('label').should('contain', 'Requires Registration');
                    cy.get('p').should('contain', 'Registration is required for this event');
                });

            // Test description section
            cy.get('[data-cy="event-details-description"]')
                .should('exist')
                .within(() => {
                    cy.get('label').should('contain', 'What');
                    cy.get('div').should('exist');
                });

            // Test price section
            cy.get('[data-cy="event-details-price"]')
                .should('exist')
                .within(() => {
                    cy.get('label').should('contain', 'Price');
                    cy.get('p').should('exist');
                });
        });

        it('should render EventDetails with user event data correctly', () => {
            const userEvent = eventFixtures.event_with_users;

            // Intercept API calls with fixture data
            cy.intercept('POST', '/api/public/event', userEvent.publicGet).as('getPublicEvent');
            cy.intercept('GET', '/api/event/7047', userEvent.getSingle).as('getSingleEvent');

            const mockLoadingStates = { current: { eventDetails: false } };
            const mockSetEventDetails = cy.stub();

            cy.mount(
                <EventDetails
                    eventId={7047}
                    setEventDetails={mockSetEventDetails}
                    eventDetails={null}
                    loadingStates={mockLoadingStates}
                />
            );

            cy.wait('@getPublicEvent');
            cy.wait('@getSingleEvent');

            // Test event name for Sky Flyers
            cy.get('[data-cy="register-event-name"]')
                .should('contain', 'Sky Flyers');

            // Test location for Sky Flyers
            cy.get('[data-cy="event-details-where"]')
                .within(() => {
                    cy.get('p').should('contain', 'Basketball Court 4');
                });

            // Test age requirements for Sky Flyers (has min and max age)
            cy.get('[data-cy="event-details-age-requirement"]')
                .within(() => {
                    cy.get('[data-cy="event-details-ages"]')
                        .should('contain', 'Participant must be between 5 - 10 years old to register');
                });
        });

        it('should handle private event status correctly', () => {
            // Create a modified fixture for private event
            const privateEventData = {
                ...eventFixtures.event_with_variants.publicGet,
                data: {
                    ...eventFixtures.event_with_variants.publicGet.data,
                    events: [{
                        ...eventFixtures.event_with_variants.publicGet.data.events[0],
                        event_status_id: 5,
                        event_status_name: 'Private',
                        requires_registration: 1
                    }]
                }
            };

            cy.intercept('POST', '/api/public/event', privateEventData).as('getPrivateEvent');
            cy.intercept('GET', '/api/event/7107', eventFixtures.event_with_variants.getSingle).as('getSingleEvent');

            const mockLoadingStates = { current: { eventDetails: false } };
            const mockSetEventDetails = cy.stub();

            cy.mount(
                <EventDetails
                    eventId={7107}
                    setEventDetails={mockSetEventDetails}
                    eventDetails={null}
                    loadingStates={mockLoadingStates}
                />
            );

            cy.wait('@getPrivateEvent');
            cy.wait('@getSingleEvent');

            // Test private event message
            cy.get('[data-cy="event-details-private"]')
                .should('exist')
                .should('contain', 'This is a private event')
                .should('contain', 'can only be registered for via a direct invitation');
        });

        it('should handle events with no registration requirement', () => {
            const noRegEvent = {
                ...eventFixtures.event_with_variants.publicGet,
                data: {
                    ...eventFixtures.event_with_variants.publicGet.data,
                    events: [{
                        ...eventFixtures.event_with_variants.publicGet.data.events[0],
                        requires_registration: 0
                    }]
                }
            };

            cy.intercept('POST', '/api/public/event', noRegEvent).as('getNoRegEvent');
            cy.intercept('GET', '/api/event/7107', eventFixtures.event_with_variants.getSingle).as('getSingleEvent');

            const mockLoadingStates = { current: { eventDetails: false } };
            const mockSetEventDetails = cy.stub();

            cy.mount(
                <EventDetails
                    eventId={7107}
                    setEventDetails={mockSetEventDetails}
                    eventDetails={null}
                    loadingStates={mockLoadingStates}
                />
            );

            cy.wait('@getNoRegEvent');
            cy.wait('@getSingleEvent');

            // Test no registration required message
            cy.get('[data-cy="event-details-registration"]')
                .within(() => {
                    cy.get('p').should('contain', 'Registration is not required for this event');
                });
        });

        it('should handle events with membership requirement', () => {
            const membershipEvent = {
                ...eventFixtures.event_with_variants.publicGet,
                data: {
                    ...eventFixtures.event_with_variants.publicGet.data,
                    events: [{
                        ...eventFixtures.event_with_variants.publicGet.data.events[0],
                        requires_membership: 1
                    }]
                }
            };

            cy.intercept('POST', '/api/public/event', membershipEvent).as('getMembershipEvent');
            cy.intercept('GET', '/api/event/7107', eventFixtures.event_with_variants.getSingle).as('getSingleEvent');

            const mockLoadingStates = { current: { eventDetails: false } };
            const mockSetEventDetails = cy.stub();

            cy.mount(
                <EventDetails
                    eventId={7107}
                    setEventDetails={mockSetEventDetails}
                    eventDetails={null}
                    loadingStates={mockLoadingStates}
                />
            );

            cy.wait('@getMembershipEvent');
            cy.wait('@getSingleEvent');

            // Test membership requirement section appears
            cy.get('[data-cy="event-details-membership"]')
                .should('exist')
                .within(() => {
                    cy.get('label').should('contain', 'Requires Membership');
                    cy.get('p').should('contain', 'A membership is required to register for this event');
                });
        });

        it('should display correct price ranges for events with multiple variants', () => {
            const variantEvent = eventFixtures.event_with_variants;

            cy.intercept('POST', '/api/public/event', variantEvent.publicGet).as('getPublicEvent');
            cy.intercept('GET', '/api/event/7107', variantEvent.getSingle).as('getSingleEvent');

            const mockLoadingStates = { current: { eventDetails: false } };
            const mockSetEventDetails = cy.stub();

            cy.mount(
                <EventDetails
                    eventId={7107}
                    setEventDetails={mockSetEventDetails}
                    eventDetails={null}
                    loadingStates={mockLoadingStates}
                />
            );

            cy.wait('@getPublicEvent');
            cy.wait('@getSingleEvent');

            // Test price range for multiple variants (should show "From $X to $Y")
            cy.get('[data-cy="event-details-price"]')
                .within(() => {
                    cy.get('p').should('exist');
                    // The component should show price range since there are multiple variants
                });
        });
    });


});
