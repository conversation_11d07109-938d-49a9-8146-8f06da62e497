/*eslint-disable*/

let baseUrl = "https://portal-qa.impactathleticsny.com/p/";
let adminUserName = Cypress.env("impact_admin_user");
let password = Cypress.env("login_password")

describe('it will make a cash transaction and attempt an advanced refund from the pos', {testIsolation: false, scrollBehavior: "center"}, ()=>{
    let orderNumber;
    let orderLast;
    let transactionNumber;
    let local;

    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, adminUserName, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach(()=>{
        cy.viewport(1920, 1080);
    });

    context("it will make a cash payment with multiple items",()=>{
        it("will visit the POS",()=>{
            cy.visit(`${baseUrl}pos/5`);
            cy.wait(500);
        })

        it("will add multiple items to the order",()=>{
            cy.get('[data-cy="product-card"]')
                .contains('Cheese Stick')
                .click({force:true});
            cy.wait(1500);
            cy.get('[data-cy="product-card"]')
                .contains('Cheese Pizza Slice')
                .click({force:true});
            cy.get('[data-cy="order-number"]')
                .invoke('text').as('orderNumber')
            cy.get('@orderNumber').then((text)=>{
                orderNumber = text
            });
            cy.get('.item-name')
                .children()
                .should('have.length', 2)
        });

        it("will checkout with a cash transaction",()=>{
            cy.get('[data-cy="pos-button-checkout"]')
                .click();
            cy.get('[data-cy="payment-type-2"]')
                .click();
            cy.get('[data-cy="cash-payment-button-group"] > :nth-child(2)')
                .click();
            cy.get('[data-cy="details-add-to-cart"]')
                .click();
            cy.get('[data-cy="success-title"]')
                .invoke('text')
                .should('include', 'Payment Successful');
            cy.get('[data-cy="success-order-number"]')
                .invoke('text')
                .should('include', orderNumber);
            cy.get('[data-cy="success-transaction-number"]')
                .invoke('text').as('transactionNumber');
            cy.get('@transactionNumber').then((text)=>{
                let test = text.split("#")
                transactionNumber = test[1].trim()
            });
            cy.get('[data-cy="payment-cancel-button"]')
                .click();
            cy.stubPrint();
        })
    });

    context("it will navigate to the orders in the POS",()=>{

        it("will open the order modal and wait for the orders to populate & refund",()=>{
            cy.get('.modal-body')
                .should('not.exist')
            cy.intercept('POST', "/api/order").as('waitOrders')
            cy.get('[data-cy="pos-button-orders"]')
                .click();
            cy.wait('@waitOrders');
            cy.get('.modal-body')
                .should('exist')
            let orderLast = orderNumber.substr(orderNumber.length - 3);
            cy.log(orderLast)
            cy.get('tr')
                .contains(orderLast)
                .parent()
                .within(()=>{
                    cy.get('button')
                        .click();
                })
            })
            
        it("will start the advanced refund by item - will go through the wizard",()=>{
            cy.get('[data-cy="advanced-refund-btn"]')
                .click();
            //select refund by items
            cy.get('[data-cy="adv-refund-items-btn"]')
                .click();
            cy.get('[data-cy="refund-next"]')
                .click();
            //removed one of the two checked items for refund
            cy.get('[data-cy="advanded-refund-item"]')
                .contains('Cheese Stick')
                .parent()
                .parent()
                .parent()
                .within(()=>{
                    cy.get('[data-cy="select-item-check-item-refund"]')
                        .click()
                })
            cy.get('[data-cy="advanded-refund-item"]')
                .contains('Cheese Pizza Slice')
                .parent()
                .parent()
                .parent()
                .within(()=>{
                    cy.get('[data-cy="adv-refund-items-max-refund"]')
                        .contains('Max Refund')
                        .invoke('text')
                        .then(($text)=>{
                            cy.wrap($text).as('maxRefund')
                        })
                })
            cy.wait(1000)
            cy.get('[data-cy="refund-next"]')
                .click()
            //add a memo
            cy.get('#refund-memo')
                .click()
                .type("Because Cypress said so");
            cy.get('[data-cy="refund-next"]')
                .click();
            cy.get('@maxRefund').then((text)=>{
                let split=text.split(" ");
                let amount = split[2];
                cy.get('[data-cy="adv-refund-summary-amount"]')
                    .invoke('text')
                    .should('include', amount)
            })
            cy.get('[data-cy="refund-submit"]')
                .click();
            cy.get('[data-cy="adv-refund-success-title"]')
                .invoke('text')
                .should('include', "Refund Successful")
            cy.get('@maxRefund').then((text)=>{
                let split=text.split(" ");
                let amount = split[2];
                cy.get('[data-cy="refund-sum-amount"]')
                    .invoke('text')
                    .should('include', amount)
            })
        })

    })


    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
})