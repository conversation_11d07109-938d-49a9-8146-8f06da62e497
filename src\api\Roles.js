import Request from './Api';

const getAllRoles = async() => {
	//let mockdata = test();

    return (
        Request({
            url: "/user/roles",
            //data: {get_all:true},
            //test: {data:mockdata} // send mock data to simulate an api call
        })
    );
}

// sets up mock data
/*const test = () => {
    return [
		{
			id: 1,
			name: "Admin",
			description: "Super-powered user"
		},
		{
			id: 51,
			name: "Store Manager",
			description: "Manages the store"
		},
		{
			id: 52,
			name: "Store Clerk",
			description: "Works at the store"
		},
		{
			id: 99,
			name: "Dev-Ops",
			description: "Nobody knows what they do but it sounds cool"
		},
	];
}*/


const Roles = {
	getAllRoles //, create, update, delete, etc. ...
}
  
export default Roles;