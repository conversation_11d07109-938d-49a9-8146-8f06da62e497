import React from 'react';
import {getDayNames, showTasks} from './Common.js';
import './Scheduler.css';

// create a calendar array
const calendarArray = (year, month, tasks) =>{    

    if (!month) month = new Date().getMonth() +1;
    if (!year) year = new Date().getFullYear();

    const monthStart = new Date(year, month-1); // first day of month date object
    const monthEnd = new Date(year, month, 0); // last day of month date object
    const calStartDay = new Date(monthStart);

    if (!tasks) tasks=[];

    let weeksArr = {
        header: {
            first_day: monthStart,
            last_day: monthEnd
        },
        weeks:[], // will fill with sub arrays for each week
        tasks:[] // will fill with sub arrays for each event within a week
    }; 
    
    let week = 1;  
    let dayCount = 0;
    let currDay;
    let dayIndex="";
    let taskArr=[];

    calStartDay.setDate(monthStart.getDate() - monthStart.getDay()); // subtract day index of first of month from it's date to get Sunday of first week

    while (week < 7) {
        currDay=new Date(calStartDay);
        if (dayCount===0) {
            if (currDay > monthEnd) break; // too far into next month, gtfo
            weeksArr.weeks.push([]);
            weeksArr.tasks.push([]);
        }
        weeksArr.weeks[week-1].push({
            date:currDay,
            tasks:[]
        });

        dayIndex=`${currDay.getFullYear()}/${(("0" + (currDay.getMonth()+1)).slice(-2))}/${("0" + currDay.getDate()).slice(-2)}`;
        taskArr=weeksArr.weeks[week-1][weeksArr.weeks[week-1].length-1].tasks;

        taskArr.push(...(tasks[dayIndex] || [])); // adds tasks in the week
        taskArr.push(...(tasks["everyday"] || [])); // adds repeatable tasks in the week

        dayCount++;
        calStartDay.setDate(calStartDay.getDate() + 1);
        if (dayCount === 7) {
            week++;
            dayCount = 0
        }
    }

    return weeksArr;

}


// create a montly view scheduler
const Month = (props) => {
    const calendar=calendarArray(props.year, props.month, props.tasks);
    
    return (
        <div className="scheduler">
            {getDayNames().map((day,i)=>{
                return <span className="day-name" key={`spdays${i}`}>{day.text.substr(0,3)}</span>
            })}
            
            {calendar.weeks.map((week,i) => {                
                return week.map((day,j) => {
                    if (day.date <= calendar.header.last_day && day.date >= calendar.header.first_day){
                        return (
                            <div className={`day${day.date.setHours(0,0,0,0)===new Date().setHours(0,0,0,0)?" day-today":""}`} key={`day${i+"-"+j}`}>
                                <span key={`sp${i+"-"+j}`}>{day.date.getDate()}</span>
                                { showTasks(day.tasks, day.date) }
                            </div>
                        )
                    } else {
                        return <div className="day day-disabled" key={`day${i+"-"+j}`}><span key={`sp${i+"-"+j}`}>{day.date.getDate()}</span></div>
                    }
                });
            })}
        </div>
    );
}

export default Month;