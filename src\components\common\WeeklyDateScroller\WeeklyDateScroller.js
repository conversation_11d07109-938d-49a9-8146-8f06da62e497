import React from 'react';
import { Form, Button, ButtonGroup } from 'react-bootstrap';
import { format, startOfISOWeek } from 'date-fns'; 
import DatePicker from "react-datepicker";

export const WeeklyDateScroller = ({
    backDisabled=false,
    smallScreen = false,
    selectedDate=null,
    onDateBack,
    onDateNext,
    onChangeDate,
    rangeStartD,
    rangeEndD,
    isInRange,
    ...props
})=>{

    return(
          <Form.Row className="service-selection datepicker">
                <ButtonGroup>
                    <Button variant="light"
                        data-cy="date-range-back"
                        disabled={backDisabled}
                        className="datepicker-back-next back"
                        onClick={onDateBack}
                    >
                        <i className="far fa-angle-left"></i>
                    </Button>
                    <DatePicker 
                        dateFormat="MM/dd/yyyy"
                        minDate={smallScreen ? new Date() : startOfISOWeek(new Date())}
                        maxDate={new Date(new Date().getFullYear()+1,12,31)}
                        showMonthDropdown
                        showYearDropdown
                        selected={selectedDate}
                        onChange={onChangeDate}
                        customInput={
                            <Button variant="light" className="datepicker-calendar" type="button" data-cy="date-range-picker">
                                {format(rangeStartD, "MM/dd/yyyy")}
                                {' - '}
                                {format(rangeEndD, "MM/dd/yyyy")}
                            </Button>
                        }
                        dayClassName={(date) => isInRange(date) ? "react-datepicker__day--selected" : ""}
                    />
                    <Button variant="light"
                        data-cy="date-range-forward"
                        className="datepicker-back-next next"
                        onClick={onDateNext}
                    >
                        <i className="far fa-angle-right"></i>
                    </Button>
                </ButtonGroup>
            </Form.Row>
    )
}