import React from 'react';
import { Form, Button, ButtonGroup } from 'react-bootstrap';
import { format, startOfISOWeek } from 'date-fns';
import DatePicker from "react-datepicker";
import styles from './WeeklyDateScroller.module.scss';

export const WeeklyDateScroller = ({
    backDisabled=false,
    smallScreen = false,
    selectedDate=null,
    onDateBack,
    onDateNext,
    onChangeDate,
    rangeStartDate,
    rangeEndDate,
    isInRange,
    ...props
})=>{

    return(
          <Form.Row className={styles.datepicker}>
                <ButtonGroup className={styles.buttonGroup}>
                    <Button variant="light"
                        data-cy="date-range-back"
                        disabled={backDisabled}
                        className={`${styles.datepickerBackNext} ${styles.back}`}
                        onClick={onDateBack}
                    >
                        <i className="far fa-angle-left"></i>
                    </Button>
                    <DatePicker 
                        dateFormat="MM/dd/yyyy"
                        minDate={smallScreen ? new Date() : startOfISOWeek(new Date())}
                        maxDate={new Date(new Date().getFullYear()+1,12,31)}
                        showMonthDropdown
                        showYearDropdown
                        selected={selectedDate}
                        onChange={onChangeDate}
                        customInput={
                            <Button variant="light" className={styles.datepickerCalendar} type="button" data-cy="date-range-picker">
                                {format(rangeStartDate, "MM/dd/yyyy")}
                                {' - '}
                                {format(rangeEndDate, "MM/dd/yyyy")}
                            </Button>
                        }
                        dayClassName={(date) => isInRange(date) ? "react-datepicker__day--selected" : ""}
                    />
                    <Button variant="light"
                        data-cy="date-range-forward"
                        className={`${styles.datepickerBackNext} ${styles.next}`}
                        onClick={onDateNext}
                    >
                        <i className="far fa-angle-right"></i>
                    </Button>
                </ButtonGroup>
            </Form.Row>
    )
}