import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Combo = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Can this discount be used in combination with others?</span>
                <Form.Row>
                    <Form.Check 
                        type="radio"
                        id="combinable-1"
                        label="Yes"
                        name="combinable"
                        value={1}
                        checked={coupon.combinable===1}
                        onChange={onChangeInput}
                        isInvalid={!!errors.combinable}
                        className="form-radio"
                    />
                    <Form.Check 
                        type="radio"
                        id="combinable-0"
                        label="No"
                        name="combinable"
                        value={0}
                        checked={coupon.combinable===0}
                        onChange={onChangeInput}
                        isInvalid={!!errors.combinable}
                        className="form-radio"
                    />
                </Form.Row>
                <div className={`err ${!!errors.combinable ? "" : "hidden"}`}>
                    {errors.combinable}
                </div>
            </Col>
        </Row>
    );
}

export default Combo;