import React,{ useState, useEffect, useCallback } from 'react';
import { ToggleButtonGroup, ToggleButton} from 'react-bootstrap';

import Addons from '../Addons';

const Variants =  props => {
    const {change} = props;
    const [selected, setSelected] = useState();

    /*
    // selects the fist variant by default
    useEffect(() => {
        if (props?.product_variants?.[0]) setSelected(props.product_variants[0]);
    },[props?.product_variants]);
    */

    useEffect(() => {
        if (props.selected){
            const variant = props?.product_variants.find(item=>item.id===props.selected);
            if (!variant) setSelected(null);
        }
    },[props.selected, props?.product_variants]);

    useEffect(() => {
        return () => {
            setSelected(null);
        }
    }, []);

    const planClickHandler = useCallback((e,variant) => {
        e.preventDefault();
        variant.addons = [];
        setSelected(variant);
        if (change) change(variant);
    },[change]);

    const addonClickHandler = useCallback((addons) => {
        selected.addons = addons;
        if (change) change(selected);
    },[change, selected]);

    return (
        <>
            {props?.product_variants?.length>1 &&
                <>
                    <ToggleButtonGroup type="radio" name={`plan-${props.id}`} aria-label={`Choose a plan`} default_value={selected?.id || null} value={selected?.id || null}>
                        {props.product_variants?.map((variant, j) => (
                            <ToggleButton key={`product-step3-${props.id}-plan-${j}`} name={`plan-${j}`} variant="light" disabled={props.submitting} value={variant.id} onClick={e=>planClickHandler(e,{...variant,product_id:props.id})}>
                                {`${variant.name ? `${+variant.price>0?`$${variant.price} ${variant.name.trim()}`:"Free"}` : `${props.name} ${j+1}`}`}
                            </ToggleButton>
                        ))}
                    </ToggleButtonGroup>
                    {selected && <Addons variant_id={selected.id} change={addonClickHandler}/>}
                </>
            }
        </>
    );
}

export default Variants;