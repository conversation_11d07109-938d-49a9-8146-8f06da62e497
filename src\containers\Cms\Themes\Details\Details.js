import React,{useEffect, useState, Suspense} from 'react';
import { useParams, useLocation, Link  } from "react-router-dom";
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import {Container,Row,Col,Card,Breadcrumb,ListGroup} from 'react-bootstrap';
import SubHeader from '../../../../components/common/SubHeader';

import APICms from '../../../../api/Cms';

import BasicInfo from '../BasicInfo';
import Styles from '../Styles';

import './Details.scss';

const Details = (props) => {    
    const location = useLocation();
    const { id } = useParams();

    const [themeInfo,setThemeInfo]=useState();

	useEffect(() => {

        const _getTheme = async () => {
            try {
                let res=await APICms.themes.get({id:id});
                if (res.data.length>0 && mounted) {
                    if (Array.isArray(res.data)) res.data=res.data[0];
                    setThemeInfo(res.data);
                    setPagePart(
                        <Suspense fallback={             
                            <SkeletonTheme color="#e0e0e0">
                                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                                <Skeleton height={12} count={5} />
                            </SkeletonTheme>
                        }>
                            <BasicInfo theme={res.data} theme_id={res.data.id} referer={location.pathname} />
                        </Suspense>
                    );
                }
            } catch (e){
                console.error(e);
            }
        }

        let mounted = true;
        _getTheme();

        return () => {
            mounted = false;
        }
	}, [id, location.pathname]);
    
    const [pagePart,setPagePart]=useState(
        <Suspense fallback={             
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
            </SkeletonTheme>
        }>
            
        </Suspense>
    );

    const loadPagePartHandler= (e) => {
        let component;
        switch (e.target.hash.substr(1)){
            case "Styles":
                component=<Styles theme={themeInfo.content} theme_id={themeInfo.id} referer={location.pathname} />;
                break;
            case "BasicInfo":
            default:
                component=<BasicInfo theme={themeInfo} theme_id={themeInfo.id} referer={location.pathname} />;
                break;
        }
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {component}
            </Suspense>
        );
    }

    if (!themeInfo) return (
        <Container fluid>
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
                <Skeleton height={30} style={{marginBottom:"1rem",marginTop:"2rem"}} />
                <Skeleton height={12} count={10} />
            </SkeletonTheme>
        </Container>
    );

    return (themeInfo &&
        <Container fluid>
            <SubHeader items={[
                {linkAs:Link,linkProps:{to:"/p/home"},text:"Home"},
                {linkAs:Link,linkProps:{to:"/p/themes/dashboard"},text:"Themes Dashboard"},
                {text:themeInfo.name}
            ]} />

            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <Row>
                            <Col sm="auto" className="order-1 order-lg-2">
                                <ListGroup className="profileMenu" variant="flush">
                                    <ListGroup.Item action href="#BasicInfo" onClick={loadPagePartHandler}>
                                        <i className="far fa-border-style-alt"></i> Edit Theme
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#Styles" onClick={loadPagePartHandler}>
                                        <i className="far fa-paint-roller"></i> Styles
                                    </ListGroup.Item>
                                </ListGroup>
                            </Col>
                            <Col className="order-2 order-lg-1">
                                {pagePart /*this is where the magic is happening :-O */ }
                            </Col>
                        </Row>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Details;