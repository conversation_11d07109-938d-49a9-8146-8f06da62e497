import React, { useState, useEffect, useCallback } from 'react';
import { Form, Row, Col, Modal } from 'react-bootstrap';
import { Button } from 'react-bootstrap';

import APICms from '../../../../../../api/Cms';
import Stack from '../../../../../../components/common/Stack';

export const AI = (props) => {    
    const [loading, setLoading] = useState(false);
    const [aiResult, setAiResult] = useState();
    const [show, setShow] = useState(false);
    const [keywords, setKeywords] = useState('');

    useEffect(() => {
        if (aiResult){
            console.log(aiResult);
        }
    }, [aiResult]);

    useEffect(() => {
        return () => {
            setLoading(false);
            setAiResult(null);
            setKeywords('');
            setShow(false);
        }
    }, []);

    const clickHandler = useCallback(e => {
        e.preventDefault();
        setLoading(true);
        APICms.ai({type: "create_page", keywords}).then(res => {
            console.log(res);
            if (res.data) setAiResult(res.data);
            setShow(false);
            setLoading(false);
        });
    }, [keywords]);

    return (
        <>
            <Stack direction="horizontal" gap={1} className="mb-2">
                <Button variant="light" href="#!" onClick={(e)=>setShow(true)} className="mt-1" disabled={loading}>
                    <i className="far fa-robot"/> Create with AI
                </Button>
                {loading &&
                    <span>Generating content...</span>
                }
            </Stack>
            {!loading &&
                <Modal show={show} onHide={() => setShow(false)} size="md" centered>
                    <Modal.Header closeButton />
                    <Modal.Body>
                        <Row>
                            <Col sm={12}>
                                <Form.Group className="mb-3">
                                    <Form.Label>Sections <small> / separated by commas ","</small></Form.Label>
                                    <Form.Control placeholder="about us, faq, contact us" defaultValue={keywords} onBlur={(e)=>setKeywords(e.target.value)} disabled={loading} />
                                </Form.Group>
                            </Col>
                            <Col sm={12}>
                                <Button variant="primary" href="#!" onClick={clickHandler} disabled={loading}>Generate</Button>
                            </Col>
                        </Row>
                    </Modal.Body>
                </Modal>
            }
        </>
    );
}