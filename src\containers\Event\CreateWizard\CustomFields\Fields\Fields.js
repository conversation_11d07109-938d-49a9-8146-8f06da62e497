import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Container, Row, Col, Form, Button } from 'react-bootstrap';

import styles from './Fields.module.scss';

const types = [ "input", "select" ];

const Fields = (props) => {
    const {change<PERSON><PERSON><PERSON><PERSON><PERSON>, remove<PERSON><PERSON><PERSON><PERSON><PERSON>, newOption<PERSON><PERSON><PERSON>, changeOptionHandler, removeOption<PERSON>andler} = props;

    return (
        <Container className={styles["field-wrapper"]}>
            <Row>
                <Col sm={5} md={1}>
                    <Form.Label>Required</Form.Label>
                    <Form.Group controlId={"required-" + props.id}>
                        <Form.Check checked={props.required === 1} onChange={e => changeFieldHandler(props.id, "required", e.target.checked ? 1 : 0)} />
                    </Form.Group>
                </Col>
                <Col sm={6} md="5" lg="2">
                    <Form.Group controlId={"type-" + props.id}>
                        <Form.Label>Field Type</Form.Label>
                        <Form.Control custom as="select" name="type" value={props.custom_field_type || "input"} onChange={e => changeFieldHandler(props.id, "custom_field_type", e.target.value)}>
                            {types.map( (type, j) => {
                                return(
                                    <option value={type} key={`type-${type}-${j}`}>
                                        { type === "input" ? "Text Box" : "Dropdown List" }
                                    </option>
                                    );
                            })}
                        </Form.Control>
                    </Form.Group>
                </Col>
                <Col sm="12" md="6" lg="8">
                    <Form.Group controlId={"name-" + props.id}>
                        <Form.Label>Label / Placeholder</Form.Label>
                        <Form.Control required type="input" defaultValue={props.placeholder_text || ""} onChange={e => changeFieldHandler(props.id, "placeholder_text", e.target.value)}/>
                    </Form.Group>
                </Col>
                <Col sm={1} md="auto" className="d-flex justify-content-center align-items-center">
                    <Button className="btn rounded my-0 me-0 mt-3" variant="outline-light" onClick={e=>removeFieldHandler(props.id)}><i className="far fa-trash-alt m-0"/></Button>
                </Col>
            </Row>
            <Row style={{display: props.custom_field_type === "select" ? null : "none" }}>
                <Col sm="12">
                    {props.options?.map( (option, j) => {
                        return (
                            <Row key={`option-${option}-${j}`}>
                                <Col sm={5} md={1} className="d-sm-none d-lg-block" />
                                <Col lg="4" className="mt-2">
                                    <Form.Label>Option #{j+1}</Form.Label>
                                    <Form.Control required type="input" value={props.options[j].text}
                                    onChange={e => changeOptionHandler(props.id, j, "text", e.target.value)}
                                    />
                                </Col>
                                <Col sm="auto" className="d-flex justify-content-center align-items-end">
                                    <Button className="btn rounded my-0 me-0" variant="outline-light" onClick={e=>removeOptionHandler(props.id, j)} ><i className="far fa-trash-alt m-0"/></Button>
                                </Col>
                            </Row>
                        );
                    })}
                </Col>
                <Col sm="12" className="mt-2">
                    <Row>
                        <Col sm={5} md={1} className="d-sm-none d-lg-block" />
                        <Col sm="auto">
                            <Button variant="light" onClick={e=>newOptionHandler(props.id)}>Add Option</Button>
                        </Col>
                    </Row>                            
                </Col>
            </Row>
        </Container>
    );
}

export default Fields;