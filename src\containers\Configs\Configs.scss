@import '../../assets/css/scss/variables';
@import '../../assets/css/scss/mixins';
@import '../../assets/css/scss/themes';

.sb-config-wrapper{
    .btn-basic{
        @include basic-button;
    }
    .select-company{
        @include flex-row-space-between;
        input{
            min-width: 350px;
        }
    }
}
.config-delete-modal{
    @include flex-column-center;
    button{
        @include basic-button;
        margin-top: 1rem;
    }
}
.config-edit-modal{
    p{
        margin-left: 1rem;
    }
    .border-box{
        border: 1px solid $primary-color;
        border-radius: 5px;
        padding: 8px;
        margin-bottom: 1rem;
    }
    .two-col{
        @include basic-flex-row;
        p:first-child{
            margin-right: 1rem;
            padding-right: .5rem;
            border-right: 1px solid $divider-color;
            font-weight: 600;
        }
        br{
            margin-bottom: 3px;
        }
    }
}
.config-title-row{
    @include flex-row-space-between;
    button{
        @include basic-button;
    }
}
.config-modal{
    .modal-header .view-edit-btn{
        @include basic-button;
    }
    .submit-btn{
        @include basic-button;
        margin-top: 5px;
    }
    .modal-header div{
        min-width: 300px;
        display: flex;
        justify-content: space-between;
    }
    form{
        input, select{
            @include basic-input-select;
        }
        label, .config-label{
            @include basic-label;
        }
        .label-pair > :first-child{
            display: inline-flex;
            font-weight: 800;
            width: 150px;
        }
        .label-pair > :last-child{
           margin-left: 1rem;
        }
    }
}