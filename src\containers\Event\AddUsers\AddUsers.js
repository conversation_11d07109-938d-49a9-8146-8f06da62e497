import React, { useState, useEffect, useCallback, useRef } from 'react';
import Container from 'react-bootstrap/Container';
import Button from 'react-bootstrap/Button';
import { Col, Table, Row, Card, Form, InputGroup } from 'react-bootstrap'
import sha256 from 'js-sha256';

//import { differenceInYears } from 'date-fns';

// import Table from '../../../components/common/Table';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';
import Pagination from '../../../components/common/Pagination';

import Events from '../../../api/Events';
import Users from '../../../api/Users';

import './AddUsers.css';


const AddUsers = (props) => {

    let subdomain =window.location.origin;

    const [loading,setLoading]=useState(true);
    const [users,setUsers]=useState([]);
    const [event, setEvent] = useState({});
    const [shownUsers, setShownUsers] = useState([]);
    const [selectedAttendees, setSelectedAttendees] = useState([]);
    const [rsvp, setRsvp] = useState(0);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();
    const [submitting, setSubmitting] = useState(false);
    const [resetChecks, setResetChecks] = useState(false);
    const [ searchTerm, setSearchTerm ]=useState('');
    const [ maxRecords, setMaxRecords ]=useState(25); //set to db default
    const [ currentPage, setCurrentPage ]=useState(1); //set to db default
    const [ sortCol, setSortCol ]=useState("id"); //set to db default
    const [ sortDir, setSortDir ]=useState("desc"); //set to db default
    const [ totalItems, setTotalItems ]=useState();
    const [ totalPages, setTotalPages]=useState();
    const [ paginationReady, setPaginationReady ]=useState(false);

    const initShownUsers = useCallback( attendees => {
        var validUsers = [];

        for(let i=0; i<users.length; i++) {

            if(!attendees?.includes(users[i].id)) {
                let user = users[i];
                validUsers.push({
                    id: user.id,
                    first_name: user.first_name,
                    last_name: user.last_name,
                    email: user.email,
                    //age: differenceInYears(new Date(), user.dob), no dob returned by this call
                    //invited: invited,
                });
            }
        }
        setShownUsers(validUsers);
    },[users]);

    const getTotalPages=useCallback(total=>{
        let pages = Math.ceil(total/maxRecords)
        setTotalPages(pages)
        setTotalItems(total)
    },[maxRecords]);

    // first run, get users
    useEffect(() => {
        let mounted = true;

        setLoading(true);
        // get all users
        Users.getList({filters:{search_words: searchTerm}, max_records:maxRecords, page_no:currentPage, sort_col: sortCol, sort_direction: sortDir })
        .then(response => {
            if(mounted) {
                setUsers(response.data?.users);
                getTotalPages(response.data.total_record_count);
            };
            setLoading(false);
        }).catch(e => console.error(e));

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
            setUsers([]);
        }        
    },[searchTerm, sortCol, sortDir, currentPage, maxRecords, getTotalPages]);
    
	useEffect(() => {
        let mounted = true;
        let attendees = [];
        setLoading(true);
        if (props.event) {
            setEvent(props.event);
            attendees = props.event.users;
        } else {
            Events.getDetail({id:props.event_id})
            .then(response => {
                if(mounted && response.data) {
                    setEvent(response.data[0]);
                    attendees = response.data[0].users;
                }
                setLoading(false);
            }).catch(e => console.error(e));
        }
        initShownUsers(attendees?.map( attendee => attendee.id ));
        setLoading(false);

        return () => {
            mounted = false;
            setLoading(false);
        }
	}, [props, initShownUsers]);

    const refSelectedAttendees = useRef(selectedAttendees);

    useEffect(() => {
        refSelectedAttendees.current = selectedAttendees;
    },[selectedAttendees]);

    useEffect(()=>{
        if(totalPages && shownUsers && loading === false){
            setPaginationReady(true)
        }
    }, [totalPages, shownUsers, loading]);

    //#region Handlers
    const inviteHandler = async () => {
        setSubmitting(true);
        setError(null);
        setSuccess(null);
        
        Events.invite({event_id: event.id, attendees: selectedAttendees, rsvp:rsvp})
        .then( response => {
            if (!response.errors) {
                setShownUsers( prevState => prevState.filter( u => !selectedAttendees.includes(u.id)) );
                setSuccess(<Toast>Invitations sent!</Toast>);
                setSelectedAttendees([]); //reset selection
                setResetChecks(true); //reset checkboxes
                setSubmitting(false); //enable invite button
            }
        }).catch(e => {
            setError(<ErrorCatcher error={e} />);
            setSubmitting(false);
        });
        setResetChecks(false);
    };

    const headerClick=(column)=>{
        setSortCol(column);
        if (sortDir==="ASC") setSortDir("DESC");
        else setSortDir("ASC");
    }

    const searchOnChange = (e)=>{
        if(e.target.value.length >2 ){
            setSearchTerm(e.target.value)
        }
        if(e.target.value.length === 0){
            setSearchTerm("")
        }
    }
    //#endregion

    const CheckBox = (props) => {
    
        const [user,setUser] = useState({});
        const [checked,setChecked] = useState(false);

        useEffect(() => {
            if (props.user) {
                setUser(props.user);
            } else console.error('Checkbox failed to get user');
        },[props.user]);

        useEffect(() => {
            if (props.reset) {
                setChecked(false);
            }
        },[props.reset]);

        useEffect(() => {
            setChecked(refSelectedAttendees.current.includes(user));
        },[user]);

        return (
            <Form.Check id={`chkbox_${user.id}`} checked={checked} name={`chkbox_${user.id}`} value={user.id} onChange={e => {
                if(checked) { //uncheck, remove from list
                    setSelectedAttendees(refSelectedAttendees.current.filter(u => u !== user));
                    setChecked(false);
                } else { //check, add to list
                    setSelectedAttendees(refSelectedAttendees.current.concat(user));
                    setChecked(true);
                }
            }}/>
        );
    };    

    const columns = React.useMemo(
        () => [{
            id: 'table',
            columns: [
                {
                    id: 'checkbox',
                    accessor: d => "",
                    className: 'align-middle',
                    Cell: props =>
                        <CheckBox user={props.row.original} reset={resetChecks}/>
                },
                {
                    Header: 'First Name',
                    id: 'name',
                    accessor: 'first_name',
                    className: 'align-middle',
                },
                {
                    Header: 'Last Name',
                    id: 'last_name',
                    accessor: 'last_name',
                    className: 'align-middle',
                },
                {
                    Header: 'Email',
                    id: 'email',
                    accessor: 'email',
                    className: 'align-middle',
                }/*,
                {
                    Header: 'Age',
                    id: 'age',
                    accessor: 'age',
                    className: 'align-middle',
                },*/
            ],
        }],[resetChecks]
    );

    return (
        <>
            <Form onSubmit={e=>e.preventDefault()}>
                <Form.Row>
                    <Form.Label>Direct Link to Registration Form:</Form.Label>
                    <Form.Control type="text" value={`${subdomain}/p/event-register?event=${event.id}?code=` + sha256(`${event.id}-${event.event_type_id}-pistachiooOO`)} />
                    <Form.Text className="text-muted">
                        This link will allow users to register for events with the status Reserved, which are not otherwise displayed publicly.
                    </Form.Text>
                </Form.Row>
            </Form>
            {success}
            <Row className="body">
                <Col>
                <div className="tab-scrol">
                    <Card>
                        <Card.Body className={`${loading?" loading":""}`}>
                            <Form>
                                <Form.Group>
                                    <InputGroup>
                                        <Form.Control
                                            id="search_input"
                                            placeholder={`Search users`}
                                            aria-label={`Search users`}
                                            onChange={searchOnChange} 
                                        />
                                    </InputGroup>
                                </Form.Group>
                            </Form>
                            <div className="ov-x-sc">
                            <Table className="table">
                                <thead>
                                    <tr>
                                        <th>
                                            <span>Invite</span>
                                        </th>
                                        <th onClick={()=>{headerClick("id")}}>
                                            <span>ID</span>
                                            <i className={`ml-1 far fa-${sortCol==="id"?sortDir==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                        </th>
                                        <th onClick={()=>{headerClick("first_name")}}>
                                            <span>First Name</span>
                                            <i className={`ml-1 far fa-${sortCol==="first_name"?sortDir==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                        </th>
                                        <th onClick={()=>{headerClick("last_name")}}>
                                            <span>Last Name</span>
                                            <i className={`ml-1 far fa-${sortCol==="last_name"?sortDir==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                        </th>
                                        <th onClick={()=>{headerClick("email")}}>
                                            <span>Email</span>
                                            <i className={`ml-1 far fa-${sortCol==="email"?sortDir==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {shownUsers.map((row, i) => (
                                        <tr key={`user-row-${i}`} > 
                                            <td><CheckBox user={row.id} reset={resetChecks} /></td>
                                            <td>{row.id}</td>
                                            <td>{row.first_name}</td>
                                            <td>{row.last_name}</td>
                                            <td>{row.email}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                            </div>
                        </Card.Body>
                    </Card>
                    </div>
                </Col>
            </Row>
            {/* <div className="userTable">
                <div className={`${loading?" loading":""}`}>
                <Table columns={columns} data={shownUsers} />
                </div>
            </div> */}
            {!shownUsers &&
                <tbody>
                    <tr>
                        <td colSpan="6">
                            No users match your search criteria.
                        </td>
                    </tr>
                </tbody>
            }
            {shownUsers && totalPages > 1 && paginationReady === true &&
                <div className="d-flex justify-content-end">
                    <Pagination
                        itemsCount={totalItems}
                        itemsPerPage={maxRecords}
                        currentPage={currentPage}
                        setCurrentPage={setCurrentPage}
                        alwaysShown={false}
                    />
                </div>}
            <Button variant="primary" disabled={submitting} onClick={inviteHandler}>Invite Selected</Button>
            {error}
        </>
    )
}

export default AddUsers;