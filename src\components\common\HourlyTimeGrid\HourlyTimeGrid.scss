@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';

.timeline {
    display: grid;
    width: 100%;
    overflow-x: auto;
    grid-template-columns: 200px repeat(20, minmax(75px, 1fr));
    grid-template-rows: auto;
    grid-auto-rows: minmax(10px, auto);
    background-color: $company-neutral-light;
}


.timeline::-webkit-scrollbar {
    width: $scrollbar-width;
}

.timeline::-webkit-scrollbar-track {
    background: $scrollbar-background-color;
}

.timeline::-webkit-scrollbar-thumb {
    background: $scrollbar-color;
    border-radius: $scrollbar-border-radius;
}

.topleft {
    background-color: $background-color;
}

.location-name {
    background-color: $background-color;
    width: 100%;
}

.location-name~div {
    width: calc(100% - 80px);
    overflow: hidden;
    min-width: 0;
}

.hour {
    width: 100%;
    display: flex;
    flex-direction: column;
    font-size: $small-font-size;
    line-height: $small-font-line-height;
    text-transform: uppercase;
    background-color: $background-color;
    text-align: left;
    border-bottom: 1px solid $divider-color;
    border-left: 1px solid $divider-color;
    padding-left: .25rem;
}

.day-name,
.location-name {
    height: 42px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    padding-right: 1rem;
}


.timeline-container {
    max-width: calc(100vw - 485px);
    padding: 0;
    box-shadow: none;
}

@media (max-width: 991px) {
    .timeline-container {
        max-width: calc(100vw - 120px);
    }
}

.time-grid {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    flex-direction: row;
    background-color: $background-color;
    border-left: 1px solid $divider-color;
}

.time-grid div {
    flex: 1;
    max-width: 100%;
    max-height: 100%;
    min-height: 2rem;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
    font-size: .5rem;
    font-weight: 300;
    color: $grey-7;
    border: 1px solid $divider-color;
    border-top: 0;
    overflow: hidden;
    background-color: $form-control-background-color;
}

.time-grid div.reserved {
    background-color: $disabled-color !important;
    border-color: $disabled-color !important;
    cursor: default !important;
}

.time-grid div.reserved-wizard {
    background-color: $secondary-color !important;
    border-color: $secondary-color !important;
    cursor: default !important;
}

.time-grid div.booked {
    background-color: $primary-color;
    border-color: $primary-color;
}

/*.time-grid div:nth-child(-n+2){
  border-top:1px solid #e0e0e0;
}*/

.time-grid div:hover {
    background-color: $primary-hover-color;
    border-color: $primary-hover-color;
    color: $primary-inverse-color;
}

.time-grid div.selected {
    background-color: $primary-color;
    border-color: $primary-color;
    color: $primary-inverse-color;
}

.time-grid div.highlight {
    background-color: $tertiary-color;
    border-color: $tertiary-color;
    color: $tertiary-light-color;
}

.time-grid div.select-error {
    background-color: $error-color;
    border-color: $error-color;
    color: $error-text-color;
}

.breadcrumb {
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
}