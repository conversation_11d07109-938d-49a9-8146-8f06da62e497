import React, { useState, useEffect } from 'react';
import { format, max } from 'date-fns';

import Card from './Card';

import "./Products.scss";
import { ProductImage } from '../ProductImage/ProductImage';

export const Products = ({expiredEvents, isAfter, ...props}) => {
    let services = props.services || false;

    const [price, setPrice] = useState(null);
    const [subItem, setSubItem]=useState(null);
    const [item, setItem]=useState(props.item);

    useEffect(() => {
        if (props.price){
            if (Array.isArray(props.price)){
                //setPrice("$"+ (Math.min( ...props.price )).toFixed(2)+" - $"+ (Math.max( ...props.price )).toFixed(2));
                setPrice(`Starting from $${Math.min(...props.price).toFixed(2)}`);
            } else {
                setPrice("$"+ (+props.price).toFixed(2));
            }
        }
    },[props.price]);

    useEffect(()=>{
        let _item = props.item;
        if(props.item.product_status_id === 1){
            //if there are more than one variant, it will be shown after clicking on the variant picker.  
            if(props?.item?.product_variants?.length === 1){
                setSubItem(props.item.product_variants[0]);
            } else {
                setSubItem(props.item);
            }
        }
        if (props.events) _item.events = props.events;
        setItem(_item)
    },[props.item, props.events]);

    let variant = props.item;
    if (props?.item?.product_variants?.length === 1) variant = props.item.product_variants[0];

    // if(props.loading) return <CardSkeleton columns={1} rows={1} />; //I think visually they're too distracting to see cards instead.  An indication of loading is good, but this it soo much for the pos

    //if an event has multiple events, will get the latest date
    const getLatestEventDate = ()=>{
        if(item?.events?.length === 1) return new Date(item?.events[0]?.end_datetime);
        else {
            let dates = item?.events.map(event => new Date(event.end_datetime));
            return max(dates);
        }
    }

    return (
        <Card 
            title={props.name}
            onClick={()=>props.click(item, props.type)}
            styles={`${services ? 'services' : ''} ${Array.isArray(props.price)?"":"single"}`}
            className="pos-product-card"
        >
            {price &&
                <div className="price" data-cy="product-price">{price} {props.isActivationFee && <span className="subscription-text">activation Fee</span>}</div>
            }
            {subItem && subItem?.hasOwnProperty("bill_interval") && subItem?.hasOwnProperty("interval_quantity") &&
                <div className="subscription-text">
                    {subItem?.bill_interval ==="m" && 
                        <p>
                            {+subItem.activation_fee>0 ?
                                <>
                                    + ${subItem.price} subscription<br/>
                                </>
                            : "Subscription"}
                            {subItem?.interval_quantity && subItem?.interval_quantity > 1 ?
                                ` billed every ${subItem?.interval_quantity} month${subItem?.interval_quantity > 1 ? "s" : ""}.` 
                            :
                                " billed monthly."
                            }
                            {subItem?.bill_num_times ? 
                                <>
                                    <br/>
                                    {`${subItem?.bill_num_times} time${subItem?.bill_num_times > 1 ? "s" : ""} in total.`}
                                </>
                            :
                                <>
                                    <br/>
                                    until cancelled.
                                </>
                            }
                        </p>
                    }

                    {subItem?.bill_interval ==="y" &&
                        <p>
                            {subItem?.bill_num_times ?
                                " billed on purchase and will not recur."
                            :
                                " billed annually until cancelled."
                            }    
                        </p>
                    }
                    {subItem?.subscription_type_name &&
                        <p>
                            - {subItem?.subscription_type_name} Subscription 
                            {subItem?.subscription_max_users && subItem?.subscription_max_users > 1 ? 
                                ` (max users: ${subItem?.subscription_max_users}).`
                            : 
                                "."
                            }
                        </p>
                    }
                </div>
            }
            {item && item?.events?.length > 0 && item?.product_type_id === 5 &&
                <div className="subscription-text">
                    {expiredEvents === "flag" && isAfter ?
                        <p>
                            <span className="expired-event-flag error-text">Event Expired on {format(getLatestEventDate(), "MM/dd/yyyy")}</span>
                        </p>
                        : null
                    }
                </div>
            }
            {props.image && !services && props.cardImages &&
                <ProductImage 
                    imgSrc={props.image}
                    shape="circle"
                    aligment="center"
                    zoomEnabled={false}
                />
            
            }
            {props.description && !services &&
                <p className="product-description" data-cy="product-card-description">{props.description}</p>
            }
        </Card>
    );
}