.timeline {
    display: grid;
    width: 100%;
    overflow-x: auto;
    grid-template-columns: 200px repeat(24, minmax(75px, 1fr));
    grid-template-rows: auto;
    grid-auto-rows: minmax(10px, auto);
    background-color:#eeeeee;
}

.timeline::-webkit-scrollbar {
    width: 12px;
    height:1.2rem;
}
.timeline::-webkit-scrollbar-track {
    background: #fff;
    margin-top: 10px;
    margin-bottom:10px;
}
.timeline::-webkit-scrollbar-thumb {
    background: #e0e0e0;
    border: 7px #fff solid;
    border-left:0;
    height:10px;
    border-radius: 0;
}

.location-name{
  background-color:#fafafa;
  border-right:1px solid #eee;
  width:100%;
  font-size: .75rem;
}

.location-name ~ div{
  width:calc(100% - 80px);
  overflow: hidden;
  min-width: 0;
}

.hour {
  width:100%;
  display:flex;
  flex-direction: column;
  font-size: .75rem;
  text-transform: uppercase;
  background-color: #fafafa;
  color: #424242;
  text-align: left;
  border-bottom: 1px solid #eee;
  border-left: 1px solid #eee;
  padding-left: .25rem;
  line-height: 40px;
  font-weight: 500;
}

.day-name, .location-name{
  height: 42px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding-right: 1rem;
}


.timeline-container{
  max-width: calc(100vw - 370px);
}

@media (max-width: 991px) {
  .timeline-container{
    max-width: calc(100vw - 120px);
  }
}

.time-grid{
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  flex-direction: row;
  background-color:#eee;
  border-left: 1px solid rgba(0,0,0,.25);
}

.time-grid div{
  flex:1;
  max-width:100%;
  max-height:100%;      
  justify-content: space-around;
  align-items: center;
  cursor:pointer;
  font-size: .5rem;
  font-weight: 300;
  color:#9e9e9e;
  border:1px solid #eee;
  border-top:0;
  overflow:hidden;
  background-color:#ffffff;
}  

.time-grid div.reserved{
  background-color:#f44336 !important;
  border-color: #f44336 !important;
  cursor: default !important;
}  

.time-grid div.booked{
  background-color:#536dfe;
  border-color:#536dfe;
}  

/*.time-grid div:nth-child(-n+2){
  border-top:1px solid #e0e0e0;
}*/ 

.time-grid div:hover{
    background-color:#536dfe;
    border-color:#536dfe;
    color:#ffffff;
} 

.breadcrumb{
    padding-top:0;
    padding-bottom:0;
    padding-right:0;
}

.react-datepicker__triangle {
  left: 20px !important;
  border-bottom-color: #fff !important;
}