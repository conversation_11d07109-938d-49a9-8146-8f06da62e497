import React from 'react';
import OverlayTrigger from 'react-bootstrap/OverlayTrigger';
import Popover from 'react-bootstrap/Popover';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import ButtonGroup from 'react-bootstrap/ButtonGroup';
import Button from 'react-bootstrap/Button';
import Dropdown from 'react-bootstrap/Dropdown';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import './Scheduler.css';

/* array of years:
    past (-10): 
        null: start from current year
        int: substract value from current year and start from there
    future (+10):
        null: end from current year
        int: add value to current year and end there
*/
export const getYears = (params) => {
    let years=[];
    let start=new Date().getFullYear();
    let end=new Date().getFullYear();
    if (params.past){
        start-=params.past;
    }
    if (params.future){
        end+=params.future;
    }

    for(let i=start;i<=end;i++){
        years.push({
            value:i,
            text:i
        });
    }

    if (params.year) return years[params.year];
    return years;
}

// array of month names in a year
export const getMonths = (month) => {
    const months= [
        {value: 1, text:"January"},
        {value: 2, text:"February"},
        {value: 3, text:"March"},
        {value: 4, text:"April"},
        {value: 5, text:"May"},
        {value: 6, text:"June"},
        {value: 7, text:"July"},
        {value: 8, text:"August"},
        {value: 9, text:"September"},
        {value: 10, text:"October"},
        {value: 11, text:"November"},
        {value: 12, text:"December"}
    ];
    
    if (month >= 0) return months[month];
    return months;
}

const getWeekRange = (week, year) => {
    const simple = new Date(year, 0, 1 + (week - 1) * 7);
    const dow = simple.getDay();
    let ISOweekStart = simple;
    if (dow <= 4)
        ISOweekStart.setDate(simple.getDate() - simple.getDay());
    else
        ISOweekStart.setDate(simple.getDate() + 7 - simple.getDay());
    
    const temp = {
      d: ISOweekStart.getDate(),
      m: ISOweekStart.getMonth(),
      y: ISOweekStart.getFullYear(),
    }
    const numDaysInMonth = new Date(temp.y, temp.m + 1, 0).getDate()
    
    return Array.from({length: 7}, _ => {
      if (temp.d > numDaysInMonth){
        temp.m +=1;
        temp.d = 1;
      }      
      return new Date(temp.y, temp.m, temp.d++)
    });
}

// week number for a given date
export const getWeekNumber = (date) => {
    let d = new Date(Date.UTC(date.getFullYear(),date.getMonth(),date.getDate())) || new Date(Date.UTC());

    d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay()||7));

    const yearStart = new Date(Date.UTC(d.getUTCFullYear(),0,1));
    const selectedWeek = Math.ceil(( ( (d - yearStart) / 86400000) + 1)/7);
    const weekRange=getWeekRange(selectedWeek, date?.getFullYear() || new Date().getFullYear());

    return {value:selectedWeek,date:weekRange[0],text:weekRange[0].toLocaleDateString("en-US",{day:"2-digit",month:"2-digit"})+" - "+weekRange[weekRange.length-1].toLocaleDateString("en-US",{day:"2-digit",month:"2-digit"})}
}


// array of weeks from a given year
export const getWeeks = (week, date) => {
    const lastWeek = getWeekNumber(date || new Date(new Date().getFullYear(),11,31)).value;

    let weeks=[];
    weeks.push({}); // adds a record so the array is in sync with the week number
    for (let i=1;i<=lastWeek;i++){
        let weekRange=getWeekRange(i, date?.getFullYear() || new Date().getFullYear());    
        weeks.push({
            value:i,
            date:weekRange[0],
            text:weekRange[0].toLocaleDateString("en-US",{day:"2-digit",month:"2-digit"})+" - "+weekRange[weekRange.length-1].toLocaleDateString("en-US",{day:"2-digit",month:"2-digit"})
        });
    }

    if (week && week>=0){
        return weeks[week];
    } 

    return weeks;
}

// array of days from a given month
export const getDays = (month,year) => {

    const firstDay = new Date(year, month, 1).getDate();
    const lastDay = new Date(year, month + 1, 0).getDate();

    let days=[];
    for (let i=firstDay;i<=lastDay;i++){
        days.push({
            value:i,
            text: i
        });
    }

    return days;
}

// array of day names in a week
export const getDayNames = (day) => {
    const days=[
        {value: 0, text:"Sunday"},
        {value: 1, text:"Monday"},
        {value: 2, text:"Tuesday"},
        {value: 3, text:"Wednesday"},
        {value: 4, text:"Thursday"},
        {value: 5, text:"Friday"},
        {value: 6, text:"Saturday"}
    ];

    if (day >= 0) return days[day];
    return days;
}

export const SchedulerHeader = (props) => {
    
    if (props.showHeader===false) return null;

    return (
        <div className="scheduler-header">
            <Container fluid className="px-0">
                <Row>
                    <Col sm="12" lg="4" className="text-lg-left">
                        <ButtonGroup>
                            {props.currentDate ? 
                                <DatePicker 
                                    dateFormat="MM/dd/yyyy"
                                    minDate={new Date("01/01/1900")}
                                    maxDate={new Date(new Date().getFullYear()+100,12,31)}
                                    showMonthDropdown
                                    showYearDropdown
                                    selected={new Date(props.currentDate)}
                                    onChange={props.dateClick}
                                    customInput={
                                        <Button variant="light" className="datepicker-calendar ml-1" type="button">{props.currentDate}</Button>
                                    }
                                />
                            : null }

                            {props.currentMonth ? 
                                <Dropdown>
                                    <Dropdown.Toggle variant="light">
                                        {getMonths(props.currentMonth-1).text}
                                    </Dropdown.Toggle>
                                    <Dropdown.Menu>
                                        {getMonths().map((month,i)=>
                                            <Dropdown.Item key={"month-"+month.value} href={`#${i}`} onClick={props.monthClick}>{month.text}</Dropdown.Item>
                                        )}
                                    </Dropdown.Menu>
                                </Dropdown>
                            : null }

                            {props.currentWeek ? 
                                <Dropdown className="week-dropdown">
                                    <Dropdown.Toggle variant="light">
                                        {getWeeks(props.currentWeek).text}
                                    </Dropdown.Toggle>
                                    <Dropdown.Menu>
                                        {getWeeks().map((week,i)=>{
                                            if (i===0) return null; // first value is empty, in the function definition it says why
                                            return <Dropdown.Item key={"week-"+week.value} href={`#${i}`} onClick={props.weekClick}>{week.text}</Dropdown.Item>
                                        })}
                                    </Dropdown.Menu>
                                </Dropdown>
                            : null }

                            {props.currentYear ? 
                                <Dropdown>
                                    <Dropdown.Toggle variant="light">
                                        {props.currentYear}
                                    </Dropdown.Toggle>
                                    <Dropdown.Menu>
                                        {getYears({past:2,future:5}).map((year,i)=>
                                            <Dropdown.Item key={"year-"+year.value} href="#!" onClick={props.yearClick}>{year.text}</Dropdown.Item>
                                        )}
                                    </Dropdown.Menu>
                                </Dropdown>
                            : null }
                        </ButtonGroup>
                    </Col>

                    {(props.showToolbar===undefined || props.showToolbar===true) &&
                        <Col className="text-center text-lg-right">
                            <Button variant="light" size="sm" onClick={props.todayClick}><i className="far fa-calendar-day"></i> Today</Button>
                            <ButtonGroup>
                                <Button variant="light" size="sm" onClick={props.dayViewClick}>Day</Button>
                                <Button variant="light" size="sm" onClick={props.weekViewClick}>Week</Button>
                                <Button variant="light" size="sm" onClick={props.monthViewClick}>Month</Button>
                                <Button variant="light" size="sm" onClick={props.yearViewClick}>Year</Button>
                                <Button variant="light" size="sm" onClick={props.scheduleViewClick}>Schedule</Button>
                            </ButtonGroup>
                        </Col>
                    }
                </Row>
            </Container>
        </div>
    );
}    


// create array of tasks sorted by time
export const showTasks = (tasks, date) =>{
    if (tasks){
        // sort by time
        tasks.sort((a, b) => {
            return a.time_start.localeCompare(b.time_start);
        });

        // output tasks
        let taskClass="";
        let prettyDate="";
        let prettyFromTime=null;
        let prettyToTime=null;
        return (
            <ul>
                {tasks.map((task)=>{
                    switch(task.type){
                        case 1:
                            taskClass=" task-everyday";
                            break;
                        default:
                            taskClass="";
                            break;
                    } 
                    if (task.day) prettyDate=new Date(task.year,task.month-1,task.day).toLocaleDateString("en-US", { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
                    else prettyDate="";
                    
                    if (task.time_start) {
                        let [hour,min,sec]=task.time_start.split(":");
                        prettyFromTime=new Date(
                            task.year || new Date().getFullYear(),
                            (task.month-1) || new Date().getMonth(),
                            task.day || new Date().getDate(),
                            hour,
                            min,
                            sec
                        ).toLocaleTimeString("en-US",{ hour12: true, hour: '2-digit', minute: '2-digit' }).toLowerCase();
                    } else prettyFromTime=null;

                    if (task.time_end) {
                        let [hour,min,sec]=task.time_end.split(":");
                        prettyToTime=new Date(
                            task.year || new Date().getFullYear(),
                            (task.month-1) || new Date().getMonth(),
                            task.day || new Date().getDate(),
                            hour,
                            min,
                            sec
                        ).toLocaleTimeString("en-US",{ hour12: true, hour: '2-digit', minute: '2-digit' }).toLowerCase();

                    } else prettyToTime=null;

                    return (
                        /*(task.day===date.getDate() || !task.day) && */
                        <OverlayTrigger trigger={['hover','focus','click']} placement="bottom" key={`ovlt${task.id}`} overlay={
                            <Popover id="popover-basic">
                            <Popover.Content>
                                <h6>{task.title}</h6>
                                {prettyDate && <p><i className="far fa-calendar-day mr-2"></i>{prettyDate}</p>}
                                {prettyFromTime && <p><i className="far fa-clock mr-2"></i>{prettyFromTime} - {prettyToTime}</p>}
                                <p><i className="far fa-align-left mr-2"></i>{task.description}</p>
                            </Popover.Content>
                            </Popover>
                        }>
                            <li className={`task${taskClass}`} key={`li${task.id}`}><a href="#!">{task.title}</a></li>
                        </OverlayTrigger>                            
                    )
                })}
            </ul>
        )
    }
    return false;
}    
