import Request from './Api';

const get = async (props) => {
    return (
        Request({
            url: "/tag",
            method: "POST",
            data: props
        })
    );
}

const create = async (props) => {
    return (
        Request({
            url: "/tag/add",
            method: "POST",
            data: props
        })
    );
}

const edit = async (props) => {
    return (
        Request({
            url: "/tag/edit",
            method: "PUT",
            data: props
        })
    );
}

const remove = async (props) => {
    return (
        Request({
            url: "/tag/delete",
            method: "DELETE",
            data: props
        })
    );
}

const Tags = {
    get, create, edit, remove
}

export default Tags;