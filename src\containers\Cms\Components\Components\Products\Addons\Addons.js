import React, { useState, useEffect, useMemo } from 'react';
import { Form } from 'react-bootstrap';

import APIProducts from '../../../../../../api/Products';

import styles from './Addons.module.scss';

const Addons =  props => {
    const {change} = props;
    const [selected, setSelected] = useState([]);

    const addons = useMemo(() => {
        let _addons = [];
        const _getAddons = async () => {
            let _res = [];
            const res = await APIProducts.Variants.Addons.get({id: props.variant_id});
            if (res.data) _res = res.data || [];
            return _res;
        }
        if (props.variant_id) _getAddons().then(res=>_addons=res);
    },[props.variant_id]);

    console.log(addons)

    const addonClickHandler = (e) => {
        const addon = addons.find(item=>item.add_ons.find(add_on=>+add_on.id===+e.target.value))?.add_ons.find(add_on=>+add_on.id===+e.target.value);
        if (e.target.checked) {
            setSelected(prev=>([...prev, addon]));
        } else {
            setSelected(prev=>prev.filter(item=>item.id!==addon.id));
        }
    }

    useEffect(() => {
        change(selected);
    },[selected, change]);

    useEffect(() => {
        return () => {
            setSelected([]);
        }
    }, []);    

    return (
        <>
            <div>
                {addons?.length>0 && 
                    <>
                        <br/>
                        <p>Personalize your plan with these add-ons:</p>
                    </>
                }
            </div>
            {addons && addons?.map((addon, j) => (
                <div key={`addon-${j}`}>
                    <span className={styles.title}>{addon.category_name}</span>
                    {addon?.add_ons?.map((add_on, k) => (
                        <Form.Check
                            key={`addon-cat-${j}-addon-${k}-${add_on.id}`}
                            type="checkbox"
                            label={`${add_on.name} ($${(+add_on.price).toFixed(2)})`}
                            checked={selected.find(item=>+item.id===+add_on.id)?.id>0 || false}
                            value={add_on.id}
                            disabled={props.submitting}
                            onChange={addonClickHandler}
                            inline
                        />
                    ))}
                </div>
            ))}
        </>
    );
}

export default Addons;