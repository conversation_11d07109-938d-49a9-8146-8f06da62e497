import Request from './Api';

const variables={
    get: async (props) =>{
        return(
            Request({
                url: "/template_variable/template_variable/",
                method: "GET",
                data: props
            })
        )
    },
    getOne: async (props) =>{
        return(
            Request({
                url: `/template_variable/template_variable/" + ${props?.id ? "/" + props?.id : ""}`,
                method: "GET",
                data: props
            })
        )
    }, 
    add: async (props) =>{
        return(
            Request({
                url: "/template_variable/add",
                method: "POST",
                data: props
            })
        )
    },
    update: async (props) =>{
        return(
            Request({
                url: "/template_variable/edit",
                method: "POST",
                data: props
            })
        )   
    },
    delete: async (props) =>{
        return(
            Request({
                url: "/template_variable/delete",
                method: "POST",
                data: props
            })
        )
    }
}

const templates = {
    get: async (props) =>{
        return(
            Request({
                url: `/email/template/template${props?.id ? "/" + props?.id : ''}`,
                method: "GET",
                data: props
            })
        )
    },
    add: async (props) =>{
        return(
            Request({
                url: "/email/template/add",
                method: "POST",
                data: props
            })
        )
    },
    delete: async (props) =>{
        return(
            Request({
                url: "/email/template/delete",
                method: "DELETE",
                data: props
            })
        )
    },
    edit: async (props) =>{
        return(
            Request({
                url: "/email/template/edit",
                method: "POST",
                data: props
            })
        )
    }
}

const templateTypes={
    get: async (props) =>{
        return(
            Request({
                url: `/email/template/type${props?.id ? "/" + props?.id : ""}`,
                method: "GET",
                data: props
            })
        )
    },
    add: async (props) =>{
        return(
            Request({
                url: "/email/template/type/add",
                method: "POST",
                data: props
            })
        )
    },
    delete: async (props)=>{
        return(
            Request({
                url: "/email/tempalte/type/delete",
                method: "DELETE",
                data: props
            })
        )
    },
    edit: async (props)=>{
        return(
            Request({
                url: "/email/template/type/edit",
                method: "POST",
                data: props
            })
        )
    }
}

const Email = {
    variables, templates, templateTypes
}

export default Email;