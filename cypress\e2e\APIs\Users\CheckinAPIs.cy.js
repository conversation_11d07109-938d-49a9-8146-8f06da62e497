/*eslint-disable*/

let baseURL = Cypress.env('qa_api_url');
let SBUser = Cypress.env('impact_sb_user');
let SBPassword = Cypress.env('login_password');

describe("It will check the checkin endpoints", {scrollBehavior: "center", testIsolation:false}, ()=>{
    let header;
    let today = new Date();
    it("will login to SB account",()=>{
        cy.loginApi(SBUser, SBPassword)
            .then((response)=>{
                header = {
                    Authorization: response.body.data.token,
                    "Content-Type": "application/json"
                }
            })
    });

    /**As SB user, endpoints should never not be permitted and should, technically, always be a 200 */
    it("will test the getAll api status as a SB user",()=>{
        cy.request({
            method: "POST",
            url: `${baseURL}/checkins`,
            headers: header
        }).then((response)=>{
            expect(response.status).to.eq(200);
        })
    });

    //create - /user/checkin - POST
    //https://nyspsp.atlassian.net/wiki/spaces/IA/pages/**********/user+checkin
    it("will make sure I can check a user in and that the date is the current date, ",()=>{
        let todayDate = today.getDate();
        cy.request({
            method: "POST",
            url: `${baseURL}/user/checkin`,
            headers: header,
            body: {
                "user_id": "3075",
            }
        }).then((response)=>{
            let checkinDate = new Date(response.body.data.checkin_at).getDate();
            expect(response.status).to.eq(200);
            expect(response.body.data.checkin_at).to.not.be.null;
            expect(todayDate).to.equal(checkinDate); 
        })
    });

    //getAll - checkins - POST
    //https://nyspsp.atlassian.net/wiki/spaces/IA/pages/2120482817/checkins
    it("will make sure that /checkins comes back with a 200 status",()=>{
        cy.request({
            method: "POST",
            url: `${baseURL}/checkins`,
            headers: header,
            body: {}
        }).then((response)=>{
            expect(response.status).to.eq(200);
        })
    });

    it("will make sure that there is at least one checkin in the response",()=>{
        cy.request({
            method: "POST",
            url: `${baseURL}/checkins`,
            headers: header,
            body: {
                limit: 5
            }
        }).then((response)=>{
            expect(response.body.data).to.not.be.null;
            expect(response.body.data?.length).to.equals(5);
        })
    })

    it("will make sure that today has at least one checkin (from the previous test)",()=>{
        // cy.log(today)
        cy.request({
            method: "POST",
            url: `${baseURL}/checkins`,
            headers: header,
            body: {
                start_datetime: today,
                limit: 15
            }
        }).then((response)=>{
            expect(response.body.data).to.not.be.null;
            expect(response.body.data).length.to.be.greaterThan(0);
        })
    })

    //get - /user/checkin_history - POST - no doc

})