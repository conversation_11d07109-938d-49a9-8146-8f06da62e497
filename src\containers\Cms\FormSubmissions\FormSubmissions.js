// page is linked as /p/form-responses
import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Card, Button, Breadcrumb, Table } from 'react-bootstrap';
import { useH<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import SubHeader from '../../../components/common/SubHeader';

import List from './List';

import APICms from '../../../api/Cms';
import Stack from '../../../components/common/Stack';

const FormSubmissions = (props) => {

	const [forms, setForms] = useState();
	const [loading, setLoading] = useState(false);
	const [selected, setSelected] = useState();

	useEffect(() => {
		const _getPageTypes=async () => {
            try {
				setLoading(true);
				const res=await APICms.websites.get();
				if (res.data) {
					let _allforms = [];
					for (const website of res.data) {
						const res2=await APICms.pages.get({page_type_id:10, website_id: website.id});
						if (res2.data) {
							_allforms.push({website_id: website.id, website_name: website.name, forms: res2.data});
						}
					}
					setForms(_allforms);
				}
				setLoading(false);
            } catch (e){
                console.error(e);
            }
        }

		_getPageTypes();
	}, []);

	useEffect(() => {
		return () => {
			setForms(null);
			setLoading(false);
			setSelected(null);
		}
	}, []);

	const clickHandler = useCallback((e, form) => {
		e.preventDefault();
		setSelected(form);
	}, []);
	return (
        <Container fluid>
            <SubHeader items={[
                {linkAs:Link,linkProps:{to:"/p/home"},text:"Home"},
				{linkAs:Link,linkProps:{to:"/p/cms"},text:"CMS Dashboard"},
                {text:"Form Submissions"}
            ]} />
			<Card className="content-card">
				<Row className="body">
					<Col>
						<Card className={`${loading?" loading":""}`}>
							{forms && forms.map((form, m) => (
								<React.Fragment key={`form-${m}`}>
									<h4 className="tm-1 section-title">{form.website_name}</h4>
									<hr/>
									<Row>
										<Col xs={12} sm={6} md={4} lg={3} xl={2}>
											<Stack direction="horizontal" gap="1">
												<Table>
													<thead>
														<tr>
															<th>Form</th>
														</tr>
													</thead>
													<tbody>
														{form.forms.map((form, i) => (
															<tr key={`form-${i}`} className={selected?.id === form.id ? "active" : null}>
																<td onClick={(e)=>clickHandler(e, form)}>
																	{form.title}
																</td>
															</tr>
														))}
													</tbody>
												</Table>
												{selected && <i className="far fa-arrow-right mx-3"/>}
											</Stack>
										</Col>
										<Col xs={12} sm={6} md={8} lg={9} xl={10}>
											{selected && 
												<h5>
													{selected.title}
												</h5>
											}
											{selected && <List {...selected} />}
										</Col>
									</Row>
								</React.Fragment>
							))}
						</Card>
					</Col>
				</Row>
			</Card>
        </Container>
	);
}

export default FormSubmissions;