import React from 'react';
import { useDrag } from 'react-dnd';

import styles from './Branch.module.scss';

export const Branch = (props) => {
    const [{ isDragging }, drag] = useDrag(() => ({
        type: 'SORTABLE_BRANCH',
        item: {id: props.id, parent_id: props.parent_id, index: props.index},
        collect: monitor => ({
            isDragging: !!monitor.isDragging(),
        }),
    }));

    const selectHandler = (e) => {
        e.preventDefault();
        if (props.onSelect) props.onSelect(props.id);
    }

    return (
        <div ref={drag} className={`${styles.branch} ${props.selected ? styles.selected : ""}`.trim()} style={{opacity: isDragging ? 0 : 1}}>
            <a href="#!" onClick={selectHandler}>{props.text}</a>
            {props.children}
        </div>
    );    
};