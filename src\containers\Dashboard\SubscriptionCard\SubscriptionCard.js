import React,{ useState, useEffect } from 'react';
import { Button, Row, Col, Image } from 'react-bootstrap';

import Products from '../../../api/Products';

//import { useHistory } from "react-router-dom";


export const SubscriptionCard = (props) => {
    //let history = useHistory();

    const [subs, setSubs] = useState([]);

    const addSub = sub => {
        setSubs(prevState => [...prevState, sub]);
    }

    useEffect(() => {
        let mounted = true;

        if(subs.length === 0) { // don't run if subs already set
            if(props?.user?.subscriptions) {
                props.user.subscriptions?.forEach( sub => {
                    Products.Variants.get({id: sub.product_variant_id})
                    .then( response => {
                        if(mounted) {
                            let interval = "";
                            switch(response.data[0].bill_interval) {
                                case "y":
                                    interval = "Year";
                                    break;
                                case "m":
                                    interval = "Month";
                                    break;
                                case "d":
                                    let qty = response.data[0].interval_qty;
                                    interval = qty > 1 ? qty + " Days" : "Day";
                                    break;
                                default:
                                case null:
                                    interval = null;
                                    break;
                            }
                            addSub(
                                {
                                    name: sub.product_name,
                                    status: sub.subscription_status,
                                    price: response.data[0].price,
                                    interval: interval
                                }
                            );
                        }
                    }).catch(e => console.error(e));
                });
            }
        }
        return () => mounted = false;
    },[props, subs]);
    
    return (
        <React.Fragment>
            <div className="profile-card">
                {subs?.map( sub =>
                    <div>
                        <span className="subs-active">{sub.status}</span>
                        <h5 className="profile-name">{sub.name}</h5>
                        {/* <div className="subs-desc">+ More Cyro Sessions</div>
                        <div className="subs-desc">+ More Fitness Class Tokens</div>
                        <div className="subs-desc">+ Discounts on golf Simulators</div> */}
                        {/* <hr /> */}
                        <span className="prof-amount">${sub.price}{sub.interval ? "/" + sub.interval : null}</span>
                        <hr />
                        {/* <div className="profile-basic">Expires on 03 Sept 2021;</div> */}
                    </div>
                )}
                {subs?.length === 0 ? <h5 className="profile-name">No Active Subscriptions</h5> : null}
            </div>
        </React.Fragment>
    );
}