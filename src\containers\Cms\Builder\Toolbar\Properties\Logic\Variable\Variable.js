import React, { useRef, useCallback } from 'react';
import { useDrag, useDrop } from 'react-dnd'
import { randomUUID } from '../../../../../../../utils/cms';
import Overlay from '../../../../Layers/Overlay';

import styles from './Variable.module.scss';

export const Variable = (props) => {
	const {move} = props;
	const ref = useRef(null);

	const [{ handlerId }, drop] = useDrop({
		accept: 'variable',
		collect(monitor) {
			return {
				handlerId: monitor.getHandlerId(),
			}
		},
		hover: useCallback((item, monitor) => {
			if (!ref.current) return;
			const dragIndex = item.index;
			const hoverIndex = props.index;

			if (dragIndex === hoverIndex) return;
			const hoverBoundingRect = ref.current?.getBoundingClientRect();
			const hoverMiddleY =(hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
			const clientOffset = monitor.getClientOffset();
			const hoverClientY = clientOffset.y - hoverBoundingRect.top;
			if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
			if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;
			move(dragIndex, hoverIndex, true);
			item.index = hoverIndex;
	  	}, [move, props.index]),
		drop: useCallback((item, monitor) => {
			if (!ref.current) return;
			const dragIndex = item.index;
			const hoverIndex = props.index;

			if (dragIndex === hoverIndex) return;
			move(dragIndex, hoverIndex, false);
			item.index = hoverIndex;
	  	}, [move, props.index]),
	});

	const [{ isDragging }, drag] = useDrag({
		type: 'variable',
		item: () => {
			return { index: props.index }
		},
		collect: (monitor) => ({
			isDragging: monitor.isDragging(),
		}),
	});

	const opacity = isDragging ? 0 : 1
	drag(drop(ref))

	if (props?.children){
		return (
			<div key={randomUUID()} ref={ref} style={{ opacity }} data-handler-id={handlerId} className={styles["variable-group"]}>
				{/*<pre>{props.id}</pre>*/}
				{props.children}
			</div>
		);
	}

	return (
		<div ref={ref} style={{ opacity }} data-handler-id={handlerId} className={styles["variable-container"]}>
			<Overlay vertical center classes={`${styles["variable-overlay"]}`} color="primary" id={props.id} delete={props.delete} />
			<pre>
				<select onChange={(e)=>props.update(props.id,{variable:e.target.value})} value={props?.selected?.variable || ""}>
					<option></option>
					{props?.variables?.map((v,j) => (
						<option key={`logic-var-${j}`} value={`${v.id}.${v.variable}`}>{v.text}</option>
					))}
				</select>
				<div className={styles['input-group']}>
					<select onChange={(e)=>props.update(props.id,{operator:e.target.value})} value={props?.selected?.operator || ""}>
						<option>=</option>
						<option>!=</option>
						<option>&gt;</option>
						<option>&gt;=</option>
						<option>&lt;</option>
						<option>&lt;=</option>
					</select> 
					<input type="text" placeholder="{value}" onChange={(e)=>props.update(props.id,{value:e.target.value})} value={props?.selected?.value || ""}/>
					<select onChange={(e)=>props.update(props.id,{andor:e.target.value})} value={props?.selected?.andor || ""}>
						<option>AND</option>
						<option>OR</option>
					</select>
				</div>
				{/*<span>{props.id}</span>*/}
			</pre>
		</div>
	);
}