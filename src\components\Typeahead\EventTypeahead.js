import React, {useCallback} from 'react';

import {Typeahead} from './Typeahead';
import Events from '../../api/Events';


/**Basic async typeahead for searching and selecting events.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Services back
*/
export const EventTypeahead = (props) => {

    const getEvents = useCallback(async(query, perPage, page=1)=>{
        try{
            let response = await Events.getSimple({
                search: query,
                page_no: page,
                max_records: perPage,
                ...props.filters
            });           
            let responseObj = {
                data: response?.data?.events || null,
                errors: response?.errors || null
            }
            return responseObj;
        }catch(ex){console.error(ex)}
    },[props.filters]);

    return(
        <Typeahead
            {...props}
            id="event-search"
            makeRequest={getEvents}
            placeholder={props.placeholder}
            paginated={true}
            multiple={props.multiple}
            async={props.async || false}
        />
    )
}
