import React, { useState, useEffect, useRef, useCallback} from 'react';
import { Button } from 'react-bootstrap'

import ErrorCatcher from '../../../components/common/ErrorCatcher';

import Users from '../../../api/Users';

//Called in the component "src\containers\AccountMerges\AccountMerges.js"
export const UndoMerge = (props) => {

    const {onClose,currentUserRole,activeMerge} = props;

    const mountedRef = useRef(false);
    const [error, setError]=useState();
    const [primaryRoles, setPrimaryRoles]=useState();
    const [theyreSure, setTheyreSure]=useState(false);
    const [canUndo, setCanUndo]=useState(false);

//#region useEffect
    useEffect(()=>{
        mountedRef.current = true;

        return()=>{
            mountedRef.current = false;
        }
    },[]);
    
    useEffect(()=>{
        //make a call for the primary user on the merged account to check the roles of that account
        const getUserRole = async(varId)=>{
            try{
                let response = await Users.get({id: varId});
                if(!response.errors){
                    setPrimaryRoles(response.data[0].roles);
                }else{
                    setError(<ErrorCatcher error={response.errors} />)
                }
            }catch(ex){console.error(ex)}
        }
        
        if(mountedRef.current && activeMerge) getUserRole(activeMerge.new_user_id);
    },[activeMerge]);

    useEffect(()=>{
        //staff admins can only undo Patron accounts.  To undo any other account, have to have SB Master Admin role
        if(currentUserRole.id !== 1 && primaryRoles){
            let canUndo = true;
            primaryRoles.forEach((role)=>{
                if(role.id !==7) canUndo = false; 
                if(mountedRef.current) setCanUndo(true);
            })
            if(!canUndo &&mountedRef.current) {
                onClose("error", "Based on your account role, only patron accounts may be unmerged.  One or more of the accounts involved with the merge possessed a higher role.")
            }
        }
        else setCanUndo(true);
        
    },[currentUserRole, primaryRoles, onClose]);

    useEffect(()=>{
        const startUndo=async()=>{
            try{
                let response = await Users.Merge.undo_merge({primary_user_id: activeMerge.new_user_id, merged_user_id: activeMerge.old_user_id })
                if(!response.errors){
                    onClose("success", "Accounts successfully unmerged!")
                }else{ 
                    setError(<ErrorCatcher error={response.errors} />);
                    setTheyreSure(false);
                }
            }catch(ex){console.error(ex)}
        }
        
        if(mountedRef.current && theyreSure && activeMerge) startUndo();
    },[theyreSure, activeMerge, onClose])
//#endregion useEffect


  return (
    <div>
        {error}
        {props.activeMerge && 
        <>
            <p>
                Are you sure you want to undo the merge of {props.activeMerge.new_user_first_name} {props.activeMerge.new_user_last_name} and {props.activeMerge.old_user_first_name} {props.activeMerge.old_user_last_name}?
            </p>
            <p>
                After you unmerge, check the accounts restored for proper or missing information.  
            </p>
            <p>
                <Button type="button" onClick={()=>setTheyreSure(true)} disabled={canUndo ? false : true}>Yes</Button>
                <Button type="button" onClick={()=>props.onClose()}>No</Button>
            </p>
        </>
        }
    </div>
  )
}
