import React, {useState, useEffect} from 'react';
import {<PERSON>, Col, Card, Button} from 'react-bootstrap';

import APIEvents from '../../../../../api/Events';

const News = (props) => {
    // this should be in every component, its used to forward the click event to the builder if in preview mode
    let preview_click=null;
    if (props.is_preview && props.onClick){
        preview_click = props.onClick;
    }

    const [data, setData] = useState([]);

    useEffect(() => {
        return () => {
            setData([]);
        }
    }, []);

    useEffect(() => {
        const _loadPlaceholderImages = async () => {
            // authenticate with pexels
            const response = await fetch(`https://api.pexels.com/v1/search?query=${props.placeholder_images}&per_page=${props?.limit || 3}`, {
                method: "GET",
                headers: {
                    Authorization:"563492ad6f91700001000001fc602e04eb6447748e9919bf75c1360f"
                }
            });
            const data = await response.json();
            if (data?.photos) return data.photos;
            return null;
        }
        

        const _loadNews = async () => {
            const response = await APIEvents.getSimple({
                start_datetime:"now",
                max_records:props?.limit || 3,
                sort_col:"start_datetime",
                sort_direction:"asc",
                event_types:props?.event_types || null
            });
            if (response?.data?.events){
                if (props?.placeholder_images){
                    const images=await _loadPlaceholderImages();
                    if (images){
                        images.forEach((item,i)=>{
                            if (!response.data.events[i].image) response.data.events[i].image=item.src.medium;
                        });
                    }
                } 
                setData(response.data.events);
            } 
        }

        if (props?.data) setData(props.data);
        else _loadNews();
    }, [props?.data,props?.limit,props?.event_types,props?.placeholder_images]);


    const _trimDescription = (description,length=120) => {
        if (description?.length>length){
            description=description.substring(0,length);
            description=description.substring(0,Math.min(description.length,description.lastIndexOf(" ")));
            description=description+"...";
        }
        return description;
    }

    return (
        <div className={`news_${props.page_id} ${props.className || ""}`} style={props?.style || null} onClick={preview_click}>
            <Row className={`row_${props.page_id}`}>
                <Col sm="12" className={`col_${props.page_id}`}>
                    <h1>News & Articles</h1>
                </Col>
                {data && data.map((item, i) => (
                    <Col sm={12} lg={4} key={`news-card-${item.id}-${i}`} className={`col_${props.page_id}`}>
                        <Card className={`card_${props.page_id}`}>
                            <Card.Img variant="top" src={item.image || props.company_context.logoNoText} className={`card-img-top_${props.page_id}`} style={{objectFit:"cover",aspectRatio:" 1 / 1"}} alt={item.name} />
                            <Card.Body className={`card-body_${props.page_id}`}>
                                <Card.Title className={`card-title_${props.page_id}`}>{item.name}</Card.Title>
                                <Card.Text className={`card-text_${props.page_id}`}>{_trimDescription(item.text)}</Card.Text>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>
            <Row className={`row_${props.page_id}`}>
                <Col className={`col_${props.page_id}`}>
                    <Button href="#!" className={`btn_${props.page_id}`}>View All</Button>
                </Col>
            </Row>
        </div>
    );
}

export default News;