import React, {useState, useEffect, useCallback} from 'react';
import { Row, Col, Button } from 'react-bootstrap';

import { Step1 } from './Step1';
import { Step2 } from './Step2';
import { Step3 } from './Step3';
import { Step4 } from './Step4';
import { Step5 } from './Step5';

import styles from './Onboarding.module.scss';

export const Onboarding = React.forwardRef((props, ref) => {    
    // this should be in every component, its used to forward the click event to the builder if in preview mode
    let preview_click=null;
    if (props.is_preview && props.onClick){
        preview_click = props.onClick;
    }

    const [error, setError] = useState();
    const [submitting, setSubmitting] = useState(false);
    const [disableNext, setDisableNext] = useState(false);
    const [disableBack, setDisableBack] = useState(false);

    const [step, setStep] = useState(1);
    const [referrerStep, setReferrerStep] = useState();
    const [component, setComponent] = useState();
    const [stepValues, setStepValues] = useState({});

    const saveStepValues = useCallback((values) => {
        let _stepstuff = {...stepValues, ...values};
        setStepValues(_stepstuff);
    }, [stepValues]);

    const clickHandler = useCallback((e, _step=null, referrer=null) => {
        e.preventDefault();
        e.stopPropagation();

        if (_step) setStep(_step);
        else setStep(step + 1);
        
        if (referrer) setReferrerStep(referrer);
    }, [step]);

    const submitHandler = async (e) => {
        e.preventDefault();
        e.stopPropagation();

        setSubmitting(true);
        
        if (step === 4){
            if (stepValues?.collectJS){
                setDisableNext(true);
                setDisableBack(true);
                await stepValues.collectJS.startPaymentRequest();
                const tokenres = await stepValues.collectJS.tokenPromise;
                if (tokenres.token) saveStepValues({token: tokenres.token});
            }
        }
    }

    useEffect(() => {
        if (stepValues?.token && step===4){
            setDisableBack(false);
            setDisableNext(false);
            setStep(5);
        }
    }, [stepValues?.token, step, setDisableBack, setDisableNext]);

    useEffect(() => {
        if (step===1) setDisableNext(false);

        const _props = {
            submitting: submitting,
            click: clickHandler,
            disableNext: setDisableNext, // sets the state to disable the next button, in case we need to wait for an async call to finish, or a validation to pass
            disableBack: setDisableBack,
            referrerStep: referrerStep, // keeps track if the next or back button was pressed, in case we need to skip a step we either go to the previous or to the next of the skipped step
            currentStep: step, // keeps track of the current step
            saveStepValues: saveStepValues,
            stepValues: stepValues,
            product_ids: [500, 775, 90],
            ...props
        }

        switch(step){
            /*
            case 7:
                setComponent(<Step7 {..._props} />);
                break;
            case 6:
                setComponent(<Step6 {..._props} />);
                break;
            */
            case 5:
                setComponent(<Step5 {..._props} />);
                break;
            case 4:
                setComponent(<Step4 {..._props} />);
                break;
            case 3:
                setComponent(<Step3 {..._props} />);
                break;
            case 2:
                setComponent(<Step2 {..._props} />);
                break;
            case "done":
            case 1:
            default:
                setComponent(<Step1 {..._props} />);
                break;
        }
    }, [step, submitting, clickHandler, props, referrerStep, saveStepValues, stepValues]);


    /*
    useEffect(() => {
        if (stepValues) console.log(stepValues);
    }, [stepValues]);
    */


    useEffect(() => {
        return () => {
            setStep(1);
            setComponent(null);
            setError(null);
            setSubmitting(false);
            setDisableNext(false);
            setDisableBack(false);
            setStepValues({});
        }
    }, []);    


    if (!component) return null;

    return (
        <Row className={styles.wrapper} ref={ref} onClick={preview_click}>
            <Col sm={12} lg={6} className={`${styles.sidebar} ${styles[`step-${step || 1}`]}`}>
                <div className={styles.logo}/>
                <p>
                    SiteBoss is a super awesome thing created by a super awesome team that loves breaking production.<br/>
                    Sound good? Then let's get started!
                </p>
                <br/>
                <ul className={styles.steps}>
                    <li className={step===1 ? styles.active : null}><span>1</span>About you</li>
                    <li className={step===2 ? styles.active : null}><span>2</span>About your company</li>
                    <li className={step===3 ? styles.active : null}><span>3</span>Select a plan</li>
                    <li className={step===4 ? styles.active : null}><span>4</span>Payment</li>
                    <li className={step===5 ? styles.active : null}><span>5</span>Done!</li>
                </ul>
            </Col>
            <Col sm={12} lg={6} className={styles.wizard}>
                {component}
                {error && <div className="error-text-ctr">{error}</div>}
                {step &&
                    <div className={`${styles.toolbar}`} style={props.is_preview ? {position: "absolute", width: "100%", left: 0, justifyContent: "flex-end"} : undefined}>
                        {step > 1 && step < 7 && <Button variant="light" disabled={props.submitting || disableBack} onClick={e=>clickHandler(e, step-1, "back")}>Back</Button>}
                        {step < 5 && <Button variant="primary" disabled={props.submitting || disableNext} onClick={e=>clickHandler(e, step+1, "next")}>Continue</Button>}
                        {step === 5 && <Button variant="primary" disabled={props.submitting || disableNext} type="button" onClick={submitHandler} id="payButton">Finish</Button>}
                    </div>
                }
            </Col>
        </Row>
    );
});