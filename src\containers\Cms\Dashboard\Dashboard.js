import React, { useState, useEffect } from 'react';
import { useH<PERSON><PERSON>,Link } from "react-router-dom";
import {Card, Container, Col, Row, Button, Breadcrumb}  from 'react-bootstrap';
import Table from '../../../components/common/Table';
import Stack from '../../../components/common/Stack';
import { authCheck } from "../../../utils/auth";

import './Dashboard.scss';
import SubHeader from '../../../components/common/SubHeader';

import APICms from '../../../api/Cms';

const Dashboard = (props) => {
    let history = useHistory();

    //let user = authCheck(history);

    const newButtonHandler = (event) => {
        history.push("/p/cms/new");
    }

    const [loading,setLoading]=useState(true);
    const [websites,setWebsites]=useState([]);

    // first load, get companies from api
    useEffect(() => {

        const _getWebsites = async () => {
            try {
                setLoading(true);
                const res=await APICms.websites.get();
                if (res.data && mounted) setWebsites(res.data?.map( cmp => cmp));
                setLoading(false);
            } catch (e){
                console.error(e);
            } 
        }

        let mounted = true;

        _getWebsites();

        
        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
            setWebsites([]);
        }        
    },[]);


    const columns = React.useMemo(
        () => [{
            id: 'table',
            columns: [
                {
                    Header: 'Website',
                    id: 'name',
                    accessor: 'name',
                },
                {
                    Header: 'Description',
                    id: 'description',
                    accessor: 'description',
                },
                {
                    Header: 'URLs',
                    id: 'urls',
                    accessor: d => (
                            d.website_urls.map((v,i)=>(
                                <>
                                    <span key={`website-url-${i}`}>{(v.subdomain?v.subdomain+".":"")+v.domain}</span><br/>
                                </>
                            ))
                    ),
                },
                {
                    Header: 'Themes',
                    id: 'themes',
                    accessor: d => (
                        d.website_urls.map((v,i)=>(
                            <>
                                <span key={`website-theme-${i}`}>{v.theme_name || "Default"}</span><br/>
                            </>
                        ))
                    ),
                },
                {
                    Header: 'Index Page',
                    id: 'index_page',
                    accessor: d => (
                        d.website_urls.map((v,i)=>(
                            <>
                                <span key={`website-index-${i}`}>{v.index_page}</span><br/>
                            </>
                        ))
                    ),
                },
                {
                    id: 'id',
                    url:"/cms/:id",
                    show:false,
                },
            ],
        }],[]
    );
        
    return (
        <Container fluid>
            <SubHeader items={[
                {linkAs:Link,linkProps:{to:"/p/home"},text:"Home"},
                {text:"CMS Dashboard"}
            ]} />
            <Row className="body">
                <Col>
                    <Card className={`${loading?" loading":""} content-card`}>
                        <Stack direction="horizontal" gap={2}>
                            <h4 className="tm-1 section-title order-2 order-lg-1 ">CMS Dashboard</h4>
                            <div className="ms-sm-auto order-1 order-lg-2">
                                <Button variant="primary" onClick={newButtonHandler}>New Website</Button>
                            </div>
                        </Stack>
                        <hr/>
                        <Table columns={columns} data={websites} />
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Dashboard;