@import '../../../assets/css/scss/themes.scss';


$bg-color: $primary-font-color;

.buttons{
    margin: $card-margin;
}

.stripes {
    position: relative;
    &::before{
        position:absolute;
        top: 0;
        left: 0;
        background-image: linear-gradient(45deg, 
            $bg-color 25%, 
            $card-background-color 25%, 
            $card-background-color 50%, 
            $bg-color 50%, 
            $bg-color 75%, 
            $card-background-color 75%, 
            $card-background-color 100%
        ) !important;
        background-size: 5px 5px;
        width:100%;
        height:100%;
        content: "";
        opacity: 0.2;
    }
}