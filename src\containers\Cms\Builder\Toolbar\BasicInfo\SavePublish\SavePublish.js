import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Button } from 'react-bootstrap';

//import APICms from '../../../../../../api/Cms';

export const SavePublish = (props) => {

    const cmsSelector = useSelector(state => state.cms);
    //const cmsSelectorElements = useSelector(state => state.cmsElements.present);

    const [submitting, setSubmitting] = useState(false);
    const [enabled, setEnabled] = useState(0); // 0 or null = all disabled, 1 = save enabled, 2 = publish enabled

    /*
    useEffect(() => {
        setEnabled(1);
    }, [cmsSelectorElements.elements]);
    */

    useEffect(() => {
        if (cmsSelector.currentPageProps?.config?.is_code===1) {
            setEnabled(1);
        }
    },[cmsSelector.currentPageProps?.config?.is_code]);

    useEffect(() => {
        setEnabled(0);
        const handleStorageChange = (e) => {
            e.preventDefault();
            setEnabled(1);
        };
        window.addEventListener("storage", handleStorageChange);
      
        return () => {
            setSubmitting(false);
            setEnabled(null);
            window.removeEventListener("storage", handleStorageChange);
        }
    }, []);

    const clickHandler=async (action) => {
        setSubmitting(true);
        const res = await props.save(action);

        if (res){
            setSubmitting(false);
            setEnabled(res.button_enabled);
            res.result.then(data=>{
                if (data.errors) props.error(data.errors);
                else props.success(res.action_text);
            });
        }
    }

    return (
        <>
            <Button variant="primary" disabled={submitting || enabled!==1} onClick={()=>clickHandler("save")}>Save</Button>
            <Button variant="publish" disabled={submitting || enabled!==2} onClick={()=>clickHandler("publish")}>Publish</Button>
        </>
    );
}