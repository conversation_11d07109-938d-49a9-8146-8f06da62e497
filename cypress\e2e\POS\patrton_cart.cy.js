/*eslint-disable*/

// to adapt to dev vs qa, find //DEV or //QA and options will be marked accordingly
let qaDevPatron = Cypress.env('impact_patron_user');
let devPatron = Cypress.env('impact_patron_symbol_name');
let qaDevStaff = Cypress.env('impact_staff_user');
let qaDevAdmin = Cypress.env('impact_admin_user');
let qaDevSB = Cypress.env('impact_sb_user');
let password = Cypress.env('login_password');
let baseUrl = "http://portal-qa.impactathleticsny.com/p/"

let eventId=550
let userFirstName
let bookingName;
let eventName;
let productName;
let bookingPrice;
let eventPrice;
let productPrice;
let numberOfTokens;

describe("This test will check that the patron cart is able to be added to from multiple sources", {testIsolation: false, scrollBehavior: "center"}, ()=>{
    let local;

    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, qaDevAdmin, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach("Restore the local user",()=>{
        cy.restoreLocalUser(JSON.parse(local));
        cy.viewport(1920, 1080);
        userFirstName = (JSON.parse(local).profile.first_name);
        cy.wait(2000)
    });//end beforeEach

    context('Set up tests',()=>{
        it("will make sure we're starting with an empty cart",()=>{
        let length;
        
        // //force in case tests move the screen in a way that the button is "covered"
        // cy.get('[data-cy="header-shopping-cart"]').click({force: true});
        cy.window()
            .its('store')
            .invoke("getState").then((state)=>{
                length = state.pos[7].items.length
                if(length > 0){
                    while (length > 0){
                        cy.log(length)
                        cy.get('[data-cy="cart-header-btn"]')
                            .click();
                        //forcing whatever one it clicks on because we want them all gone
                        cy.get('[data-cy="preview-item"]')
                            .first()
                            .get('[data-cy="delete-prod-btn"]')
                            .first()
                            .click({force: true});
                        cy.get('[data-cy="confirmation-proceed-btn"]')
                            .contains('Yes')
                            .click({multiple: true, force: true});
                        // cy.get('[data-cy="header-shopping-cart"] > .far')
                        cy.wait(250)
                        length--
                    }
                    return
                }
            })
        });//end remove all items from the cart before beginning the test     

        it("will get the event Id", ()=>{
            cy.visit(`${baseUrl}events`)
            cy.get('#searchTerm')
                .type("Sky Flyers")
            cy.get('tbody > :nth-child(1) > :nth-child(2)')
                .contains("Sky Flyers").parent().within(()=>{
                    cy.get(':nth-child(1)')
                        .invoke('text')
                        .then((text)=>{
                            eventId = text;
                        })
                })
        })
    })

    context("It will add a product from a shop face available to patrons",()=>{
        let product;
    
        it("will go to the shop page and check a product has data",()=>{
            cy.intercept('POST', '/api/register/*').as('getRegister');
            cy.intercept('GET', '/api/group/**').as('getGroups')
            cy.intercept('GET', '/api/category/**').as('getCategories')
            cy.intercept('POST', '/api/product').as('getProducts');
            cy.intercept('POST', '/api/order/latest_open').as('getLatestOrder')
            cy.intercept('/**').as('getRequest')
            cy.visit(`${baseUrl}shop`); 
            cy.wait('@getRegister');
            // cy.wait('@getGroups');
            cy.wait('@getLatestOrder')
            cy.wait('@getCategories');
            cy.wait('@getProducts').then((response)=>{
                product= (JSON.parse(response.response.body)).data.products[0]
                cy.log(product)
            });

            cy.wait('@getRequest')
            cy.wait(2000)
            cy.wait('@getRequest')

            cy.get('[data-cy="product-cards-wrapper"]')
                .children()
                .eq(0)
            cy.get('[data-cy="product-name"]')
                .eq(0)
                .invoke('text').as('productName')
            cy.get('@productName').then((text)=>{
                let name = text
                cy.wrap(name).as('productName')
            })
            cy.get('@productName').then(text=>{
                productName=text
            })
            cy.get('[data-cy="product-price"]')
                .eq(0)
                .invoke('text').as('productPrice')
            cy.get('@productPrice').then((text)=>{
                let price = text
                cy.wrap(price).as('productPrice')
            })
            cy.get('@productPrice').then(text=>{
                productPrice=text.trim()//keeps ending up with a random space at the end
            })
            cy.get('[data-cy="product-cards-wrapper"]')
                .children()
                .eq(0)
                .click()
        });//end checking product has data

        it("will check the modal and add the product to the cart",()=>{
            cy.intercept('PUT', "/api/order/update").as('orderUpdate')
            
            cy.get('[data-cy="item-details-modal"]').should('exist')
            cy.get('[data-cy="detail-product-name"]')
                .invoke('text')
                .should('equal', productName)
            cy.get('[data-cy="detail-product-price"]')
                .invoke('text')
                .should('include', productPrice)
            cy.get('[data-cy="detail-product-description"]').should('exist');
            cy.get('[data-cy="details-add-to-cart"]')
                .click();

                
                if(product.product_variants.length > 1){
                cy.log(`${String.fromCodePoint(0x1F92F)} Next we use the variant picker to select our item! There should currently only ever be one option`);
                cy.get('[data-cy="variant-picker-modal"]').should('be.visible');
                cy.get('[data-cy="variant-picker-wrap"]')
                    .children()
                    .its('length')
                    .should('equal', 1)
                cy.get('[data-cy="variant-picker-wrap"]')
                    .children()
                    .eq(0)
                    .click(0);
                cy.wait(500)
                cy.get('[data-cy="details-add-to-cart"]').should('exist')
                cy.get('[data-cy="details-add-to-cart"]').click();
            }
            cy.wait('@orderUpdate');
        }); //end checking the modal and adding the product to the cart

        it("will check the amount of items in Redux",()=>{
            cy.window()
                .its('store')
                .invoke('getState')
                .its('pos')
                .its('7')
                .its('items')
                .should('have.length', 1);
        }); //check the number of items in Redux

    });//end add a product in shop face

    context("It will register for an event",()=>{
        
        let event;
        let product;

        it('will navigate to an event registration page and check the data',()=>{
            cy.intercept('GET', `/api/event/${eventId}**`).as('getSkyFlyers'); //DEV
            // cy.intercept('GET', `/api/event/${eventId}**`).as('getSkyFlyers'); //QA
            cy.intercept('POST', '/api/product').as('getPrice');
            cy.intercept('GET', '/api/user/user/**').as('getUser');
            cy.visit(`${baseUrl}event-register?event=${eventId}`)
            
            cy.wait('@getSkyFlyers');
            cy.wait('@getPrice');

            cy.get('[data-cy="register-event-name"]').invoke('text').should('include', "Sky Flyers");
            eventName="Sky Flyers"
            cy.get('[data-cy="event-register-top-card"]').within(()=>{
                cy.get('[data-cy="event-when"]')
                    .invoke('text')
                    .should('include', "When:Thu 05/16/2024 11:15 PM - 11:29 PM");
                cy.get('[data-cy="event-where"]')
                    .invoke('text')
                    .should('include', 'Where:Basketball Court 4');
                cy.get('[data-cy="event-fee"]')
                    .invoke('text')
                    .should('include', 'Event Fee:$10.00');
                    eventPrice = "$10.00"
            });
        });//end check navigate to registration page

        it("will register for an event",()=>{
            cy.intercept('PUT','api/order/update').as('updateOrder')
            //this set of tests is being done with a user that has no family
            cy.get('[data-cy="register-event-participant"]')
                .invoke('text')
                .should('include', userFirstName)
            cy.get('[data-cy="event-page-register-btn"]').click();
            cy.get('[data-cy="pop-up-button"]')
                .should('contain', 'OK')
                .click();
            cy.wait('@updateOrder')
        })//end register for event

        it("will check that Redux has the proper number of items",()=>{
            cy.wait(2000)
            cy.window()
                .its('store')
                .invoke('getState')
                .its('pos')
                .its('7')
                .its('items')
                .should('have.length',2);
        }); //end check Redux

        it("will make sure the cart shows both items",()=>{
            cy.get('[data-cy="header-shopping-cart"] > .far')
                .click();
                //at this level, each row will have 3 columns, so check in multiples of 3
                cy.get('[data-cy="preview-item"]')
                    .children()
                    .its('length')
                    .should('equal', 6);
                cy.get('[data-cy="preview-item"]')
                    .invoke('text')
                    .should('contain', eventName)
                    .and('contain', eventPrice);
                cy.get('[data-cy="preview-item"]')
                    .invoke('text')
                    .should('contain', productName)
                    .and('contain', productPrice);  
        }); //end checking cart display has both items
        
    });//end register for an event

    context("It will book a service",()=>{

        //for whatever reason, the timing changes to App.js and the auth/theme have made this tests not work if they're individual tests.  So....one long test it is
        it("will navigate to services and open modal",()=>{
            cy.intercept('POST', '/api/service').as('getServices');
            cy.intercept('POST', '/api/product').as('getProduct');
            cy.intercept('POST', '/api/location').as('getLocation');

            cy.visit(`${baseUrl}my/services`);
            cy.wait(500)
            cy.wait('@getServices')
            cy.get('[data-cy="book-service-btn"]')
                .click();
            cy.get('[data-cy="service-booking-modal"]')
                .should('exist')
            cy.get('#search_input')
                .type("Midnight's Party")
            cy.wait('@getServices')
            cy.wait('@getLocation')
            cy.get('[data-cy="booking-rows"]')
                .should('exist')
                .each(($row)=>{
                    cy.get($row)
                    let rowText=$row.text()
                    if(rowText.includes("Midnight's Party")){
                        cy.get($row)
                        .click();
                        return
                    }
            })
        //end navigate to services and open booking modal

        cy.log(`${String.fromCodePoint(0x1F92F)}will make sure the modal has data`)
            cy.get('[data-cy="service-name"]').should('contain', "Midnight's Party")
            bookingName="Midnight's Party"
            cy.get('[data-cy="service-description"]').invoke('text').should('contain', "A party")
            cy.get('[data-cy="service-details"]')
                .children()
                .eq(0)
                .should('contain', "15 minutes")
            cy.get('[data-cy="service-details"]')
                .children()
                .eq(1)
                .should('contain', '1 hour (4 timeslots = $0.6)')
            bookingPrice="0.6"
            numberOfTokens=4
            cy.get('[data-cy="service-details"]')
                .children()
                .eq(2)
                .should('contain', "registered users")
            cy.get('[data-cy="service-details"]')
                .children()
                .eq(3)
                .invoke('text')
                .should('contain', "at least 10 minutes");
        //end make sure the booking has data

        cy.log(`${String.fromCodePoint(0x1F92F)}will select a date range`)
            cy.get('[data-cy="date-range-picker"]').click();
            cy.get('.react-datepicker').should('exist');
            cy.get('[data-cy="service-description"]').click();
            cy.get('.header-date')
                .children()
                .eq(2)
                .invoke('text')
                .as('firstDate');
            cy.get('[data-cy="date-range-forward"]').click()
            cy.get('.header-date')
                .children()
                .eq(2)
                .invoke('text')
                .as('secondDate')
            expect('@firstDate').to.not.equal('@secondDate');
            //select service blocks
            cy.get('[data-cy="time-cell"]')
                .children()
                .eq(0)
                .click();
            cy.get('[data-cy="next-btn"]').click({force:true});
        //end select date range

        cy.log(`${String.fromCodePoint(0x1F92F)}will double check the service confirmation page`)
            cy.intercept('PUT', 'api/order/update').as('updateOrder')
            cy.get('[data-cy="booking-summary"]')
                .invoke('text')
                .should('contain', "Price").and('contain', bookingPrice);
            cy.get('[data-cy="booking-summary"]')
                .invoke('text')
                .should('contain', bookingName).and('contain', "Arcade");
            cy.get('[data-cy="not-enough-tokens"]')
                .invoke('text')
                .should('contain', "You do not have enough tokens to redeem for this timeslot. But that's ok!")
            cy.get('[data-cy="booking-summary-confirm"]')
                .click()
            cy.wait('@updateOrder');
            cy.get('[data-cy="complete-confirm-booking"]')
                .click()
        //end double check the confirmation page
        }); 
    });//end book a service

    context("it will follow up the tests with checks on the cart and logout",()=>{
        it("will log all the variables for checking",()=>{
            cy.log("User First Name", userFirstName)
            cy.log("Booking Name", bookingName)
            cy.log("Event Name", eventName)
            cy.log("Product Name", productName)
            cy.log("Booking Price", bookingPrice)
            cy.log("Event Price", eventPrice)
            cy.log("Product Price", productPrice)
            cy.log("Number of Tokens", numberOfTokens)
        });

        it("will make sure the page has been changed to the cart and check the subtotal",()=>{
            // cy.intercept('POST', "/api/event").as('eventDetails')
            // cy.wait('@eventDetails')
            cy.url().should('include', "cart");
            cy.log(`${String.fromCodePoint(0x1F92F)} we will check the redux state of the cart.  This service takes 4 tokens so it should have a length of 4.`)
            cy.window().its("store")
                .invoke('getState')
                .its('pos')
                .its('7')
                .its('items')
                .should('have.length', 2+numberOfTokens)
            cy.get('[data-cy="pc-order-sum-name"]')
                .eq(0)
                .should('contain', productName)
            cy.get('[data-cy="pc-order-sum-name"]')
                .eq(1)
                .should('contain', eventName)
            cy.get('[data-cy="pc-order-sum-name"]')
                .eq(2)
                .should('contain', bookingName);
            cy.get('[data-cy="pc-order-sum-price"]')
                .eq(0)
                .should('contain', productPrice)
            cy.get('[data-cy="pc-order-sum-price"]')
                .eq(1)
                .should('contain', eventPrice)
            cy.get('[data-cy="pc-order-sum-price"]')
                .eq(2)
                .should('contain', bookingPrice)

            cy.log(`${String.fromCodePoint(0x1F92F)} have to remove the dollar signs from the variables and convert them to ints in the background!`)
            bookingPrice = bookingPrice.substring(1)
            eventPrice = eventPrice.substring(1)
            productPrice = productPrice.substring(1)

            cy.get('[data-cy="pc-subtotal"]')
                .children()
                .last()
                .should('contain', +bookingPrice + +productPrice + +eventPrice);
        })//end check page change and subtotal
    
        it("Will logout to be able to log back in to check the integrity of the order",()=>{
            cy.get('[data-cy="header-profile-name"]').click()
            cy.get('.list-group > [href="/signout"]').should('contain', 'Sign out').click()
        });//end logout
    });

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
});

describe("It will be able to log back in and go to the patron cart", {scrollBehavior: "center", testIsolation:false}, ()=>{
    let local;
    
    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, qaDevAdmin, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach("Restore the local user",()=>{
        cy.viewport(1920, 1080);
        cy.restoreLocalUser(JSON.parse(local));
        cy.wait(1000)
    });//end beforeEach

    context("It will check the check the contents of the cart match those added from the previous set of tests",()=>{
        it("will make sure the items exist in the cart",()=>{
            cy.window()
                .its('store')
                .invoke('getState')
                .its('pos')
                .its('7')
                .its('items')
                .should('have.length', 2 + numberOfTokens);
        });//end ensuring items still exist

        it("will make sure that the tokens in the preview are condensed into one service",()=>{
            cy.get('[data-cy="header-shopping-cart"]').click();
            cy.get('[data-cy="preview-item').its('length').should('equal', 3)
        });//end checking the tokens condensed

        it("will make sure the product && event has a price",()=>{
            cy.get('[data-cy="header-shopping-cart"]').click()
            cy.get('[data-cy="preview-item"]')
                .eq(0)
                .invoke('text')
                .should('include', productName)
                .and('include', productPrice);
            cy.get('[data-cy="preview-item"]')
                .eq(1)
                .invoke('text')
                .should('include', eventName)
                .and('include', eventPrice);
        });//end checking each has a price


        //because CSS is no longer always loading correctly in Cypress d/t the theming changes, these ARE visible in Cypress and needs to be checked manually
        // it("will check that each row has a - button and no copy button and that subtotal matches the value of all products",()=>{
        //     cy.get('[data-cy="copy-prod-btn"]').should('not.exist')
        //     cy.get('[data-cy="delete-prod-btn"]').should('exist');

        //     cy.get('[data-cy="order-subtotal"]')
        //         .should('contain', +bookingPrice + +eventPrice + +productPrice)
        // })//end check for - and no copy

        it("will navigate to the cart page using a url",()=>{
            cy.visit(`${baseUrl}cart`);
            cy.get('.description-banner').should('exist');
            cy.get('.patron-cart-left').should('exist');
            cy.get('.patron-cart-right').should('exist');
            cy.wait(1000);
            cy.get('[data-cy="pc-header-home-bc"]').click()
            cy.wait(1000);

        });//end navigate to cart via url

        it("will go back home and then navigate to the cart page via the button",()=>{
            cy.get('[data-cy="header-shopping-cart"]').click();
            cy.get('.sc-checkout-btn')
                .should('exist')
                .click();

        });//end go back home and navigate via button
    });//end check contents of the cart from previous tests

    context("It will check that the patron cart has a proper display",()=>{
        it("with check for the proper headers", ()=>{
            cy.get('.description-banner')
                .should('exist')
                .should('contain', "Events & Services");
            cy.log(`${String.fromCodePoint(0x1F92F)} Check the header of the summary table too`)
            cy.get('.pc-item-sum-heading')
                .should('exist')
                .invoke('text')
                .should('include', "Product Name")
                .and('include', "For User")
                .should('include', "Item Description")
                .and('include', 'Price');
        });//the page has a header

        it("will check that there are three items",()=>{
            cy.get('[data-cy="pc-order-sum-row"]')
                .its('length')
                .should('equal', 3);
        });

        it("will check the details of the product entry",()=>{
            cy.get('[data-cy="pc-order-sum-row"]').then(($row)=>{
                cy.get($row)
                    .eq(0)
                    .children()
                    .eq(0)
                    .invoke('text')
                    .should("include", productName)
                cy.get($row)
                    .eq(0)
                    .children()
                    .eq(1)
                    .invoke('text')
                    .should("include", userFirstName)
                cy.get($row)
                    .eq(0)
                    .children()
                    .eq(2)
                    .invoke('text')
                    .should('exist')
                cy.get($row)
                    .eq(0)
                    .children()
                    .eq(3)
                    .invoke('text')
                    .should('include', productPrice)
            })
        })//end checking the product

        it("will check the details of the event product",()=>{
            cy.get('[data-cy="pc-order-sum-row"]').then(($row)=>{
                cy.get($row)
                    .eq(1)
                    .children()
                    .eq(0)
                    .invoke('text')
                    .should("include", eventName)
                cy.get($row)
                    .eq(1)
                    .children()
                    .eq(1)
                    .invoke('text')
                    .should("include", userFirstName)
                cy.get($row)
                    .eq(1)
                    .children()
                    .eq(2)
                    .invoke('text')
                    .should('exist')
                cy.get($row)
                    .eq(1)
                    .children()
                    .eq(3)
                    .invoke('text')
                    .should('include', eventPrice)
            })
        });//end checking event 

        it("will check the details of the service booking",()=>{
            cy.get('[data-cy="pc-order-sum-row"]').then(($row)=>{
                cy.get($row)
                    .eq(2)
                    .children()
                    .eq(0)
                    .invoke('text')
                    .should("include", bookingName)
                cy.get($row)
                    .eq(2)
                    .children()
                    .eq(1)
                    .invoke('text')
                    .should("include", userFirstName)
                cy.get($row)
                    .eq(2)
                    .children()
                    .eq(2)
                    .invoke('text')
                    .should('exist')
                cy.get($row)
                    .eq(2)
                    .children()
                    .eq(3)
                    .invoke('text')
                    .should('include', bookingPrice)
            })
        }); //end checking service booking

    }); //end checking summary display check

    context("each field of the input will be typeable", ()=>{
        it("load with the payment button disabled",()=>{
            cy.get('[data-cy="pay-button"]')
                .should('exist')
                .and('be.disabled');
        }); //end payment button disabled check

        it("will accept input into the CC, CCV, zip, and exp date inputs",()=>{
            cy.get('#CollectJSInlineccnumber')
                .type("4111111111111111")
            cy.get('#CollectJSInlineccexp')
                .type('1025');
            cy.get('#CollectJSInlinecvv')
                .type('999');

        });//end input into cc, ccv, exp date, zip

        it("will still have the button disabled",()=>{
            cy.get('[data-cy="pay-button"]')
                .should('exist')
                .and('be.disabled');
        }); //end check button disabled

        it("will enter the rest of the address fields", ()=>{
            cy.get('[data-cy="cart-first-name"]')
                .type("Fairies");
            cy.get('[data-cy="cart-last-name"]')
                .type("Unicorns")
            cy.get('[data-cy="cart-address-1"]')
                .type("1234")
            cy.get('[data-cy="cart-address-2"]')
                .type("5678")
            cy.get('[data-cy="cart-city"]')
                .type("Magical Land")
            cy.get('[data-cy="cart-state"]')
                .select("OR")
            cy.get('[data-cy="cart-postal-code"]')
                .type("77777")
        }); //enter the rest of the fields

        it("will check that there is input in each field",()=>{
            cy.get('[data-cy="cart-first-name"]')
                .invoke('val')
                .should('equal', "MidnightFairies")
            cy.get('[data-cy="cart-last-name"]')
                .invoke('val')
                .should('equal', "BooUnicorns" )
            cy.get('[data-cy="cart-address-1"]')
                .invoke('val')
                .should('equal', "1234" )
            cy.get('[data-cy="cart-address-2"]')
                .invoke('val')
                .should('equal', "5678")
            cy.get('[data-cy="cart-city"]')
                .invoke('val')
                .should('equal', "Magical Land" )
            cy.get('[data-cy="cart-state"]')
                .invoke('val')
                .should('equal', "OR")
            cy.get('[data-cy="cart-postal-code"]')
                .invoke('val')
                .should('equal', "77777" )
        }) //check input

    }); //each field will be typeable

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
});


