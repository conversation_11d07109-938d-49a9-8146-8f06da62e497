import React, { useState,useEffect,useRef,Suspense } from 'react';
//import { useDispatch } from 'react-redux';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import Canvas from '../../../components/Canvas';
// import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { Button } from 'react-bootstrap';
//import * as actions from '../../../store/actions';

import Locations from '../../../api/Locations';

export const Map = (props) => {
    //const dispatch = useDispatch();

    const ref = useRef(null);
    const [locations,setLocations]=useState();
    //const [selectedItems,setSelectedItems]=useState([]);

    const clickHandler=(items)=>{
        //setSelectedItems(items); // this is triggered in components/Canvas once a specific shape is selected
    }

    /*
    // this will trigger the timeline as soon as an element is selected/deselected. Remove this for timeline to load like a wizard
    useEffect(() => {
        dispatch(actions.loadTimeline(selectedItems));
    }, [props,dispatch,selectedItems]);
    */
    
    useEffect(() => {
        let mounted = true;

        Locations.getStructured()
        .then(response=> {
            if(mounted) {
                setLocations(response.data[0].children);
                //main facility is not selectable, so we only want the child locations of the facility
            }
        }).catch(e => console.error(e));

        return () => {
            mounted = false;
        }
    }, []);

    if (!locations){
        return (
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
            </Suspense>
        );
    } else {
        return (
            <React.Fragment>
                <h1>Where is it happening?</h1>
                <p>
                    Select the location within our facility where your event is taking place.
                </p>
                <div ref={ref}>
                    {/*selectedItems.length>0 &&
                        <Button variant="primary" size="sm" href="#Timeline" onClick={(e)=>props.clickHandler(e,selectedItems)}>Next</Button>
                    */}
                    {/* <TransformWrapper
                    maxScale={1.5}
                    minScale={0.75}
                    >
                        {({ zoomIn, zoomOut, resetTransform, ...rest }) => (
                            <div>
                                <div className="tools">
                                    <Button onClick={() => zoomIn()}>
                                        <i className="far fa-search-plus" />
                                    </Button>
                                    <Button onClick={() => zoomOut()}>
                                        <i className="far fa-search-minus" />
                                    </Button>
                                    <Button onClick={() => resetTransform()}>Reset</Button>
                                </div>
                                <TransformComponent>
                                    <Canvas items={locations} clickHandler={clickHandler} />
                                </TransformComponent>
                            </div>
                        )}
                    </TransformWrapper> */}
                </div>
            </React.Fragment>
        );
    }
}