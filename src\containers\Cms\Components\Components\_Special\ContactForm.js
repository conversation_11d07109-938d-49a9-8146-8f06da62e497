import React, {useState, useEffect} from 'react';
import {Row, Col, Form, Button} from 'react-bootstrap';
import Toast from '../../../../../components/Toast';
import APICms from '../../../../../api/Cms';

const ContactForm = (props) => {
    // this should be in every component, its used to forward the click event to the builder if in preview mode
    let preview_click=null;
    if (props.is_preview && props.onClick){
        preview_click = props.onClick;
    }   

    const [fields, setFields] = useState(["first_name:required","last_name:required","email:required","phone","message:required"]);
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

    useEffect(() => {
        return () => {
            setFields([]);
            setValidated(false);
            setSubmitting(false);
            setError(null);
            setSuccess(null);
        }
    }, []);

    useEffect(() => {
        if (props?.fields){
            if (typeof props.fields === 'string') setFields(JSON.parse(props.fields));
            else setFields(props.fields);
        } 
    }, [props?.fields]);

    const submitHandler = async (e) => {
        if (!props.is_preview){
            const form = e.currentTarget;

            e.preventDefault();
            e.stopPropagation();

            setValidated(true);
            setSubmitting(true);
            setError(null);

            if (form.checkValidity() === true) {
                const formData = new FormData(e.target);
                const formDataObj = Object.fromEntries(formData.entries());
                
                // send the email
                try{
                    const response = await APICms.sendmail(formDataObj,props.send_to);
                    if (!response.errors && response.status === 200){
                        setSuccess(<Toast>Your information was sent successfully!</Toast>);
                    } else {
                        setError(response?.errors || response?.message || "There was an error sending your information. Please try again later.");
                    }
                    setSubmitting(false);
                } catch(e){
                    setError("There was an error sending your information. Please try again later.");
                    setSubmitting(false);
                }
            } else setSubmitting(false);
        }
    }

    const _format = (str) => {
        return str.replace(/_/g, " ").replace(/\w\S*/g, (txt) => {return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();});
    }

    return (
        <div className={`contact-form_${props.page_id} ${props.className || ""}`} style={props?.style || null} onClick={preview_click}>
            {props?.title && 
                <Row className={`row_${props.page_id}`}>
                    <Col className={`col_${props.page_id}`}>
                        <h1>{props.title}</h1>
                        {props?.subtitle && <p>{props.subtitle}</p>}
                    </Col>
                </Row>
            }
            <Form className={`form_${props.page_id}`} noValidate validated={validated} onSubmit={submitHandler} autoComplete="off">
            <Row className={`row_${props.page_id} mt-3`}>
                {fields.map((item, i) => {
                    const field = item.split(":");
                    let placeholder=field?.[2] || _format(field[0]), type="text";
                    switch(field[0]){
                        case "first_name":
                            placeholder="John";
                            break;
                        case "last_name":
                            placeholder="Doe";
                            break;
                        case "email":
                            placeholder="<EMAIL>";
                            type="email";
                            break;
                        case "phone":
                            placeholder="(*************";
                            type="tel";
                            break;
                        case "message":
                            placeholder="Your message here...";
                            type="textarea";
                            break;
                        default:
                            placeholder=field?.[2] || _format(field[0]);
                    }
                    return (
                        <Col sm={12} lg={field[0]==="message"?12:4} key={`contact-form-field-${i}`} className={`col_${props.page_id}`}>
                            <Form.Group className={`form-group_${props.page_id}`} controlId={`_contact-form-field-${i}`}>
                                <Form.Label className={`form-label_${props.page_id}`}>{field?.[2] || _format(field[0])}</Form.Label>
                                <Form.Control
                                    as={type==="textarea"?"textarea":"input"}
                                    type={type}
                                    placeholder={placeholder}
                                    required={field[1] === "required"}
                                    name={field[0]}
                                    className={`form-control_${props.page_id}`}
                                    rows={type==="textarea"?10:0}
                                />
                            </Form.Group>
                        </Col>
                    );
                })}
            </Row>
            {error &&
                <Row className={`row_${props.page_id}`}>
                    <Col className={`col_${props.page_id} error-text`}>
                        <div className={`card`}>{error}</div>
                    </Col>
                </Row>
            }           
            <Row className={`row_${props.page_id}`}>
                <Col className={`col_${props.page_id}`}>
                    <Button type="submit" className={`btn_${props.page_id}`} disabled={submitting}>Send</Button>
                </Col>
            </Row>
            </Form>
            {success}
        </div>
    );
}

export default ContactForm;