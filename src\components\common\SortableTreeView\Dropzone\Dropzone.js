import React from 'react';
import { useDrop } from 'react-dnd';

import styles from './Dropzone.module.scss';
export const Dropzone = (props) => {
    const [{canDrop, isOver}, drop] = useDrop({
        accept: 'SORTABLE_BRANCH',
		collect(monitor) {
			return {
				canDrop: monitor.canDrop(),
				isOver: monitor.isOver({shallow: true}),
			}
		},        
        drop: (item, monitor) => {
            if (!monitor.canDrop()) return;
            if (item.id === props.data.id) return;
            if (item.id === props.data.parent_id) return;

            if (!monitor.didDrop()) {
                if (props.onDrop) props.onDrop(item, props.data, props.isSubBranch);
            }
        }
    });

    return <div ref={drop} className={`${styles.dropzone} ${props.isSubBranch ? styles.internal : ""} ${(isOver && canDrop) ? styles.active : ""}`}/>;
};