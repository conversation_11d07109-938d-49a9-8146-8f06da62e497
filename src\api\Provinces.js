const get = () => {
    let data = test();

    return data || []
}

const test = () => {
    return [
        {
            id: 1,
            name: "Alberta",
            short_name:"AB",
        },
        {
            id: 2,
            name: "British Columbia",
            short_name:"BC",
        },
        {
            id: 3,
            name: "Manitoba",
            short_name:"<PERSON>",
        },
        {
            id: 4,
            name: "New Brunswick",
            short_name:"NB",
        },
        {
            id: 5,
            name: "Newfoundland and Labrador",
            short_name:"NL",
        },
        {
            id: 6,
            name: "Northwest Territories",
            short_name:"NT",
        },
        {
            id: 7,
            name: "Nova Scotia",
            short_name:"NS",
        },
        {
            id: 8,
            name: "Nunavut",
            short_name:"NU",
        },
        {
            id: 9,
            name: "Ontario",
            short_name:"ON",
        },
        {
            id: 10,
            name: "Prince Edward Island",
            short_name:"PE",
        },
        {
            id: 11,
            name: "Quebec",
            short_name:"QC",
        },
        {
            id: 12,
            name: "Saskatchewan",
            short_name:"SK",
        },
        {
            id: 13,
            name: "Yukon",
            short_name:"YT",
        }
    ];
}

const Provinces = {
    get
}
  
export default Provinces;