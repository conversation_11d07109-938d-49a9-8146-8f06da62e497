import React from 'react';

import Overlay from '../../../../Builder/Layers/Overlay';
import styles from './Item.module.scss';

export const Item = (props) => {
	if (!props.children) return null;

	return (
		<>
			{props.type === 0 &&
				<div>
					{props.children}
				</div>
			}
			{props.type === 1 &&
				<div className={styles["item-container"]}>
					<Overlay horizontal center inset classes={`${styles["item-overlay"]}`} color="primary" id={props.id} delete={props.delete} />
					<div className={styles.item}>
						<div className={styles["item-properties"]}>
							{props.children}
						</div>
					</div>
				</div>
			}
		</>
	);
}