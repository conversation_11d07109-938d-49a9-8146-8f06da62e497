import React,{useCallback} from 'react';
import Variable from '../Variable';

export const Dustbin = (props) => {

    const _groupByParentId = useCallback((items, parentId = null) => {
        const filteredItems = items.filter(item => item.parent_id === parentId);
        const groupedItems = filteredItems.map((item, i) => {
            const children = _groupByParentId(items, item.id);
            if (children.length) {
                return {...item, children};
            }
            return {...item};
        });
        return groupedItems;
    },[]);

    // recursive function to render groups and variables inside groups
    const _renderGroup = (items) => {
        const _render = (items) => {
            return items.map((item, i) => (
                <Variable key={`data-var-${item.id}`} id={item.id} index={item.index} selected={item} {...props}>
                    {item?.children && _render(item.children)}
                </Variable>
            ));
        }

        return _render(_groupByParentId([...items]));
    }    
      
    if (!props?.currentElement) return null;

    return _renderGroup(props.data);
}