ul.tips{
    list-style-position: inside !important;
}
ul.tips li:before {    
    font-family: 'Font Awesome 5 Pro';
    content: '\f0eb';
    margin-right:.3rem;
    color:#ff8f00;
}

.blocky-workspace{
    position:relative;
    width:100%;
    height:100vh;    
}

.blocklyToolboxDiv{
    background:#E5E5E5;
    padding-top:1rem 0 0 1rem;
    
}

.blocklyToolboxCategory{
    cursor:pointer;
    padding-right:.6rem;
}

.blocklyTreeRow {
    padding-right:2rem;
}

.blocklyTreeLabel{
    font-size: .85rem !important;
    margin-left:.7rem;
    font-family: 'Roboto', sans-serif !important;
}

.blocklyTreeIcon{
    display:none !important;
}

.geras-renderer.classic-theme .blocklyText, 
.geras-renderer.classic-theme .blocklyFlyoutLabelText {
    font-size: .85rem !important;
    font-family: 'Roboto', sans-serif !important;
}
