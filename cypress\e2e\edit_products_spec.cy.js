/* eslint-disable */ 
let qaDevAdmin = Cypress.env('impact_admin_user');
let password = Cypress.env('login_password');
let baseUrl = "http://localhost:3000/p/"

describe("It will check that products are able to be created successfully",{scrollBehavior: "center", testIsolation: false}, ()=>{
    let local;

    before("It will log in a user",()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', 'api/user/menu').as('getMenu');
        cy.loginLocal(baseUrl, qaDevAdmin, password)
        cy.wait('@getUserUser').then(()=>{
            local = localStorage.getItem('user');
        });
    });

    beforeEach("It will renew the user in local storage and viewport size",()=>{
        cy.viewport(1920, 1080);
        cy.restoreLocalUser(JSON.parse(local));
        cy.wait(500)
    });

    context("It will access a relatively new subscription product and edit it",()=>{
        
        let productName;
        let productDescription;
        let productStatus;
        let bundle;
        let categories;
        let newName;
        let newDescription;

        it("will access a relatively new subscription product",()=>{
            cy.intercept('POST', 'api/product').as('getProducts')
            cy.intercept('GET', 'api/product/variant/**').as('getVariants')
            
            cy.restoreLocalUser(JSON.parse(local));
            cy.wait(500)
            cy.visit(`${baseUrl}products/dashboard`)
            cy.wait('@getProducts');

            cy.get('[data-cy="pd-type-select"]')
                .select("Subscription")
            cy.wait('@getProducts');
            cy.get('[data-cy="pd-status-select"]')
                .select("Pending")
            cy.wait('@getProducts');
            //descend the ID order for the newest (which will likely be made by the product tests if it was run first)
            cy.get(':nth-child(1) > .ml-1')
                .click();
            cy.wait('@getProducts');
            cy.wait(2500);
            //pick the first result
            cy.get('tbody > :nth-child(1) > :nth-child(2)')
                .click();
            cy.wait('@getProducts');
            cy.wait('@getVariants');

            cy.get(':nth-child(1) > [data-cy="imported-edit-btn"]')
                .click();

            
        })// end accessing the product

        it("will change the product name, description, status, categories, and bundle",()=>{
            cy.intercept('POST', 'api/product').as('getProducts')
            cy.intercept("GET", '/api/product/variant/**').as('getVariants')
            cy.wait(2000);
            cy.get('[data-cy="new-product-bd-name"] > input')
                .invoke('val')
                .as('productName')
            cy.get('@productName').then(text=>{
                productName = text;
                newName = text + " Cy"
            });
            cy.get('[data-cy="new-product-bd-name"] > input')
                .type(' Cy')
            cy.get('[data-cy="new-product-bd-description"] > textarea')
                .invoke('val')
                .as('productDescription')
            cy.get('@productDescription').then(text=>{
                productDescription = text;
                newDescription = text + " Edit me! Mwrow!!"
            });
            cy.get('[data-cy="new-product-bd-description"] textarea')
                .type(' Edit me! Mwrow!!')
            cy.get('[data-cy="new-product-bd-status"] > select')
                .invoke('val')
                .as('productStatus')
            cy.get('@productStatus').then(text=>{
                productStatus = text;
            });
            cy.get('[data-cy="new-product-bd-status"] > select')
                .select('3')
            cy.get('[data-cy="new-product-bd-new-categories"] > .form-group > .rbt > .form-control')
                .click();
            cy.get('#categories_autocomplete-item-0')
                .click()
            cy.get('[data-cy="new-product-bd-new-categories"] > .form-group > .rbt > .form-control')
                .invoke('val')
                .as('category')
            cy.get('@category').then(text=>{
                categories = text;
            }) 
            cy.wait(1000) //I cannot find a call that fills the typeahead.  So we're gonna wait and hope the data is there
            cy.get('.rbt-input-main')
                .invoke('val')
                .as('bundleName');
            cy.get('@bundleName').then(text=>{
                bundle = text;
            });
            cy.get('.rbt-aux > .close > [aria-hidden="true"]')
                .click();
            cy.get('.rbt-input-main')
                .click();
            cy.get('#regular-item-0')
                .click();

            cy.get('[data-cy="create-and-exit-btn"]')
                .click();
            cy.wait('@getProducts');
        }) //end changing name, description, status, bundle

        it("will go back to that product and make sure the data changed",()=>{
            cy.intercept('POST', 'api/product').as('getProducts')
            cy.intercept('GET', '/api/category').as('getCategories')
            cy.intercept('GET', '/api/product/variant/**').as('getVariants')

            cy.get('[data-cy="pd-status-select"]')
                .select("Not Available")
            cy.wait('@getProducts')
            cy.get('[data-cy="pd-type-select"]')
                .select('Subscription')
            cy.wait('@getProducts')
            cy.get('tbody')
                .children().each(($child)=>{
                    cy.get($child)
                        .invoke('text')
                        .as('rowText')
                        cy.get('@rowText').then(text=>{
                            if(text.includes(productName))
                            cy.get($child)
                                .click();
                        })
                })
            cy.wait('@getProducts');
            cy.wait('@getVariants');
            cy.wait(2000);

            cy.get(':nth-child(1) > [data-cy="imported-edit-btn"]')
                .click();

            cy.wait('@getCategories');
            cy.wait('@getProducts');
            cy.wait('@getVariants');

            cy.get('[data-cy="new-product-bd-name"] > input')
                .invoke('val')
                .should('contain', newName);
            cy.get('[data-cy="new-product-bd-description"] > textarea')
                .invoke('val')
                .should('contain', newDescription);
            cy.get('[data-cy="new-product-bd-status"] > select')
                .invoke('val')
                .should('not.contain', productStatus);
            cy.wait(2000)
            cy.get('.rbt-input-main')
                .invoke('val')
                .should('not.contain', bundle);
            
        }); //end checking the new data
    })

    context("it will edit a variant of a physical product",()=>{
        let productName;
        let SKU;
        let UPC;
        let weight;
        let length;
        let height;
        let width;
        let newSKU;
        let newUPC;
        let newWeight=Math.floor(Math.random() * 1000);
        let newLength=Math.floor(Math.random() * 1000);
        let newHeight=Math.floor(Math.random() * 1000);
        let newWidth=Math.floor(Math.random() * 1000);
        
        it("will access a relatively new physical product",()=>{
            cy.intercept('POST', '/api/product').as('getProducts')
            cy.intercept('GET', '/api/product/variant/**').as('getVariants')

            cy.restoreLocalUser(JSON.parse(local));
            cy.wait(500)
            cy.visit(`${baseUrl}products/dashboard`)
            cy.wait('@getProducts');

            cy.get('[data-cy="pd-type-select"]')
                .select("Physical")
            cy.wait('@getProducts');
            //descend the ID order for the newest (which will likely be made by the product tests if it was run first)
            cy.get(':nth-child(1) > .ml-1')
                .click();
            cy.wait('@getProducts');
            cy.wait(1500);
            //pick the first result
            cy.get('tbody > :nth-child(1) > :nth-child(2)')
                .click();
            cy.wait('@getProducts');
            cy.wait('@getVariants');
        })// end accessing the product
        
        //all the new physical products are made with a variant that has these elements
        it("will edit the measurement/sku details of the variant",()=>{
            cy.intercept('POST', 'api/product').as('getProducts')
            cy.intercept('GET', '/api/product/variant/**').as('getVariants');
            
            cy.get(':nth-child(1) > [data-cy="imported-edit-btn"]')
                .click();
                
            cy.wait('@getVariants');
            cy.get('[data-cy="new-product-bd-name"] > input')
                .invoke('val')
                .as('productName')
            cy.get('@productName').then(text=>{
                productName = text
            });
            cy.get('[data-cy="new-product-variants"] > :nth-child(1)').within(()=>{
                //SKU
                cy.get('[data-cy="variant-sku"] > input')
                    .should('be.visible')
                    .invoke('val')
                    .as('SKU')
                cy.get('@SKU').then(text=>{
                    SKU=text;
                    newSKU = text + '12345'
                })
                cy.get('[data-cy="variant-sku"] > input')
                    .type('12345')
                //UPC
                cy.get('[data-cy="variant-upc"] > input')
                    .should('be.visible')
                    .invoke('val')
                    .as('UPC')
                cy.get('@UPC').then(text=>{
                    UPC = text;
                    newUPC = text + "abcde"
                });
                cy.get('[data-cy="variant-upc"] > input')
                    .type('abcde')

                //weight
                cy.get('[data-cy="variant-weight"] > .product-flex-row > .measure-value')
                    .should('be.visible')
                    .invoke('val')
                    .as('weight')
                cy.get('@weight').then(text=>{
                    weight = text;
                });
                cy.get('[data-cy="variant-weight"] > .product-flex-row > .measure-value')
                    .type(`{backspace}{backspace}{backspace}{backspace}{backspace}${newWeight}`);

                //length
                cy.get('[data-cy="variant-length"] > .product-flex-row > .measure-value')
                    .should('be.visible')
                    .invoke('val')
                    .as('length');
                cy.get('@length').then(text=>{
                    length =text;
                });
                cy.get('[data-cy="variant-length"] > .product-flex-row > .measure-value')
                    .type(`{backspace}{backspace}{backspace}{backspace}{backspace}${newLength}`);

                //height
                cy.get('[data-cy="variant-height"] > .product-flex-row > .measure-value')
                    .should('be.visible')
                    .invoke('text')
                    .as('height');
                cy.get('@height').then(text=>{
                    height=text
                });
                cy.get('[data-cy="variant-height"] > .product-flex-row > .measure-value')
                    .type(`{backspace}{backspace}{backspace}{backspace}{backspace}${newHeight}`);

                //width
                cy.get('[data-cy="variant-width"] > .product-flex-row > .measure-value')
                    .should('be.visible')
                    .invoke('text')
                    .as('width')
                cy.get('@width').then(text=>{
                    width=text;
                })
                cy.get('[data-cy="variant-width"] > .product-flex-row > .measure-value')
                    .type(`{backspace}{backspace}{backspace}{backspace}{backspace}${newWidth}`)
            })
            cy.get('[data-cy="create-and-exit-btn"]')
                .click();
            cy.wait(2000)
        })

        it("will go back to that product and make sure the data changed",()=>{
            cy.intercept('POST', 'api/product').as('getProducts');
            cy.intercept('GET', '/api/category').as('getCategories');
            cy.intercept('GET', '/api/product/variant/**').as('getVariants')

            cy.get('[data-cy="pd-search-input"]')
                .type(productName);
            cy.get('[data-cy="pd-type-select"]')
                .select('Physical')
            // cy.wait('@getProducts')
            cy.get('tbody')
                .children().each(($child)=>{
                    cy.get($child)
                        .invoke('text')
                        .as('rowText')
                        cy.get('@rowText').then(text=>{
                            if(text.includes(productName)){
                                cy.get($child)
                                    .click();
                            }
                        })
                })
            cy.wait('@getProducts');
            cy.wait('@getVariants')

            cy.get('[data-cy="product-name"]')
                .invoke('text')
                .should('contain', productName);

            cy.get(':nth-child(1) > [data-cy="imported-edit-btn"]')
                .click();
            cy.wait('@getVariants');
            cy.wait(1500);
            cy.get('[data-cy="new-product-variants"] > :nth-child(1)').within(()=>{
                //SKU
                cy.get('[data-cy="variant-sku"] > .sku-input')
                    .invoke('val')
                    .as('newSKU')
                cy.get('[data-cy="variant-sku"] > .sku-input')
                    .invoke('val')
                    .should('contain', newSKU)
                cy.get('@newSKU').then(text=>{
                    expect(SKU).to.not.equal(text)
                })

                //UPC
                cy.get('[data-cy="variant-upc"] > .sku-input')
                    .invoke('val')
                    .as('newUPC')  
                cy.get('[data-cy="variant-upc"] > .sku-input')
                    .invoke('val')    
                    .should('contain', newUPC);
                cy.get('@newUPC').then(text=>{
                    expect(UPC).to.not.equal(text)
                })

                //weight
                cy.get('[data-cy="variant-weight"] > .product-flex-row > .measure-value')
                    .invoke('val')
                    .as('newWeight')
                cy.get('[data-cy="variant-weight"] > .product-flex-row > .measure-value')
                    .invoke('val')
                    .should('contain', newWeight);
                cy.get('@newWeight').then(text=>{
                    expect(weight).to.not.equal(text)
                })

                //length
                cy.get('[data-cy="variant-length"] > .product-flex-row > .measure-value')
                    .invoke('val')
                    .as('newLength')
                cy.get('[data-cy="variant-length"] > .product-flex-row > .measure-value')
                    .invoke('val')
                    .should('contain', newLength);
                cy.get('@newLength').then(text=>{
                    expect(length).to.not.equal(text)
                })

                //height
                cy.get('[data-cy="variant-height"] > .product-flex-row > .measure-value')
                    .invoke('val')
                    .as('newHeight')
                cy.get('[data-cy="variant-height"] > .product-flex-row > .measure-value')
                    .invoke('val')
                    .should('contain', newHeight);
                cy.get('@newHeight').then(text=>{
                    expect(height).to.not.equal(text);
                })

                //width
                cy.get('[data-cy="variant-width"] > .product-flex-row > .measure-value')
                    .invoke('val')
                    .as('newWidth')
                cy.get('[data-cy="variant-width"] > .product-flex-row > .measure-value')    
                    .invoke('val')
                    .should('contain', newWidth)
                cy.get('@newWidth').then(text=>{
                    expect(width).to.not.equal(text)
                })
            })
        })
    });

     it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
})