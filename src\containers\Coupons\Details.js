import React, { useState, useEffect } from 'react';
import { useH<PERSON><PERSON>, use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import Container from 'react-bootstrap/Container';
import { Col, Row, DropdownButton, Button, Card } from 'react-bootstrap';
import SubHeader from '../../components/common/SubHeader';
import Table from '../../components/common/Table';
import Coupons from '../../api/Coupons';

import './Coupon.scss';

const Details = (props) => {
    const { id } = useParams();
    let history = useHistory();

    const [loading,setLoading]=useState(true);
    const [coupon,setCoupon]=useState([]);

    // first load, get companies from api
    useEffect(() => {
        let mounted = true;
        setLoading(true);

        if(id) {
            Coupons.get({id: id})
            .then(response => {
                if(mounted && response.data[0]) {
                    let cmp = response.data[0];
                    cmp.name = "Coupon " + cmp.id;
                    setCoupon(cmp);
                }
                setLoading(false);
            }).catch(e => console.error(e));
        }

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
        }        
    },[props, id]);

    return (
        <Container fluid>
            
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/discount/dashboard" }, text: "Discount Dashboard" },
                { text: "Details" }
            ]} />
            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <h4>Edit Discount</h4>
                        <hr/>

                        Form goes here.
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Details;