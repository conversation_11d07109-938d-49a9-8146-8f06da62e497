@use '../../../assets/css/scss/pages.scss';
@import '../../../assets/css/scss/themes.scss';


.fc-timeline-event{     
    cursor: pointer; 
}
.fc-event-main {
    cursor: pointer;
}



.conflict {
    cursor: not-allowed;
}

.fc-header-toolbar > .fc-toolbar-chunk:nth-child(3) 
.fc-button-group:first-child .fc--button,
.fc-daygrid-event-dot{
    display: none;
}
.fc-header-toolbar > .fc-toolbar-chunk:nth-child(3) 
.fc-button-group:first-child .fc-button{
    border-top-right-radius: 0.25em;
    border-bottom-right-radius: 0.25em;
}


.fc-button{
    @extend .btn;
}

.fc-button-primary {
    @extend .btn-light;
}

table[role="grid"]{
    @extend .table;
}

th[role="columnheader"],
td[role="gridcell"],
th.fc-slot,
td.fc-timegrid-axis{
    font-family: $secondary-font-family;
    // overflow:hidden;

    a{
        color: $primary-font-color;
        font-family: $secondary-font-family;
        text-decoration: none;
    
        &:hover{
            text-decoration: none;
        }
    
    }
}

td[role="gridcell"]{
    border-color: $divider-color !important;
}

div.fc-theme-standard td, div.fc-theme-standard th{
    border-color: $divider-color !important;
}


div.fc table{
    margin:0;
    border-color: $divider-color !important;
}



.fc-event{
    &[class*="event-"] {
        font-family: $secondary-font-family;
        color: $primary-font-color;
        padding: 2px 5px;
        background-color: transparent;
        border-color: transparent;
        border-radius: $card-border-radius;
        border-width: 2px;
        // border-bottom: $divider-border;
        .fc-event-main{
            color: $primary-font-color;
        }        
    }

    &.event-conflict{
        border-color: $primary-color;
    }
    
    // so acceptable colors in the calendar acceptable color array can be previewed all together if more are added
    // &.event-1{
    //     border-color: #5E46E7;
    //     border-color: #DFCC24;
    //     border-color: #1A57B0;
    //     border-color: #197F18;
    //     border-color: #E628BB;
    //     border-color: #2196f3;
    //     border-color: #DE8736;
    //     border-color: #76D34B;
    //     border-color: #7F1DB4;
    //     border-color: #93BE38;
    //     border-color: #C72346;
    //     border-color: #1AB077;
    // }

}



@media screen and (max-width: 991px) {
    .fc-header-toolbar{
        flex-direction: column;
        
        .fc-toolbar-chunk{
            padding-bottom: 0.25rem;
        }
    }
}