// ***********************************************************
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';


import { mount } from 'cypress/react'
import { Provider } from 'react-redux';
import store from '../../src/redux-store';
import { MemoryRouter } from 'react-router-dom';
import FakeApp from './FakeApp';

Cypress.Commands.add('mount', (component, options = {})=>{
    const { reduxStore = store, routerProps = { initialEntries: ['/'] }, ...mountOptions } = options
    const wrapped =         
        <Provider store={reduxStore}>
            <MemoryRouter {...routerProps}>
                <FakeApp content={component}/>
            </MemoryRouter>
        </Provider>
        document.getElementById('root')
    return mount(wrapped, mountOptions)
})


// Example use:
// cy.mount(<MyComponent />)
