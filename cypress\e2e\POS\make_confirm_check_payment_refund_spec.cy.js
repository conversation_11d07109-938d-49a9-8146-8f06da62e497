/*eslint-disable*/

let baseUrl = 'https://portal-qa.impactathleticsny.com/p/';
let staffUserName = Cypress.env('impact_staff_user');
let adminUserName = Cypress.env("impact_admin_user")
let password = Cypress.env('login_password');
let local;

describe('It will log in and visit a register as a staff user', {testIsolation: false, scrollBehavior: "center"}, ()=>{
    
    let orderNumber;
    let transactionNumber;
    let productPrice1 = "2.92";
    let tax1 = "0.20";
    let productPrice2 = "2.92";
    let tax2 = ".41";
    let fullTotal1 = "3.12";
    let fullTotal2 = "6.25"

    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, staffUserName, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach(()=>{
        cy.viewport(1920, 1080);
    });

    context("It will check that card input maintains value, cannot complete card payment",()=>{
        it("will navigate to a POS",()=>{
            cy.visit(`${baseUrl}pos/5`);
            cy.wait(5000);
        });
    
        it("will add a product to the cart and check the data",()=>{
            cy.get('[data-cy="product-card"]')
                .contains('Arizona Gummy')
                .click({force: true});
            cy.wait(1500);
            cy.get('.item-name > :nth-child(1) > [data-cy="product-name"]')
                .invoke('text')
                .should('include', 'Arizona Gummy')
            cy.get('[data-cy="order-number"]')
                .invoke('text').as('orderNumber')
            cy.get('@orderNumber').then((text)=>{
                orderNumber = text
            })
            cy.get('[data-cy="preview-item"] > .item-price')
                .invoke('text')
                .should('include', productPrice1)
            cy.get('.product-buttons')
                .should('exist')
            cy.get('[data-cy="order-subtotal"]')
                .invoke('text')
                .should('include', productPrice1);
            cy.get('[data-cy="order-tax"]')
                .invoke('text')
                .should('include', tax1);
            cy.get('[data-cy="order-total"]')
                .invoke('text')
                .should('include', fullTotal1)
        });
    
        it("will add a second product",()=>{
            cy.get('[data-cy="product-card"]')
                .contains('Bagels')
                .click({force: true});
            cy.wait(1500)
            cy.get('[data-cy="product-card"]')
                .contains('Cream Cheese')
                .click({force: true})
            cy.wait(1500);
            cy.get('[data-cy="done-btn"]') //close modal
                .click();
            cy.get(':nth-child(3) > .false > .item-name')
                .invoke('text')
                .should('include', "Bagels")
            cy.get(':nth-child(3) > .false > .item-price > .multiple-lines > [data-cy="product-price"]')
                .invoke('text')
                .should('include', productPrice2)
            cy.get('[data-cy="order-tax"]')
                .invoke('text')
                .should('include', tax2);
            cy.get('[data-cy="order-total"]')
                .invoke('text')
                .should('include', fullTotal2)       
        });
    
        it("will open up the payment modal and click to enter card details",()=>{
            cy.get('[data-cy="pos-button-checkout"]')
                .click();
            cy.get('[data-cy="details-add-to-cart"]')
                .should('have.attr', 'disabled');
            cy.wait(1000);
            cy.get('[data-cy="payment-type-1"]')
                .click();
            cy.get('#first_name')
                .click()
                .type("Jane");
            cy.get('#last_name')
                .click()
                .type("Doe");
            cy.get('#bill_address1')
                .click()
                .type("123 Main Street")
            cy.get('#bill_city')
                .click()
                .type("CypressLand")
            //will skip the state to ensure someone who lives in Alabama would work properly without having to reselect
            cy.get('#amount')
                .invoke('val')
                .should('include', "6.25")
            cy.get('#bill_postalcode')
                .click()
                .type("12345")

            cy.get('#first_name')
            .invoke('val')
                .should('equal', "Jane")
            cy.get('#last_name')
                .invoke('val')
                .should('equal', "Doe")
            cy.get('#bill_address1')
                .invoke('val')
                .should('equal', "123 Main Street")
            cy.get('#bill_address2')
                .invoke('val')
                .should('equal', '')
            cy.get('#bill_city')
                .invoke('val')
                .should('include', "CypressLand")
            cy.get('#amount')
                .invoke('val')
                .should('include', "6.25")
            cy.get('#bill_postalcode')
                .invoke('val')
                .should('equal', '12345')
            cy.get('#bill_state')
                .invoke('val')
                .should('equal', "AL")
    
        });

    })

    context("it will make a payment with a check and check data", ()=>{   

        it("will finish the order with a check payment",()=>{
            cy.get('[data-cy="payment-type-3"]')
                .click();
            cy.get('#check_number')
                .click()
                .type('999')
            cy.get('#check_name')
                .click()
                .type('Midnight')
            cy.get('[data-cy="add-check-button"]')
                .click()
            cy.get('[data-cy="details-add-to-cart"]')
                .click()
            cy.get('.col-lg-4 > :nth-child(1)').within(()=>{
                cy.get('[data-cy="success-transaction-number"]')
                    .invoke('text').as('trans');
                cy.get('@trans').then((text)=>{
                    let test = text.split("#")
                    transactionNumber = test[1].trim()
                })
            })
        })

        it("will check that the order page has multiple items",()=>{
            cy.visit(`${baseUrl}order/${orderNumber}`)
            cy.wait(1000)
            cy.get('[data-cy="advanced-refund-btn"]')
                .should('not.exist')
            cy.get('[data-cy="refund-all-btn"]')
                .should('not.exist');
            cy.get('[data-cy="order-tax-total"]')
                .invoke('text')
                .should('include', tax2);
            cy.get('[data-cy="each-order-item"]')
                .should('have.length', 3);
            cy.get('[data-cy="each-order-item"]')
                .eq(0).within(()=>{
                    cy.get(':nth-child(4)')
                        .invoke('text')
                        .should('include', productPrice1)
                    cy.get(':nth-child(6)')
                        .invoke('text')
                        .should('include', tax1)
                })
            cy.get('[data-cy="each-order-item"]')
                .eq(1).within(()=>{
                    cy.get(':nth-child(4)')
                        .invoke('text')
                        .should('include', productPrice2)
                })
            cy.get('[data-cy="each-order-item"]')
                .eq(2).within(()=>{
                    cy.get(':nth-child(5)')
                        .invoke('text')
                        .should('include', "0.00")
                })
            cy.get('.transaction-hist-wrapper')
                .children()
                .should('have.length', 1)
            cy.get('.transaction-hist-wrapper')
                .children()
                .eq(0)
                .within(()=>{
                    cy.get('[data-cy="transaction-number"]')
                    .invoke('text')
                    .should('include', transactionNumber);
                })
        })
    });

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
});