/*eslint-disable*/

import SortOrderTree from "../index";
import { adjustMenuItems } from "../SortOrderTreeUtils";
import { Button } from 'react-bootstrap'
import FeatureData from './Feautres.json';
import MenuItemData from './MenuItems.json'

describe("It will test the sort order tree against different sets of data", ()=>{

    let user;
    const features = FeatureData;
    const menuItems = MenuItemData;

    before(()=>{
        cy.fixture('User/userResponse.json').then((data)=>{
            user = data;
        });
    })

    beforeEach("it will put a fake user in local storage", ()=>{
        window.localStorage.setItem('user',
            JSON.stringify({
                menu:[],
                roles: user.adminUser.data[0].roles,
                profile: user.adminUser.data[0],
                token: "bearer eyJASDKJ239849skdfjJKFJ.eyasdkj*9"
            })
        );
        cy.viewport(1200, 1080);
    }); //end beforeEach

    context("It will check the sort order tree with no data", ()=>{
        beforeEach(()=>{
            cy.intercept('POST', 'api/feature', features).as('getFeatures');
            cy.mount(
                <SortOrderTree />
            )
        })

        it("will see loading skeletons when there is no data",()=>{
            cy.get('.sort-order-tree').should('exist');
            cy.get('[data-cy="sort-tree-skeleton"]').should('exist');
        })
    }); //end tree with no data

    context("It will check the tree with feature data", ()=>{
        beforeEach(()=>{
            cy.spyLog();
            cy.intercept('POST', 'api/feature', features).as('getFeatures');
            cy.mount(
                <SortOrderTree 
                    treeClearBtn={true}
                    data={adjustMenuItems(features.data)}
                    optionalDetails={{
                        title: "Selected Feature Details",
                        type: "info",
                        requiresActive: true,    
                        data: [
                            {name: 'name', label: "Name"},
                            {name: 'sort_order', label: "Original Sort Order"},
                            {name: 'description', label: "Description"}
                        ]
                    }}
                />
            )
        })

        it("will check the visible elements vs dummy data", ()=>{
            cy.get('[data-cy="tree-optional-details"]').should('not.exist');
            
            //check the details on a clicked item
            cy.get(':nth-child(2) > .rct-tree-item-title-container > .rct-tree-item-button').click();
            cy.get('[data-cy="tree-optional-details"]').should('be.visible');
            cy.get(':nth-child(2) > label').should('have.text', "Name:");
            cy.get(':nth-child(2) > span').should('contain', "User Profile");
            cy.get(':nth-child(3) > label').should('have.text', "Original Sort Order:");
            cy.get(':nth-child(3) > span').should('contain', "2");
            cy.get(':nth-child(4) > label').should('have.text', "Description:");
            cy.get(':nth-child(4) > span').should('contain', "--");
            cy.get('.rct-tree-items-container').should('exist');
            cy.get('.rct-tree-items-container').children().should('have.length', 5);
            
            //checked according to dummy data and its sort order
            cy.get('.rct-tree-items-container')
                .children().eq(0).should('have.text', 'Required');
            cy.get('.rct-tree-items-container')
                .children().eq(1).should('have.text', 'User Profile');
            cy.get('.rct-tree-items-container')
                .children().eq(2).should('have.text', 'Events');
            cy.get('.rct-tree-items-container')
                .children().eq(3).should('have.text', 'Registers');
            cy.get('.rct-tree-items-container')
                .children().eq(4).should('have.text', 'Admin');
            
            //change the active item to check that details have changed
            cy.get(':nth-child(4) > .rct-tree-item-title-container > .rct-tree-item-button').click();
            cy.get(':nth-child(2) > span').should('contain', "Registers");
            cy.get(':nth-child(3) > span').should('contain', "10");
            cy.get(':nth-child(4) > span').should('contain', "--");
            
            //clear the active item and check details
            cy.get('[data-cy="clear-btn"]').click();
            cy.get('[data-cy="tree-optional-details"]').should('not.exist');
        })
    });

    context("It will check the tree with menu item data", ()=>{
        beforeEach(()=>{
            cy.spyLog();
            cy.intercept('POST', 'api/feature', menuItems).as('getFeatures');
            cy.mount(
                <SortOrderTree 
                    treeClearBtn={false}
                    data={adjustMenuItems(menuItems.data)}
                    optionalDetails={{
                        title: "",
                        type:"html",
                        requiresActive: false,
                        data:[
                            <div className="text-center">
                                <div className="btn-section">
                                    <h6>Handle Sort</h6>
                                    <Button>
                                        I'm a button!
                                    </Button>
                                    <Button disabled>
                                        I'm another one!
                                    </Button>
                                </div>
                            </div>
                        ]
                    }}
                />
            )
        })

        it("will check mounted data",()=>{
            cy.get('.rct-tree-items-container').should('exist');
            cy.get('.rct-tree-items-container').children().should('have.length', 6);
            cy.get('[data-cy="tree-optional-details"]').should('exist');
            cy.get('.rct-tree-item-li-selected > .rct-tree-items-container > .rct-tree-item-li > .rct-tree-item-title-container > .rct-tree-item-button')
                .should('not.exist');
                cy.get(':nth-child(3) > .rct-tree-item-title-container > .rct-tree-item-button').click()
            cy.get('.rct-tree-item-li-selected > .rct-tree-items-container > .rct-tree-item-li > .rct-tree-item-title-container > .rct-tree-item-button')
                .should('exist');
        })
    })
})

//should check that tree settings work (no drag, no folder, folder on drag, etc)
//should check that different elements can be used on the side of the tree
//   test different types of buttons on the side
//check the filters for default and company functions
//check that tree is in the right sort order based on data provided 
//check that tree name is respected (as it's necessary for multiple trees)
//check that data with missing elements are handled correctly 
//      check those missing "name" and having text instead
//check reset and clear reset
//need to figure out how to test D&D with Cypress