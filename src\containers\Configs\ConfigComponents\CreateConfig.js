import React, { useState, useEffect, useRef } from 'react'

import CharacterCounterInput from '../../../components/common/CharacterCounter/CharacterCounterInput'
import CreateEachParam from './CreateEachParam';
import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';
import Config from '../../../api/Config';
import './ConfigComponents.scss';

const CreateConfig = ({setSuccess, setError, highestSortOrder, handleHide, activeConfig, ...props}) => {

    const [createdParams, setCreatedParams] =useState([
        {
            tempId: 1,
            paramName: "new param",
            paramType: "text",
            isRequired: 1,
            description: ""
        }
    ]);
    const [name, setName] = useState("");
    const [isActive, setIsActive] = useState(1);
    const [localError, setLocalError] = useState("");
    const [loading, setLoading]=useState(true);

    useEffect(()=>{
        if(activeConfig && !name){ //if edit has already been loaded, it will have this info already, don't need to check again
            let configCopy = {...activeConfig};
            setName(configCopy.name);
            setIsActive(configCopy.is_active);
            let keys=Object.keys(configCopy.config_params);
            for(let i = 0; i < keys.length; i++){
                configCopy.config_params[keys[i]].tempId = i+1;
                configCopy.config_params[keys[i]].paramName = keys[i];
            }
            let configArray = Object.values(configCopy.config_params);
            setCreatedParams(configArray);
            setLoading(false);
        } else if (!activeConfig) setLoading(false);
    },[activeConfig, name, isActive]);

    const createNewParam=()=>{
        setLocalError("")
        const createdCopy = [...createdParams];
        //making sure we don't get any duplicate Id - we don't need all temp Ids in order, just to be unique
        let tempIds = createdParams.map(param=>param.tempId);
        let highestId = Math.max(...tempIds)
        createdCopy.push({
            tempId: highestId+=1,
            paramName: "",
            paramType: "text",
            isRequired: 1,
            description: ""
        })
        setCreatedParams(createdCopy)
    }

    const removeParam=(tempId)=>{
        if(createdParams.length>1){
            const createdCopy = [...createdParams];
            const index = createdCopy.findIndex((param)=>param.tempId===tempId);
            createdCopy.splice(index, 1);
            setCreatedParams(createdCopy);
        }
        else {
            setLocalError(<span>You have to have at least one config parameter</span>)
        }
    }

    const errorHandling = ()=>{
        let localError = "";
        createdParams.forEach((param)=>{
            if(param.paramName==="" || !param.paramName){
                localError = "You have to name all parameters"
            }
        })
        return localError;
    }

    const saveConfig= async()=>{
        let localError = errorHandling();
        if(!localError){
            let configObject = {
                name: name,
                is_active: isActive,
                config_params: {},
                sort_order: activeConfig ? activeConfig.sort_order : highestSortOrder
            }
            createdParams.forEach((param)=>{
                configObject.config_params[param.paramName] = {
                    type: param.type,
                    required: param.isRequired,
                    description: param.description
                }
            })
            let response;
            try{
                if(activeConfig){
                    response = await Config.ConfigTypes.edit({...configObject, id: activeConfig.id});
                }else response = await Config.ConfigTypes.create(configObject);
                if(response.status===200){
                    setSuccess(setSuccessToast("You've created a new config type sucessfully"));
                    handleHide();
                }
                else if(response.error){
                    setError(setErrorCatcher(response.error))
                }
            }catch(ex){console.error(ex)}
        }
        else setLocalError(errorHandling)
    }

    return (
        <div className="create-config-wrapper">
            {/* main config */}
            <div className="main-config-wrapper">
                <div className="label-input-pair">
                    <CharacterCounterInput 
                        characterLimit={255}
                        limitedVariable={name}
                        label="Name"
                        required={true}
                        name="config-name"
                        placeholder='Config Name'
                        defaultValue={name}
                        onChange={setName}
                        columnRow='column'
                    />
                </div>
                <div className="label-input-pair">
                    <label htmlFor="is-active">
                        Is Active?
                    </label>
                    <select name="is-active" onChange={(e)=>setIsActive(+e.target.value)}>
                        <option value={1} selected={isActive===1}>Yes</option>
                        <option value={0} selected={isActive===0}>No</option>
                    </select>
                </div>
            </div>

            {activeConfig &&
                <p className="text-center">
                    <br />
                    <span className="required-star bold">!!WARNING!!</span>
                    <br/>
                    Be mindful if editing a company config! These are utilized in the codebase and hard coded in for checks.  Only edit a field if you are sure of the effects it will have to edit that particular one!
                </p>
            }

            {/* each config */}
            {!loading &&
                <>
                    {createdParams?.map((param)=>(
                        <div key={`each-param-${param.tempId}`} className="config-params">
                            <CreateEachParam 
                                edit={activeConfig ? true : false}
                                createdParam={param}
                                removeParam={removeParam}
                            />
                        </div>
                    ))}
                    <div className="btn-row">
                        <button onClick={createNewParam}>
                            Create New Param
                        </button>

                        <button onClick={saveConfig}>
                            Save Config
                        </button>
                    </div>
                    <p className="error-p">
                        {localError}
                    </p>
                </>
            }
        </div>
    )
}

export default CreateConfig