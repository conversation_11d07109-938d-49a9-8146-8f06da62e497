import React, {useState, useCallback, useEffect, useRef } from 'react';
import { Button, Modal } from 'react-bootstrap'
import { Token } from 'react-bootstrap-typeahead';

import { Typeahead } from './Typeahead';
import usePrevious from '../common/CustomHooks';
import BasicInfo from '../../containers/Product/Category/BasicInfo'
import Products from '../../api/Products';

/**Basic async typeahead for searching addons.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Services back
*/
export const AddOnsTypeahead = ({allowNew=false, setParentAddOnCategories, initialCategories=[], multiple=true, singleImportAddon=[], setSingleImportAddon=()=>{}, hideSelected=false, ...props}) => {

    const mountedRef = useRef(false)
    const [loading, setLoading]=useState("Loading...");
    const [selectedCategories, setSelectedCategories]=useState([]);
    const [firstLoad, setFirstLoad]=useState(true);
    const [hideShow, setHideShow]=useState(false);
    const [newAddon, setNewAddon]=useState(null);
    const [overrideAddonTypeahead, setOverrideAddonTypeahead]=useState(false)

    const previousSingleImport = usePrevious(singleImportAddon);

    const getAddOns=useCallback(async(query, perPage, page=1)=>{
        let responseObj={data: null, errors: null}
        try{
            let response = await Products.Categories.filter({
                add_on_only: 1,
                max_records: perPage,
                page_no: page,
                search: query
            })
            // if(!response.errors && mountedRef.current) setAddOnCategories(response.data.categories)
            if(!response.errors && mountedRef.current) responseObj.data = response.data?.categories
            else if(response.errors && mountedRef.current){
                setLoading("Currently No Addons Available")
                responseObj.errors = response.errors
            } 
            setLoading()
        }catch(ex){
            console.error(ex)
            responseObj.errors = ex
            setLoading("Currently No Addons Available")
        }
        return responseObj
    },[]);

    useEffect(()=>{
        mountedRef.current = true
        return()=>{
            mountedRef.current = false
        }
    },[]);

    useEffect(()=>{
        if(firstLoad
            && initialCategories?.length >0 
            && selectedCategories?.length===0 
            && mountedRef.current){
                setSelectedCategories(initialCategories);
                setFirstLoad(false);
        }
    //selected categories is condition, not trigger
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[initialCategories, firstLoad]);

    useEffect(()=>{
        if(singleImportAddon !== previousSingleImport && singleImportAddon.length>0){
            let tempCategories = Array.from(selectedCategories);
            let notUnique = tempCategories.some(category => category?.id === singleImportAddon[0].id)
            if(!notUnique || tempCategories.length===0){
                tempCategories.push(singleImportAddon[0]);
                setSelectedCategories(tempCategories)
                setSingleImportAddon([])
                setOverrideAddonTypeahead(true)
            }
        }
    //don't want selectedCategories trigginer the effect, just import Addon
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[singleImportAddon])

    useEffect(()=>{
        if(newAddon){
            setSelectedCategories([newAddon]);
            setNewAddon(null);
        }
    },[newAddon]);

    //sends back up to the parent of this typeahead
    useEffect(()=>{
        //put two arrays in order so they can be compared
        //we don't want to update if they already match, lest we loop
        selectedCategories.sort((a,b)=>(a.id > b.id ? 1: -1))
        initialCategories.sort((a,b)=>(a.id > b.id ? 1: -1))
        if(mountedRef.current &&  JSON.stringify(selectedCategories?.map(category=>category.id)) !== JSON.stringify(initialCategories)){
            setParentAddOnCategories(selectedCategories);
        }
    // addOnCategories is condition, not trigger
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[selectedCategories, setParentAddOnCategories])

    const passNewCategory=(addOn)=>{
        getAddOns();
        setNewAddon(addOn)
    }
        
    const handleAddonSelection=(option)=>{
        setSelectedCategories(option);
    }

    return (
        <>
            {((initialCategories && selectedCategories) || !initialCategories) && //make sure things are loaded if need be before showing
                <Typeahead
                    id="addon-category"
                    labelKey={"name"}
                    multiple={multiple}
                    passSelection={option=>handleAddonSelection(option)}
                    minLength={0}
                    makeRequest={getAddOns}
                    placeholder={loading ? `${loading}`: "Search Add On Categories"}
                    perPage={10}
                    clearButton={false}
                    initialData={selectedCategories}
                    overrideFirstLoad={overrideAddonTypeahead}
                    setOverrideFirstLoad={setOverrideAddonTypeahead}
                />
            }
            {allowNew && 
                <Button className="new-addon-btn" onClick={()=>setHideShow(true)}>Create New Addon</Button>
            }
            <Modal data-cy="new-cat-modal" show={hideShow} onHide={()=>setHideShow(false)}>
                <Modal.Header data-cy="new-cat-header">
                    Create a New Addon
                </Modal.Header>
                <Modal.Body data-cy="new-cat-body">
                    <BasicInfo 
                        passNewCategory={passNewCategory} 
                        onClose={()=>setHideShow(false)} 
                        addOnToggle={false} 
                        isAddOn={true} 
                        edit={false}
                        alwaysCreate={true} 
                    />
                </Modal.Body>
            </Modal>
        </>
    )
}
