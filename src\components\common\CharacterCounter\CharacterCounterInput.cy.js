/*eslint-disable*/
import React from 'react';
import CharacterCounterInput from './CharacterCounterInput';

describe('CharacterCounterInput Component', {scrollBehavior: "center"}, () => {
    const defaultProps = {
        characterLimit: 50,
        name: 'testInput',
        label: 'Test Label',
        placeholder: 'Enter text here'
    };

    it('renders with default props', () => {
        cy.mount(<CharacterCounterInput {...defaultProps} />);

        cy.get('label').should('contain', 'Test Label');
        cy.get('input').should('have.attr', 'placeholder', 'Enter text here');
        cy.get('.count-limit').should('contain', '0 / 50');
    });

    it('shows required star when required prop is true', () => {
        cy.mount(<CharacterCounterInput {...defaultProps} required={true} />);

        cy.get('.required-star').should('exist').and('contain', '*');
    });

    it('updates character count when typing', () => {
        cy.mount(<CharacterCounterInput {...defaultProps} />);

        cy.get('input').type('Hello World');
        cy.get('.count-limit').should('contain', '11 / 50');
    });

    it('shows invalid feedback when exceeding character limit', () => {
        cy.mount(<CharacterCounterInput {...defaultProps} characterLimit={10} />);

        cy.get('input').type('This is too long');
        cy.get('.count-limit').should('have.class', 'invalid-feedback');
    });

    it('handles external value prop correctly', () => {
        cy.mount(<CharacterCounterInput {...defaultProps} value="Initial Value" />);

        cy.get('input').should('have.value', 'Initial Value');
        cy.get('.count-limit').should('contain', '13 / 50');
    });

    it('handles limitedVariable prop correctly', () => {
        cy.mount(
            <CharacterCounterInput 
                {...defaultProps} 
                limitedVariable="Controlled Value"
                onChange={cy.spy().as('onChangeSpy')}
            />
        );

        cy.get('input')
            .should('have.value', 'Controlled Value');
        cy.get('input')
            .focus()
            .clear()
            .type('New Value');
        cy.get('@onChangeSpy').should('have.been.called');
    });

    it('renders in row layout when specified', () => {
        cy.mount(<CharacterCounterInput {...defaultProps} columnRow="row" />);

        cy.get('.character-counter-input-wrapper')
            .should('have.class', 'row')
            .and('not.have.class', 'column');
    });

    it('renders in column layout by default', () => {
        cy.mount(<CharacterCounterInput {...defaultProps} />);

        cy.get('.character-counter-input-wrapper')
            .should('have.class', 'column')
            .and('not.have.class', 'row');
    });

    it('maintains internal state when no limitedVariable is provided', () => {
        cy.mount(<CharacterCounterInput {...defaultProps} />);

        cy.get('input').type('Internal State Test');
        cy.get('input').should('have.value', 'Internal State Test');
        cy.get('.count-limit').should('contain', '19 / 50');
    });

    it('calls onChange callback with new value', () => {
        const onChangeSpy = cy.spy().as('onChangeSpy');
        cy.mount(
            <CharacterCounterInput 
                {...defaultProps} 
                onChange={onChangeSpy}
                limitedVariable="Value"
            />
        );

        cy.get('input').type('New Value');
        cy.get('@onChangeSpy').should('have.been.calledWith');
    });
});