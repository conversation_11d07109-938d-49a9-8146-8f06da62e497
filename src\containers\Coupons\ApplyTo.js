import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const ApplyTo = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Should this be applied to the entire order or only qualifying items?</span>
                <Form.Row>
                    <Form.Check 
                        type="radio"
                        id="apply_to_all-1"
                        label="Entire Order"
                        name="apply_to_all"
                        value={1}
                        checked={coupon.apply_to_all===1}
                        onChange={onChangeInput}
                        isInvalid={!!errors.apply_to_all}
                        className="form-radio"
                    />
                    <Form.Check 
                        type="radio"
                        id="apply_to_all-0"
                        label="Specific Items"
                        name="apply_to_all"
                        value={0}
                        checked={coupon.apply_to_all===0}
                        onChange={onChangeInput}
                        isInvalid={!!errors.apply_to_all}
                        className="form-radio"
                    />
                </Form.Row>
                <div className={`err ${!!errors.apply_to_all ? "" : "hidden"}`}>
                    {errors.apply_to_all}
                </div>
            </Col>
        </Row>
    );
}

export default ApplyTo;