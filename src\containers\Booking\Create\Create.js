import React,{useEffect, useState, Suspense} from 'react';
import { useLocation, Link  } from "react-router-dom";
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import SubHeader from '../../../components/common/SubHeader';

import BasicInfo from '../BasicInfo'
import AssignUsers from '../AssignUsers';
import Groups from '../Groups';
import '../Details/Details.css';

const Create = (props) => {    
    const location = useLocation();

	useEffect(() => {
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                <BasicInfo {...props} />
            </Suspense>
        );

	}, [location.pathname,props]);
    
    const [pagePart,setPagePart]=useState(
        <Suspense fallback={             
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
            </SkeletonTheme>
        }>            
        </Suspense>
    );

    /*
    const loadPagePartHandler= (e) => {
        let component;
        switch (e.target.hash.substr(1)){
            case "AssignUsers":
                component=<AssignUsers {...props} />;
                break;
            case "Groups":
                component=<Groups {...props} />;
                break;
            case "BasicInfo":
            default:
                component=<BasicInfo {...props} />;
                break;
        }
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {component}
            </Suspense>
        );
    }
    */

    return (
        <Container fluid>
            {!props.modal && 
                <SubHeader items={[
                    { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                    { linkAs: Link, linkProps: { to: "/p/booking" }, text: "Event Booking" },
                    { text: "New Event" }
                ]} />
            }

            <Row>
                {/*
                <Col sm="auto" className="order-1 order-lg-2">
                    <ListGroup className="profileMenu" variant="flush">
                        <ListGroup.Item action href="#BasicInfo" onClick={loadPagePartHandler}>
                            <i className="far fa-calendar-day"></i> Event Info
                        </ListGroup.Item>
                        <ListGroup.Item action href="#AssignUsers" onClick={loadPagePartHandler}>
                            <i className="far fa-users"></i> Add Users
                        </ListGroup.Item>
                        <ListGroup.Item action href="#Groups" onClick={loadPagePartHandler}>
                            <i className="far fa-users"></i> Add Groups
                        </ListGroup.Item>
                        <ListGroup.Item action href="#Payment" onClick={loadPagePartHandler}>
                            <i className="far fa-credit-card"></i> Payment
                        </ListGroup.Item>
                    </ListGroup>
                </Col>
                */}
                <Col className="order-2 order-lg-1">
                    {pagePart /*this is where the magic is happening :-O */ }
                </Col>
            </Row>
        </Container>
    );
}

export default Create;