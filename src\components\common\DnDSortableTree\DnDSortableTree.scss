@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes.scss';


.DnD-tree-wrapper {
    height: 550px;
    width: 1500px;
    max-width: 1500px;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.DnD-top-row-p {
    width: 1100px;
    margin-bottom: 1rem;
}

.module-save-btn {
    width: 10rem;
    margin-top: .5rem;
}

.search-matches {
    margin-left: 2rem;
    margin-top: .25rem;
    display: flex;
    align-items: center;
}

.DnD-search {
    margin-left: 2rem;
}

.DnD-f-b-btn {
    margin-left: .5rem;
    margin-right: .5rem;
    font-size: 1.5rem;
    color: $primary-color;
    cursor: pointer;
}

div.rst__rowLandingPad>*,
div.rst__rowCancelPad>* {
    opacity: 50% !important;
}

.rst__rowContents {
    min-width: 250px !important;
    background-color: $card-background-color !important;
}

.rst__rowSearchFocus {
    outline: solid 3px $tertiary-light-color !important;
}

.rst__moveHandle,
.rst__loadingHandle {
    background: $primary-color url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MiIgaGVpZ2h0PSI0MiI+PGcgc3Ryb2tlPSIjRkZGIiBzdHJva2Utd2lkdGg9IjIuOSIgPjxwYXRoIGQ9Ik0xNCAxNS43aDE0LjQiLz48cGF0aCBkPSJNMTQgMjEuNGgxNC40Ii8+PHBhdGggZD0iTTE0IDI3LjFoMTQuNCIvPjwvZz4KPC9zdmc+') no-repeat center !important;
}

.module-edit-btn {
    background-color: $button-background-color;
    border: 2px solid $primary-color;
    border-radius: 5px;
}

@media only screen and (max-width: 500px) {
    .DnD-tree-wrapper {
        height: 500px;
        width: 20rem;
    }

    .DnD-search {
        margin-left: 0;
    }

    .DnD-top-row-p {
        width: 300px;
        text-align: justify;
    }
}

@media only screen and (min-width: 501px) and (max-width: 1000px) {
    .DnD-tree-wrapper {
        height: 650px;
        width: 30rem;
    }

    .DnD-search {
        margin-left: 0;
    }

    .DnD-top-row-p {
        width: 500px;
        text-align: justify;
    }
}

@media only screen and (min-width: 1001px) and (max-width: 1899px) {
    .DnD-tree-wrapper {
        width: 900px;
    }

    .DnD-top-row-p {
        width: 800px;
    }
}