import React, { useState }  from 'react';
import { useSelector } from 'react-redux';

import Line from '../Line';

export const Restricted = (props) => {
    const cmsSelector = useSelector(state => state.cms);

    const [selected, setSelected] = useState(false);

    const changeHandler = (e, type) => {
        setSelected(e.target.checked);
        props.onChange(e, type);
    }

    return (
        <>
            {cmsSelector?.currentPageProps?.page_type === 1 &&
                <>
                    <Line 
                        key={`restricted-access`}
                        type="checkbox"
                        name="Logged In Access"
                        field_name="restricted_access"
                        tooltip="If you want to restrict access to this page to logged in users only, check this box"
                        placeholder={null}
                        change={(e)=>changeHandler(e, "restricted_access")} 
                    />
                    {selected &&
                        <>
                            <Line 
                                key={`redirect-url`}
                                type="text"
                                name="Redirect URL"
                                field_name="redirect_url"
                                tooltip="If you want to redirect users to another page, enter the URL here"
                                placeholder="Enter URL"
                                change={(e)=>props.onChange(e, "redirect_url")} 
                            />


                                
                        </>
                    }
                </>
            }
        </>
    );
}