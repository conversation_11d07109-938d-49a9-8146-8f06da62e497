import React, { useState, useCallback, useEffect } from 'react';
import { Card, Accordion, Modal } from 'react-bootstrap';
import { randomUUID } from '../../../../../utils/cms';

import Logic from './Logic';
import Property from './Property';
import Editor from './Editor';

export const Properties = (props) => {

    const [showModal, setShowModal] = useState(false);
    const [modalData, setModalData] = useState({});
    const [activeKey, setActiveKey] = useState('0');
    const [logic, setLogic] = useState();

    const clickHandler = useCallback((e, id) => {
        e.preventDefault();
        e.stopPropagation();
        if (props?.currentElement){
            let _currentElement = JSON.parse(JSON.stringify(props.currentElement));
            _currentElement.properties.forEach(prop => {
                if (prop.name === id){
                    setModalData(prop);
                    setShowModal(true);
                    return false;
                }
            });
        }
    }, [props?.currentElement]);

    useEffect(() => {
        if (!props.currentElement) setActiveKey('0');
    }, [props.currentElement]);

    useEffect(() => {
        if (props?.currentElement?.properties){
            let _logic = props.currentElement.properties.filter(prop => prop.name === "logic");
            if (_logic.length > 0) {
                setLogic({id: _logic[0].id, value: _logic[0].value});
            }
        }
    }, [props?.currentElement?.properties]);


    useEffect(() => {
        return() => {
            setShowModal(false);
            setModalData({});
            setActiveKey('0');
            setLogic(null);
        }
    },[]);

    return (
        <>
            {!props?.currentElement && <span className="nodata">No element selected.</span>}
            {props?.currentElement &&
                <>
                    <Accordion className="props" activeKey={activeKey} onSelect={k=>setActiveKey(k)}>
                        <div>
                            <Accordion.Toggle as={Card.Header} eventKey="0">Content</Accordion.Toggle>
                            <Accordion.Collapse eventKey="0">
                                <>
                                    {activeKey==='0' && 
                                        <Property type="content" {...props} click={clickHandler}/>
                                    }
                                </>
                            </Accordion.Collapse>
                        </div>
                        {props?.currentElement?.apply_styles!==false &&
                            <>
                                <div>
                                    <Accordion.Toggle as={Card.Header} eventKey="1">Style</Accordion.Toggle>
                                    <Accordion.Collapse eventKey="1">
                                        <>
                                            {activeKey==='1' && 
                                                <Property type="basic" {...props} click={clickHandler}/>
                                            }
                                        </>
                                    </Accordion.Collapse>
                                </div>
                                <div>
                                    <Accordion.Toggle as={Card.Header} eventKey="2">Advanced</Accordion.Toggle>
                                    <Accordion.Collapse eventKey="2">
                                        <>
                                            {activeKey==='2' && 
                                                <Property type="advanced" {...props} click={clickHandler}/>
                                            }
                                        </>
                                    </Accordion.Collapse>
                                </div>
                            </>
                        }
                        <div>
                            <Accordion.Toggle as={Card.Header} eventKey="3">Display Logic</Accordion.Toggle>
                            <Accordion.Collapse eventKey="3">
                                <>
                                    {activeKey==='3' && 
                                        <Logic {...props} click={clickHandler} pageFactor={props.pageFactor} data={logic?.value?.condition || null} actionTrue={logic?.value?.onTrue} actionFalse={logic?.value?.onFalse} id={logic?.id || null} key={`toolbar-logic-${randomUUID()}`} />
                                    }
                                </>
                            </Accordion.Collapse>
                        </div>
                    </Accordion>      
                    <Modal show={showModal} onHide={() => setShowModal(false)} size="xl" className="cms-modal">
                        <Modal.Header closeButton />
                        <Modal.Body>
                            <Editor data={modalData} save={props.save} close={()=>setShowModal(false)} />
                        </Modal.Body>
                    </Modal>
                </>
            }
        </>
    );
}