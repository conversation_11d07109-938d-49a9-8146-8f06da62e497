import React, { useEffect, useState, useRef } from 'react';
import { Button, Form } from 'react-bootstrap';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import { QrReader } from 'react-qr-reader';
import useSound from 'use-sound';
import { format } from 'date-fns';

import beepSound from '../../assets/sounds/beep2.mp3';
import { UserSidebar } from './UserSidebar';
import { parseErrorToString } from '../../utils/validation';
import { getBaseCompanyOfUrl } from '../../utils/urls';

import UsersAPI from '../../api/Users';

const NUM_SECONDS_TO_SHOW_USER = 20;
const NUM_SECONDS_TO_SHOW_ERROR = 5;

const style = {
    position: 'absolute',
    top: '0',
    left: '0',
    height: '100vh',
    width: '100vw',
    zIndex: '1040',
    background: '#FFFFFF',
    overflow: 'hidden'
};

/*
 *  This component uses the webcam to scan QR codes from membership cards and checks in the user appropriately
 */

export const CheckIn = (props) => {
    const mountedRef = useRef(false);

    const timeoutUserRef = useRef();
    const timeoutErrorRef = useRef();

    const [error, setError] = useState("");
    const [scannedData, setScannedData] = useState("");
    const [userId, setUserId] = useState();
    const [playBeep] = useSound(beepSound);
    const [checkinTime, setCheckinTime] = useState("");
    const [ facingMode, setFacingMode ]=useState("env")

    let isMobile;
    isMobile = navigator.userAgentData.mobile //this works on Edge, Chrome, and Opera - not Safari and Firefox
    if(!isMobile && window.innerWidth < 800) isMobile= true; //fallback for Safari and Firefox browsers

    useEffect(() => {
        mountedRef.current = true;
        const timerReload = setTimeout(() => window.location.reload(true), 60*60*1000);
        return () => {
            clearTimeout(timerReload);
            clearTimeout(timeoutUserRef.current);
            clearTimeout(timeoutErrorRef.current);
            mountedRef.current = false;
        }
    },[]);

    useEffect(() => {
        const checkinUser = async (user_id) => {
            // send the user_id to check in
            await UsersAPI.Checkin.create({
                user_id: user_id,
            }).then(async response => {
                if (!response.errors && mountedRef.current) {
                    setUserId(user_id);
                    if (response?.data?.checkin_at) setCheckinTime(new Date(response.data.checkin_at));
                    setTimeoutClearUser();
                } else {
                    displayError(parseErrorToString(response.errors));
                    console.error(response.errors);
                }
            }).catch(e => console.error(e));
        }

        if (scannedData!=="") {
            playBeep();
            let url = scannedData;
            let splitData = url.split('/');
            // we have to accomodate for both the full URL https://portal.impactathleticsny.com/user/1234
            // and the short url impactathleticsny.com/1234
            // also, we have to accomodate for localhost:3000/...
            let baseDomainScanned = getBaseCompanyOfUrl(url);
            let baseDomainCurrent = getBaseCompanyOfUrl(window.location.href);
            let isLocalhost = baseDomainCurrent==='localhost';
            let isCurrentSite = isLocalhost ? '' : baseDomainScanned===baseDomainCurrent;
            let userId = parseInt(splitData[splitData.length-1]); // grab the user_id from the url
            if ((isCurrentSite || isLocalhost) && !isNaN(userId)) {
                checkinUser(userId);
            } else {
                displayError("QR code scanned doesn't contain the expected URL");
                console.log("QR code scanned doesn't contain the expected URL");
            }
            // reset the scanned info so that it will beep again if the same qr code is scanned
            const timerToResetBeepSound = setTimeout(() => setScannedData(""), 2000);
            return () => clearTimeout(timerToResetBeepSound);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[scannedData]);


    const onScan = (result, error) => {
        if (!!result) {
            setScannedData(result?.text);
        }
        if (!!error) {
            console.info(error);
        }
    }

    const displayError = (error) => {
        setError(error);
        timeoutErrorRef.current = setInterval(() => {
            setError();
        }, NUM_SECONDS_TO_SHOW_ERROR * 1000);
    }

    const setTimeoutClearUser = () => {
        timeoutUserRef.current = setInterval(() => {
            setUserId();
            setCheckinTime();
        }, NUM_SECONDS_TO_SHOW_USER * 1000);
    }

    const handleFacingMode=()=>{
        facingMode==="user" ? setFacingMode("env") : setFacingMode("user")
    }

    return (
        <Row style={style} className="checkin">
            <Col sm="12" md="4">
                <h4>Check In Scanner</h4>
                {isMobile && 
                    <Button onClick={handleFacingMode}>
                        Switch to {facingMode==="user" ? "rear" : "front"} camera
                    </Button>
                }
                {facingMode ==="user" &&
                <>
                <h2>User Camera</h2>
                    <QrReader
                        onResult={onScan}
                        containerStyle={{ width: '400px' }}
                        constraints={{facingMode: "user"}}
                    />
                </>
                }
                {facingMode ==="env" &&
                    <>
                    <h2>Environment Camera</h2>
                        <QrReader
                            onResult={onScan}
                            containerStyle={{ width: '400px' }}
                            constraints={{facingMode: "environment"}}
                        />
                    </>
                }
                {error &&
                    <h2 className="error-text mt-4">{error}</h2>
                }
            </Col>
            <Col sm="12" md="8">
                <h4>Most Recently Checked In User
                    {checkinTime &&
                        <span className="checkin-time">
                            {format(checkinTime, " @ h:mm b")}
                        </span>
                    }
                </h4>
                
                <UserSidebar userID={userId} />
                
            </Col>
        </Row>
    );
}