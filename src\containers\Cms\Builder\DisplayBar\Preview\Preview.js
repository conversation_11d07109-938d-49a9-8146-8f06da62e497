import React, { useState, useEffect, useContext } from 'react';
import { FileURLContext } from '../../../../../contexts/FileURLContext';

import { parseCmsComponent, randomUUID } from '../../../../../utils/cms';

export const Preview = (props) => {
    const companyStuff = useContext(FileURLContext);
    const [content, setContent] = useState([]);
 
    useEffect(() => {
        const _loadPage = async () => {
            const cms_data=JSON.parse(localStorage.getItem(`cms_${props.pageFactor}`));
        
            let content={...cms_data};
            content.content = cms_data?.elements || [];
            if (cms_data?.css?.id) content.css = cms_data.css;
            if (cms_data?.js?.id) content.js = cms_data.js;
            content=JSON.stringify(content);

            const data = typeof content === "string" ? JSON.parse(content) : content;
            if (companyStuff){
                let _props = {
                    page_id: `preview-${randomUUID()}`,
                    onClick: null,
                    is_preview: true
                };
                const res = await parseCmsComponent(data, companyStuff, _props);
                setContent(res);
            }
        }

        if (companyStuff) _loadPage();
    }, [companyStuff, props.pageFactor]);

    useEffect(() => {
        return() => {
            setContent([]);
        }
    },[]);


    if (!content) return null;

    return (
        <>{content}</>            
    );
};