name: Docker Image CI QA

on:
  push:
    branches: [ qa ]

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout repo
      uses: actions/checkout@v2
      
    - name: Log in to the Container registry
      uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
      with:
        registry: hub.impactathleticsny.com
        username: impact
        password: t@8LHY8p&QmEDnBBetCPocTHupz$Mia3cHN#Pa  
        
    - name: Build and push Docker images
      uses: docker/build-push-action@ad44023a93711e3deb337508980b4b5e9bcdc5dc
      with:
        context: .
        file: Dockerfile.qa
        no-cache: true
        pull: true
        push: true
        tags: hub.impactathleticsny.com/impact-admin-portal_qa:latest
        
    - name: Update Docker service via portainer
      run: curl -q -X POST "https://portainer.impactathleticsny.com/api/webhooks/7d57e9a1-d624-4bfa-b898-6b547f03c612"

    - name: Use Node.js 18.x
      uses: actions/setup-node@v3
      with:
        node-version: 18.13
        cache: 'npm'

