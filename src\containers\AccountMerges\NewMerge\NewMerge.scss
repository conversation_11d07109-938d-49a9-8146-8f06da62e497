@import '../../../assets/css/scss/variables.scss';
@import '../../../assets/css/scss/themes.scss';
@import '../../../assets/css/scss/mixins';

.account-new-merge-wrapper {

    .new-merge-select-users{
        @include basic-flex-column
    }

    input.form-control {
        width: 50%;
    }

    .selected-users{
        @include flex-column-center;
    }

    fieldset {
        padding: 1rem;
        border: 2px solid $company-neutral-dark;
        border-radius: 10px;
        width: 50%;
        margin-bottom: 1rem;
    }

    legend {
        width: auto;
        font-weight: 700;
    }

    .you-sure-merge {
        font-size: 1.25rem;
        text-align: center;
        color: $company-bright-error;
    }
}