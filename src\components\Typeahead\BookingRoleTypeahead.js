import React, { useCallback } from 'react';

import { Typeahead } from './Typeahead';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import './Typeahead.scss';

/**Basic async typeahead for searching locations.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Services back
*/
export const BookingRoleTypeahead = (props) => {

    const makeRequest = useCallback(async (query, perPage, page=1) => {
        return {
            data: [
                // {                // this is not currently used for bookings
                //     id: 1,
                //     name: "Owner",
                // },
                {
                    id: 2,
                    name: "Manager",
                },
                {
                    id: 3,
                    name: "Attendee",
                }
            ],
            errors: null
        };
    },[]);

    // each item in responseObj.data is an option
    const formatForLabel = (option) => (
        `${option?.name}`
    );

    return (
        <Typeahead
            {...props}
            id="booking-role-search"
            formatForLabel={formatForLabel}
            makeRequest={makeRequest}
            async={false}
            paginated={false}
            placeholder={props.placeholder ? props.placeholder : "Enter a booking role..."}
        />
    )
}
