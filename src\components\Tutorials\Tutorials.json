{"User": {"id": 1, "icon": "far fa-user", "content": {"access_and_above": 5, "module_id": 4, "module_id_array": [4], "basic": "<p>On the user dashboard, you will be greeted with a table of information.  Through this information, you can filter, find, and access any user in the system.  The search bar at the top of the page allows you to search and draw results from the user’s first name, last name, email, and username.  So if, for example, you typed in <PERSON><PERSON>, you may return a user with the first name “<PERSON>, one with the last name of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and a user with the username of “NikonCameraMan”.  </p><p>The table on the user dashboard provides you with the following information:</p><table><tbody><tr><td>1. Id</td><td>2. First Name</td><td>3. Last Name</td><td>4. Username</td><td>5. Email</td></tr></tbody></table><p>Each of these table columns have a toggle next to them to sort the columns.  For example, if you don’t know the user’s name, but know they were a newly created user, you could toggle the IDs to change them to ascending order, putting the newest users first.  The ID of a user is unique and will never be shared.  Likewise, you can sort the other columns to appear in alphabetical ascending or descending order, but keep in mind numbers and characters precede “A” alphabetically. </p>", "navigation": "<p>Users can be created in multiple locations: </p><ul><li>You can navigate to the user search page by typing in ‘/users’ to the end of your URL or by using the side navigation by opening the “Users” menu and then clicking on “User Dashboard”.  In the top right of the page, you will find a button that says “New User” which you can click to add a new user.</li><li>In the POS, in the left sidebar, there is a “New User” button here.  A user can be created and then they will immediately become the active user in the POS after creation.</li><li>In a user’s profile, click on the “Family Members” tab on the right hand side.  You will be able to add a family member and create a new user there as well, at the bottom of each family card.  However, this will automatically add that new user to the family you just clicked on.  </li><li>Finally, you can type in “/users/create” to the end of your url to directly access the user creation page. </li></ul>", "subsections": [{"id": 1, "short_name": "Create", "module_id": 4, "access_and_above": 5, "title": "Create a User", "patron_block": "", "admin_block": "", "staff_block": "<ul><li>Usernames cannot be created with characters (such as @, $)</li><li>Usernames have to be unique and cannot be used if another user uses it</li><li>Emails have to be unique.</li><li>A valid date of birth must be selected.  Dates of birth cannot be within the last year. </li><li>If any of these fields are missing, the user will not be created and a message will return to you, showing what was wrong with creation.</li></ul>"}]}}, "User Profile": {"id": 2, "icon": "far fa-user-tie", "content": {"access_and_above": 7, "module_id": 49, "module_id_array": [49, 7, 14, 202, 207, 270, 206, 76, 77, 78, 79, 80, 184, 11], "basic": "<p>The user profile contains a lot of “at a glance information” about a user and some of that information persists through loading tabs about the user. That information that persists on the left of the screen is as follows:</p><p>In the top left card:<ul><li>The user’s profile photo. This photo can be editing by clicking on the photo. In absence of a photo, a generic image will appear and can still be clicked to edit</li></ul><ul><li>User’s name (first and last)</li><li>A button to quickly and easily check a user in</li><li>The date/time of their last check-in.</li><li>The user’s number of available tokens</li><li>A button that allows more detailed viewing of the specific tokens</li></ul></p><p>In the bottom left card:<ul><li>Color coded subscriptions the user has. Color coding is based on the subscription status (green active, red expired, blue canceled, and orange suspended)</li><li>The user’s contact information that they have on their profile. If the user does not have some (such as a home phone number) it will be completely absent</li><li>The groups of the user</li><li>Clicking on the group will take you to the group edit page</li></ul></ul><p></p><p>On the remaining right of the screen, there's a card that allows for advanced management of users. On the far right of that section, there is a navigation for these advanced management options. When a patron is logged in, they have access to some of these management options for themselves.</p><p>On their own profile, a patron can edit their profile, access and view their groups, edit their family if they have an admin role in their family, view their subscriptions, view their outstanding charges and add them to the cart, view the upcoming events they’ve registered for, request a reset of their password, download a copy of their waiver, and edit their notification settings.</p>", "navigation": "<p>To access another user, you can type in “/users”<p>To access another user, you can type in “/users” to the end of your URL or click on “Users” and then “User Dashboard”. </p> to the end of your URL or click on “Users” and then “User Dashboard”. This will allow you to search for a user.  If you know the id of the user you wish to access already, you can type in “/users/{id}”</p>", "subsections": [{"id": 1, "short_name": "BasicInfo", "access_and_above": 7, "module_id": 49, "title": "Edit Profile", "patron_block": "", "admin_block": "", "staff_block": "<p> You can edit the user’s basic information.  The username and email must still be unique.  If the birthday is changed, it must not be within the last two years.  Any changes that prevent the user from being updated will not make changes but will show what’s preventing the user from being created (the username, email, etc)</p>"}, {"id": 2, "access_and_above": 7, "module_id": 14, "short_name": "Groups", "title": "Groups", "patron_block": "", "admin_block": "", "staff_block": "<p>The groups tab allows for the user’s groups to be viewed, as well as their group role and status within each group they’re a part of.</p><p>The “Details” button has two different purposes - if it’s a family group, it will take you to the user’s family tab. If it’s any other kind of group, it will take you to the group management page. You also have the option, if the user has been invited to a group, to accept or decline that invitation on their behalf.</p>"}, {"id": 3, "access_and_above": 5, "module_id": 202, "short_name": "Notes", "title": "Notes", "patron_block": "", "admin_block": "", "staff_block": "<p>Staff and admin are able to create a note that the user does not have access to.  Users currently do not have access to this subpage.  A note is created by clicking the “New Note” button in the top right corner of the subpage.  Each note can be created with a different viewability.  </p><p>“Admin”</p><ul><li>Users who have the role of “Company Staff Admin” can view this note.</li></ul><p>“All”</p><ul><li>All other categories can see this note - Admin, Staff, and IT can all see this note.</li></ul><p>“Only Me”</p><ul><li>This is essentially a “note to self” about the user.</li></ul><p>“Staff”</p><ul><li>“Company Staff” and “Staff Admins” can see this note</li></ul><p>“IT/Support”</p><ul><li>These notes are viewable for IT and support staff.</li></ul><p>Regardless of role and viewability selected, if you wrote a note, you are able to view it and you’re able to edit the note.  If you’re a “Company Staff Admin”, you are able to delete notes, regardless of who wrote them.  When editing a note, the viewability can be edited as well. </p>"}, {"id": 4, "access_and_above": 7, "module_id": 207, "short_name": "Family", "title": "Family Members", "patron_block": "", "admin_block": "", "staff_block": "<p>On the family subpage of the user profile, each family group that a user belongs to will show up with their family members.  As a staff member, each member of the family can be accessed via the left of the participant.  </p><p>Additionally, a new family member can be added the family group by clicking the “Add Family Member” button.  After clicking that button, a new pop-up will with the same information as creating a new user.  In addition, there is another field for “Group Role” where you can select their group role.  Adding a new user via the family follows the same rules as adding any other user:</p><ul><li>Usernames cannot be created with characters (such as @, $)</li><li>Usernames have to be unique and cannot be used if another user uses it</li><li>Emails have to be unique.</li><li>A valid date of birth must be selected.  Dates of birth cannot be within the last year. </li><li>If any of these fields are missing, the user will not be created and a message will return to you, showing what was wrong with creation.</li></ul>"}, {"id": 5, "access_and_above": 5, "module_id": 270, "short_name": "Settings", "title": "Roles & Permissions", "patron_block": "", "admin_block": "", "staff_block": "<p>On the roles and permissions tab, a staff member may promote up to their own role.  A staff member, however, cannot edit someone of the same role.  Once they’re a staff member, any addition or loss of roles needs to be done by a Company Administrator.  The same applies for Company Administrators once they promote someone else up to the same role.  Until that point, they may edit someone of a lesser role.  To remove a Company Administrator, you must contact support.</p>"}, {"id": 6, "access_and_above": 7, "module_id": 206, "short_name": "Subscriptions", "title": "Subscriptions", "patron_block": "", "admin_block": "", "staff_block": "<p>Staff has the ability to view subscriptions.  Each subscription will have its own box containing the details relevant to that subscription.As Administrative Staff, you would have the ability to make changes to a user’s subscriptions.  You click on the button at the bottom of the box that says “Edit Subscription”.  </p><p>A pop-up box will appear with two options to be edited independent of one another.  These two options allow for setting a new final bill date and setting a new restart date.</p><p>The current value for these two options will be displayed and labeled as “current”.  To remove the date entirely, you can check the box next to “Remove Date” instead.  After making your selection and edit, click “Save Final Bill Date”.  </p><p>After you complete any changes and alterations you make, you click the X in the top right, press the “Esc” button, or click outside of the pop-up box to close it.   </p>"}, {"id": 7, "access_and_above": 7, "module_id": 76, "short_name": "Outstanding", "title": "Outstanding Charges", "patron_block": "", "admin_block": "", "staff_block": ""}, {"id": 8, "access_and_above": 7, "short_name": "Schedule", "module_id": 77, "title": "Event Schedule", "patron_block": "", "admin_block": "", "staff_block": "<p>The event schedule simply shows events that a user has registered for.  You’ll see a table beneath a search bar, which you can use to filter the events.  The table you’ll see will have the following information:</p><p> </p><table><tbody><tr><td><p>1. Event Name</p></td><td><p>2. Start Date</p></td><td><p>3. End Date</p></td><td><p>4. Event Time</p></td><td><p>5. Event Status</p></td></tr></tbody></table><p>These events will show all types, including meta events that the user has registered for. </p>"}, {"id": 9, "access_and_above": 7, "short_name": "Password", "module_id": 78, "title": "Reset Password", "patron_block": "", "admin_block": "", "staff_block": "<p>A user can request a password reset on their own account via their profile, but as a staff member, you can use this tab to also request a reset to the user’s account.  After clicking the button with “Reset Password”, an email will be sent to that user to be able to finish the reset process on their own.  There is no direct access to a user’s password, with the one exception being setting up an account for a user.  After that, the user is free to change their password and it is inaccessible. </p>"}, {"id": 10, "access_and_above": 7, "short_name": "Documents", "module_id": 79, "title": "Documents", "patron_block": "", "admin_block": "", "staff_block": "<p>If a user has any digital signed documents on file, they will be available for download to review via this tab.</p>"}, {"id": 11, "access_and_above": 7, "short_name": "Notifications", "module_id": 80, "title": "Notification Settings", "patron_block": "", "admin_block": "", "staff_block": "<p>The notification settings control if a user will receive text messages for certain actions, such as receipts or event notifications.  You may select to receive “All” notifications for a particular type or “None”.  If you need to quickly change the phone number on file that the user is receiving these text notifications at, there is a “Change Contact Info” button where you’re able to quickly do so without having to go back to the “Edit Profile” tab. </p>"}, {"id": 12, "access_and_above": 5, "short_name": "Checkin", "module_id": 184, "title": "Check In History", "patron_block": "", "admin_block": "", "staff_block": "<p>Here you can see a quick and simple list of whenever a user was checked into the facility.  This list will appear with the most recent up top and the oldest at the bottom.</p>"}, {"id": 13, "access_and_above": 7, "short_name": "Transactions", "module_id": 11, "title": "Transaction History", "patron_block": "", "admin_block": "", "staff_block": "<p>The transactions tab will take you to a different page.  If you click on it, you will be redirected to the transaction page for that individual user.  Accessing all orders is done through the side navigation, but doing so through a user’s profile is exclusive to that user and their family and more information specific to transactions can be found there.</p>"}, {"id": 14, "access_and_above": 7, "short_name": "Waiver", "module_id": null, "title": "Waiver Warning", "patron_block": "", "admin_block": "", "staff_block": "<p>If a user has not signed a waiver, you will find a banner across their profile (and in the side pane of the POS) to warn you that a user has not signed a waiver.  A user can sign in and sign their waiver themselves, but staff can also sign a waiver on the user’s behalf while the user is present.  There is a section both for an adult and a separate section for a minor.  Only one of these sections can be filled out per user.  As such, if the user has a family member who is a minor, they need to have their own associated account to sign their own waiver.  This child account could be associated with the parent account and created via that user’s family tab to automatically create that family group.  </p>"}, {"id": 15, "access_and_above": 7, "short_name": "Birthdate", "module_id": null, "title": "Birthday Warning", "patron_block": "", "admin_block": "", "staff_block": "<p>All users need to have a valid birthday on file.  Accounts created before this was required will have a banner to prompt them to add a valid birthday.  This is also a requirement for account creation and the creation form will ensure that birthday is created.  </p>"}]}}, "Groups": {"id": 3, "icon": "fal fa-user-friends", "content": {"access_and_above": 5, "module_id": 5, "module_id_array": [5, 102, 103, 303], "basic": "<p>A group can be used to sort users or for user’s to belong with their intended group or family.  For example, you can use them to sort department staff, organize a team, or associate a family with one another.  Keep in mind that with a family, some family roles will be able to see all the transactions and details of a member so do not improperly create a family group when it’s not intended.  Groups have other advantages, such as being able to associate a discount with a particular group. </p>", "navigation": "<p>To navigate to the group accessibility, you can use the side menu on the left and click on the navigation titled “Groups” (by default you have to click on “Users” first and it expands to other options, one of which is groups).  You can also type in “/groups/dashboard” to the url.</p>", "subsections": [{"id": 1, "short_name": "Filter", "access_and_above": 5, "module_id": 14, "title": "Filtering the Group Dashboard", "patron_block": "", "admin_block": "", "staff_block": "<p>There are multiple options available to help you find the group you seek: </p><ul><li>You can search the groups with a search string.  For example, typing in the word “the” would return a family called “The Thunders” or “Luthers”.  </li><li>Group type will return only the groups you pick - for example, if you have the type “Family”, you would then only see family groups and group status is similar.  </li><li>If you know the ID of a user, you may search for it and get back all groups that user is a part of. Or, in the last field, you can search directly for a group Id.</li></ul><p>All search options will compound upon one another.  For example, if you know the group you’re looking for contains “the” and has a group status of “banned’, you could use both search options.</p><p>Optionally, you can also filter table with the column’s that have arrows in them.  Clicking those arrows will change that column between ascending and descending.  So if, for example, you know you’re looking for a newly made group, you can simply filter the column for group ids, putting the newest ones at the top.  Only so many groups will be shown at a time, and to view more, aside from filtering the results, at the bottom right of the table are numbers that will take you to subsequent pages of results.</p>"}, {"id": 2, "short_name": "Reading", "access_and_above": 5, "module_id": 14, "title": "Reading the Group Table", "patron_block": "", "admin_block": "", "staff_block": "<table><tbody><tr><td><p>1.ID</p></td><td><p>2. Name</p></td><td><p>3. Type</p></td><td><p>4. Status</p></td><td><p>5.Description</p></td></tr></tbody></table><ol><li>The first column on the group table is the unique id associated with the group.  No two groups will share the same id.</li><li>The name of the group.  This is custom and can be repeated (i.e. you could have two “Luthers” families but they would each have their own ID)</li><li>The third column is the type of group (for example, the group is “Family”, “Department”, “Team”, etc)</li><li>The status will show if a group is “Active”, “Suspended”, etc.</li><li>A custom description will show here if one is entered for the group.  This is dependent on whomever has created or edited the group to add.  If no description was added, it will be blank.</li></ol>"}, {"id": 3, "short_name": "Create", "access_and_above": 5, "module_id": 102, "title": "Creating Groups", "patron_block": "", "admin_block": "", "staff_block": "<p>To create a new group, you can navigate straight to /groups/create or use the menu to navigate to “Users” and then click “Groups”.  On the dashboard page for groups, you will also see a “New Group” button in the top right corner of the screen.  </p><p>Clicking this button, or navigating via URL, will take you to a page with two inputs and two dropdowns.  Selection of a group type and entry of a group name is required to create a group successfully.</p><p>The group name can be whatever you or the customer wants it to be, as can the description.  The type selected should reflect the intention for the group so much as possible.  </p><p>The status should likewise reflect the intention for the group as much as possible.  </p><p>All of these fields are editable.</p>"}, {"id": 4, "short_name": "Edit", "access_and_above": 5, "module_id": 103, "title": "Editing a Group", "patron_block": "", "admin_block": "", "staff_block": "<p>To edit a group, on the group dashboard (‘/groups’) or navigate to ‘Groups’ in the side navigation, and filter the table as necessary.  Click on the group that you want to edit.  By default, the edit tab will be active. After switching to any other tab, you can return to ‘Edit Group’ on the right of the group screen. </p><p>All group fields are editable: The group name can be whatever you or the customer wants it to be, as can the description.  The type selected should reflect the intention for the group so much as possible.  The status should likewise reflect the intention for the group as much as possible. </p>"}, {"id": 5, "short_name": "ManageMembers", "access_and_above": 5, "module_id": 103, "title": "Manage Group Users", "patron_block": "", "admin_block": "", "staff_block": "<p>While on a group page, you can click on ‘Manage Users’ on the right hand side.  After clicking manage users, you will see a table with the following information:</p><table><tbody><tr><td><p>1. Name</p></td><td><p>2. Role</p></td><td><p>3. Status</p></td><td><p>4. Edit Options</p></td></tr></tbody></table><ol><li>This will be the name of a user in the group</li><li>This will be the user’s role within this specific group.  Bear in mind that a user can belong to multiple groups and maintain different roles within each one.  A user’s role and status are unique to group.</li><li>The user’s status in the group (here you’ll find if a user is already confirmed, invited, etc.  </li><li>These are the edit options for a user.  Hovering over the icons will show you which is what.  </li></ol><ul><li>The left icon is ‘Change Role’.  The roles available depend on the type of group.  Admin members of groups may maintain certain other permissions over other members. Clicking this icon will bring up a pop up with a drop down including the available options.</li><li>The middle icon is ‘Change Status’.  Here is where you can change a member from invited to confirmed for them, as well as some other status options.</li><li>The right icon is to remove a member from a group.  To prevent misclicks, a pop-up appears to confirm that you do, in fact, want to remove said member.</li></ul><p>This page also contains an additional search bar to filter large groups.  You can search by name, role, or status. </p>"}, {"id": 6, "short_name": "AddMembers", "access_and_above": 5, "module_id": 303, "title": "Invite Users to a Group", "patron_block": "", "admin_block": "", "staff_block": "<p>To invite new users to the group, on the right hand side of the group page, click on ‘Invite Users’ button.  On this page, you can search groups for a user with their name, username, or email.  You can also filter this page by user roles.  On this page you will see the following table: </p><table><tbody><tr><td><p>1. Box</p></td><td><p>2. ID</p></td><td><p>3. First Name</p></td><td><p>4. Last Name</p></td><td><p>5. Email</p></td><td><p>6. Profile Icon</p></td></tr></tbody></table><ol><li>This is the checkbox used to confirm invitees.</li><li>This is the ID of the user in that row.  </li><li>This is the first name of the user in that row.</li><li>This is the last name of the user in that row</li><li>This is the email of the user in that row.</li><li>Clicking the icon in this sixth row will bring up a pop up containing the profile of that user.  This allows you to double check that you’re selecting the correct user if the row’s details alone don’t help you.  </li></ol><p>2, 3, 4, and 5 can sort the columns into ascending and descending order alphabetically.  Keep in mind that some symbols (such as a space) alphabetically come before A-Z.  </p><p>To invite one of the user’s to the group, select the checkbox for the desired users and then click ‘Invite Selected’ button.  You will see a success message at the top of the screen to indicate they were invited.  You can then confirm the user manually on the ‘Manage Users’ tab or the user can accept the group invitation themselves.</p><ul><li>Users that are already in the group will not be present on this list</li></ul><ul><li>If you start a new search or navigate to a new page, you will lose your selected members</li><li>If you want to check that the user is who you’re looking for, you can open up their profile in a separate pop-up   </li></ul>"}]}}, "Orders": {"id": 4, "icon": "fal fa-receipt", "content": {"access_and_above": 5, "module_id": 59, "module_id_array": [59, 103, 178], "basic": "", "naviation": "<p>Orders can be viewed from two different locations.  In a full register POS, some registers have a button enabled in the top right corner that says “search orders”.  This view in the POS offers nearly all the same information as the full page in a smaller view.  Alternatively, you can visit ‘/all-orders’ or navigate to the ‘All Orders’ side navigation on the left menu.  </p>", "subsections": [{"id": 1, "access_and_above": 5, "short_name": "Filtering", "title": "Filtering the Orders", "module_id": 59, "patron_block": "", "admin_block": "", "staff_block": "<p>There are multiple filtering options to find what you need for the orders.  You can search for the last 3 digits of an order number and return those that match.  In the POS, you can toggle between complete/pending orders whereas in the full page, you can also filter by canceled or refunded.  If none of the status filters are selected, all orders will be returned.  On the full order’s page, you can also change how many orders are displayed at one time, up to 99 records per page.  You may navigate through multiple pages on the bottom right of the table.</p><p>On this table, the columns “Date” and “Order #” can be clicked on to toggle between ascending and descending order.</p>"}, {"id": 2, "access_and_above": 5, "short_name": "", "module_id": 59, "title": "Reading the Table", "patron_block": "", "admin_block": "", "staff_block": "<p>Upon reaching the all-orders page or opening the orders pop-up in the POS, you will see the following table: </p><table><tbody><tr><td><p>1 <p></p>Date</p></td><td><p>2<p></p>Order #</p></td><td><p>3 <p></p>First N</p></td><td><p>4 <p></p>Last N</p></td><td><p>5 <p></p>Username</p></td><td><p>6 <p></p> Location</p></td><td><p>7 <p></p>Total</p></td><td><p>8 <p></p> Button</p></td></tr></tbody></table><ol><li>This is the original transaction took place on (if the order was later modified with a refund, or through other means, this is not that date).  This column can be used to create ascending and descending order results.</li><li>This is the order number.  This will be unique per order and no two will have the exact same one.</li><li>The first name of account the transaction was made under</li><li>The last name of the account the transaction was made under</li><li>The username of the account the transaction was made under</li><li>The physical location of the purchase</li><li>The total amount for the order (including taxes and fees, etc)</li><li>Details Button.  Clicking on this button will allow you to view the order in more detail if what is on the table is not sufficient.</li></ol>"}, {"id": 3, "access_and_above": 5, "short_name": "Individual", "module_id": 305, "title": "Viewing an Individual Order", "patron_block": "", "admin_block": "<p>As noted on the order screen, tokens and bundles are not currently refundable at all and once an item in an order is refunded, no further refunds can be completed on that transaction.  Admins have the additional functionality of being able to issue refunds on these orders if need be.</p>", "staff_block": "<p>After clicking on the “Details” button on any order, you’ll be brought to a different page.  If you want a copy of this transaction for records or to give to a customer, rather than just using your browser and it’s print window, there’s a “Print Invoice Copy” in the top right hand corner, allowing for a more formatted copy of the transaction that’s currently open.  Likewise if you keep digital copies or want to email a copy to a customer, once the print screen is open, you can select to save to a pdf and be able to distribute it.</p><p>Most of the items on the order page are labeled and self explanatory, how much it cost, the type of transaction, who completed the transaction, etc.  While customers have access to their own transaction page and can view their orders in a likewise fashion, there are some fields that aren’t revealed to them, such as who completed the transaction and memos.  </p><p>On the lower portion of the order screen contains information about the items in an order.  An item will contain the name, product Id, and various aspects of pricing.  If the item was refunded, it will include different payment aspects in the table.  </p>"}, {"id": 4, "access_and_above": 4, "short_name": "Refunds", "title": "Refunds", "module_id": 178, "patron_block": "", "admin_block": "<p>There are two options for issuing refunds.  You can do a simple “Refund All” in the bottom left of the order screen and it will issue all of the order, including any fees associated with it and the items.</p><p>If you want more control over the refund, there is a checkbox in the far left of each row on the order screen.  Any items that are unable to be refunded (such as tokens) will be disabled.  Check the items that you want to refund and then select “Advanced Refund” after you’ve selected all you want.</p>", "staff_block": ""}, {"id": 5, "access_and_above": 4, "short_name": "Advanced", "title": "Advanced Refunds", "module_id": 178, "patron_block": "", "admin_block": "<p>On the Advanced Refund screen, there are many options to customize what you want refunded for a customer.  You’ll be reminded of the total amount you can refund up at the top.  Each item can have its options collapsed into the top row if you’re dealing with many items and want less clutter on your screen.  </p><p>If the order had no administrative fee, the option to add an amount will be blocked, otherwise you can enter an amount up to the amount of the admin fee for the transaction here.  Likewise, you can just select “Full Amount” if that’s your intention.</p><p>If you just want to refund the maximum amount for the items you’ve selected, select the box next to “Refund Everything” </p><p>To have more control over the amount of refund, click on the name of the product if the options aren’t already visible.  There are some quick helper buttons for amounts such as “Full Amount”, “Half Amount”, and “Shipping” and the math will be done for you.  There’s also a box to enter a memo such as “customer did not like item” and a box to enter a custom flat amount for the refund.  </p><p>In the bottom portion of the refund screen, you’ll see the original payment method and an option to select a register.  Select the register you’re currently refunding at and take note of the payment method so you know how the refund will be issued.  </p><p>You will not be able to complete the refund until a register is selected and the button will remain grayed out until you do.  After doing so and selecting the “Refund” button on the bottom left, a message will appear on your screen confirming what you chose.  Remind the customer that if it was a card transaction, their bank can take up to a week to process the refund on their end.</p>", "staff_block": ""}]}}, "Products": {"id": 5, "icon": "fal fa-drumstick-bite", "content": {"access_and_above": 5, "module_id": 13, "module_id_array": [13, 111, 110], "basic": "<p>Products have uses beyond the physical goods sold in a business.  Products can be your access fee for an event, your ability to view a digital stream, or a token used as a specific type of currency for a service.  </p>", "navigation": "<p>To navigate to the home of products, you can type “/product/dashboard” or use the side navigation to click on the product dashboard, which, by default, is nested under the “Products” header.  </p>", "subsections": [{"id": 1, "short_name": "Table", "access_and_above": 5, "title": "Reading the Product Table", "module_id": 13, "patron_block": "", "admin_block": "", "staff_block": "<p>Once on the product dashboard, you will find the products laid out in a table as follows:</p><table><tbody><tr><td><p>1. <p></p>ID</p></td><td><p>2. <p></p>Name</p></td><td><p>3. <p></p>Type</p></td><td><p>4. <p></p>Status</p></td><td><p>5. <p></p>Categories</p></td><td><p>6.<p></p> Print Locations</p></td></tr></tbody></table><ol><li>The ID of the product. This ID is unique to every product and will not be duplicated.</li><li>The custom product name input at creation. This can be any string and will show in this column.</li><li>The product type</li><li>The current status of that product</li><li>The categories this product is in. Categories for a product determine where it will show up.</li><li>Printer locations will show if an item will cause an automated print on a specific printer. If the product in this row, if included in an order or a specific page, will automatically make that printer print.</li></ol>"}, {"id": 2, "short_name": "Filtering", "access_and_above": 5, "module_id": 13, "title": "Filtering the Product Table", "patron_block": "", "admin_block": "", "staff_block": "<p>To help you find the product you want in potentially thousands of results, there are a number of filters available on the dashboard to find what you need.  The first is a search input.  In this search, any word typed will return any product whose name or description matches the term entered.  </p><p>There are also options to filter by categories, types, status, and taxability.</p>"}, {"id": 3, "short_name": "Bulk", "access_and_above": 5, "title": "Bulk Edit", "module_id": 111, "patron_block": "", "admin_block": "", "staff_block": "<p>If you toggle bulk edit on, you can edit some aspects of products “en masse”.  After you toggle “Bulk Edit” near the filter options, checkmarks will appear in the rows of the products.  After you select your desired products, you can click on “Edit X Selected Products” at the top right of the page.  </p><p>The options available for edit on bulk edit are the product’s status, category, or printer.  Making changes here will affect every item selected.  When adding a category or a printer, you have the option to replace what’s already on the products and overwrite it with what’s selected.  If the checkbox for “Replace Existing Categories” is not checked, it will add the categories or printers to all the products selected.  You can also add a brand new category to apply to those products on this screen as well.  The new category will be applied automatically after creation. </p>"}, {"id": 4, "short_name": "Create", "access_and_above": 5, "module_id": 110, "title": "Creating a New Product", "patron_block": "", "admin_block": "", "staff_block": "<p>A product is a type of item you wish to sell.  There are a variety of different categories to help you to sort your products effectively and efficiently, each with different types of options.  There are a couple different ways you can create products.  For example, if you ran a Cereal Bar and wanted to sell multiple types of cereal, you could create a product for “Fruit Loops” and another for “Lucky Charms”.  Likewise, you could make one product, “Cereal”, and create variants for those different types of cereal instead.</p><p>Every product can be assigned a category (which determines what register they appear on), a status, a name and description, and a location printer.</p><p>If you are missing a required field, which are marked with asterisks, when you try to create the product, you will find a notification.  If you are missing certain components, you will have to check a box to confirm you understand creation without these components.  The two scenarios that you will have to confirm are creating a product with no category (as they won’t appear in any capacity for use) and if the product is created as anything other than available but the variants are marked as available.  In that case, all the variants will be switched to match the parent product upon creation.</p>"}, {"id": 5, "short_name": "Variants", "access_and_above": 5, "module_id": 110, "title": "Product Variants", "patron_block": "", "admin_block": "", "staff_block": "<p>Variants are where you create each individual product.  This is where you detail what you’re selling.  Each product requires at least one variant, which is used by default with selection of the product if there are no other variants.  If there are other variants, when selecting that product in the POS, you will have a pop up to select which variant you are adding the cart/order.  In the example of running a Cereal Bar, you could use variants to be the different types of cereal.  Likewise, if you do a “Cereal of the Day” where “Lucky Charms” are a dollar cheaper on Mondays, you could create a variant called “Monday Price” at $2.00 and “Other Days” at $3.00.  It’s best to decide how variants will work best with your own individual workflow.  </p><p>If you have only a single variant, some data will be carried over from the base product, such as the product available date and status.  When you add an additional variant, this can be customized per variant.</p>"}, {"id": 6, "short_name": "Product Types", "access_and_above": 5, "module_id": 110, "title": "Product Types", "admin_block": "", "patron_block": "", "staff_block": "<p>There are different options available for each different product type.  The different types and their options are listed below:</p><ol><li>Subscription</li><li>Physical</li><li>Digital</li><li>Service</li><li>Class</li><li>Bundle</li><li>Rental</li><li>Food &amp; Drink</li><li>Token</li><li>Cancellation Fee</li></ol><p>Most product types allow you to assign a name, price, addons, dates available, and dates ending in addition to individual options below for each type. While some product types have similar options, assigning them the proper product type both keeps them organized and allows for any future options to these types to be correctly available in the future.</p><p><strong>Subscriptions</strong></p><p>Subscriptions are a product that will remain active on a user's account for the duration of the subscription.  Subscriptions can also be billed on a recurring basis.  This can be customized with the interval, quantity, and number of times billed.  <p>You can choose a monthly or yearly billing cycle.  If, for example, you pick a monthly bill cycle, put in 1 for the interval, and 3 for the number of bill times, the user will be billed once (on purchase) and then their subscription will bill two more times and be active for the entire three months.  You can customize these per variant of a product to your specifications. </p>   Subscriptions can also include a bundle to be renewed with each billing cycle. </p><p><strong>Physical, Rental, Food &amp; Drink</strong></p><p>Physical products are your standard, tangible items.  Your clothes, mugs, or any other type of good.  Rentals can be product rentals (such as roller skates) or space rentals (such as a birthday room).  Food and Drink can be any served, delivered, or shipped food product.  These products also all have the options of adding dimensions and whether the item is shippable or not and to record the UPC/SKU of an item. </p><p><strong>Digital</strong></p><p>Digital products can be used for things such as streaming events, videos, music, or photos.</p><p><strong>Service</strong></p><p>A service has basic product creation options and can primarily be used for organizing your products.  A service product could be something like drop-in daycare, parties, or other special amenities.</p><p> <strong>Class</strong></p><p>A class product can be used for any sort of class, whether it’s for sport or for learning.  They have minimal product options and are simple and quick to make.  Class products are created automatically when a fee is added to an event, as well.  </p><p><strong>Bundle</strong></p><p>Bundles are for lumping together multiple products.  Tokens can be bundles and attached to a specific user in the quantity determined when creating the product.  Only one token can be applied to a product so each variant created that way will be for those tokens.  </p><p><strong>Rental</strong></p><p>A rental product can be used for things like a space rental (for an event) or a product rental (like roller skates at a rink).  Rental products allow the creator to add shippability, measurement, and inventory details optionally.  </p><p><strong>Food</strong></p><p>Like a physical product, you can optionally track inventory details in addition to the basic details of a variant.  </p><p><strong>Token</strong></p><p>A token product allows for no variants.  A token is to be attached to a service and after purchase, the usage of the token is automatically dictated by the use booking of a service.  </p><p><strong>Cancellation Fee</strong></p><p>A category of products to organize cancellation fees, such as event cancellation, day of cancellations, etc. </p>"}, {"id": 7, "short_name": "Addons", "access_and_above": 5, "module_id": 110, "title": "Addon Categories", "patron_block": "", "admin_block": "", "staff_block": "<p>Addons are a special type of category that can be created.  Addons can be applied to be used on a product or can be a category FOR certain products.  This is best illustrated with an example.  Let’s take a sandwich and use it as our example.</p><p>In the categories, we’ll have created addon categories of “Jams/Jellies”, “Meats”, “Cheeses”, and “Breads”.  After the categories are created, we’ll start creating the products associated with these categories.  While this process requires some setup for use, it allows you the freedom to add things in a way that make sense for you and your business and are easy to use once they’re in place.  You have the freedom to make your addon items different from one another - in your “Jams/Jellies” category, you can make the locally sourced blackberry jam available for no cost but the custom imported apple cranberry jelly cost an additional dollar to add on an order.  As you create each addon, you’ll want to be sure to assign their category in the “Categories” field of the product creation.  So when you create your blackberry jam addon, you will want to select “Jams/Jellies” in the categories.  You will continue this process for each addon you want to create.</p><p>After you’ve set up your addon categories as you desire.  Then you can create a product for sandwiches and add variants of “PB &amp; J” and “Meat and Cheese” and fill out the other details accordingly.   In the variant “PB &amp; J”, you’ll want to select the addon categories “Jams/Jellies” and “Breads”.  In the variant “Meat and Cheese”, you’ll want to select the addon categories “Meats”, “Cheeses”, and “Breads”.  </p><p>This is not the only way that products, variants, and addons can be used, just an example to illustrate a possible use case.  It’s also possible to create your product as “Meat and Cheese Sandwich” and have your variants instead be “Half Sammy”, “Whole Sammy”, and “Double Decker” and add the corresponding addon categories to each of those.  Creating products and addons takes a little bit of forethought and planning to create them in a way that works best for your business model and workflow. </p>"}, {"id": 8, "short_name": "Edit", "access_and_above": 5, "module_id": 111, "title": "Editing a Product", "patron_block": "", "admin_block": "", "staff_block": "<p>Editing products is similar to creating products.  After navigating to the product dashboard, click on the row of the product that you wish to edit.  Initially, a screen with just the product information will load for ease of checking products.  At both the top and the bottom of it are buttons to access the editing screen, which is identical to the creation screen and all the creation elements apply.</p>"}]}}, "Features": {"id": 6, "icon": "fal fa-list", "content": {"access_and_above": 1, "basic": "", "navigation": "", "module_id": 278, "module_id_array": [278], "subsections": [{"id": 1, "short_name": "Sort", "access_and_above": 1, "module_id": 278, "title": "Sorting Features", "patron_block": "", "admin_block": "<p>Features can easily be sorted into a new order.  This page allows for search, multi-drag, and single drag.</p><ul><li>Search for an item by having the tree active (select in it) and begin typing anywhere.  The search will highlight results and focus the screen to the result if need be.  The search bar will also reorient to where your screen is focused so that it's always accessible.</li><li>By holding select, you can select multiple items to move at the same time.  You can drag and drop them as a group.</li></ul><p>To save the new order, save must be clicked.  Clicking cancel will result in the saved order being reloaded instead.  </p>", "staff_block": ""}]}}, "Modules": {"id": 7, "icon": "fal fa-sitemap", "content": {"access_and_above": 1, "basic": "", "navigation": "", "module_id": 29, "module_id_array": [29], "subsections": [{"id": 1, "short_name": "Dashboard", "access_and_above": 1, "title": "Module Dashboard", "module_id": 29, "patron_block": "", "admin_block": "<p>Note that this is a long, currently unfiltered list.  The sorting is being done via the front end and done for speed and maximum usage, rather than smooth efficiency.  </p><p>Searching by text will search the name, the url, the feature, and description for the text. Alternatively, you can search by whether it has endpoints and/or has parents but not with text.</p><p>Because it's a long, slow list, it only redraws results on initial page load, not on searches. </p>", "staff_block": ""}, {"id": 2, "short_name": "Parts", "access_and_above": 1, "title": "Module Elements", "module_id": 29, "patron_block": "", "admin_block": "<p><strong>Module Types</strong>:</p><ol><li><p>Page - A page on our site that needs a URL generated</p></li><li><p>Function/Widget - A component that does not have a URL, where its permissions are different than the surrounding page</p></li><li><p>External Link - A menu link that points to any URL but is not building one of our front end routes</p></li></ol><p><strong>Name, Description</strong>:</p><p>Displayed in the Permissions pages and Module lists</p><p><strong>URL</strong>:</p><p>The url on the site that it will be displayed on</p><p><strong>Company ID</strong>:</p><p>leave null for module to be available for all companies or select a company to tie the module only to one specific company.</p><p><strong>Belongs to Feature</strong>:</p><p>Choose which feature this module will be included within. This matters when it comes to turning Features on and off for clients. If this module is not included in a Feature then you can select “None”.</p><p><strong>Required Endpoints</strong>:</p><p>You must select ALL endpoints that this module uses in order for the permissions to work properly for users viewing the page. If the Module calls Users.get and Groups.get then you need to make sure you add <code>user/user[/{id}]</code> GET and <code>/group[/{group_id}]</code> GET to the Module. Please note that the urls used for the endpoints MUST match exactly what is in the backend. When it doubt check the backend routes list here: <a href=\"https://github.com/Huersch-Marketing-Group-LLC/impact-athletics/blob/main/routes/web.php\">https://github.com/Huersch-Marketing-Group-LLC/impact-athletics/blob/main/routes/web.php</a></p><p><strong>Component URL:</strong></p><p>(for Menu Items) This is used to build the route list - it is the location in our files <code> src/ folder</code> of where the component is located that this page should point to. so it could be <code>containers/Event/Calendar</code> for /calendar or <code>containers/Streaming</code> for <code>/livestream</code> - if this is not correct it will not build the route correctly in React.</p><p><strong>Available Permission Levels</strong>:</p><p>This directly links to the backend endpoints and what they are set for. For example, if your Module is editing a user’s page we know that certain roles are going to have full access to modify all users in their company - that does not require Permission Levels. However if you want to allow fine-grained control for family members to be able to edit only their own family members then this is how you do it. First in the endpoint the developer would build in options - say the endpoint for edit user has Self, Same Family, Same Family Admin built in to the code - it will check each of those options. So now when you create your Module you can select one (or more) of those options to be valid checks for allowing the user permission to access the Module. To allow a user to modify his family members I would check the box for “Same Family”.</p><p><strong>(For Menu Item type only)</strong></p><p><strong>Displayed Text:</strong> The text that will show up on the menu for this Module (This currently does not work)</p><p><strong>Parent Menu Item:</strong> This Menu Item can be nested under another one - select the parent here.</p><p><strong>Displayed Icon:</strong> Just the className of the font awesome icon to be used.</p>", "staff_block": ""}, {"id": 3, "short_name": "Levels", "access_and_above": 1, "module_id": 29, "title": "Permission Levels", "patron_block": "", "admin_block": "<p>(Auth User = the user who is currently logged in and performing the actions)</p><p><strong>1 - Self ::</strong> Auth user can only look up their own info</p><p><strong>2 - Same Family ::</strong> Auth user must be in the same family group</p><p><strong>3 - Same Family Admin ::</strong> Auth user must be in the same family group and be an admin of the family group</p><p><strong>4 - Same Group ::</strong> Auth user must be in the same group</p><p><strong>5 - Same Group Admin ::</strong> Auth user must be in the same group and be an admin of the group</p><p><strong>6 - Equal or Higher Role* ::</strong> Auth user must be of equal or higher role than the user being queried (example: auth user is role 5 (staff) and queried user is role 5 (staff)</p><p><strong>7 - Higher Role* ::</strong> Auth user must be of higher role than the user being queried (example: auth user is role 4 (company admin) and queried user is role 5 (staff)</p><p><strong>8 - Same Service Manager ::</strong> Auth user must be a Service Manager for a Service that the user is booked for (if the query is about a user), or the Service being queried.</p><p><strong>9 - Service Manager ::</strong> Auth user must be a Service Manager for any Service.</p><p><strong>10 - Same Event Manager ::</strong> Auth user must be Manager of the Event (if querying an Event), or of an Event the queried user is signed up for.</p><p><em>* Yes in this case “Higher Role” means a LOWER role_id, as low numbers mean more permissions.</em></p>", "staff_block": ""}]}}, "Endpoints": {"id": 8, "icon": "fas fa-ellipsis-v-alt", "content": {"access_and_above": 1, "module_id": 282, "module_id_array": [282], "basic": "<p><strong>API Url & Method:</strong></p><p> this must match exactly what is in the backend code in web.php <a href=\"https://github.com/Huersch-Marketing-Group-LLC/impact-athletics/blob/main/routes/web.php\">https://github.com/Huersch-Marketing-Group-LLC/impact-athletics/blob/main/routes/web.php</a></p><p>Examples of format: square brackets are used for optional parameters<p></p><em><code>user/event[/{id}] GET</code></em><p></p><em><code>user/user/{id} DELETE</code></em><p></p><em><code>user/menu POST</code></em></p><ul><li>You can add as many endpoints as you would like at one time</li><li>You can assign all new endpoints on that page to Module(s) at the time of creation</li></ul><p>If the URL does not match the backend or the endpoint is not connected to the Module then attempting to access that <PERSON><PERSON><PERSON> will return permission denied.</p>", "navigation": "", "subsections": []}}, "Assigning Permissions": {"id": 9, "icon": "far fa-stream", "content": {"access_and_above": 1, "module_id": 141, "module_id_array": [141, 142, 144, 143], "basic": "<p>Permission for a module will default to FALSE, so no one except SiteBoss admins will have access to the module until permissions have been assigned</p>", "navigation": "", "subsections": [{"id": 1, "short_name": "<PERSON><PERSON><PERSON>", "access_and_above": 1, "title": "Assigning De<PERSON>ult Permissions", "module_id": 141, "patron_block": "", "admin_block": "<p>This page will only be accessible by SiteBoss Staff.</p><p>Setting permissions in this grid will assign default permissions by role for all modules in the system for Companies who did not set an overriding permission.</p><p>Permissions on the role of Company Owner will only be applied if that company has access to that Feature.</p><p>The Save Permissions button must be clicked to save changes.</p>", "staff_block": ""}, {"id": 2, "short_name": "Default<PERSON><PERSON><PERSON>", "access_and_above": 1, "title": "Assigning De<PERSON><PERSON> <PERSON>s", "module_id": 142, "patron_block": "", "admin_block": "<p>SiteBoss admins will have access to set default permissions for all companies in the system.</p><p>Currently this is located at <code>/p/permissionAccess/</code></p>", "staff_block": ""}, {"id": 3, "short_name": "Company", "access_and_above": 1, "title": "Assigning Company Role Permissions", "module_id": 142, "patron_block": "", "admin_block": "<p>This page will be accessible by Company Owner and SiteBoss Staff.</p><p>Setting permissions in this grid will assign permissions by role for this company only. It will supersede any default permissions that exist.</p><p>Modules will only be visible if the Company Owner has access to them, since Company Owners can only pass out permissions that they themselves have. ** Even if a SiteBoss staff views the page to set permissions, they will not see all the Modules that they have access to themselves, but only those the company has access to.</p><p>A dot will appear next to the checkbox for permissions that are different from the default, ie a permission that has been overridden with a new permission for that company.</p><p>The Return to Defaults button will remove all company specific permissions for the Modules currently on-screen, setting them back to the SiteBoss default.</p><p>The Save Permissions button must be clicked to save changes.</p>", "staff_block": ""}, {"id": 4, "short_name": "CompanyRoles", "access_and_above": 1, "title": "Company Roles", "module_id": 142, "patron_block": "", "admin_block": "<p>Company Owners will have access to set permissions for every role in their company.</p><p>Currently this is located at: <code>/p/permissionAccess/</code>{id}<p></p>(change the id at the end for different companies)</p><p>The default permissions show what are assigned to that module for all companies as our defaults. If the default is overridden for that company then a dot will show beside that checkbox.</p>", "staff_block": ""}, {"id": 5, "short_name": "Group", "access_and_above": 1, "module_id": 144, "title": "Assigning Group Permissions", "patron_block": "", "admin_block": "<p>Setting permissions in this grid will assign additional permissions to all confirmed members of this group. Group permissions function a little differently than the other permission types because users in the group could be a variety of different roles. It will NOT remove any permissions from users who have permissions! This will only ADD any additional permissions to users that do not have them. This page does not show a dot for any checkbox.</p><p>checked=true : all users in this group will have this permission added<p></p>checked=false: no additional permissions will be added to members of this group</p><p>If permission_levels are assigned then they will be cumulatively added with a user’s role permission_levels (company or default) and any that are assigned to the specific user.</p>", "staff_block": ""}, {"id": 6, "short_name": "GroupRoles", "access_and_above": 1, "module_id": 144, "title": "Group Roles", "patron_block": "", "admin_block": "<p>Company Owners will have access to assign additional permissions for members of every group in their company.</p><p>Currently this is located at:<code> /p/permissions/group/</code>{id}<p></p>(Change the id at the end for different groups)</p><p>Group permissions do NOT have a default value at all because group members could be any of the different roles with a various assortment of permissions. This group permission assignment page then is only for adding permissions if someone does not already have them. You cannot deny someone permissions through the group permission - an unchecked box means only that group members will default to whatever permissions they otherwise would have.</p>", "staff_block": ""}, {"id": 7, "short_name": "User", "access_and_above": 1, "module_id": 143, "title": "Assigning User Permissions", "patron_block": "", "admin_block": "<p>This permissions grid shows the default permissions for that particular user: default role permissions, company role permissions, and group permissions. A dot beside the checkbox means that this user has a permission assigned that is different from their default would be.</p><p>If the user is given specific permission_levels then those levels are cumulatively added to their default permissions. (So if their role gives them permission_level 1, and a group of theirs gives them permission level 2, and you assign them permission level 3, then their finally permission_levels would be [1,2,3].</p><p>Block a user from a Module: If a user’s module permission is turned off at this screen then they will not have any access to that module no matter what their role or group permissions are set.</p>", "staff_block": ""}, {"id": 8, "short_name": "UserRoles", "access_and_above": 1, "module_id": 143, "title": "User Roles", "patron_block": "", "admin_block": "<p>Currently this is located at: <code>/p/permissions/user/{id}</code><p></p>(change the id at the end for different users)</p><p>User permissions shows the default permissions for that particular user - that default permission includes roles, company roles, and group permissions. If the user is given specific permission_levels then those levels are cumulatively added to their default permissions. (So if their role gives them permission_level 1, and a group of theirs gives them permission level 2, and you assign them permission level 3, then their finally permission_levels would be [1,2,3]. This user permission grid allows us to either add to the permissions or block a user from a feature. If a user’s permission is turned off at this screen then they will not have any access to that module no matter what their role or group permissions are set.</p><p>A dot beside the checkbox means that this user has a permission assigned that is different from their default would be.</p>", "staff_block": ""}]}}, "Online Ordering": {"id": 10, "icon": "fal fa-cash-register", "content": {"access_and_above": 7, "module_id": 328, "module_id_array": [328, 331, 332, 13], "basic": "", "navigation": "", "subsections": [{"id": 1, "short_name": "Orders", "access_and_above": 7, "title": "Online Ordering", "module_id": 328, "patron_block": "<p>Visit your online register: This is done by adding /p/online/9 Each time you visit, you will have a fresh order. This is so that orders never get mixed up with events and or services. You can click on items to read their description and add them to your cart. After you’ve added your items to the cart, input the fields on screen marked with a red required star. After your order has processed, it will offer you the chance to go directly to that order page where you’ll be able to see the status of the order as it changes.</p>", "staff_block": "", "admin_block": ""}, {"id": 2, "short_name": "Management", "access_and_above": 5, "title": "Pickup Management", "module_id": 331, "patron_block": "", "staff_block": "<p>There are multiple ways to manage an order.  </p><ul><li>On registers where the order is picked up, there is a bar going across the bottom.  Through this bar, you’re able to interact with online pickup orders.  It will show a handful of orders but when the orders are in excess, it will show you the number of orders that are in the queue as well.  </li><li>You can click directly on that singular order number to access its order statuses, the order items, or the full order.  From there, the order statuses will automatically check for changes every couple minutes.  If you suspect it has been updated soon than that elsewhere, you can manually refresh it via a button. </li><li>You can also view the entire list via the POS.  This is different from the regular orders page in that it only shows orders with statuses related to online pickup.  To access this menu, in the left hand side of the bar of the POS, the little list icon.  Through this list, you can find any order that has been completed via a pickup register, is in process, and is ready for pick up.  Once it’s marked as picked up, the order is removed from these extra lists.  </li><li>This was all done with large buttons to suit the needs of a high-paced environment such as a kitchen that needs to move quickly.  These orders will reflect orders from the current day, only.</li></ul><p>Another way to access the orders that need to be managed is through a page called Pickup Management.  Via this page, like in the POS, an entire list of the orders that have statuses related to online ordering.  Unlike the POS, this will show orders for all time that have the unique order statuses related to online pickup.  This page, though, doesn’t require the POS to access.  This page is best utilized by a kitchen manager who is off site or needs to override an order status remotely.  </p>", "admin_block": ""}, {"id": 3, "short_name": "Hours", "access_and_above": 5, "title": "Setting Register Hours", "module_id": 332, "patron_block": "", "staff_block": "<p>In order to set the register hours, you must go to the register dashboard and click the clock icon.  Likewise you could go to /p/registers/{registerId}/hours.  you'll see the time zone already properly set and the ability to add extra closed days (such as holidays or other days where preexisting events may make pickup difficult) and general open/closed hours.  Any time outside of these hours will see a message telling the user that the register is closed and show the hours of availability (as well as highlighting the day if it's closed because of it being listed as an additional day.)  Users won't even be able to view the register at this point so there's no chance of them accidentally placing an order outside of hours</p><p>If you click the box to say 'closed', the register will be unavailable for all 24 hours of that day.  If the start AND end time is blank, the register will be available for all 24 hours that day.  Otherwise, it needs to have both a start and an end time.  The system will warn you if you accidentally add an end time before the start time.  </p>", "admin_block": ""}, {"id": 4, "short_name": "Products", "access_and_above": 5, "title": "Adjusting Products for Online Ordering", "module_id": 13, "patron_block": "", "staff_block": "<p>Navigate to the product dashboard either by expanding the product menu on the left hand side and clicking on 'Products Dashboard' or by adding '/products/dashboard' to the url after the '/p'</p><p>Here, you can use your filters to find the products you want.  But then there are two ways this can be done - individually or in bulk.  Bulk edit will allow you to add the printer and the category but not a description.  It is advised to have a description for patrons to see before they add an item to the cart to alleviate questions they may have about said products preemptively.</p> <p>To bulk edit, toggle the bulk edit on the right of the top toolbar where the filter options are.</p><p> In this modal, you can select the categories and add the printer. You'll want to add 'Food For Pickup' or any of it's child categories (if you've added any).  You'll want to add 'Online Ordering Printer'.  If you end here, it will add these to what already exists on the product.  If you wanted to REPLACE what's on the product and remove what was there prior, you would click the box for 'Replace existing...'.  </p><p> The disadvantage to the bulk edit is that you cannot add a product description here.  Ideally a customer will see product descriptions before adding something to the cart.  This has to be done on the individual product edit page, but all of the above can be done there as well.  Don't forget to think about addons being available for each product for pickup vs in person as well. </p><p> It may also be helpful with products to be able to view children categories.  For now, you must navigate to the category list at /p/product/categories/dashboard (or just Categories in the menu options).  Here, you can expand a parent category and take note of the children categories that are included in each one.  You can also adjust categories here if you want to add additional categories underneath your category related to online pickup. </p>", "admin_block": ""}]}}}