import React,{ lazy, Suspense } from 'react';
import { Route, Switch, with<PERSON><PERSON>er } from 'react-router-dom';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';

export const Component = withRouter((props) =>{
    const LazyComponent = lazy(() => import(`../../${props.component_url}`));
    return (
        <div>
            <span></span>
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
                }>
                <Switch>
                    <Route key={`rtr-${props.component}`} exact={props.exact} path={`/p${props.url}`} component={LazyComponent}/>
                </Switch>
            </Suspense>
        </div>
    );
});