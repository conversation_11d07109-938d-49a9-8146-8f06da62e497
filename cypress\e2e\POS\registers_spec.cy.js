
/*eslint-disable*/

let baseUrl = 'https://portal-qa.impactathleticsny.com/p/'
// let baseUrl = 'http://localhost:3000'
let staffUserName = Cypress.env('impact_staff_user')
let patronUserName = Cypress.env('impact_patron_user')
let patronFullname = Cypress.env('impact_patron_name')
let checkFullNameSB = Cypress.env('impact_sb_name')
let password = Cypress.env('login_password')
let typeEmail = Cypress.env('impact_email')
let typePhone = Cypress.env('impact_phone')
let typeName = Cypress.env('impact_sb_user')
let patronUserID = 3593;
let patronPendingChargeID = 369;

describe('Registers exist for Staff', {scrollBehavior: "center"}, ()=>{
    let local;
    let user;

    before(()=>{
        cy.fixture('User/userResponse.json').then((data)=>{
            user = data;
        });
    })

    beforeEach('login as staff', ()=>{
        // we should probably have this pulled into a function somewhere as loginPatron, as this is going to be used on every e2e test
        // same with loginStaff, loginCompanyAdmin
        cy.visit(baseUrl)
        cy.viewport(1920, 1080);
        cy.get('[data-cy="login-username"]').type(staffUserName)
        cy.get('[data-cy="login-password"]').type(password)
        cy.get('[data-cy="login-submit"]').click()

        cy.intercept('POST', '/api/user/login').as('loginCall')
        cy.intercept('GET', "/api/user/menu").as('getMenus')
        // cy.wait('@getMenus')

        cy.intercept('GET', "/api/user/user/**").as('getUserUser');
        cy.wait('@getUserUser').then(()=>{
            local = (localStorage.getItem('user'))
        })
    })

    //can only set the user after sure that the calls are all done
    it("will make sure the user's highest role is staff",()=>{
        let pLocal = JSON.parse(local)
        expect(local).to.include("roles")
        let roleIds= pLocal.roles.map(role => {return role.id})
        let maxRole = Math.min(...roleIds)
        expect(maxRole).to.equal(5)
    });

    it('will show registers in menu', ()=>{
        cy.get('#menu-item-37').click()  // Registers
        cy.get('#menu-item-39').click()  // Front Desk Impact-03
        cy.url().should('include','/pos/2')
    })

    describe('User Checkin', ()=>{

        it('will show recent checkins on register 2 and 4', ()=>{
            cy.visit(baseUrl+'/pos/2')
            cy.get('[data-cy="checkin-user-list"]')
                .should('exist');
            cy.visit(baseUrl+'/pos/4')
            cy.get('[data-cy="checkin-user-list"]')
                .should('exist')
        })

        it('will not show recent checkins on register 1, 3 or 5', ()=>{
            cy.visit(baseUrl+'/pos/1')
            cy.get('[data-cy="checkin-user-list"]').should('not.exist')
            cy.visit(baseUrl+'/pos/3')
            cy.get('[data-cy="checkin-user-list"]').should('not.exist')
            cy.visit(baseUrl+'/pos/5')
            cy.get('[data-cy="checkin-user-list"]').should('not.exist')
        })

    })

    describe('User Selection',  {scrollBehavior: "center"}, ()=>{

        it('will search for user by first name', ()=>{
            cy.visit(baseUrl+'/pos/2')
            cy.intercept('POST', '/api/user/list').as('listUsers')
            // search by name
            cy.get('[data-cy="search-users"]').clear().type(typeName)
            cy.wait('@listUsers')
            cy.get('[aria-label="menu-options"]').contains(`${checkFullNameSB}`)
            // search by phone number
            cy.get('[data-cy="search-users"]').clear().type(typePhone)
            cy.wait('@listUsers')
            cy.get('[aria-label="menu-options"]').contains(`${checkFullNameSB}`)
            // search by email
            cy.get('[data-cy="search-users"]').clear().type(typeEmail)
            cy.wait('@listUsers')
            cy.get('[aria-label="menu-options"]').contains(`${checkFullNameSB}`)
        })

        it('will select and load a user and display their data', ()=>{
            let pLocal = (JSON.parse(local))

            cy.visit(baseUrl+'/pos/2')
            cy.intercept('POST', '/api/user/list').as('listUsers')
            cy.intercept('POST', "/api/user/user/**", user.patronUser2).as('getUser')
            cy.intercept('POST', '/api/user/list').as('listUsers')
            cy.intercept('POST', '/api/user/checkin').as('checkinUser')

            cy.get('[data-cy="search-users"]').clear().type(patronUserName)
            cy.wait('@listUsers')
            cy.get('[aria-label="menu-options"]').contains(patronUserName).click().then((btn) => {
                cy.get(`[data-cy="user-select-${patronUserID}"]`).should('be.visible')
                cy.get('[data-cy="users-change-user-btn"]').should('be.visible')
                cy.get('[data-cy="users-fullname"]').contains(patronFullname)

                // user has signed waiver
                cy.wait('@getUserUser').then(()=>{
                    if(pLocal.has_signed_waiver===0){
                        cy.get('[data-cy="waiver-banner-signed"]').should('not.exist')
                        cy.wait(1000)
                        cy.get('[data-cy="waiver-banner-unsigned"]').should('exist')
                        cy.get('[data-cy="waiver-btn"]').should('exist')
                    }else if(pLocal.profile.has_signed_waiver===1){
                        cy.get('[data-cy="waiver-banner-signed"]').should('exist')
                        cy.get('[data-cy="waiver-banner-unsigned"]').should('not.exist')
                    }
                })

                // checkin button
                cy.get('[data-cy="checkin-btn"]').click().then((btn)=>{
                    const beforeClick = new Date(Date.now() - 60000)  // subtract 60 seconds because the display does not include seconds
                    // this will be prone to breaking because there is no way to set custom data or classes on this 3rd party confirmation modal
                    cy.get('.btn').contains("No")
                    cy.get('.btn').contains("Yes").click()
                    cy.wait('@checkinUser')
                    cy.get('[data-cy="checkin-last"]').then($elem=>{
                        const displayedDate = new Date(Date.parse($elem.text()))
                        expect(displayedDate).to.be.gte(beforeClick)
                    })
                })

                // pending charges listed
                // clicking on pending charge adds to preview
                cy.get(`[data-cy="outstanding-item-${patronPendingChargeID}"]`).first().click().then((btn)=>{
                    cy.get('[data-cy="product-name"]').contains("Midnight's Party Group")
                })
                cy.get('[data-cy=pos-user-pending-charges]')
                    .children()
                    .should('have.length', 22)

                // subscriptions listed
                // cy.get('[data-cy="subscription-435"]')
                cy.get('[data-cy="pos-user-subscriptions"]')
                    .children()
                    .should('have.length', 2);

                // tokens listed
                cy.get('[data-cy="pos-user-tokens"]')
                    .children()
                    .should('have.length', 6);

                // // family members listed
                // cy.get('.user-container').scrollTo('0%', '100%', {ensureScrollable: false})
                // cy.get('[data-cy="family-user-3075"]')
                // cy.get('[data-cy="family-user-2691"]')
                // cy.get('[data-cy="family-user-6465"]')
                cy.get('[data-cy="pos-user-family"]')
                    .children()
                    .should("have.length", 12)

                // groups listed
                cy.get('[data-cy="group-1017"]').should('have.length',1)
                cy.get('[data-cy="pos-user-groups"]')
                    .children()
                    .should('have.length', 3)

                // check edit profile button works
                cy.get('#pos-edit-profile-btn').click().then((btn)=>{
                    cy.get('[data-cy="user-profile-container"]', { timeout: 10000 })
                    cy.get('button[class="close"]').click()
                })

                // check change user button works
                cy.get('[data-cy="users-change-user-btn"]').click().then((btn)=>{
                    cy.get('[data-cy="search-users"]').should('exist')
                    cy.get(`[data-cy="user-select-${patronUserID}"]`).should('not.exist')
                })
            })
        })
    })

    describe('Viewing Products', {scrollBehavior: "center"}, ()=>{

        it('will display an alphabetical list of products', ()=>{
            cy.visit(baseUrl+'/pos/3')
            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts').then(()=>{
                // get a list of all .product-item
                // sort that list by the text within data-cy="product-name"
                let productNameList = [];
                cy.get('.product_item')
                    .each(($els) => {
                        // pull out just the name of the product
                        let text = $els.find('[data-cy="product-name"]').text()
                        productNameList.push(text)
                    })
                    .then(()=>{
                        // sort the list alphabetically
                        let sortedProductNameList = productNameList.sort((a,b) => a.localeCompare(b))
                        expect(productNameList).to.equal(sortedProductNameList)
                    })
            })

        })

        it('will display category buttons', ()=>{
            cy.visit(baseUrl+'/pos/3')
            cy.intercept('GET', "/api/category/**").as('getCategory');
            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getCategory').then((interception)=>{
                let response = JSON.parse(interception.response.body)
                cy.get('[data-cy="btn-category"]').should('have.length', response.data[0].children.length)
                // click on a category button
                cy.get('[data-cy="btn-category"]').contains("Sandwiches").click()
                cy.wait(30)
                cy.get('.product_item').first().contains("Burger") //DEV
                // cy.get('.product_item').first().contains("Avocado Toast") //QA
                // click on the all button
                cy.get('[data-cy="btn-category-all"]').click()
                cy.wait('@getProducts')
                // cy.get('.product_item').first().contains("4 Compartment Snacks")
            })
        })

    })

    describe('Editing Orders', {scrollBehavior: "center"}, ()=>{

        it('will add and remove product to an order', ()=>{
            cy.visit(baseUrl+'/pos/3')
            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts')

            // regular product
            cy.get('.product_item').contains("Attack Caesar").click() //DEV
            // cy.get('.product_item').contains("Avocado Toast").click() //QA
            cy.get('.group-wrapper')
                .contains("Attack Caesar")
                // .contains("Avocado Toast")
                .parents('div')
                .within((item)=>{
                    // cy.get('[data-cy="product-price"]').contains("8.41")
                    cy.get('[data-cy="product-price"]').contains("7.48")
                    // delete product
                    cy.get('[data-cy="delete-prod-btn"]').click()
                })
            cy.get('.btn:contains("Yes")').click()
            // cy.get('.group-wrapper:contains("Avocado Toast")').should('not.exist')
            cy.get('.group-wrapper:contains("Attack Caesar")').should('not.exist')

            // product with variants
            // cy.get('.product_item').contains("Bakery").click()
            // cy.get('a').contains("Brownies").click()
            // cy.get('.group-wrapper')
            //     .contains("Brownies")
            //     .parents('div')
            //     .within((item)=>{
            //         cy.get('[data-cy="product-price"]').contains("2.80")
            //     })
            cy.get('.product_item').contains("Fountain Drinks").click()
            cy.get('a').contains("Root Beer").click()
            cy.get('.group-wrapper')
                .contains("Root Beer")
                .parents('div')
                .within((item)=>{
                    cy.get('[data-cy="product-price"]').contains("2.81")
                })
        })
        
        // it('will add products with addons to an order', ()=>{
        //     cy.visit(baseUrl+'/pos/3')
        //     cy.intercept('POST', "/api/product").as('getProducts');
        //     cy.intercept('PUT', "/api/order/update").as('orderUpdate');
        //     cy.wait('@getProducts')
        
        //     // product with addons
        //     cy.get('.product_item').contains("Build-A-Burger").click()

        //     cy.get('.modal-content').within(() => {

        //         cy.get('.addon-preview').contains("Build-A-Burger")

        //         cy.get('a.product-card').contains("Bacon").parents('a').click()
        //         cy.wait('@orderUpdate')
        //         cy.wait(1000)
        //         cy.get('.addon-preview').within(table => {
        //             cy.get('.item-name').contains("Bacon")
        //         })
        //         cy.get('a.product-card').contains("Lettuce").parents('a').click()
        //         cy.wait('@orderUpdate')
        //         cy.wait(1000)
        //         cy.get('.addon-preview').within(table => {
        //             cy.get('.item-name').contains("Lettuce")
        //         })
        //         cy.get('a.product-card').contains("Blue Cheese").parents('a').click()
        //         cy.wait('@orderUpdate')
        //         cy.wait(1000)
        //         cy.get('.addon-preview').within(table => {
        //             cy.get('.item-name').contains("Blue Cheese")
        //         })
        //         cy.get('[data-cy="done-btn"]').click()
        //     })

        //     // if this fails check the timing above
        //     cy.get('[data-cy="order-subtotal"]').contains("9.34")
        //     cy.get('[data-cy="order-tax"]').contains("0.66")
        //     cy.get('[data-cy="order-admin-fee"]').contains("0.40")
        //     cy.get('[data-cy="order-total"]').contains("10.40")

        //     cy.get('.row-wrapper')
        //     .contains("Bacon")
        //     .parents('.row-wrapper')
        //     .within((item)=>{
        //         cy.get('[data-cy="product-price"]').contains("0.93")
        //         // delete addon
        //         cy.get('[data-cy="delete-prod-btn"]').click()
        //     })
        //     cy.get('.btn:contains("Yes")').click()
        //     cy.get('.group-wrapper').contains("Bacon").should('not.exist')

        //     cy.get('[data-cy="order-subtotal"]').contains("8.41")
        //     cy.get('[data-cy="order-tax"]').contains("0.59")
        //     cy.get('[data-cy="order-admin-fee"]').contains("0.36")
        //     cy.get('[data-cy="order-total"]').contains("9.36")

        //     cy.get('.row-wrapper')
        //     .contains("Blue Cheese")
        //     .parents('.row-wrapper')
        //     .within((item)=>{
        //         cy.get('[data-cy="product-price"]').contains("0.93")
        //     })

        //     cy.get('.row-wrapper')
        //     .contains("Lettuce")
        //     .parents('.row-wrapper')
        //     .within((item)=>{
        //         cy.get('[data-cy="product-price"]').contains("0.00")
        //     })

        //     cy.get('.row-wrapper')
        //     .contains("Build-A-Burger")
        //     .parents('.row-wrapper')
        //     .within((item)=>{
        //         cy.get('[data-cy="product-price"]').contains("7.48")
        //         // copy product with addons
        //         cy.get('[data-cy="copy-prod-btn"]').click()
        //     })

        //     cy.get('.group-wrapper:contains("Lettuce")').should('have.length',2)
        //     cy.get('.group-wrapper:contains("Blue Cheese")').should('have.length',2)

        //     cy.get('[data-cy="order-subtotal"]').contains("16.82")
        //     cy.get('[data-cy="order-tax"]').contains("1.18")
        //     cy.get('[data-cy="order-admin-fee"]').contains("0.72")
        //     cy.get('[data-cy="order-total"]').contains("18.72")

        //     // TODO: should re-open the addons modal - won't work on localhost for some reason
                
        // }) //QA

        it('will add products with addons to an order', ()=>{
            cy.visit(baseUrl+'/pos/3')
            cy.intercept('POST', "/api/product").as('getProducts');
            cy.intercept('PUT', "/api/order/update").as('orderUpdate');
            cy.wait('@getProducts')
        
            // product with addons
            cy.get('.product_item').contains("Chicken Tenders").click()

            cy.get('.modal-content').within(() => {

                cy.get('.addon-preview').contains("Chicken Tenders")

                cy.get('a.product-card').contains("Honey Mustard").parents('a').click()
                cy.wait('@orderUpdate')
                cy.wait(1000)
                cy.get('.addon-preview').within(table => {
                    cy.get('.item-name').contains("Honey Mustard")
                })
                cy.get('a.product-card').contains("Ranch Dressing").parents('a').click()
                cy.wait('@orderUpdate')
                cy.wait(1000)
                cy.get('.addon-preview').within(table => {
                    cy.get('.item-name').contains("Ranch Dressing")
                })
                cy.get('a.product-card').contains("Buffalo Sauce").parents('a').click()
                cy.wait('@orderUpdate')
                cy.wait(1000)
                cy.get('.addon-preview').within(table => {
                    cy.get('.item-name').contains("Buffalo Sauce")
                })
                cy.get('[data-cy="done-btn"]').click()
            })

            // if this fails check the timing above
            cy.get('[data-cy="order-subtotal"]').contains("10.04")
            cy.get('[data-cy="order-tax"]').contains("0.71")
            cy.get('[data-cy="order-admin-fee"]').contains("0.43")
            cy.get('[data-cy="order-total"]').contains("11.18")

            cy.get('.row-wrapper')
            .contains("Honey Mustard")
            .parents('.row-wrapper')
            .within((item)=>{
                cy.get('[data-cy="product-price"]').contains("1.00")
                // delete addon
                cy.get('[data-cy="delete-prod-btn"]').click({force: true})
            })
            cy.get('.btn:contains("Yes")').click()
            cy.wait('@orderUpdate');
            cy.get('.group-wrapper').contains("Honey Mustard").should('not.exist')

            cy.get('[data-cy="order-subtotal"]').contains("9.04")
            cy.get('[data-cy="order-tax"]').contains("0.64")
            cy.get('[data-cy="order-admin-fee"]').contains("0.39")
            cy.get('[data-cy="order-total"]').contains("10.07")

            cy.get('.row-wrapper')
            .contains("Buffalo Sauce")
            .parents('.row-wrapper')
            .within((item)=>{
                cy.get('[data-cy="product-price"]').contains("2.50")
            })

            cy.get('.row-wrapper')
            .contains("Ranch Dressing")
            .parents('.row-wrapper')
            .within((item)=>{
                cy.get('[data-cy="product-price"]').contains("0.00")
            })

            cy.get('.row-wrapper')
            .contains("Chicken Tenders")
            .parents('.row-wrapper')
            .within((item)=>{
                cy.get('[data-cy="product-price"]').contains("6.54")
                // copy product with addons
                cy.get('[data-cy="copy-prod-btn"]').click()
            })

            // cy.get('.group-wrapper:contains("Ranch Dressing")').should('have.length',2)
            // cy.get('.group-wrapper:contains("Buffalo Sauce")').should('have.length',2)

            // cy.get('[data-cy="order-subtotal"]').contains("16.82")
            // cy.get('[data-cy="order-tax"]').contains("1.18")
            // cy.get('[data-cy="order-admin-fee"]').contains("0.72")
            // cy.get('[data-cy="order-total"]').contains("18.72")

            // TODO: should re-open the addons modal - won't work on localhost for some reason
                
        }) //Dev

        it('will retain order after page refresh', {scrollBehavior: "center"}, ()=>{
            cy.visit(baseUrl+'/pos/3')

            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts')

            cy.intercept('PUT', "/api/order/update").as('orderUpdate');

            cy.intercept('GET', "/api/user/user/**").as('getUserUser');
        
            // add items to order
            cy.get('.product_item').contains("Chicken Tenders").click()
            cy.wait(500)
            cy.get('[data-cy="product-card"]').contains("Honey Mustard").parents('a').click()
            cy.get('.addon-preview').within(table => {
                cy.get('.item-name').contains("Chicken Tenders")
            })
            cy.wait('@orderUpdate')
            cy.wait(1000)
            cy.get('[data-cy="done-btn"]').click()
            

            // select staff user
            cy.intercept('POST', '/api/user/list').as('listUsers')
            cy.get('[data-cy="search-users"]').clear().type(staffUserName)
            cy.wait('@listUsers')
            cy.get('[aria-label="menu-options"]').contains(staffUserName).click()

            // select a discount
            cy.get('[data-cy="order-discount-link"]').click()
            cy.get('[data-cy="discount-name"]')
                .contains("Employee Discount")
                .parents('[data-cy="discount-item"]')
                .click()
                cy.wait('@orderUpdate')
            cy.get('[data-cy="done-btn"]').click()

            // save order number
            cy.get('[data-cy="order-number"]').invoke('text').then((text)=>{

                cy.reload()
                cy.wait('@getProducts')

                cy.get('[data-cy="order-number"]').should('have.text', text)

                cy.get('[data-cy="users-fullname"]').contains(staffUserName)

                cy.get('.group-wrapper:contains("Chicken Tenders")').should('have.length',1)
                cy.get('.group-wrapper:contains("Honey Mustard")').should('have.length',1)

                cy.get('[data-cy="order-discount-link"]').click()
                //give the css a chance to update after the click
                cy.wait('@getUserUser')
                cy.wait(500)
                cy.get('[data-cy="discount-name"]:contains("Employee Discount")')
                    .parents('[data-cy="discount-item"]')
                    .should('have.class', 'selected')

            })

        })

    })
        
    describe('Discounts', {scrollBehavior: "center"}, ()=>{

        it('will apply discounts',()=>{
            cy.visit(baseUrl+'/pos/3')
            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts')
        
            cy.intercept('PUT', "/api/order/update").as('orderUpdate');
        
            // add items to order
            cy.get('.product_item').contains("Chicken Tenders").click()
            cy.get('.modal-content').within(() => {
                cy.get('.addon-preview').contains("Chicken Tenders")
                cy.get('a.product-card').contains("Honey Mustard").parents('a').click()
                cy.wait('@orderUpdate')
                cy.wait(1000)
                cy.get('.addon-preview').within(table => {
                    cy.get('.item-name').contains("Honey Mustard")
                })
                cy.get('[data-cy="done-btn"]').click()
            })

            // no user selected, no discount link
            cy.get('[data-cy="order-discount-link"]').should('not.exist')

            // select staff user, discount link appears
            cy.intercept('POST', '/api/user/list').as('listUsers')
            cy.get('[data-cy="search-users"]').clear().type(staffUserName)
            cy.wait('@listUsers')
            cy.get('[aria-label="menu-options"]').contains(staffUserName).click()

            // click on discount link to open discount modal
            cy.get('[data-cy="order-discount-link"]').click()

            // staff discount should be visible in the list
            cy.get('[data-cy="discount-name"]')
                .contains("Employee Discount")
                .parents('[data-cy="discount-item"]')
                .click()
        
            // staff discount should be highlighted
            cy.get('[data-cy="discount-name"]')
                .contains("Employee Discount")
                .parents('[data-cy="discount-item"]')
                .should('have.class', 'selected')

            // non-combinable discount disabled
            cy.get('[data-cy="discount-name"]')
                .contains("Extra Discount")
                .parents('[data-cy="discount-item"]')
                .should('have.class', 'disabled')
                .within(()=>{
                    cy.get('[data-cy="discount-no-combine"]').should('exist')
                })

            // combinable discount not disabled
            cy.get('[data-cy="discount-name"]')
                .contains("test coupon")
                .parents('[data-cy="discount-item"]')
                .should('not.have.class', 'disabled')
                .within(()=>{
                    cy.get('[data-cy="discount-no-combine"]').should('not.exist')
                })

            // show correct "amount saved" at top of modal
            cy.get('[data-cy="discount-amount"]').contains("1.51")

            cy.get('[data-cy="done-btn"]')
                .click()

            // original price and discounted price are shown for each item
            cy.get('.group-wrapper')
            .contains("Chicken Tenders")
            .parents('.row-wrapper')
            .within((item)=>{
                cy.get('[data-cy="original-price"]').contains("6.54").should('be.visible')
                cy.get('[data-cy="product-price"]').contains("5.23")
            })
            cy.get('.row-wrapper')
            .contains("Honey Mustard")
            .parents('div')
            .within((item)=>{
                cy.get('[data-cy="original-price"]').contains("1.00").should('be.visible')
                cy.get('[data-cy="product-price"]').contains(".80")
            })

            // selecting the discount changes the price of the items, subtotal and total
            cy.get('[data-cy="order-subtotal"]').contains("6.03")
            cy.get('[data-cy="order-tax"]').contains("0.43")
            cy.get('[data-cy="order-admin-fee"]').contains("0.26")
            cy.get('[data-cy="order-total"]').contains("6.72")

        }) //Dev
        // it('will apply discounts',()=>{
        //     cy.visit(baseUrl+'/pos/3')
        //     cy.intercept('POST', "/api/product").as('getProducts');
        //     cy.wait('@getProducts')
        
        //     cy.intercept('PUT', "/api/order/update").as('orderUpdate');
        
        //     // add items to order
        //     cy.get('.product_item').contains("Build-A-Burger").click()
        //     cy.get('.modal-content').within(() => {
        //         cy.get('.addon-preview').contains("Build-A-Burger")
        //         cy.get('a.product-card').contains("Extra Patty").parents('a').click()
        //         cy.wait('@orderUpdate')
        //         cy.wait(1000)
        //         cy.get('.addon-preview').within(table => {
        //             cy.get('.item-name').contains("Extra Patty")
        //         })
        //         cy.get('[data-cy="done-btn"]').click()
        //     })

        //     // no user selected, no discount link
        //     cy.get('[data-cy="order-discount-link"]').should('not.exist')

        //     // select staff user, discount link appears
        //     cy.intercept('POST', '/api/user/list').as('listUsers')
        //     cy.get('[data-cy="search-users"]').clear().type(staffUserName)
        //     cy.wait('@listUsers')
        //     cy.get('[aria-label="menu-options"]').contains(staffUserName).click()

        //     // click on discount link to open discount modal
        //     cy.get('[data-cy="order-discount-link"]').click()

        //     // staff discount should be visible in the list
        //     cy.get('[data-cy="discount-name"]')
        //         .contains("Employee Discount")
        //         .parents('[data-cy="discount-item"]')
        //         .click()
        
        //     // staff discount should be highlighted
        //     cy.get('[data-cy="discount-name"]')
        //         .contains("Employee Discount")
        //         .parents('[data-cy="discount-item"]')
        //         .should('have.class', 'selected')

        //     // non-combinable discount disabled
        //     cy.get('[data-cy="discount-name"]')
        //         .contains("Extra Discount")
        //         .parents('[data-cy="discount-item"]')
        //         .should('have.class', 'disabled')
        //         .within(()=>{
        //             cy.get('[data-cy="discount-no-combine"]').should('exist')
        //         })

        //     // combinable discount not disabled
        //     cy.get('[data-cy="discount-name"]')
        //         .contains("Courtside Special Discount 10%")
        //         .parents('[data-cy="discount-item"]')
        //         .should('not.have.class', 'disabled')
        //         .within(()=>{
        //             cy.get('[data-cy="discount-no-combine"]').should('not.exist')
        //         })

        //     // show correct "amount saved" at top of modal
        //     cy.get('[data-cy="discount-amount"]').contains("2.06")

        //     // original price and discounted price are shown for each item
        //     cy.get('.group-wrapper')
        //     .contains("Build-A-Burger")
        //     .parents('.row-wrapper')
        //     .within((item)=>{
        //         cy.get('[data-cy="original-price"]').contains("7.48").should('be.visible')
        //         cy.get('[data-cy="product-price"]').contains("5.98")
        //     })
        //     cy.get('.row-wrapper')
        //     .contains("Extra Patty")
        //     .parents('div')
        //     .within((item)=>{
        //         cy.get('[data-cy="original-price"]').contains("2.80").should('be.visible')
        //         cy.get('[data-cy="product-price"]').contains("2.24")
        //     })

        //     // selecting the discount changes the price of the items, subtotal and total
        //     cy.get('[data-cy="order-subtotal"]').contains("8.22")
        //     cy.get('[data-cy="order-tax"]').contains("0.42")
        //     cy.get('[data-cy="order-admin-fee"]').contains("0.35")
        //     cy.get('[data-cy="order-total"]').contains("8.99")

        // }) //QA

    })

    describe('Search and Print', {scrollBehavior: "center"}, ()=>{

        it(`should print the current order`, () => {
            let printCall
            cy.visit(baseUrl+'/pos/3')
        
            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts')
        
            // stub print
            cy.window().then(win => {
                printCall = cy.stub(win, 'print')
            })
        
            // add items to order
            cy.get('.product_item').contains("Attack Caesar").click()
            cy.get('.product_item').contains("Candy Bars").click()
        
            // make sure the print window opened
            cy.get('[data-cy="pos-button-print"]').click().then(()=>{
                // check that the print iframe was created successfully
                cy.get('iframe[id="printWindow"]').its('0.contentDocument')
                cy.window().its('printWasCalled').should('equal', true)
                // expect(printCall).to.be.called   // this doesn't work
            })
        })
        
        it(`should search and print completed orders`, () => {
            cy.visit(baseUrl+'/pos/3')
        
            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts')
        
            cy.get('[data-cy="pos-button-orders"]').click()
        
            cy.get('#search_input').should('exist')
            cy.get('#status-paid').should('exist')
            cy.get('[data-cy="orders-list"] > tbody').children('tr').its('length').should('be.gt', 0)
            cy.get('[data-cy="orders-list"] > tbody').children('tr').first().within(()=>{
                cy.get('[data-cy="order-details-btn"]').click()
            })
            cy.get('[data-cy="order-status-name"]').contains('Completed')
        
            // make sure the print window opened
            cy.get('[data-cy="order-details-print-btn"]').click().then(()=>{
                // check that the print iframe was created successfully
                cy.get('iframe[id="printWindow"]').its('0.contentDocument')
                cy.window().its('printWasCalled').should('equal', true)
            })
        })
        
        it(`should search and print incomplete orders`, () => {
            cy.visit(baseUrl+'/pos/3')
        
            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts')
        
            cy.get('[data-cy="pos-button-orders"]').click()
        
            cy.get('#status-paid').click({ force: true })
            cy.get('[data-cy="orders-list"] > tbody').children('tr').its('length').should('be.gt', 0)
            cy.get('[data-cy="orders-list"] > tbody').children('tr').first().within(()=>{
                cy.get('[data-cy="order-details-btn"]').click()
            })
            cy.get('[data-cy="order-status-name"]').contains('Pending')
        
            // make sure the print window opened
            cy.get('[data-cy="order-details-print-btn"]').click().then(()=>{
                // check that the print iframe was created successfully
                cy.get('iframe[id="printWindow"]').its('0.contentDocument')
                cy.window().its('printWasCalled').should('equal', true)
            })
        })

    })

    describe('Completing Transactions',  {scrollBehavior: "center"}, ()=>{

        it("Will process a cash transaction successfully",()=>{
            cy.visit(baseUrl+'/pos/3')

            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts')
        
            // add items to order
            cy.get('.product_item').contains("Attack Caesar").click()

            // select patron user
            cy.intercept('POST', '/api/user/list').as('listUsers')
            cy.get('[data-cy="search-users"]').clear().type(patronUserName)
            cy.wait('@listUsers')
            cy.get('[aria-label="menu-options"]').contains(patronUserName).click()

            cy.intercept('POST', "/api/payment/record_cash_payment").as('getCashTransaction')

            // checkout
            cy.get('[data-cy="pos-button-checkout"]').click()
            cy.get('[data-cy="payment-cash-button"]').click()
            cy.get('[data-cy="payment-submit-button"]').click()
            cy.wait('@getCashTransaction')

            cy.get('[data-cy="transaction-status-successful"]').should('exist')
        })

        it("Will check that a cash process error is handled properly",()=>{
            cy.visit(baseUrl+'/pos/3')

            cy.intercept('POST', "/api/product").as('getProducts');
            cy.wait('@getProducts')
        
            // add items to order
            cy.get('.product_item').contains("Attack Caesar").click()

            // select patron user
            cy.intercept('POST', '/api/user/list').as('listUsers')
            cy.get('[data-cy="search-users"]').clear().type(patronUserName)
            cy.wait('@listUsers')
            cy.get('[aria-label="menu-options"]').contains(patronUserName).click()

            cy.intercept('POST', "/api/payment/record_cash_payment", {fixture: 'Errors/array_error.json'}).as('getError')

            // checkout
            cy.get('[data-cy="pos-button-checkout"]').click()
            cy.get('[data-cy="payment-cash-button"]').click()
            cy.get('[data-cy="payment-submit-button"]').click()
            cy.wait('@getError')

            cy.get('[data-cy="error-catcher-modal"]').contains("Oh no you don't have permission for this!")
        })

    })

})