import React, { useState, useEffect, useRef } from 'react'
import { Container, Card, Breadcrumb } from 'react-bootstrap';
import { Link } from 'react-router-dom'; 
import SubHeader from '../../common/SubHeader/SubHeader';

// adjust pathing
// import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';
// import './something.scss'

export const CreateComponent = () => {

    const mountedRef = useRef(false);
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();

    useEffect(()=>{
        mountedRef.current = true

        return()=> mountedRef.current = false
    },[])

    return (
        <Container fluid>
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "New Location" }
            ]} />
            <Card className="content-card">
                <h4 className="section-title">
                    {/* Section Title */}
                </h4>
                {/* <div className='something-something-wrapper'>

                </div> */}
            </Card>
        </Container>
    )
}
