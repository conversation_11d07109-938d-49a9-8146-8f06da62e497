import React, { useState,createRef } from 'react';
import { useHistory, Link } from "react-router-dom";
import Container from 'react-bootstrap/Container';
import Col from 'react-bootstrap/Col';
import Row from 'react-bootstrap/Row';
import Button from 'react-bootstrap/Button';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';
import SubHeader from '../../../components/common/SubHeader';
import Discounts from '../../../api/Discounts';

import BlocklyJS from 'blockly/javascript';
import BlocklyComponent, { Category, Block, Value, Field, Shadow } from '../../../components/Blockly';

import './Dashboard.css';

const Dashboard = (props) => {
    let history = useHistory();    
    
    const simpleWorkspace=createRef();

    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

    const submitHandler = async (e) => {
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        const code=BlocklyJS.workspaceToCode(simpleWorkspace.current.workspace);

        if (code) {
            let formDataObj = {
                "name":"Discount",
                "code":code,
                "id":null,
            };

            let response;
            if (props.discount_id) response=await Discounts.update({formDataObj});
            else response=await Discounts.create({formDataObj});
            
            //Promise.all(promise).then(response => {
                if (!response.errors) {
                    setSubmitting(false);
                    setSuccess(<Toast>Discount saved successfully!</Toast>);
                    history.push(props.referer || "/p/discounts/dashboard"); // pushes to profile again to avoid resubmission
                } else { // api returned errors
                    setSubmitting(false);
                    setError(<ErrorCatcher error={response.errors} />);
                } 
            /*}).catch((error)=>{
                setSubmitting(false);
                setError(<ErrorCatcher>{error.message}</ErrorCatcher>);
            });*/
        } else {
            setSubmitting(false);
            setError(<ErrorCatcher>
                <ul>
                    <li>Discount logic is empty.</li>
                </ul>
            </ErrorCatcher>);
        }
    };

    return (        
        <Container fluid>
            
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Discount Manager" }
            ]} />
            <Row className="body">
                <Col sm="12" lg="10">
                    {success}
                    <div className="blockly-workspace">
                        <BlocklyComponent 
                            ref={simpleWorkspace}
                            readOnly={false} 
                            trashcan={true}
                            media={'/'}
                            move={{scrollbars: true,drag: true,wheel: true}}
                            initialXml={``}                        >                           
                            <Category name="Variables" categorystyle="variable_category" >
                                <Block type="variables_get">
                                    <Field name="VAR">product_id</Field>
                                </Block>
                                <Block type="variables_get">
                                    <Field name="VAR">product_category_id</Field>
                                </Block>
                                <Block type="variables_get">
                                    <Field name="VAR">product_price</Field>
                                </Block>
                                <Block type="variables_get">
                                    <Field name="VAR">discount</Field>
                                </Block>
                                <Block type="variables_set">
                                    <Field name="VAR">discount</Field>
                                </Block>
                            </Category>
                            <Category name="Logic" categorystyle="logic_category">
                                <Block type="controls_if" />
                                <Block type="logic_compare" />
                                <Block type="logic_operation" />
                                <Block type="logic_negate" />
                                <Block type="logic_boolean" />
                            </Category>
                            <Category name="Math" categorystyle="math_category">
                                <Block type="math_number">
                                    <Field name="NUM">1</Field>
                                </Block>
                                <Block type="math_arithmetic">
                                    <Value name="A">
                                        <Shadow type="math_number">
                                            <Field name="NUM">1</Field>
                                        </Shadow>
                                    </Value>
                                    <Value name="B">
                                        <Shadow type="math_number">
                                            <Field name="NUM">1</Field>
                                        </Shadow>
                                    </Value>
                                </Block>
                            </Category>
                            <Category name="Text" categorystyle="text_category">
                                <Block type="text">
                                    <Field name="TEXT">Hello!</Field>
                                </Block>
                                <Block type="text_isEmpty">
                                    <Value name="VALUE">
                                        <Shadow type="text">
                                            <Field name="TEXT"></Field>
                                        </Shadow>
                                    </Value>
                                </Block>
                                <Block type="text_indexOf">
                                    <Value name="VALUE">
                                        <Block type="variables_get">
                                            <Field name="VAR">text</Field>
                                        </Block>
                                    </Value>
                                    <Value name="FIND">
                                        <Shadow type="text">
                                            <Field name="TEXT">COUPON-CODE</Field>
                                        </Shadow>
                                    </Value>
                                </Block>
                            </Category>
                        </BlocklyComponent>
                    </div>
                </Col>
                <Col sm="12" lg="2" className="d-flex flex-column">
                    <p>Create custom discounts meeting different criteria that will be evaluated when an item is added, removed or refreshed.</p>
                    <p>Discounts could be applied when:</p>
                    <ul className="tips">
                        <li>A specific product or category is added.</li>
                        <li>A coupon code is entered.</li>
                        <li>Another discount criteria is met.</li>
                    </ul>
                    <br/>
                    {error}
                    <Button variant="primary" type="button" onClick={submitHandler} disabled={submitting} className={`mt-auto ${submitting?" submitting":""}`}>Save</Button>
                </Col>
            </Row>
        </Container>
    );
}

export default Dashboard;