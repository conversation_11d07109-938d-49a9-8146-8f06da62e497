import React,{useEffect, useState, Suspense} from 'react';
import { useLocation, Link  } from "react-router-dom";
import { Container, Row, Col, Card, Breadcrumb, ListGroup} from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import SubHeader from '../../../components/common/SubHeader';

import Wizard from '../Wizard';
import BasicInfo from '../BasicInfo'
import '../Details/Details.css';

const Create = (props) => {    
    const location = useLocation();
    const adminDash = JSON.parse(localStorage.getItem("adminDash"));

    const urlParts = location.pathname.split("/");
    const lastPart = urlParts[urlParts.length-1];

	useEffect(() => {
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {lastPart === "wizard" ? <Wizard /> : <BasicInfo />}
            </Suspense>
        );
	}, [lastPart]);
    
    const [pagePart,setPagePart]=useState(
        <Suspense fallback={             
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
            </SkeletonTheme>
        }>            
        </Suspense>
    );

    const loadPagePartHandler= (e) => {
        let component;
        switch (e.target.hash.substr(1)){
            case "Wizard":
                component=<Wizard />;
                break;
            case "BasicInfo":
            default:
                component=<BasicInfo />;
                break;
        }
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {component}
            </Suspense>
        );
    }
    
    return (
        <Container fluid>
            <SubHeader items={[
                {linkAs:Link,linkProps:{to:"/p/home"},text:"Home"},
                {linkAs:Link,linkProps:{to:"/p/companies/dashboard"},text:"Company Dashboard"},
                {text:"New Company"}
            ]} />
            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <Row>
                            <Col sm="auto" className="order-1 order-lg-2">
                                <ListGroup className="profileMenu" variant="flush">
                                    <ListGroup.Item action href="#BasicInfo" onClick={loadPagePartHandler}>
                                        <i className="far fa-store-alt"></i> Company Info
                                    </ListGroup.Item>
                                </ListGroup>
                            </Col>
                            <Col className="order-2 order-lg-1">
                                {pagePart /*this is where the magic is happening :-O */ }
                            </Col>
                        </Row>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Create;