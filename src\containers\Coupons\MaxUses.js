import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import Coupons from '../../api/Coupons';
import * as actions from '../../store/actions';

import './Coupon.scss';

const MaxUses = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Is there a max number of times that a user can use this coupon?</span>
                <Row>
                    <Col className="col-sm-auto form-row">
                        <Form.Check 
                            type="radio"
                            id="unlimited-1"
                            label="No, Unlimited"
                            name="unlimited"
                            value={1}
                            checked={coupon.unlimited===1}
                            onChange={onChangeInput}
                            isInvalid={!!errors.unlimited}
                            className="form-radio"
                        />
                        <Form.Check 
                            type="radio"
                            id="unlimited-0"
                            label="Yes"
                            name="unlimited"
                            value={0}
                            checked={coupon.unlimited===0}
                            onChange={onChangeInput}
                            isInvalid={!!errors.unlimited}
                            className="form-radio"
                        />
                    </Col>
                    {coupon.unlimited===0 &&
                        <>
                        <Col className="col-sm-auto">
                            <i className="far fa-arrow-right mt-4"/>
                        </Col>
                        <Col>
                            <Form.Label>Number amount</Form.Label>
                            <Form.Control
                                type="numeric"
                                id="max_uses"
                                name="max_uses"
                                value={coupon.max_uses}
                                onChange={onChangeInput}
                                isInvalid={!!errors.max_uses}
                                style={{width: "110px"}}
                            />
                        </Col>
                        </>
                    }
                </Row>
                <div className={`err ${!!errors.unlimited || !!errors.max_uses ? "" : "hidden"}`}>
                    {errors.unlimited}
                    {errors.max_uses}
                </div>
            </Col>
        </Row>
    );
}

export default MaxUses;