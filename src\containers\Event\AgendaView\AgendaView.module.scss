@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';
@import '../../../assets/css/scss/mixins';

.agenda-date-pick-wrapper{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: $main-padding;
    .date{
        font-family: $secondary-font-family;
        font-size: 2rem;
        i{
            margin-right: 1rem;
        }
    }
    .agenda-label{
        margin: 0 0 0 2.5rem;
        text-align: center;
    }
    i:not(.fa-print){
        font-size: 2rem;
    }
    .title-row{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }
}

.search-filters{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .each-input{
        @include basic-flex-column;
        margin-right: 16px;
    }
    label{
        @include basic-label;
    }
    select, input{
        @include basic-input-select;
        margin-right:0;
    }
    input[type=number] {
        -moz-appearance: textfield;
    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
}
.agenda-each-day{
    @media print{
        .agenda-date{
            font-size: 10px;
            span{
                font-size: 16px !important;
            }
            .date-secondary{
                margin-top: 3px !important;
            }
        }
        .agenda-event, .no-event{
            font-size: 10px;
        }
    }
    width: 100%;
    display: flex;
    flex-direction: row;
    border-top: 1px solid $divider-color;
    flex-wrap: wrap;
    margin: 0;
    .agenda-each-day{
        display: flex;
    }  
    .agenda-date{
        display: flex;
        flex-direction: row;
        margin-right: .5rem;
        width:150px;
        span{
            font-family: $secondary-font-family;
            font-size: 22px;
            font-weight: 800;
            margin-right: .5rem;
            margin-left: .2rem;
        }
        .date-secondary{
            margin-top: .8rem;
        }
        p{
            margin: 0;
            padding: 0;
            margin-right: .5rem;
            vertical-align: text-bottom;
        }
    }
    .agenda-wrapper{
        flex:1;
        width: 100%;
    }
    .agenda-event{
        flex: 1;
        &:nth-child(even){
            background-color: $neutral-background-color;
        }
        display: flex;
        &:not(:first-child){
            border-top: 1px solid $neutral-background-color;
        }
        p{
            margin: 0;
            padding: 0;
        }
        .date{
            margin-right: 1rem;
        }
        .event-name{
            font-weight: 600;
        }
        .each-time{
            margin-right: 3rem;
            width: 75px;
        }
        .start-end{
            font-weight: 500;
            margin-right: .25rem;
            margin-left: .5rem;
        }
    }
}
// @media screen and (max-width: 1400px){
//     .agenda-date-pick-wrapper{
//         flex-direction: column-reverse;
//     }
//     input{
//         max-width: 300px;
//     }
//     .form-control{
//         max-width: 210px;
//     }
// }
// @media screen and (max-width: 600px) {
//     .agenda-each-day{
//         flex-direction: column;
//         .agenda-event{
//             flex-direction: column;
//             margin-left:3rem;
//         }
//         .start-end{
//             margin-left: 0;
//         }
//     }
// }