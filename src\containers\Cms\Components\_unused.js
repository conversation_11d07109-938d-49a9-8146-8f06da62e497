/* saving this here just in case */
import Banner from './Components/_Special/Banner';
import News from './Components/_Special/News';
import ContactForm from './Components/_Special/ContactForm';
import GroupPage from './Components/_Special/GroupPage';
import ComingSoon from './Components/_Special/ComingSoon';
import Contact from './Components/_Special/Contact';
import EventSchedule from './Components/Events/EventSchedule';
import EventDetail from './Components/Events/EventDetail';
import Hero from './Components/_Special/Hero';

export const Components = {
    Hero: {
        component: Hero,
        name: "<PERSON> Image",
        component_name: "Hero",
        tooltip: "The hero image is the large image at the top of the page",
        apply_styles: true,
        can_drop: true,  // flag the component as droppable, they're only draggable by default
        list: "Components",
        props: [
            {
                name: "image",
                type: "image",
                description: "The image to display in the hero",
                display_name: "Hero Image",
                value: "https://siteboss.s3.amazonaws.com/themes/1/events.png",
                elements: ["Hero"]
            },
            {
                name: "height",
                type: "number",
                description: "The height of the hero image",
                display_name: "Height",
                value: 400,
                elements: ["Hero"]
            }
        ]
    },    
    EventSchedule: {
        component: EventSchedule,
        name: "Schedule Widget",
        component_name: "EventSchedule",
        tooltip: "The schedule widget shows a list of upcoming events",
        apply_styles: true,
        list: "Components",
        props:[
            {
                name: "limit",
                type: "number",
                description: "The number of events to show",
                display_name: "Limit",
                value: 3,
                elements: ["EventSchedule"]
            },
            {
                name: "event_types",
                type: "json",
                description: "A list of Events Type IDs to show, for example: [1,2,3]",
                display_name: "Event Type IDs",
                value: [],
                elements: ["EventSchedule"]
            },
            {
                name: "data",
                type: "json",
                description: "A list of events to show if not using Event Type IDs",
                display_name: "Events",
                value: [
                    {
                        id: 1,
                        name: "Event 1",
                        image: "https://siteboss.net/images/event.png",
                        start_datetime: "2023-01-01 10:00:00",
                        location_name: "Location 1",
                    },
                    {
                        id: 2,
                        name: "Event 2",
                        image: "https://siteboss.net/images/event.png",
                        start_datetime: "2023-01-01 11:00:00",
                        location_name: "Location 2",
                    },                    
                ],
                elements: ["EventSchedule"]
            }
        ]
    },
    EventDetail: {
        component: EventDetail,
        name: "Event Details",
        component_name: "EventDetail",
        tooltip: "The event details shows the details for an event",
        apply_styles: true,
        list: "Components",
        props:[
            {
                name: "id",
                type: "number",
                description: "The ID of the event to show",
                display_name: "Event ID",
                value: "",
                elements: ["EventDetail"]
            },
            {
                name: "data",
                type: "json",
                description: "The event to show if not using Event ID",
                display_name: "Event",
                value: [
                    {
                        id: 1,
                        name: "Event 1",
                        description: "This is a description of the event",
                        image: "https://siteboss.net/images/event.png",
                        date: "2023-01-01 10:00:00",
                        requires_registration: true,
                        requires_membership: true,
                        location:{
                            name: "Location 1",
                            address: "123 Main St",
                            city: "Anytown",
                            state: "CA",
                            zip: "12345",
                        },
                        product:{
                            price: 10,
                            name: "Event Ticket",
                        },
                    },
                ],
                elements: ["EventDetail"]
            }
        ]
    },
    Banner: {
        component: Banner,
        name: "Banner",
        component_name: "Banner",
        tooltip: "The banner is a large image with text overlay",
        apply_styles: true,
        can_drop: true,
        list: "Components",
        props:[
            {
                name: "image",
                type: "image",
                description: "The image to show",
                display_name: "Image",
                value: "https://siteboss.net/images/banner.png",
                elements: ["Banner"]
            },
            {
                name: "height",
                type: "number",
                description: "The height of the banner in pixels",
                display_name: "Height",
                value: 300,
                elements: ["Banner"]
            },
            {
                name: "href",
                type: "text",
                description: "The link to go to when the banner is clicked",
                display_name: "Link",
                value: "",
                elements: ["Banner"]
            }
        ]
    },
    News: {
        component: News,
        name: "News",
        component_name: "News",
        tooltip: "The news widget shows a list of news items",
        apply_styles: true,
        list: "Components",
        props:[
            {
                name: "limit",
                type: "number",
                description: "The number of news articles to show",
                display_name: "Limit",
                value: 3,
                elements: ["News"]
            },
            {
                name: "show_images",
                type: "select",
                options: ["true","false"],
                description: "Whether to show images for the news articles",
                display_name: "Show Images",
                value: "true",
                elements: ["News"]
            },
            {
                name: "placeholder_images",
                type: "text",
                description: "A prompt to fetch images from the placeholder service",
                display_name: "Placeholder Image Prompt",
                value: "baseball coach",
                elements: ["News"]
            },
            {
                name: "data",
                type: "json",
                description: "A list of news articles to show if not using Article Type IDs",
                display_name: "News Articles",
                value: [
                    {
                        id: 1,
                        name: "Headline 1",
                        image: "https://siteboss.net/images/news.png",
                        date: "2023-01-01 10:00:00",
                        text: "This is the content of the news article",
                    },
                    {
                        id: 2,
                        name: "Headline 2",
                        image: "https://siteboss.net/images/news.png",
                        date: "2023-01-01 10:00:00",
                        text: "This is the content of the news article",
                    },                    
                ],
                elements: ["News"]
            }
                
        ]
    },    
    ContactForm: {
        component: ContactForm,
        name: "Contact Form",
        component_name: "ContactForm",
        tooltip: "The contact form allows users to send a message to the company",
        apply_styles: true,
        list: "Components",
        props:[
            {
                name: "title",
                type: "text",
                description: "The title to display above the form",
                display_name: "Title",
                value: "Contact Us",
                elements: ["ContactForm"]
            },
            {
                name: "subtitle",
                type: "text",
                description: "The subtitle to display above the form",
                display_name: "Subtitle",
                value: "Send us a message",
                elements: ["ContactForm"]
            },
            {
                name: "send_to",
                type: "json",
                description: "A list of email addresses to send the message to, for example: [name@email] or: [name1@email, name2@email]",
                display_name: "Send To",
                value: "[<EMAIL>, <EMAIL>]",
                elements: ["ContactForm"]
            },
            {
                name: "fields",
                type: "json",
                description: "A list of fields to show in the form, for example: [name:required, subject:required, message]",
                display_name: "Fields",
                value: ["first_name:required", "last_name:required", "email:required", "phone", "message:required"],
                elements: ["ContactForm"]
            },
        ]
    },
    Contact: {
        component: Contact,
        name: "Contact Page",
        component_name: "Contact",
        tooltip: "The contact page shows contact information for the company and allows users to send a message to the company",
        apply_styles: true,
        list: "Components",
        props:[
            {
                name: "title",
                type: "text",
                description: "The title to display above the form",
                display_name: "Title",
                value: "Contact Us",
                elements: ["Contact"]
            },
            {
                name: "subtitle",
                type: "text",
                description: "The subtitle to display above the form",
                display_name: "Subtitle",
                value: "Send us a message",
                elements: ["Contact"]
            },
            {
                name: "send_to",
                type: "json",
                description: "A list of email addresses to send the message to, for example: [name@email] or: [name1@email, name2@email]",
                display_name: "Send To",
                value: "[<EMAIL>, <EMAIL>]",
                elements: ["Contact"]
            },
            {
                name: "fields",
                type: "json",
                description: "A list of fields to show in the form, for example: [name:required, subject:required, message]",
                display_name: "Fields",
                value: ["first_name:required", "last_name:required", "email:required", "phone", "message:required"],
                elements: ["Contact"]
            },
            {
                name: "map_type",
                type: "select",
                options: ["google"],
                description: "The type of map to show",
                display_name: "Map Type",
                value: "google",
                elements: ["Contact"]
            },
            {
                name: "api_key",
                type: "text",
                description: "The API key to use for the map",
                display_name: "Map API Key",
                value: "xxxxx",
                elements: ["Contact"]
            },
            {
                name: "lat",
                type: "number",
                description: "The latitude of the location to show on the map",
                display_name: "Latitude",
                value: 42.266757,
                elements: ["Contact"]
            },
            {
                name: "lng",
                type: "number",
                description: "The longitude of the location to show on the map",
                display_name: "Longitude",
                value: -72.66898,
                elements: ["Contact"]
            },
            {
                name: "zoom",
                type: "number",
                description: "The zoom level of the map",
                display_name: "Zoom",
                value: 13,
                elements: ["Contact"]
            },
        ]
    },
    GroupPage: {
        component: GroupPage,
        name: "Groups Page",
        component_name: "GroupPage",
        tooltip: "The groups page shows members that are in groups like teams",
        apply_styles: true,
        list: "Components",
        props:[
            {
                name: "group_id",
                type: "number",
                description: "The id of the group to show",
                display_name: "Group ID",
                value: "",
                elements: ["GroupPage"]
            },
            {
                name: "data",
                type: "json",
                description: "The event to show if not using Event ID",
                display_name: "Event",
                value: [
                    {
                        id: 1,
                        name: "Average Joe Dodgeball Team",
                        description: "Dodge, Duck, Dip, Dive, and Dodge.",
                        group_members:[
                            {
                                first_name: "Peter",
                                last_name: "LaFleur",
                                group_member_role_name: "Captain",
                            },
                            {
                                first_name: "Kate",
                                last_name: "Veatch",
                                group_member_role_name: "Member",
                            },
                            {
                                first_name: "Steve",
                                last_name: "The Pirate",
                                group_member_role_name: "Member",
                            },
                        ]
                    },
                ],
                elements: ["GroupPage"]
            }
        ]
    },
    ComingSoon: {
        component: ComingSoon,
        name: "Coming Soon",
        component_name: "ComingSoon",
        tooltip: "The coming soon page shows a message that the page is coming soon",
        apply_styles: true,
        list: "Components",
        props:[
            {
                name: "title",
                type: "text",
                description: "The title to display on the page",
                display_name: "Title",
                value: "This page is coming soon",
                elements: ["ComingSoon"]
            },
            {
                name: "subtitle",
                type: "text",
                description: "The subtitle to display on the page",
                display_name: "Subtitle",
                value: "Check back soon",
                elements: ["ComingSoon"]
            },
            {
                name: "image",
                type: "image",
                description: "The image to show on the page",
                display_name: "Image",
                value: "https://www.siteboss.net/images/coming-soon.png",
                elements: ["ComingSoon"]
            },
        ]
    },

}