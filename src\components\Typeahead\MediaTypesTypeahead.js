import React, {useCallback } from 'react'
//import API from '../../api/something';
import { Typeahead } from './Typeahead'

const MEDIA_TYPES = [
    {id: 1, name: "Images"},
    {id: 4, name: "Videos"},
    {id: 5, name: "Documents"},
    {id: 7, name: "Audio"},
    {id: 8, name: "Other"}
]

const MediaTypesTypeahead = (props) => {
    
    const getMediaTypes=useCallback(async()=>{
        let responseObj={
            data: null,
            errors: null
        };
        // try{
        //     let response = await API.something.get();
        //     responseObj.data = response.data || null;
        //     responseObj.errors = response.errors || null;
        // }catch(ex){
        //     responseObj.errors = ex;
        // }
        responseObj = {
            data: MEDIA_TYPES
        }
        return responseObj
    },[])

    return (
        <div>
            <Typeahead
                {...props}
                id="media-types-typeahead"
                makeRequest={getMediaTypes}
                async={props.async}
                multiple={props.multiple}
                paginated={props.paginated}
                initialDataIds={props.initialDataIds}
            />
        </div>
    )
}

export default MediaTypesTypeahead