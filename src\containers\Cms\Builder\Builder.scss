@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@400;500;600;900&family=Roboto:wght@400;500;700&display=swap');
@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';

$primary: #662d91;
$secondary: #f57f17;
$default: $primary-font-color;
$color: var(--color);
$toolbar-width: 425px;

.x-primary{
    --color: #{$primary};
}
.x-secondary{
    --color: #{$secondary};
}
.x-default{
    --color: #{$default};
} 

.cms-builder-loading{
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100%;
    z-index: 99999999999;
    background-color:rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem;

    .indeterminate {
        position: relative;
        margin-right: $toolbar-width;
        .progress-bar{
            position: relative;
            animation: progress-indeterminate 3s ease-in-out infinite;
        }
    }
      
      @keyframes progress-indeterminate {
         from { left: -15%; width: 15%; }
         to { left: 100%; width: 15%;}
      }
}

.cms-preview-modal{
    > .modal-dialog{
        max-width: 100vw !important;
        max-height: 100vh !important;

        > .modal-content{
            height: 95vh;
            width: 95vw;
            margin: auto;
            border: 5px solid #000 !important;

            > .modal-body{
                font-family: $primary-font-family !important;
                font-size: $primary-font-size !important;
                color: $primary-font-color !important;
                background-color: $background-color !important;
                padding: 0 !important;
                margin: 0 !important;
            }
        }
    }
}

.cms-builder-container {
    /*font-family: 'Roboto', sans-serif;*/
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100%;
    z-index: 1040;
    margin:0;
    padding:0;
    background: #fff;
    box-sizing: border-box;
    overflow: hidden;
    scrollbar-width: thin !important;
    scrollbar-color: #9e9e9e #e5e5e5 !important;

    $toolbar-width: $toolbar-width;
    $acoordion-content-height: calc(100vh - 226px); // 37 per item

    ::-webkit-scrollbar {
        width: 10px;
    }
    
    ::-webkit-scrollbar-track {
        background: #e5e5e5;
    }
    
    ::-webkit-scrollbar-thumb {
        background: #9e9e9e;
        border-radius: 0;
    }

    .layout-structure-svg{
        cursor: pointer;
        z-index:9999;
        rect {
            fill: #ddd;
        }

        &:hover{
            rect {
                fill: #9e9e9e;    
            }
        }
    }

    div.designer-area{
        display: flex;
        flex-direction: column;
        height: 100vh;
        width: 100%;

        .cover-area{
            width: 100%;

            &.editor{
                overflow-y: auto;
            }
    
        }
    }

    .toolbar-area{
        padding:0;
        width: $toolbar-width;

        .nav-pills{
            align-items: center;
            justify-content: center;
            border-radius: 0;

            .nav-link{
                color: #000;
                text-decoration: none;
                border-radius: 0;
            }
            
            .nav-link.active, 
            .show>.nav-link {
                color: #fff;
                background-color: #662d91;
                border-radius: 0;
            }
    
        }


        .tab-pane{
            background-color: transparent;
            font-size: 0.85rem;
            padding:0;
            height: calc(100vh - 40px);
            overflow-x: hidden;
            overflow-y: auto;

            &.active{
                display: flex !important;
                flex-direction: column;
            }

            .props{
                margin-top:2.2rem;
                display: flex;
                flex-direction: column;
                flex-grow: 1;
            }

            .accordion{
                height: calc(100vh - 2rem);
                display: flex;
                flex-flow: column nowrap;

                > div{
                    display: flex;
                    flex-direction: column;
                }
                .card-header{
                    background-color: #e7e7e7; 
                    font-family: 'Outfit', sans-serif;
                }
            }

            .nodata{
                display: block;
                margin-top: 1rem;
                padding:1rem;
                font-weight: 400;
            }

            > div.title{
                position:fixed;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                background-color: #e7e7e7;
                padding:0.25rem;
                width:$toolbar-width;
                height:fit-content;
                box-sizing: border-box;
                z-index: 9999;

                span{
                    text-align: center;
                    width: 100%;
                    text-transform: uppercase;
                    font-weight: 900;
                }

                a{
                    color: #000;
                    text-decoration: none;
                    justify-self: flex-end;
                    margin-left:auto;
                    font-size: 0.85rem;
                    padding: 0.15rem 0.35rem
                }
            }

            > div:not(.accordion,.title){
                display: flex;
                flex-direction: column;
                background-color: #fff;
                padding:1rem 0.5rem;
                width:100%;
                height:100%;
            }

            .treeview{
                font-family: 'Roboto', sans-serif;
                padding: 1rem 0.5rem;
                flex-grow: 1;
                width: 100%;
                font-weight: 400;
                background-color: #fff;
                height: 100%;
                            
                .MuiTreeItem-root.Mui-selected > .MuiTreeItem-content .MuiTreeItem-label{
                    background-color: rgba(0,0,0,.05);
                }

                * {
                    font-size: 0.85rem;
                }

                .far{
                    color: #414141;
                }
            }
        }
    }

    .frame-container{
        border:0;
        display: flex; 
        flex-direction: column;
        padding: 0; 
        margin: auto; 
        overflow: hidden;
        justify-content: center;
        align-items: center;
    }

    div.designer{
        display: flex;
        flex-direction: column;
        height: 100vh;
        width: 100%;
        margin:auto;
        overflow-y: auto;
        overflow-x: hidden;
        box-sizing: border-box;

        &.mobile{
            width:576px
        }

        &.mobile-horizontal{
            width:992px;
            height: 576px;
        }

        &.show-grid{
        background-size: 32px 32px;
        background-position: center 0;
        background-image:
          linear-gradient(to right, #eee 1px, transparent 1px),
          linear-gradient(to bottom, #eee 1px, transparent 1px);
        }

        &.designer-can-drop,
        &.designer-ready-drop{
            background-color: inherit;
        }
            
        &.designer-ready-drop{
            &:before {
                content: "";
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: 2px dashed $primary-font-color;
                /*z-index: 99999;*/
            }
        }

        .step-label{
            display:block;
            color: #ddd;
            background-color: rgba(0,0,0,0.5);
            text-transform: uppercase;
            font-size: 0.75rem;
            font-weight: 900;
            padding: 0.25rem 0.5rem;
            margin-top: 1rem;
        }

        .preview-element-container{
            display:flex;
            flex-direction:column;

        }

        .active-element:not(.xray-element):hover{
            &:before {
                content: "";
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: 2px solid $color;
            }            
        }

        span.preview-element-x{
            z-index:999998;
        }

        .preview-element,
        .preview-element-x{
            position:relative;
            //border:1px dashed #eee;
            //padding:0.5rem;
            transition: all 0.15s ease-in-out;
            z-index: 999;

            &.column-x{
                min-height: 100px;
            }

            .text-element{
                box-sizing: content-box;
            }

            .remove-btn{
                position: absolute;
                right:0;
                top:0;
                display: none;

                /*
                &:after{
                    font-family: 'Font Awesome 5 Pro';
                    font-weight: 300;
                    font-size: 12px;
                    content: '\f00d';
                    position:absolute;
                    height:16px;
                    width:16px;
                    border: 1px dashed;
                    text-align: center;
                }
                */
            }

            &:hover {
                > .element-overlay{
                    display: flex;
                }
                /*
                
                &:not(.active-element){
                    position:relative;
                    //border:1px dashed #fff;
                    //padding:5px;
                    //transform:scale(0.985);

                    &:before {
                        content: "";
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        border: 2px dashed $primary-font-color;
                    }

                    .remove-btn{
                        display: block;
                    }
                }
                */
            }
        }

        .preview-element-x:not(:has(.preview-element-x:hover)):hover{
            > .element-overlay{
                display: flex;
                position: relative;
                z-index: 99999;
            }
            
            &:not(.active-element){
                position:relative;
                //border:1px dashed #fff;
                //padding:5px;
                //transform:scale(0.985);
                cursor: move;

                &:not(#div-dustbin):before {
                    content: "";
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    border: 2px solid $color;
                }

                .remove-btn{
                    display: block;
                }
            }
        }


        .dustbin{
            /*min-width:100vh;*/
            padding-bottom: 5rem;
            flex-shrink: 0;
            flex-grow: 1;
        }

        .empty-dustbin{
            position:relative;
            min-height: 100px;
            /*width: 100%;*/
            border: 2px dashed $primary-font-color;
            /*flex-grow: unset !important;*/

            &:after{
                content:"";
                position:absolute;
                top:0;
                left:0;
                width:100%;
                height:100%;
                mask-image: url(../../../assets/images/plus.svg);
                mask-size: 16px;
                mask-position: center center;
                mask-repeat: no-repeat;
                background-color: $primary-font-color;
                opacity: 0.5;
            }

            &.preview-element-x:hover{
                border-color: transparent;
            }
        }

        /*
        .empty-dustbin:first-child{
            flex-grow: 1 !important;
        }
        */

        .empty-contentblock{
            position:relative;
            min-height: 200px !important;
            width: 100%;
            border: 3px dashed $primary-font-color;
            background: url(../../../assets/images/empty.svg) no-repeat bottom center;
            background-size: contain;
            flex-grow: unset !important;
        }
        
        .preview-element.absolute{
            position:absolute;
        }

        img.preview-element{
            width:100px;
            height:100px;
        }

        div.preview-element{
            display:flex;
            flex-direction: column;
            flex-grow:1;
            width:100%;
            min-height:fit-content;
            height:fit-content;
            box-sizing: border-box;
        }

        .xray-element{
            display:block;
            border: 2px dashed $primary-font-color;
            padding: 0.5rem 1rem;
            margin: 0.5rem 0;
            background-color: rgba(#fff, 0.1);
            cursor: move;
            /**/
            font-size: 0.85rem;
            font-weight: 500;
            line-height: 1rem;
            color: #000;
            text-transform: uppercase;
            /**/
            transition: background-color 0.15s ease-in-out;

            &.type-row,
            &.type-container{
                margin: 0.5rem;
            }

            .active-element{
                border-color: #662d91;
            }

            &:hover{
                background-color: rgba(#fff, 0.25);
            }
        }

        .can-drop-hover{
            background-color: rgba(#662d91, 0.25) !important;
        }

        .can-drop{
            background-color: rgba(#662d91, 0.25) !important;
        }

        .cannot-drop{
            background-color: rgba(#f44336, 0.25) !important;
        }

        .is-dragging{
            opacity: 0;
        }

        .dragging-down{
            margin-bottom: 35px !important;
        }

        .dragging-up{
            margin-top: 35px !important;
        }
    }

    div.cms-builder-displaybar{
        justify-content: center;
        align-items: center;
        background-color: #E7E7E7;
        width:100%;
        z-index: 9999;
        padding: 0;

        .page-title{
            font-family: 'Outfit', sans-serif;
            font-size: 0.75rem;
            font-weight: 500;
            padding-left:1rem;
        }

        div.dropdown-menu .dropdown-item {
            font-family: 'Roboto', sans-serif;
            background-color: #fff;
            border: 0;
            border-radius: 0;
            padding: 0.75rem 1rem;
            margin: 0;
            font-size: 0.85rem;
            font-weight: 400;
            line-height: 1rem;
            color: #000;
            position:relative;
            z-index: 9999999999;

            &:hover{
                background-color: #662d91;
                color: #fff;
            }
        }        

        a#mobile-dropdown{

            &:after{
                content: '';
                display: none;
            }

            &~div{
                z-index:99999;
            }
        }


        .nav-link{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            height:36px;
            width:36px;
            margin:1px .25rem;
            /*box-shadow: rgba(#000, .2) 0px 3px 1px -2px, rgba(#000, .14) 0px 2px 2px 0px, rgba(#000, .12) 0px 1px 5px 0px;*/
            color: #000;

            &.active,
            &:hover{
                text-decoration: none;
                background-color: #662d91;
                color: #fff;
            }
        }
    }

    div.cms-builder-toolbar{
        font-family: 'Roboto', sans-serif;
        background-color: #eee;
        color: #000;
        min-height: 100vh;
        height: 100%;
        min-width: $toolbar-width;
        max-width: $toolbar-width;
        width: fit-content;
        position:fixed;
        top:0;
        right:0;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 0;
        margin: 0;
        transition: width 0.15s ease-in-out;
        border-radius: 0;
        border:1px solid #eee;
        border-left-color: #bdbdbd;
        z-index: 99999;

        .card{
            border-radius: 0;
            background-color: #fff;
            color: #000;
            border: 0;
            padding: 2rem;
            margin: 0 1rem 1rem 0;
            font-size: 0.85rem;
        }

        div.btn-group-toggle {
            label.btn {
                font-size: 0.65rem;
                color: inherit;
                background-color: transparent;
                flex: unset;
                margin:0;

                &.active{
                    font-weight: 600;
                }
            }

        }

        select{
            border-radius: 0 !important;
        }
        .form-label{
            font-family: 'Outfit', sans-serif;
            font-size: 0.85rem;
            font-weight: 700;
            line-height: 1rem;
            margin: 0.5rem 0;
            color: #000;
        }
        .form-control,
        .custom-select {
            font-family: 'Roboto', sans-serif !important;
            font-size: 0.75rem !important;
            font-weight: 400 !important;
            line-height: 1rem !important;
            color: #000 !important;
            background-color: #fff !important;
            border: 1px solid #eee !important;
            border-radius: 0 !important;
            padding: 0.5rem 0.75rem !important;
        
            &:focus{
                outline: 0;
                box-shadow: 0 0 0 0.2rem rgba(#662d91, 0.25) !important;
            }
        }

        .form-check-label{
            font-family: 'Outfit', sans-serif;
            font-size: 0.85rem;
            line-height: 1rem;
            color: #000;
        }
    
        input.form-range{
            &:focus {
              &::-webkit-slider-thumb { box-shadow: 0 0 0 1px #eee, 0 0 0 0.125rem #000 !important; }
              &::-moz-range-thumb     { box-shadow: 0 0 0 1px #eee, 0 0 0 0.125rem #000 !important; }
            }
          
            &::-webkit-slider-thumb {
                background-color: #662d91 !important;
        
              &:active {
                background-color: #b39ddb !important;
              }
            }
          
            &::-webkit-slider-runnable-track {
              background-color: #eee !important;
            }
          
            &::-moz-range-thumb {
                background-color: #000 !important;
          
              &:active {
                background-color: #b39ddb !important;
              }
            }
          
            &::-moz-range-track {
              background-color: #662d91 !important;
            }
        }
        
        input.form-check-input {
            margin-right: 0.5rem;
        
            &:checked {
                background-color: #662d91 !important;
                border-color: #662d91 !important;
            }
            &:focus{
                border-color: #662d91 !important;
                box-shadow: 0 0 0 0.2rem rgba(#662d91, 0.25) !important;        
            }
        }
        
        .form-switch{
            padding-left:0 !important;
        
            input.form-check-input{
                border-radius: 1rem;
            }
        }

        hr {
            border-top-color: #eee !important;
            opacity: 1 !important;
        }
            
        table{
            margin: 2rem 0;
            background-color: #fff;

            thead,
            th{
                font-family: 'Outfit', sans-serif !important;
                font-size: 0.6rem !important;
                font-weight: 700 !important;
                font-variant: small-caps !important;
                line-height: 1rem !important;
                color: #9e9e9e;
                padding: 0.5rem;
                margin: 0;
                border: 1px solid #fafafa;
                border-radius: 0;
                background-color: #fafafa !important;
                text-align: left;
                text-transform: uppercase;
                box-shadow: 0;
                letter-spacing: 1px;
            }

            tbody tr{
                font-family: 'Roboto', sans-serif;
                font-size: 0.75rem;
                font-weight: 400;
                line-height: 1rem;
                color: #000;
                margin: 0;
                border:0;
                border-bottom: 1px solid #fafafa;
                border-radius: 0;
                background-color: transparent;
                text-align: left;
                box-shadow: 0;
                vertical-align: middle;

                &:nth-child(odd) {
                    background-color: transparent;
                }
                &:hover {
                    background-color: #fafafa;
                }

                td{
                    padding-bottom:0 !important;
                    padding-top:2px !important;
                }

                td:first-child{
                    text-align: left;
                    min-width: 150px;
                }
            }

            a{
                text-decoration: none;
                font-size: 0.85rem;
                font-weight: 400;
                display: block;

                span{
                    font-family: 'Outfit', sans-serif;
                    margin-left:5px;
                    font-size:0.75rem;
                }
            }
        }
        
        .property-group{
            background-color: #fafafa;
            font-family: 'Outfit', sans-serif;
            font-size: 0.8rem;
            font-weight: 700;
            line-height: 1rem;
            color: #000;
            padding: 0.5rem;
            margin: 0;
            border: 1px solid #eee;
            border-radius: 0;
            text-align: left;
            box-shadow: none;
            text-transform: uppercase;
        }



        .variable{
            font-size:0.85rem;
            cursor: pointer;
            border: 1px solid #eee;
            padding: 1rem;
            margin: 0 0.5rem 0.5rem 0;
            width: fit-content;
            display: inline-block;

            &:hover{
                background-color: #512da8;

                code{
                    color: #fff;
                }
            }
        }

        .logic-bin{
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            flex-grow: 1;
            align-items: stretch;
            /*height: $acoordion-content-height;*/

            &.hover{
                background-color: #512da8;
                color:#fff;
            }

            var{
                margin-right:5px;
            }
        }

        .general-props{
            position:absolute;
            bottom: 3rem;
            left:1rem;
            width: calc(100% - 2rem);
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }

        .collapse.show{
            background-color: #fff;
            /*min-height: $acoordion-content-height;*/
            overflow-y: auto;
            overflow-x: hidden;
            flex-grow: 1;
            //height:100%;
        }
        
        > div{
            flex-direction: column;
            flex-wrap: unset;
            width: 100%;
            height: 100%;
            font-family: 'Outfit', sans-serif;
            font-weight: 600;

            .content-block-container{
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                padding:1rem;
                flex-flow:wrap;

                .content-block-container{
                    display: inline-flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    margin-right: 0.5rem;
                    margin-bottom: 0.5rem;
                    width:118px;
                    height:100px;
                    border:1px solid #eee;
                    padding:0.5rem;
                    overflow: hidden;
                    cursor: pointer;
                    text-align: center;
                }

                .content-block-container:hover{
                    background-color: #512da8;
                    color: #fff;

                    .content-block-icon{
                        border-color: #512da8;

                        &:after{
                            background-color: #fff;
                        }
                    }
                }                

                .content-block-icon{
                    position: relative;
                    width: 2rem;
                    height: 2rem;
                    border-radius: 50%;
                    border:2px solid #000;

                    &:hover{
                        border-color:#512da8;
                    }

                    &:after{
                        content: '';
                        position: absolute;
                        background-color: #000;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        mask-repeat: no-repeat;
                        mask-size: 1rem;
                        mask-position: center;
                        mask-image: url(../../../assets/images/cms/default.svg);
                    }
                    &._columns:after{
                        mask-image: url(../../../assets/images/cms/columns.svg);
                    }
                    &._heading:after{
                        mask-image: url(../../../assets/images/cms/heading.svg);
                    }
                    &._img:after,
                    &._image:after{
                        mask-image: url(../../../assets/images/cms/image.svg);
                    }
                    &._header:after{
                        mask-image: url(../../../assets/images/cms/header.svg);
                    }
                    &._footer:after{
                        mask-image: url(../../../assets/images/cms/footer.svg);
                    }
                    &._hero:after{
                        mask-image: url(../../../assets/images/cms/hero.svg);
                    }
                    &._map:after{
                        mask-image: url(../../../assets/images/cms/map2.svg);
                    }
                    &._event-schedule:after{
                        mask-image: url(../../../assets/images/cms/time.svg);
                    }
                    &._upcoming-events:after{
                        mask-image: url(../../../assets/images/cms/calendar.svg);
                    }
                    &._events-view:after,
                    &._event-detail:after{
                        mask-image: url(../../../assets/images/cms/calendar2.svg);
                    }
                    &._news:after{
                        mask-image: url(../../../assets/images/cms/news.svg);
                    }
                    &._coming-soon:after{
                        mask-image: url(../../../assets/images/cms/soon.svg);
                    }
                    &._contact:after,
                    &._contact-form:after{
                        mask-image: url(../../../assets/images/cms/form.svg);
                    }
                    &._banner:after{
                        mask-image: url(../../../assets/images/cms/banner.svg);
                    }
                    &._group-page:after{
                        mask-image: url(../../../assets/images/cms/groups.svg);
                    }
                    &._div:after{
                        mask-image: url(../../../assets/images/cms/div.svg);
                    }
                    &._content-block:after{
                        mask-image: url(../../../assets/images/cms/contentblock.svg);
                    }
                    &._form-block:after{
                        mask-image: url(../../../assets/images/cms/form.svg);
                    }
                    &._hr:after{
                        mask-image: url(../../../assets/images/cms/hr.svg);
                    }
                    &._text:after{
                        mask-image: url(../../../assets/images/cms/text.svg);
                    }
                    &._list:after{
                        mask-image: url(../../../assets/images/cms/list.svg);
                    }
                    &._a:after{
                        mask-image: url(../../../assets/images/cms/link.svg);
                    }
                    &._button:after{
                        mask-image: url(../../../assets/images/cms/button.svg);
                    }
                    &._login:after{
                        mask-image: url(../../../assets/images/cms/login.svg);
                    }
                    &._video:after{
                        mask-image: url(../../../assets/images/cms/video.svg);
                    }
                    &._audio:after{
                        mask-image: url(../../../assets/images/cms/audio.svg);
                    }
                    &._gallery:after{
                        mask-image: url(../../../assets/images/cms/gallery.svg);
                    }
                    &._card:after{
                        mask-image: url(../../../assets/images/cms/card.svg);
                    }
                    &._menu:after{
                        mask-image: url(../../../assets/images/cms/menu.svg);
                    }
                    &._wysiwyg:after{
                        mask-image: url(../../../assets/images/cms/wysiwyg.svg);
                    }
                    &._sign-up:after,
                    &._sign-up-family:after{
                        mask-image: url(../../../assets/images/cms/register.svg);
                    }
                    &._layout:after{
                        mask-image: url(../../../assets/images/cms/layout.svg);
                    }
                    &._logic:after{
                        mask-image: url(../../../assets/images/cms/logic.svg);
                    }
                    &._anchor:after{
                        mask-image: url(../../../assets/images/cms/anchor.svg);
                    }
                    &._cart-icon:after{
                        mask-image: url(../../../assets/images/cms/cart.svg);
                    }
                    &._html:after{
                        mask-image: url(../../../assets/images/cms/html.svg);
                    }
                }
            }

            table{
                margin-top:0;
                font-size: .85rem;
            }
        }

        table.history-table{
            tbody tr{
                td{
                    padding: inherit !important;
                }
            }
        }

    }
}

.cms-modal{
    .modal-content{
        border-radius: 0 !important;
        background-color: transparent !important;
        border: 0 !important;
        max-height: calc(100vh - 72px) !important;

        .modal-header{
            font-family: "Outfit", sans-serif !important;
            font-size: 1rem;
            border: 0 !important;
            background-color: transparent;
            padding: 0;
            margin: 1rem 0;
            max-height: 72px !important;

            button{
                border: 1px solid #212121;;
                border-radius: 50%;
                background-color: #000;
                color: #fff;
                padding: 0.5rem;
                margin: 1rem 0 1rem auto;
                opacity: 1;
                width: 2.5rem;
                height: 2.5rem;
                box-shadow: rgba(#000, .2) 0px 2px 1px -1px, rgba(#000, .14) 0px 1px 1px 0px, rgba(#000, .12) 0px 1px 3px 0px;

                &:hover{
                    background-color: #662d91;
                    color: #fff;
                    opacity: 1 !important;
                }
            }
        }

        .modal-body{
            background-color: #fff;
            border-radius: 0;
            font-family: "Roboto", sans-serif !important;
            font-size: 0.85rem;
            border: 0;
            padding: 2rem;
            line-height: 1rem;
            box-shadow: rgba(#000, .2) 0px 2px 1px -1px, rgba(#000, .14) 0px 1px 1px 0px, rgba(#000, .12) 0px 1px 3px 0px;
            /*overflow-y: auto !important;
            overflow-x: hidden !important;*/
            overflow: visible !important;
        
            .modal-header{
                padding: 0;
                margin: 1rem 0;
        
                * {
                    display:block;
                }
                .modal-title{
                    font-family: "Outfit", sans-serif;
                    font-size: 1rem;
                }
            }
        }
    }


    .modal-editor{
        h3 {
            font-family: "Outfit", sans-serif;
        }

        .code-editor{
            min-height:400px;
        }

    }
}


div.cms-builder-container .cms-builder-toolbar,
div.cms-builder-container div.cms-builder-displaybar,
.cms-modal .modal-editor,
.section {
    button.btn{
        font-family: 'Outfit', sans-serif;
        font-size: 0.8rem;
        position: relative;
        font-weight: 500;
        line-height: 1rem;
        border-radius: 0;
        padding: .5rem 1.5rem !important;
        margin: 0.25rem 0.5rem 0.25rem 0;
        border: 0;
        color: #fff;
        background-color: #662d91;
        text-transform: uppercase;
        text-shadow: 0 0 2px #9e9e9e;
        box-shadow: rgba(#000, .2) 0px 2px 1px -1px, rgba(#000, .14) 0px 1px 1px 0px, rgba(#000, .12) 0px 1px 3px 0px;
        text-decoration: none;
        align-items: center;
        width: fit-content;
        &:hover {
            background-color: #512da8;
            color: #fff;
            text-decoration: none;
        }

        &:first-child{
            margin-left: 0;
        }

        &:disabled,
        .disabled,
        &:disabled:hover {
            color: #000;
            background-color: #bdbdbd;
        }

        &.btn-secondary{
            background-color: #e0e0e0;
            color: #000;
            &:hover {
                background-color: #bdbdbd;
                color: #000;
            }
        }

        &.btn-publish{
            background-color: #388e3c;
            color: #fff;
            &:hover {
                background-color: #558b2f;
                color: #fff;
            }
        }

        &.rounded{
            border-radius: 50% !important;
            width: 2rem;
            height: 2rem;
        }
    }

    a.btn{
        &.rounded{
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50% !important;
            height:24px;
            width:24px;
            font-size: 0.75rem !important;
            padding: 0.5rem !important;
        }
    }
}    


div.cms-builder-container div.cms-builder-displaybar button.btn{
    font-size: 0.75rem;
    color: #000;
    background-color: transparent;
    box-shadow: none;
    padding: .5rem !important;

    &:disabled,
    .disabled,
    &:disabled:hover {
        color: #000;
        background-color: transparent;
    }

    &.btn-primary{
        &:hover {
            background-color: #662d91;
            color: #fff;
        }
    }

    &.btn-publish{
        background-color: transparent;
        color:#000;
        &:hover {
            background-color: #558b2f;
            color: #fff;
        }
    }

}