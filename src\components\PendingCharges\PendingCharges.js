import React, { useEffect, useState } from 'react';
import Button from 'react-bootstrap/Button';
import Table from 'react-bootstrap/Table';
import { useHistory } from 'react-router-dom';

import Users from '../../api/Users';

import './PendingCharges.css';

const PendingCharges = (props) => {
    
    let history = useHistory();

    const [loading,setLoading]=useState(false);
    const [pendingCharges, setPendingCharges] = useState();

    useEffect(() => {
        let mounted = true;
        setLoading(true);

        let user = JSON.parse(localStorage.getItem("user")).profile;

        Users.get({"id": 9}) // change to user.id when done testing
            .then( response => {
                if(mounted) {
                    if(response.data) {
                        let charges = [];
                        let familyCharges = [];
                        if(response.data[0].outstanding_charges.length) charges.push({name: response.data[0].first_name, charges: []});
                        response.data[0].outstanding_charges.forEach( charge => {
                            charges[0].charges.push(charge);
                        })
                        let i = 0;
                        response.data[0].family.forEach( user => {
                            if(user.outstanding_charges.length) {
                                familyCharges.push({name: user.first_name, charges: []});
                                user.outstanding_charges.forEach( charge => {
                                    familyCharges[i]?.charges.push(charge);
                                });
                                i += 1;
                            }
                        })
                        familyCharges.forEach(charge => charges.push(charge));
                        setPendingCharges(charges);

                        setLoading(false);
                    } else {
                        setLoading(false);
                    }
                }
            }).catch( e=> console.error(e));

        //setLoading(false);

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
        }        
    },[]);

    return (
        <div className={`notification-table${loading?" loading":""}`}>
            <Table hover>
                <tbody>
                    {pendingCharges?.length ? pendingCharges?.map( (user, i) => 
                        <tr key={`charge${i}`}>
                            <td>{user.name}:</td>
                            <td>
                                <ul>
                                    {user.charges.map( (charge, j) =>
                                        <li>{charge.product_name}, ${charge.product_variants[0].price}</li>
                                    )}
                                </ul>
                            </td>
                        </tr>
                    ) : loading ? "" : "None"}
                </tbody>
            </Table>
            {!loading && pendingCharges?.length ?
            <div className="paynow-btn-wrapper">
                <Button variant="secondary" onClick={() => history.push("/p/payment")}>Pay Now</Button>
            </div>
            : null}
        </div>
    );
}

export default PendingCharges;