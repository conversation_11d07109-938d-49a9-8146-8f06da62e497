import React,{useState, useEffect, useCallback, useContext} from 'react';
import { Form, Table, OverlayTrigger, Tooltip, ToggleButtonGroup, ToggleButton } from 'react-bootstrap';
import ColorPicker from '../../../../components/common/ColorPicker';

import { FileURLContext } from '../../../../contexts/FileURLContext';

const Gradient = React.forwardRef((props, _) => {
    const {selection, gradient} = props;
    const company_context = useContext(FileURLContext);

    const [colorCount, setColorCount] = useState(2);
    const [type, setType] = useState("linear-gradient");
    const [position, setPosition] = useState("center center");
    const [angle, setAngle] = useState(0);
    const [angleType, setAngleType] = useState("deg");
    const [colors, setColors] = useState([]);

    const clickHandler = useCallback((colors, type, angle, angleType, position, colorCount) => {
        if (colors.length!== +colorCount) {
            let _color = {color: company_context.primaryColor, location: (colors.length+1)*100/(colorCount-1)};
            if (colors.length < +colorCount){
                for (let i=0; i<+colorCount - colors.length; i++){
                    colors.push(_color);
                }
            } else {
                colors = colors.slice(0, +colorCount);
            }
            setColors(colors);
        }
        //if (colors.length === +colorCount){
            let _gradient = `${type.toLowerCase()}(${type==="radial-gradient"?`circle at ${position}`:`${angle}${angleType}`}`;
            colors.forEach((item,i) => {
                _gradient += `, ${item.color} ${item.location || 0}%`;
            });
            _gradient += ")";

            selection(_gradient);
        //}
    },[selection, company_context.primaryColor]);

    const addColorHandler = (e,i,prop) => {
        e.preventDefault();
        const _colors = [...colors];

        if (!_colors?.[i]) _colors[i]={};
        _colors[i]={..._colors[i],...prop};
        setColors(_colors);
        clickHandler(_colors, type, angle, angleType, position, colorCount);
    }

    useEffect(() => {
        if (!colors.length) setColors([{color:company_context.primaryColor,location:0},{color:company_context.secondaryColor,location:100}]);
    }, [company_context.primaryColor, company_context.secondaryColor, colors]);

    useEffect(() => {
        if (gradient?.length){
            let gr = gradient;
            if (Array.isArray(gradient)) gr=gradient[0];
            if (gr){
                const _type = gr.split("(")?.[0];
                if (_type) setType(_type || "linear-gradient");

                const _gradient = gr.trim().split("(")?.[1]?.split(")")?.[0]?.split(",") || null;

                if (_gradient){

                    if (_type === "radial-gradient"){
                        const _position = _gradient[0]?.split("circle at")?.[1]?.trim() || null;
                        if (_position) setPosition(_position || "center center");
                    } else {
                        const _angle = _gradient[0]?.split("deg")?.[0]?.split("grad")?.[0]?.split("rad")?.[0]?.split("turn")?.[0] || null;
                        if (_angle) setAngle(_angle || 0);

                        const _angleType = _gradient[0]?.split(_angle)?.[1] || null;
                        if (_angleType) setAngleType(_angleType || "deg");
                    }

                    const _colors = [];
                    _gradient?.forEach((item,i) => {
                        if (i > 0){
                            const _color = item.trim().split(" ");
                            _colors.push({color:_color[0],location:_color[1].split("%")[0]});
                        }
                    });

                    setColors(_colors);
                    setColorCount(_colors.length);
                }
            }
        }
    }, [gradient]);

    useEffect(() => {
        return () => {
            setColorCount(2);
            setAngle(0);
            setAngleType("deg");
            setColors([]);
        }
    }, []);

    return (
        <Table className="mb-0">
            <tbody>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>The number of colors to show on the gradient</Tooltip>}><span>Colors</span></OverlayTrigger>
                    </td>
                    <td>
                        <Form.Control type="number" placeholder="2" min="2" max="10" value={colorCount} onChange={(e)=>{
                            setColorCount(e.target.value);
                            clickHandler(colors, type, angle, angleType, position, e.target.value);
                        }}/>
                    </td>
                </tr>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>The type of gradient</Tooltip>}><span>Type</span></OverlayTrigger>
                    </td>
                    <td>
                        <Form.Control as="select" custom value={type || "linear-gradient"} onChange={(e)=>{
                            setType(e.target.value);
                            clickHandler(colors, e.target.value, angle, angleType, position, colorCount);
                        }}>
                            <option value="linear-gradient">Linear</option>
                            <option value="radial-gradient">Radial</option>
                        </Form.Control>
                    </td>
                </tr>
                {type === "radial-gradient" &&
                    <tr>
                        <td>
                            <OverlayTrigger placement="bottom" overlay={<Tooltip>Position of the gradient</Tooltip>}><span>Position</span></OverlayTrigger>
                        </td>
                        <td>
                            <Form.Control as="select" custom value={position || "center center"} onChange={(e)=>{
                                setPosition(e.target.value);
                                clickHandler(colors, type, angle, angleType, e.target.value, colorCount);
                            }}>
                                <option value="center center">Center Center</option>
                                <option value="center left">Center Left</option>
                                <option value="center right">Center Right</option>
                                <option value="top center">Top Center</option>
                                <option value="top left">Top Left</option>
                                <option value="top right">Top Right</option>
                                <option value="bottom center">Bottom Center</option>
                                <option value="bottom left">Bottom Left</option>
                                <option value="bottom right">Bottom Right</option>
                            </Form.Control>
                        </td>
                    </tr>
                }
                {type === "linear-gradient" &&
                    <tr>
                        <td>
                            <OverlayTrigger placement="bottom" overlay={<Tooltip>The angle of the gradient</Tooltip>}><span>Angle</span></OverlayTrigger>
                        </td>
                        <td>
                            <ToggleButtonGroup type="radio" size="sm" name="angle" value={angleType} style={{marginLeft:"auto"}} className="w-100 justify-content-end" onChange={(val)=>{
                                setAngleType(val);
                                clickHandler(colors, type, angle, val, position, colorCount);
                            }}>
                                <ToggleButton variant="link" size="sm" value="deg">deg</ToggleButton>
                                <ToggleButton variant="link" size="sm" value="rad">rad</ToggleButton>
                                <ToggleButton variant="link" size="sm" value="turn">turn</ToggleButton>
                            </ToggleButtonGroup>
                            <Form.Control 
                                type="range"
                                className="form-range"
                                step={1}
                                min={0}
                                max={360}
                                value={angle || 0}
                                onChange={(e)=> {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setAngle(e.target.value);
                                    clickHandler(colors, type, e.target.value, angleType, position, colorCount);
                                }}
                            />                            
                        </td>
                    </tr>
                }
            </tbody>
            {colors.map((color,i)=>(
                <tbody key={`gradient-color-wrapper-${i}`}>
                    <tr>
                        <td>
                            <OverlayTrigger placement="bottom" overlay={<Tooltip>Color {i + 1}</Tooltip>}><span>Color #{i + 1}</span></OverlayTrigger>
                        </td>
                        <td>
                            <ColorPicker type="text" placeholder={`Color #${i + 1}`} value={color?.color || ""} onChange={(e,c)=>addColorHandler(e,i,{color: c})} />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <OverlayTrigger placement="bottom" overlay={<Tooltip>Location within the element for the color to appear</Tooltip>}><span>Location</span></OverlayTrigger>
                        </td>
                        <td>
                            <Form.Control 
                                type="range"
                                className="form-range"
                                step={1}
                                min={0}
                                max={100}
                                value={color?.location || i*100/(colorCount-1)}
                                onChange={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    addColorHandler(e,i,{location: e.target.value});
                                }
                            }/>
                        </td>
                    </tr>
                </tbody>
            ))}
        </Table>
    );
});

export default Gradient;