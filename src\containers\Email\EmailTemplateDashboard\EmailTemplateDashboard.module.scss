@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/mixins';

.email-template-dash-wrapper{
    .header-row{
        @include flex-row-space-between;
        button{
            @include basic-button;
        }
    }
}
.create-email-template-wrapper{
    display: flex;
    flex-direction: row;
    @media (max-width: 999px){
        flex-direction: column;
    }
    container: create-email-template-container / inline-size;
    .insert-selection-wrapper{
        min-width: 425px;
        max-width: 600px;
        @include flex-row-space-between;
        flex-wrap: wrap;
        button{
            @include basic-button;
        }
        input[type="color"]{
            margin: $button-margin;
            width: 73px;
        }
        div{
            .addition-group{
                min-width: 200px;
                @include flex-row-space-between;
                margin: 4px;
            }
        }
    }
    .simple-btn{
        button{
            @include basic-button;
            margin: 1rem 0 1rem 0; 
            justify-self: flex-end;
        }
    }
    
    .text-editor-wrapper{
        min-width: 425px;
        max-width: 600px;
        @include basic-flex-column;
        align-items: center;
        button {
            margin-top: 1rem;
        }
        textarea{
            min-width: 400px;
            max-width: 500px;
            min-height: 300px;
            max-height: 500px;
        }
    }
    
    .email-preview-wrapper{
        padding: 0 20px 0 10px;
        width: 100%;
        @include basic-flex-column;
        align-items: center;
        .preview{
            width: 90%;
            border: 1px solid $divider-color;
            min-height: 600px;
            margin-left: auto;
            margin-right: auto;
        }
    }
}

@container create-email-template-container (min-width: 1000px){
    .insert-selection-wrapper{
        min-width: 550px !important;
        max-width: 700px !important;
    }
    .text-editor-wrapper{
        min-width: 550px !important;
        max-width: 700px !important;
    }
}

.create-simple-template-wrapper{
    img{
        max-height: 300px;
    }
    .inputs{
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }
    .input-col{
        @include basic-flex-column;
        align-items: center;
        width: 500px;
        margin-right: 2rem;
        input[type="color"]{
            padding: 0;
            margin-left: 0;
            margin-right: 0;
        }
        input, textarea{
            @include basic-input-select;
            width: 300px;
            margin-left: 0;
            margin-right: 0;
        }
        textarea{
            height: 200px;
        }
        label{
            @include basic-label;
            font-weight: 1000;
            margin-top: 2rem;
            font-size: 1.25rem;
        }
        p{
            text-align: center;
        }
    }
    .preview{
        width: 100%;
        height: 800px;
        iframe{
            width: 100% !important;
            height: 100% !important;
        }
    }
    .active{
        border: 3px solid $tertiary-color;
    }
}