/*eslint-disable*/


let baseURL = Cypress.env('qa_api_url');
let SBUser = Cypress.env('impact_sb_user');
let adminUser = Cypress.env('impact_admin_user');
let staffUser = Cypress.env('impact_staff_user');
let patronUser = Cypress.env('impact_patron_user');
let password = Cypress.env('login_password');
let otherGroup = 1281;

let header;

const createGroup=(params)=>{
    cy.request({
        method: "POST",
        url: `${baseURL}/group/create`,
        headers: header,
        body: params
    }).then((response)=>{
        cy.log(response)
        cy.wrap(response.body.data[0].id).as("tempId");
    })
}

//group Permissions (Impact) -
//dashboard (down to non-staff)
//create new (down to non-staff)
//edit groups (down to patron if their own, staff+ all)
//view group page (all)
//accept group invitation (all if self, same family)
//view user's group (down to staff)
//Add member to user's family group (down to staff, own family down to patron)
//leave family group (down to staff, own family down to patron)

//These aspects are being tested here:
// /group/create - POST 
//https://nyspsp.atlassian.net/wiki/spaces/IA/pages/1497825347/group+create
//check creating group with different memember roles
//create with descriptio

// /group/edit - POST
//https://nyspsp.atlassian.net/wiki/spaces/IA/pages/1512046601/group+edit
 //check changing the status of member_roles
//change type id, change description

// /group/{id}
//https://nyspsp.atlassian.net/wiki/spaces/IA/pages/1497989330/group+id

// /group/delete
// https://nyspsp.atlassian.net/wiki/spaces/IA/pages/1512079389/group+delete

describe("It will check how a patron user interacts with groups",()=>{
    let patronTeamId;
    let patronFamilyId;

    it("will login as a patron user",()=>{
        cy.loginApi(patronUser, password)
            .then((response)=>{
                header = {
                    Authorization: response.body.data.token,
                    "Content-Type": "application/json"
                }
            })
    });


    it("will see if a patron can create a regular group",()=>{
        createGroup({
            name: "Cypress Patron Team Group",
            group_type_id: 2,
            group_status_id: 1
        })
        cy.get("@tempId").then((id)=>{
            patronTeamId = id;
        })
    });

    //not sure how this is handled as far as the backend goes.  Only relying on how the permissions are laid out in terms of what it's supposed to do
    // it("will make sure the patron did not make said group",()=>{

    // });

    it("will check the created group",()=>{
        cy.request({
            method: "GET",
            url: `${baseURL}/group/${patronTeamId}`,
            headers: header,
            body: {
                id: patronTeamId
            }
        }).then((resp)=>{
            let response = resp.body.data[0];
            expect(response.id).to.eq(patronTeamId);
            expect(response.company_id).to.eq(2);
            expect(response.group_type_id).to.eq(2);
            expect(response.group_status_id).to.eq(1);
            expect(response.group_members.length).to.eq(1);
        })
    })

    it("will make sure the patron can create a family group",()=>{
        createGroup({
            name: "Cypress Patron Family Group",
            group_type_id: 4,
            group_status_id: 1
        })
        cy.get("@tempId").then((id)=>{
            patronFamilyId = id;
        })
    });

    it("will make sure the patron can see said created family group",()=>{
        cy.request({
            method: "GET",
            url: `${baseURL}/group/${patronFamilyId}`,
            headers: header,
            body: {
                id: patronFamilyId
            }
        }).then((resp)=>{
            let response = resp.body.data[0];
            expect(response.id).to.eq(patronFamilyId);
            expect(response.company_id).to.eq(2);
            expect(response.group_type_id).to.eq(4);
            expect(response.group_status_id).to.eq(1);
            expect(response.group_members.length).to.eq(1);
        });
    });

    it("will make sure a patron cannot edit a family group that isn't their family group",()=>{
        let currentName;
        cy.request({
            method: "GET",
            url: `${baseURL}/group/${otherGroup}`,
            headers: header,
            body: {
                id: otherGroup
            }
        }).then((response)=>{
            cy.log(response)
            currentName = response.body.data[0].name;
        })
        cy.request({
            method: "POST",
            url: `${baseURL}/group/edit`,
            headers: header,
            body:{
                id: otherGroup,
                name: currentName + "s"
            }
        }).then((response)=>{
            expect(response.status).to.eq(202);
            expect(response.body.errors[0]).to.eq("Permission Error: Not authorized to access group");
        })

    });

    it("will make sure they can edit their own family group",()=>{
        let currentName;
        cy.request({
            method: "GET",
            url: `${baseURL}/group/${patronFamilyId}`,
            headers: header,
            body: {
                id: patronFamilyId
            }
        }).then((response)=>{
            cy.log(response.body.data[0].name)
            cy.wrap(response.body.data[0].name).as("currentName");
        })


        cy.get("@currentName").then((resp)=>{
            cy.request({
                method: "POST",
                url: `${baseURL}/group/edit`,
                headers: header,
                body: {
                    id: patronFamilyId,
                    name: resp + "s"
                }
            }).then((response)=>{
                expect(response.status).to.eq(200);
                currentName = resp + "s"
            })
        }) 

        cy.request({
            method: "GET",
            url: `${baseURL}/group/${patronFamilyId}`,
            headers: header,
            body: {
                id: patronFamilyId
            }
        }).then((response)=>{
            expect(response.body.data[0].name).to.eq(currentName);
        })
    })
})