import React, { useState, useEffect, useCallback } from 'react';
import { Row, Col, Card } from 'react-bootstrap';
import CCPayment from '../CCPayment';

import styles from './Onboarding.module.scss';

export const Step4 =  props => {
    const {stepValues} = props;
    
    const [collectJS, setCollectJS] = useState();
    const [summary, setSummary] = useState();
    
    const collectJsHandler = useCallback(response => {
        if (response.isToken && response.token) {
            //saveStepValues({token: response.token});
        }
        if (!collectJS){
            setCollectJS(response);
            //saveStepValues({collectJS: response});
        }
    }, [/*saveStepValues,*/ collectJS]);

    useEffect(() => {
        if (stepValues){
            setSummary(
                <Card className="mb-3">
                    <Row>
                        <Col sm={12} lg={6}>
                            <Row>
                                <Col sm={12}>
                                    <span className="bold">{stepValues?.company?.company_name}</span>
                                    <br/>
                                    https://{stepValues?.company?.subdomain}.siteboss.net
                                </Col>
                                {stepValues?.company?.description &&
                                    <Col sm={12}>
                                        <q className={styles.quote}>
                                            {stepValues?.company?.description}
                                        </q>
                                    </Col>
                                }
                            </Row>
                        </Col>
                        <Col sm={12} lg={6}>
                            <Row>
                                <Col sm={12}>
                                    <span className="bold">{stepValues?.plan?.name}</span>
                                    <br/>
                                    ${stepValues?.plan?.price} / month
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                </Card>
            );
        }
    }, [stepValues]);

    useEffect(() => {
        return () => {
            setCollectJS(null);
            setSummary(null);
        }
    }, []);

    
    return (
        <>
            <div className={styles["step-description"]}>
                <h5>Step 4</h5>
                <h4>Payment</h4>
                <p>Oh and did we tell you have a 15 day trial period that you may cancel at any time? No questions asked.</p>
            </div>
            {summary}
            <CCPayment {...props} forceMobile hideButton callback={collectJsHandler} price={10} />
        </>
    );
}