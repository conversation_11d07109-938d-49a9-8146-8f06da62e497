import React,{useState,useEffect} from 'react';
import {useHistory} from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Row, Col, Nav, NavDropdown, Modal, Button } from 'react-bootstrap';

import {confirm} from '../../../../components/Confirmation';
import SavePublish from '../Toolbar/BasicInfo/SavePublish';
import Toast from '../../../../components/Toast';
import ErrorCatcher from '../../../../components/common/ErrorCatcher';

import APIThemes from '../../../../api/Themes';
import APICms from '../../../../api/Cms';
import { removeCss, resetSASSVars } from '../../../../utils/cms';

import * as actions from '../../../../store/actions';
import Preview from './Preview';

// json of common phone and tablet sizes
const devices = APICms.deviceList() || [];

export const DisplayBar = (props) => {
    const history = useHistory();
    const dispatch = useDispatch();
    const cmsSelector = useSelector(state => state.cms);
    const cmsSelectorElements = useSelector(state => state.cmsElements);

    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [themes, setThemes] = useState([{id:1,name:"Default"}]);
    const [showPreview, setShowPreview] = useState(false);
    const [currentKey, setCurrentKey] = useState(2);

    useEffect(() => {
        const _getThemes = async () => {
            try {
                const res = await APIThemes.get({website_id:cmsSelector.currentWebsite});
                if (res.data) {
                    setThemes(res.data);
                }
            } catch (e) {
                console.error(e);
            }
            
        }
        if (cmsSelector.currentWebsite) _getThemes();

    }, [cmsSelector.currentWebsite]);

    useEffect(() => {
        return () => {
            setSuccess(null);
            setError(null);
            setThemes([]);
            setShowPreview(false);
            setCurrentKey(2);
        }
    }, []);

    const clickHandler = (key, e) => {
        e.preventDefault();

        if (+key !== 4 && +key !== 6 && +key !== 7) {
            if (+currentKey !== +key) setCurrentKey(+key);
            else {
                setCurrentKey(2);
                key = 2;
            }
        }

        if (parseInt(+key) === 1) {
            const device_idx = key.split('.')[1];
            const device = devices[device_idx];
            //dispatch(actions.CMSDisplayMode(`mobile-${device.resX/device.pixelRatio}x${device.resY/device.pixelRatio}`));
            dispatch(actions.CMSDisplayMode(`mobile-${device.width}x${device.height}`));
        
        }

        if (parseInt(+key) === 3) {
            const theme_id = key.split('.')[1];
            const newtheme = themes.filter(a=>a.id===+theme_id);
            dispatch(actions.CMSSetCurrentWebsiteTheme(newtheme[0]?.content?.variables || null));
            dispatch(actions.CMSSetCurrentWebsiteCss(newtheme[0]?.css_ids || []));
        } 
                
        switch(+key){
            case 0:
                dispatch(actions.CMSDisplayMode('expand'));
                break;
            case 2:
                dispatch(actions.CMSDisplayMode('desktop'));
                break;
            case 5:
                dispatch(actions.CMSDisplayMode('xray'));
                break;
            case 7:
                setShowPreview(!showPreview);
                break;
            case 6:
                const _exit = () => {
                    let _list=["preview"];
                    cmsSelectorElements.present.elements.forEach(e => e && _list.push(`css_${e.id}`));
                    removeCss(_list); // removes all css that have been injected
                    resetSASSVars(); // resets all sass variables to the company's theme
                    history.push(`/p/cms${cmsSelector.currentWebsite ? `/${cmsSelector.currentWebsite}` : ''}`);
                }

                confirm("Are you sure you want to leave without saving?", {
                    title: "", 
                    okText: "Save", 
                    thirdText: "Leave", 
                    fourthText: "Save & Publish",
                    cancelText: "Stay", 
                    cancelButtonStyle: "light", 
                    thirdButtonStyle: "light",
                    fourthButtonStyle: "primary"
                }).then(result =>{
                    let res;
                    switch(result){
                        case true: // save
                            res = props.save("save");
                            break;
                        case 4: // save & publish
                            res = props.save("publish");
                            break;
                        case 3: // leave anyway
                            _exit();
                            break;
                        case false: 
                        default:
                            break;
                    }
                    if (res) {
                        res.then(r=>{
                            r.result.then(data => {
                                if (data) {
                                    setSuccess(r.action_text);
                                    _exit();
                                } else {
                                    setError("An error ocurred");
                                }
                            }).catch(e => {
                                console.error(e);
                                setError("An error ocurred");
                            });
                        })
                    }
                }).catch(e => {
                    console.error(e)
                });

                //_exit();
    
                break;
            case 4:
                if (cmsSelector.displayMode.indexOf('mobile') > -1){
                    const newmode = cmsSelector.displayMode.indexOf('-landscape') > -1 ? cmsSelector.displayMode.replace('-landscape','') : cmsSelector.displayMode + '-landscape';
                    dispatch(actions.CMSDisplayMode(newmode));
                }
                break;
            default:
                break;
        }
    }

    return (
        <>
            <Row className="m-0 p-0 cms-builder-displaybar">
                <Col sm="auto" className="m-0 p-0">
                    <span className="page-title">
                        Editing {cmsSelector.currentPageProps?.title} {cmsSelector.currentPageProps?.page_type_name ? ` (${cmsSelector.currentPageProps?.page_type_name})` : ""}
                    </span>
                </Col>
                <Col className="m-0 p-0">
                    <Nav className="cms-builder-displaybar" onSelect={clickHandler} activeKey={currentKey}>
                        <Nav.Item style={{width:"125px"}}/>
                        <Nav.Item>
                            <Nav.Link href="#!" eventKey={6}>
                                <i className="far fa-times"/>
                            </Nav.Link>
                        </Nav.Item>
                        {!cmsSelector.currentPageProps?.config?.is_code &&
                            <>
                                <NavDropdown id="mobile-dropdown" title={<i className="far fa-paint-roller"/>}>
                                    {themes.map((theme,i) => (
                                        <NavDropdown.Item key={`theme-preview-${i}`} eventKey={`3.${theme.id}`}>{theme.name}</NavDropdown.Item>
                                    ))}
                                </NavDropdown>
                                <Nav.Item>
                                    <Nav.Link href="#!" eventKey={5}>
                                        <i className="far fa-x-ray"/>
                                    </Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link href="#!" eventKey={2}>
                                        <i className="far fa-desktop"/>
                                    </Nav.Link>
                                </Nav.Item>
                                <NavDropdown id="mobile-dropdown" title={<i className="far fa-mobile"/>}>
                                    {devices.map((device,i) => (
                                        <NavDropdown.Item key={`device-preview-${i}`} eventKey={`1.${i}`}>{device.name}</NavDropdown.Item>
                                    ))}
                                </NavDropdown>
                                {cmsSelector.displayMode.indexOf('mobile') > -1 &&
                                    <Nav.Item>
                                        <Nav.Link href="#!" eventKey={4}>
                                            <i className="far fa-sync"/>
                                        </Nav.Link>
                                    </Nav.Item>
                                }
                                <Nav.Item>
                                    <Nav.Link href="#!" eventKey={0}>
                                        <i className="far fa-expand"/>
                                    </Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link href="#!" eventKey={7}>
                                        <i className="far fa-eye"/>
                                    </Nav.Link>
                                </Nav.Item>
                            </>
                        }
                    </Nav>
                </Col>
                <Col sm="auto" className="m-0 p-0">
                    <Button variant="primary" className="rounded" style={{marginRight:0}} onClick={props.undo} disabled={cmsSelectorElements.past.length<=0}><i className="far fa-undo"/></Button>
                    <Button variant="primary" className="rounded" onClick={props.redo} disabled={cmsSelectorElements.future.length<=0}><i className="far fa-redo"/></Button>
                    <SavePublish save={props.save} page_type={props.page_type} error={err=>setError(<ErrorCatcher skipSave={true} error={err} />)} success={succ=>setSuccess(<Toast>{succ} successfully!</Toast>)}/>
                    {success}
                    {error}
                </Col>
            </Row>

            <Modal className="cms-preview-modal" show={showPreview} onHide={()=>setShowPreview(false)} centered>
                <Modal.Body>
                    <Preview pageFactor={props.pageFactor} />
                </Modal.Body>
            </Modal>
        </>
    );
}