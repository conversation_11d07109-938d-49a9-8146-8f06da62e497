import React, { useState, useEffect } from 'react';
import { useHistory } from "react-router-dom";
import {Container,Col,Row,Form,InputGroup,Button} from 'react-bootstrap';

import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import Toast from '../../../../components/Toast';
import Stack from '../../../../components/common/Stack';

import APICms from '../../../../api/Cms';

const BasicInfo = (props) => {
    let history = useHistory();

    const [theme, setTheme] = useState({});
    const [themes, setThemes] = useState([]);
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

	useEffect(() => {
        const _getTheme=async (id) => {
            try {
                const res=await APICms.themes.get({id:id || null});
                if (res.data && mounted) {
                    if (id) setTheme(res.data[0]);
                    else setThemes(res.data);
                }
            } catch (e){
                console.error(e);
            }
        }

        let mounted = true;
        if (props.theme) setTheme(props.theme);
        else _getTheme(props.theme_id);

        _getTheme(); // get all themes for the clone dropdown

        return () => {
            mounted = false;
        }

	}, [props.theme_id, props.theme]);


    useEffect(() => {
        return () => {
            setTheme({});
            setThemes([]);
        }
    }, []);

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            if (props.theme_id) formData.append("id", props.theme_id);
            const formDataObj = Object.fromEntries(formData.entries());

            let response;
            if (formDataObj.clone > 0) {
                response=await APICms.themes.copy({theme_id:formDataObj.clone,...formDataObj});
            } else {
                delete formDataObj.clone;
                response=await APICms.themes.create(formDataObj);
            }
                        
            if (!response.errors) {
                setSubmitting(false);
                setValidated(false);
                setSuccess(<Toast>Theme saved successfully!</Toast>);
                history.push(props.referer || "/p/themes/dashboard"); // pushes to profile again to avoid resubmission
            } else { // api returned errors
                setSubmitting(false);
                setError(<ErrorCatcher error={response.errors} />);
            } 
        } else setSubmitting(false);
    };


    return (
        <Container fluid>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Row>
                    <Col sm="12" lg="12">
                        <Form.Group controlId="name">
                            <Form.Label>Theme Name</Form.Label>
                            <Form.Control required type="text" name="name" defaultValue={theme?.name || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12">
                        <Form.Group controlId="description">
                            <Form.Label>Description</Form.Label>
                            <Form.Control type="text" name="description" defaultValue={theme?.description || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="clone">
                            <Form.Label>Clone theme</Form.Label>
                            <Form.Control as="select" custom name="clone">
                                {props.theme_id && <option value=""></option>}
                                {themes && themes.map((theme,i) => (
                                    <option key={`theme-${i}`} value={theme.id}>{theme.name}</option>
                                ))}
                            </Form.Control>
                        </Form.Group>
                    </Col>
                </Row>
                <Row>
                    <Col sm="12" className="mt-4 mb-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={`${submitting?" submitting":""}`}>Save</Button>
                    </Col>
                </Row>
            </Form>
            {error}
        </Container>
    );
}

export default BasicInfo;