import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, Button } from 'react-bootstrap';
import { AsyncTypeahead, Typeahead, Token } from 'react-bootstrap-typeahead';
import { format } from 'date-fns';

import * as actions from '../../store/actions';

import './Coupon.scss';
import Products from '../../api/Products';
import Groups from '../../api/Groups';
import Events from '../../api/Events';


// sub-component for a Typeahead list - currently used for Events, Groups, Products - ones that have a POST with paginated filters function
const AsyncInput = ({ paramItem, onChange=()=>{} }) => {
    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
 
    const [allItems, setAllItems] = useState([]);
    const [isInitialized, setIsInitialized] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        if (coupon.params[paramItem.id].length>0 && Number.isInteger(coupon.params[0])) {
            setIsInitialized(false);
        } else {
            setIsInitialized(true);
        }
    },[coupon.params,paramItem.id]);

    const maxRecords = 25;

    const onSearch = async (query) => {
        if('method' in paramItem && typeof paramItem.method === "function" && paramItem.isPost) {
            // if (!!errors[paramItem.id]) dispatch(actions.setServiceWizardErrors({managers: false}));

            let mounted = true;
            setIsLoading(true);

            let filters = {  
                max_records: maxRecords,
                page_no: 1,
                sort_col: "name",
                sort_direction: "ASC"
            };
            if (paramItem.id==="groups") {
                // groups POST call structures the filters differently
                filters.filters = { search_words:query || null };
            } else {
                filters.search = query || null;
            }
            
            paramItem.method(filters)
            .then(response => {
                if(mounted && response.status===200) {
                    // items that use Products.get returns an object with an array named products, instead of just an array
                    if (response.data.products) {
                        setAllItems( response.data.products );
                    } else if (response.data.events) {
                        setAllItems( response.data.events );
                    } else if (response.data.groups) {
                        setAllItems( response.data.groups );
                    } else {
                        setAllItems( response.data );
                    }
                }
                setIsLoading(false);
            }).catch(e => console.error(e));

            // cancel stuff when component unmounts
            return () => {
                mounted = false;
                setIsLoading(false);
            }
        }
    }

    const formatForLabel = useCallback((option) => {
        let returnString = `${option?.name}`;
        // events
        if (option.start_datetime && option.end_datetime) {
            let startDate = format(new Date(option.start_datetime), "MM/dd/yyyy");
            let endDate = format(new Date(option.end_datetime), "MM/dd/yyyy");
            returnString += ` (${startDate} - ${endDate})`;
        }
        return returnString;
    },[]);

    const renderInput = useCallback(({ inputClassName, inputRef, referenceElementRef, ...props },{ onRemove, selected }) => (
        <>
            <input
                {...props}
                className="form-control"
                ref={input => {
                    referenceElementRef(input);
                    inputRef(input);
                }}
                type="text"
            />
            <div style={{ marginTop: '4px' }} className="tokens-list">
                {coupon.params[paramItem.id].map((option, i) => {
                    return (
                    option &&
                    <Token key={`tkn-${i}`} style={{ marginTop: '2px' }} onRemove={() => {
                        return onRemove(option);
                    }}>
                        {formatForLabel(option)}
                    </Token>
                    )
                })}
            </div>
        </>
    ),[coupon.params, paramItem, formatForLabel]);

    return (
        <>
            <AsyncTypeahead
                isLoading={isLoading && !isInitialized}
                id={paramItem.id}
                labelKey={formatForLabel}
                multiple={true}
                onChange={(e) => onChange(e, paramItem.id) }
                onSearch={onSearch}
                filterBy={() => true}
                minLength={3}
                options={allItems}
                placeholder={`Enter a ${paramItem.name} name...`}
                searchText="Searching..."
                selected={coupon.params[paramItem.id]}
                className="lg-input"
                isInvalid={!!errors[paramItem.id]}
                renderInput={renderInput}
            />
        </>
    )
}


// sub-component for a Typeahead list
const MultiInput = ({ paramItem, onChange=()=>{} }) => {
    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
    const paramMultiInputData = useSelector(state => state.coupon.param_multi_input_data);
    return (
        <>
            <Typeahead
                id={paramItem.id}
                labelKey={option => `${option.name}`}
                multiple
                onChange={(e) => onChange(e, paramItem.id) }
                options={paramMultiInputData[paramItem.id]}
                placeholder={`Enter a ${paramItem.name} name...`}
                selected={coupon.params[paramItem.id]}
                isInvalid={!!errors[paramItem.id]}
            />
        </>
    )
}

// sub-component for a text input
const SingleInput = ({ paramItem, onChange=()=>{} }) => {
    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
    return (
        <Form.Control
            type="text"
            id={paramItem.id}
            name={paramItem.name}
            value={coupon.params[paramItem.id]}
            onChange={onChange}
            isInvalid={!!errors[paramItem.id]}
            className={`sm-input`}
        />
    )
}

const Conditions = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();
    const ref = React.createRef();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
    const selectedConditions = useSelector(state => state.coupon.conditions);
    const paramData = useSelector(state => state.coupon.param_init_data);
    const paramMultiInputData = useSelector(state => state.coupon.param_multi_input_data);

    // const [paramData, setParamData] = useState(params || []);
    const [paramFormInputs, setParamFormInputs] = useState([]);
    const [pagePart, setPagePart] = useState();
    const [pagePartSelect, setPagePartSelect] = useState();
    const [loading, setLoading] = useState(false);
    const [pagePartErrors, setPagePartErrors] = useState();
    const [firstPageLoad, setFirstPageLoad] = useState(true);


    const loadData = useCallback(conditionName => {
        if('method' in paramData[conditionName] && typeof paramData[conditionName].method === "function") {

            let mounted = true;
            setLoading(true);

            if (paramData[conditionName].isPost) {

                let query = { };
                if (conditionName==="groups") {
                    query.filters = { group_id: coupon.params[conditionName] };
                } else {
                    query.id = coupon.params[conditionName];
                }
                paramData[conditionName].method(query) 
                .then(response => {
                    if(mounted) {
                        let data = response.data;
                        // items that use Products.get returns an object with an array named products, instead of just an array
                        if (response.data.products) data = response.data.products;
                        if (response.data.events) data = response.data.events;
                        if (response.data.groups) data = response.data.groups;

                        let param = coupon.params[conditionName];
                        if(param && Array.isArray(param) && Number.isInteger(param[0])) {
                            dispatch(actions.setParams({ [conditionName]: data }));
                        }
                    }
                })
                .catch(e => console.error(e));

            } else {

                // get list of all data from the method listed
                paramData[conditionName].method() 
                .then(response => {
                    if(mounted) {
                        // items that use Products.get returns an object with an array named products, instead of just an array
                        if (response.data.products) {
                            dispatch(actions.setParamMultiInputData( {[conditionName]: response.data.products} ));
                        } else if (response.data.events) {
                            dispatch(actions.setParamMultiInputData( {[conditionName]: response.data.events} ));
                        } else {
                            dispatch(actions.setParamMultiInputData( {[conditionName]: response.data} ));
                        }
                    }
                })
                .catch(e => console.error(e));

            }

            // cancel stuff when component unmounts
            return () => {
                mounted = false;
                setLoading(false);
            }
        }  
    },[coupon.params, dispatch, paramData]);

    const onRemove = useCallback(event => {
        if(event.target.id) { // was throwing an error if user clicked on the icon, puttin this in if statement just in case even though it should be fixed
            let tempParams = coupon.params;
            tempParams[event.target.id] = [];
            dispatch(actions.selectedCoupon({ 'params': tempParams }));
            dispatch(actions.setConditions( selectedConditions.filter(c => c !== event.target.id) ));
        }
    },[coupon.params, dispatch, selectedConditions]);

    const onAdd = useCallback(event => {
        if (event.target.value !== "") {
            if (!selectedConditions.includes(event.target.value)) {
                dispatch(actions.setParams({[event.target.value]: paramData[event.target.value].default}));
                dispatch(actions.setConditions( [...selectedConditions, event.target.value] ));
            }
            loadData(event.target.value);
        }
    },[dispatch, paramData, selectedConditions, loadData]);

    const onChangeMulti = useCallback((event, conditionName) => {
        onChangeInput(conditionName, event);
    },[onChangeInput]);

    const onChangeTextbox = useCallback(event => {
        onChangeInput(event.target.id, event.target.value);
    },[onChangeInput]);

    const onChangeCheckbox = useCallback(event => {
        onChangeInput(event.target.id, event.target.checked ? 1 : 0);
    },[onChangeInput]);    

    useEffect(() => {
        // check for param data - if an array of integers is detected, load the appropriate data for the typeahead
        if(coupon.id && firstPageLoad) {
            let conditions = [];
            Object.keys(coupon.params)
                .filter(conditionName => paramData[conditionName]
                    && coupon.params[conditionName]!==paramData[conditionName].default) // don't include items that are populated with the default value
                .forEach((conditionName) => {
                    if (Array.isArray(coupon.params[conditionName]) && coupon.params[conditionName].length>0) {
                        loadData(conditionName);
                    }
                    // add to selected conditions
                    conditions.push(conditionName);
                });
            dispatch(actions.setConditions(conditions));
            setFirstPageLoad(false);    // removing this and setting useEffect to [] does not work right
        }
    },[coupon, firstPageLoad, paramData, dispatch, loadData]);

    useEffect(() => {
        // after new data is loaded, check each paramData for a default array that is not empty
        Object.keys(coupon.params)
            .forEach(conditionName => {
                // check if this is an array of integers, which needs to be converted to an array of data objects - only for regular typeaheads
                if (Array.isArray(paramMultiInputData[conditionName]) && paramMultiInputData[conditionName].length>0 && !paramMultiInputData[conditionName].isPost) {
                    let param = coupon.params[conditionName];
                    if(param && Array.isArray(param) && Number.isInteger(param[0])) {
                        dispatch(actions.setParams({ [conditionName]: paramMultiInputData[conditionName].filter(item => param.includes(item.id)) }));
                    }
                }
            });
    },[paramMultiInputData, coupon.params, dispatch]);

    useEffect(() => {
        let formInputs = {};
        Object.keys(paramData).forEach(conditionName => {
            if (selectedConditions.includes(conditionName)) {
                
                // create the inputs to display for the screen
                if('method' in paramData[conditionName] && paramData[conditionName].isPost) {
                    // if method exists and isPost is true then make an AsyncTypeahead
                    formInputs = {
                        ...formInputs,
                        [conditionName]: (
                            <AsyncInput paramItem={paramData[conditionName]} onChange={onChangeMulti} />
                        )
                    }
                }

                else if('default' in paramData[conditionName] && Array.isArray(paramData[conditionName].default)) {
                    // if data is an array, the option needs a Typeahead
                    if ((!coupon.params[conditionName])
                            || !paramMultiInputData[conditionName]
                            || (coupon.params[conditionName] && coupon.params[conditionName][0] && Number.isInteger(coupon.params[conditionName][0]))
                        ) {
                        // data hasn't finished loading yet
                        formInputs = {
                            ...formInputs,
                            [conditionName]: (
                                <span>loading...</span>
                            )
                        }
                    } else {
                        formInputs = {
                            ...formInputs,
                            [conditionName]: (
                                <MultiInput paramItem={paramData[conditionName]} onChange={onChangeMulti} />
                            )
                        }
                    }

                } else if ('default' in paramData[conditionName] && paramData[conditionName].default === 0) {
                    // if data is 0 or 1 it needs a checkbox
                    formInputs = {
                        ...formInputs,
                        [conditionName]: (
                            <Form.Check
                                type="checkbox"
                                id={conditionName}
                                checked={coupon.params[conditionName] === 1}
                                onChange={onChangeCheckbox}
                            />
                        )
                    };
                } else {
                    // simple number - textbox
                    formInputs = {
                        ...formInputs,
                        [conditionName]: (
                            <SingleInput paramItem={paramData[conditionName]} onChange={onChangeTextbox} />
                        )
                    };
                }
            }
        });
        setParamFormInputs(formInputs);
    },[paramData, coupon.params, paramMultiInputData, selectedConditions, onChangeMulti, onChangeTextbox, onChangeCheckbox]);

    useEffect(() => {
        // remove items from the list if they are already displayed on the page
        // display the select a condition drop-down
        let conditionsInSelect = Object.keys(paramData).filter((conditionName) => !selectedConditions.includes(conditionName));
        if (conditionsInSelect.length > 0) {
            setPagePartSelect(
                <Row>
                    <Col>
                        <Form.Label>Condition</Form.Label>
                        <Form.Control
                            required
                            as="select"
                            name="condition"
                            onClick={onAdd}
                        >
                            <option key={`select-null`} value='' hidden>Select another condition to add...</option>
                            {conditionsInSelect.map(conditionName => (
                                <option key={`select-${paramData[conditionName].id}`} value={paramData[conditionName].id}>{paramData[conditionName].name}</option>
                            ))}
                        </Form.Control>
                    </Col>
                </Row>
            );
        } else {
            setPagePartSelect(
                <div>No more conditions to add.</div>
            );
        }
    },[paramData, selectedConditions, onAdd]);

    useEffect(() => {
        if(paramData) {
            // display the form inputs
            setPagePart(
                <>
                    {selectedConditions.map(conditionName => (
                        <Row key={`row-${conditionName}`}>
                            <Col className="col-sm-auto">
                                <Button variant="light" onClick={onRemove} id={conditionName}><i className="far fa-times m-0" id={conditionName}></i></Button>                                
                            </Col>
                            <Col>
                                <Form.Label>{paramData[conditionName].name}</Form.Label>
                                {paramFormInputs[conditionName]}
                                <p className="subtitle mt-3">
                                    {paramData[conditionName].description}
                                </p>
                            </Col>
                        </Row>
                    ))}
                </>
            );
        }
    },[paramData, paramFormInputs, selectedConditions, onRemove]);

    useEffect(() => {
        let paramErrors = Object.keys(errors).filter(conditionName => selectedConditions.includes(conditionName));
        setPagePartErrors(
            <div className={`err ${paramErrors.length>0 ? "" : "hidden"}`}>
                {paramErrors.map(conditionName => (
                    <div key={`error-${conditionName}`}>{errors[conditionName]}</div>
                ))}
            </div>
        );
    },[errors, selectedConditions]);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">What are the conditions to be met?</span>
                <p className="subtitle">
                    Please note that these conditions are cumulative: if you select multiple conditions they must ALL be true in order for the discount to apply.
                </p>
                {pagePart}
                {pagePartSelect}
                {pagePartErrors}
            </Col>
        </Row>
    );
}

export default Conditions;