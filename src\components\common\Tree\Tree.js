import React, { useState, useEffect } from 'react';
import { Button } from 'react-bootstrap'
import TreeView from '@material-ui/lab/TreeView';
import MuiTreeItem from '@material-ui/lab/TreeItem';
import { withStyles } from '@material-ui/styles';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import usePrevious from '../../common/CustomHooks'

import './Tree.scss';

const Tree = (props) => {
    const { data, expandedAtStart=["root"], expanded, selected, handleIconSelect=()=>{}, handleSelect, referer, displayLabel, showRootLabel=true, linkOnClick=true, setExpandedNodeHasChanged, expandedNodeHasChanged } = props;

    const [expandedNodes, setExpandedNodes] = useState(["root"]);
    const [firstLoad, setFirstLoad]=useState(true);
    const oldExpanded = usePrevious(expanded);

    useEffect(()=>{
        if(oldExpanded !== expanded && setExpandedNodeHasChanged){
            setExpandedNodeHasChanged(!expandedNodeHasChanged)
        }
    // if props.expandedNodeHasChanged is included it will create a loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[expandedNodes, oldExpanded])

    useEffect(() => {
        if (expanded && firstLoad) {
            setExpandedNodes(expanded);
            setFirstLoad(false);
        } else if(expandedAtStart && firstLoad) {
            setExpandedNodes(expandedAtStart);
            setFirstLoad(false)
        };
    }, [props, firstLoad, expanded, expandedAtStart]);

    function labelDefault(node) {
        let displayDescription = "none";
        if(node.description) displayDescription = "";
        let name = node.name;
        if (linkOnClick) {
            name = (<a href={`${referer}/${node.id}`}>{node.name}</a>);
        }
        return (
            <div className="node-row">
                <div 
                    data-id={node?.id} 
                    data-name={node?.name} 
                    data-add-on={node?.add_on_only} 
                    data-parent={node?.parent_id} 
                    data-description={node?.description}
                    data-company={node?.company_id}
                >
                    {name}
                </div>
                <div display={displayDescription}>
                    <div className="description">{node?.description || ""}</div>
                </div>
            </div>
        )
    };

    const renderTree = (node) => {
        if (!node || node.length === 0) {
           return null;
        }
        let label = "";
        let renderFunc = displayLabel || labelDefault;
        if (showRootLabel && node.id === "root") label = renderFunc(node);
        else if (node.id !== "root") label = renderFunc(node);
        return (
            <TreeItem 
                key={node.id} 
                nodeId={node.id?.toString()} 
                label={label}
                onIconClick={handleIconSelect}
                onLabelClick={handleSelect}
            >
                {Array.isArray(node.children) ? node.children.map((node) => renderTree(node)) : null}
            </TreeItem>
        );
    };

    const onNodeToggle = (event, nodeIds) => {
        setExpandedNodes(nodeIds);
    };

    return (
        <div className="tree-view-common">
            <TreeView
                defaultCollapseIcon={<ExpandMoreIcon />}
                defaultExpandIcon={<ChevronRightIcon />}
                defaultExpanded={expandedAtStart}
                selected={selected}
                onNodeToggle={onNodeToggle}
            >
                {renderTree(data)}
            </TreeView>
        </div>
    );
};

export default Tree;

const TreeItem = withStyles({
    root: {
        '&.MuiTreeItem-root.Mui-selected > .MuiTreeItem-content .MuiTreeItem-label': {
            backgroundColor: 'transparent',
        }
    },
})(MuiTreeItem);