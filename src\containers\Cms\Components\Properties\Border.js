import React, {useState, useEffect, useCallback} from 'react';
import { Form, Table, OverlayTrigger, Tooltip, ToggleButtonGroup, ToggleButton } from 'react-bootstrap';
import ColorPicker from '../../../../components/common/ColorPicker';
//import Stack from '../../../../components/common/Stack';

const Border = React.forwardRef((props, ref) => {
    const {border, selection} = props;

    const [type, setType] = useState();
    const [color, setColor] = useState();
    const [width, setWidth] = useState(0);
    const [unit, setUnit] = useState("px");
    
    /*const [widthTop, setWidthTop] = useState();
    const [widthRight, setWidthRight] = useState();
    const [widthBottom, setWidthBottom] = useState();
    const [widthLeft, setWidthLeft] = useState();*/

    const clickHandler = useCallback((unit, type, width, color) => {
        selection(`${width || 0}${unit || "px"} ${type || "default"} ${color || ""}`);
    },[selection]);

    useEffect(() => {
        if (border){
            const [width, type, color] = border.split(" ");
            const unit = width.replace(/[0-9]/g, '');
            setWidth(width.replace(unit, ''));
            setUnit(unit);
            setType(type);
            setColor(color);
        }
    }, [border]);

    useEffect(() => {
        return () => {
            setType(null);
            setColor(null);
            setWidth(0);
            setUnit("px");
        }
    }, []);

    return (
        <Table className="mb-0">
            <tbody>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>The type of border to display</Tooltip>}><span>Type</span></OverlayTrigger>
                    </td>
                    <td>
                        <Form.Control as="select" custom value={type || "default"} onChange={(e)=>{
                            setType(e.target.value);
                            clickHandler(unit, e.target.value, width, color);
                        }}>
                            <option value="default"></option>
                            <option value="none">None</option>
                            <option value="dotted">Dotted</option>
                            <option value="dashed">Dashed</option>
                            <option value="solid">Solid</option>
                            <option value="double">Double</option>
                            <option value="groove">Groove</option>
                            <option value="ridge">Ridge</option>
                            <option value="inset">Inset</option>
                            <option value="outset">Outset</option>
                        </Form.Control>
                    </td>
                </tr>
                {type && (type !== "none" && type !== "default") &&
                    <>
                        <tr>
                            <td>
                                <OverlayTrigger placement="bottom" overlay={<Tooltip>Sets the width for each side of the border</Tooltip>}><span>Width</span></OverlayTrigger>
                            </td>
                            <td>
                                <ToggleButtonGroup type="radio" size="sm" name="unit" value={unit} style={{marginLeft:"auto"}} className="w-100 justify-content-end" onChange={(val)=>{
                                    setUnit(val);
                                    clickHandler(val, type, width, color);
                                }}>
                                    <ToggleButton variant="link" size="sm" value="px">px</ToggleButton>
                                    <ToggleButton variant="link" size="sm" value="em">em</ToggleButton>
                                    <ToggleButton variant="link" size="sm" value="rem">rem</ToggleButton>
                                    <ToggleButton variant="link" size="sm" value="%">%</ToggleButton>
                                </ToggleButtonGroup>
                                <Form.Control 
                                    type="range"
                                    className="form-range"
                                    step={1}
                                    min={0}
                                    max={20}
                                    value={width || 0}
                                    onChange={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        setWidth(e.target.value);
                                        clickHandler(unit, type, e.target.value, color);
                                    }
                                }/>
                                {/*
                                <Stack direction="horizontal" gap={0}>
                                    <Form.Group controlId="border-top-width" className="text-center">
                                        <Form.Control type="number" placeholder="0" min="0" max="10" value={widthTop} onChange={(e)=>{
                                            setWidthTop(e.target.value);
                                            
                                        }}/>
                                        <small>TOP</small>
                                    </Form.Group>
                                    <Form.Group controlId="border-right-width" className="text-center">
                                        <Form.Control type="number" placeholder="0" min="0" max="10" value={widthRight} onChange={(e)=>{
                                            setWidthRight(e.target.value);
                                            
                                        }}/>
                                        <small>RIGHT</small>
                                    </Form.Group>
                                    <Form.Group controlId="border-bottom-width" className="text-center">
                                        <Form.Control type="number" placeholder="0" min="0" max="10" value={widthBottom} onChange={(e)=>{
                                            setWidthBottom(e.target.value);
                                            
                                        }}/>
                                        <small>BOTTOM</small>
                                    </Form.Group>
                                    <Form.Group controlId="border-left-width" className="text-center">
                                        <Form.Control type="number" placeholder="0" min="0" max="10" value={widthLeft} onChange={(e)=>{
                                            setWidthLeft(e.target.value);
                                            
                                        }}/>
                                        <small>LEFT</small>
                                    </Form.Group>
                                </Stack>
                                */}
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <OverlayTrigger placement="bottom" overlay={<Tooltip>Color for the border</Tooltip>}><span>Color</span></OverlayTrigger>
                            </td>
                            <td>
                                <ColorPicker ref={ref} type="text" placeholder={`Border Color`} value={color} onChange={(e,c)=>{
                                    setColor(c);
                                    clickHandler(unit, type, width, c);
                                }}/>
                            </td>
                        </tr>
                    </>
                }
            </tbody>
        </Table>
    );
});

export default Border;