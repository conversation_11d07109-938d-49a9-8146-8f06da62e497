import React from 'react';
import {useHistory } from 'react-router-dom';

const Banner = (props) => {
    // this should be in every component, its used to forward the click event to the builder if in preview mode
    let preview_click=null;
    if (props.is_preview && props.onClick){
        preview_click = props.onClick;
    }

    const history = useHistory();
    
    if (!props.style) props.style={};
    props.style.backgroundImage=`url(${props.image})`;
    props.style.backgroundRepeat="no-repeat";
    props.style.backgroundPosition="left center";
    props.style.backgroundSize="100px";
    props.style.height=props.height;

    const clickHandler = () => {
        if (props?.href) {
            history.push(props.href);
        }
    }

    return (
        <div onClick={preview_click || clickHandler} className={`banner_${props.page_id} ${props.className || ""}`} style={props?.style || null}>
            {props.children}
        </div>
    );
}

export default Banner;