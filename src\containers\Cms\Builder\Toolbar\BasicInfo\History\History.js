import React, { useState, useEffect } from 'react';
import {/*useSelector,*/ useDispatch } from 'react-redux';
import { format } from 'date-fns';
import { Table } from 'react-bootstrap';

import APICms from '../../../../../../api/Cms';
import * as actions from '../../../../../../store/actions';
import { emptyPage, formatCMSFromJson } from '../../../../../../utils/cms';

export const History = (props) => {
    const dispatch = useDispatch();
    const [pageHistory, setPageHistory] = useState();

    useEffect(() => {
        const _getPageHistory=async () => {
            try {
                const res = await APICms.pages.history.get({page_id:props.page_id});
                if (res.data) {
                    // sort by created_at desc
                    res.data.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                    setPageHistory(res.data);
                }
            } catch (e){
                console.error(e);
            }
        }
        if (props.page_id) _getPageHistory();
    }, [props.page_id]);

    useEffect(() => {
        return () => {
            setPageHistory(null);
        }
    }, []);


    const clickHandler = async (e, id) => {
        e.preventDefault();

        if (id){
            props.setLoading(true);
            const res = await APICms.pages.history.get({id: id, page_id: props.page_id});
            if (res.data?.[0]) {
                const page = res.data[0].page;
                if (typeof page.content === "string") page.content = JSON.parse(page.content);
                if (typeof res.data[0].content === "string") res.data[0].content = JSON.parse(res.data[0].content);
                let headers = {};
                headers.page_id = page.id;
                headers.css = res.data[0]?.css?.id || 0;
                headers.js = res.data[0]?.js?.id || 0;
                headers.title = res.data[0]?.title;
                headers.slug = res.data[0]?.slug;
                headers.description = res.data[0]?.description;
                headers.keywords = res.data[0]?.keywords;
                headers.themes = page.content?.themes || [];
                headers.page_type_id = page.page?.page_type_id || 2;
                headers.page_type = page.page?.page_type_id || 2;
                headers.page_type_name = page.page?.page_type?.name || "";
                headers.config = page.page?.page_type?.config || {};
                headers.website_id = page.page?.website_id || null;
                headers.restricted_access = page.page?.restricted_access || 0;
                headers.redirect_to = page.page?.redirect_to || "";
                headers.restricted_roles = page.page?.restricted_roles || [];
                headers.restricted_groups = page.page?.restricted_groups || [];
    
                const _data = res.data[0].content?.content || res.data[0].content || [emptyPage(res.data[0].page?.page_type?.config || {})];

                formatCMSFromJson(_data, headers).then(res => {
                    if (!res.length) res=[];
                    localStorage.setItem(`cms_${props.pageFactor}`,JSON.stringify({...headers, elements: res}));
                    dispatch(actions.CMSSetCurrentPageProps(headers));
                    dispatch(actions.CMSReset(res));
                }).finally(() => {
                    props.setLoading(false);
                });
            } else {
                props.setLoading(false);
            }
        }
    }

    if (!props.page_id) return null;
    if (!pageHistory) return null;

    return (
        <Table className="history-table">
            <thead>
                <tr>
                    <th>Version</th>
                    <th>Created</th>
                </tr>
            </thead>
            <tbody>
                {pageHistory.map((history, i) => (
                    <tr key={`history-${i}`} onClick={(e)=>clickHandler(e,history.id)}>
                        <td>{(pageHistory.length-i)}</td>
                        <td>{format(new Date(history.created_at), "M/d/yy h:mm a" )}</td>
                    </tr>
                ))}
            </tbody>
        </Table>
    );
}
