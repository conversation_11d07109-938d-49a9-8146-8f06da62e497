@import '../../assets/css/scss/variables.scss';
@import '../../assets/css/scss/themes.scss';

@media(max-width: 700px) {
    .card-body {
        padding: 0;
    }
}

.coupon-creator {

    // the clear all button for the asynctypeaheads
    .rbt-aux {
        position: absolute;
        top: 6px;
        right: 12px;
    }

    .tokens-list {
        display: flex;
        flex-direction: column;
        padding-left: 1rem;
        padding-right: 1rem;

    }
    
    .form-label.table-mid-header {
        display: block;
        font-size: 1rem;
        font-weight: $bold-font-weight;
        margin-bottom: 0;
        margin-top: 0.5rem;
    }
    .form-row  {
        align-items: center;
    }
    .form-row.stacked  {
        flex-direction: column;
    }
    .form-row.stacked.left  {
        align-items: flex-start;
    }
    .form-row.stacked.float-left  {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
    }

    .button-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin: 1rem 0;
    }

    .form-check.form-radio {
        width:200px;
        height:60px;
    }

    @media(max-width: 700px) {
        .button-row div {
            flex-grow: 1;
            padding: 0 10px;
        }
        .button-row .btn {
            width: 100%;
        }
        .lg-input {
            width: 95%;
        }
    }

    .form-error {
        color: $error-color;
    }

    .card {
        /* makes sure react-datepicker-popper is visible */
        overflow: visible; 
    }

    .date-container {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        width: 200px;
    }
    .date-container .form-check {
        margin-top: 5px;
    }
    .card-body .react-datepicker-popper {
        z-index: 99;
    }
    .stacked.summary {
        align-items: flex-start;
        margin-left: 2rem;
    }
    .summary>div {
        margin: 5px 0;
    }
    .type-answer {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
    .type-answer span {
        margin: 0 10px;
        visibility: hidden;
    }
    .type-answer span.visible {
        margin: 0 10px;
        visibility: visible;
    }

    .err {
        color: $error-color;
        display: flex;
        flex-direction: column;
    }

    .conditionals {
    
        .row {
            margin: 1.5rem 0;
        }

        .form-label {
            margin-top: 12px;
        }
    }

    .horizontal-align {
        display: flex;
        align-items: center;
    }
    .horizontal-align label {
        margin-bottom: 0;
    }
    .btn-remove-condition {
        border-radius: .25rem;
        padding: 2px 6px;
        font-size: 1.0rem;
    }
    .btn-remove-condition i.fas {
        margin: 0;
    }

    .edit-coupon .card-body {
        padding-top: 0;
    }
    .edit-coupon .form-label.question {
        margin-top: 1.5rem;
    }
    .edit-coupon .form-row {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
    }
    .edit-coupon .form-check.form-radio {
        width: 160px;
        height: 40px;
    }
    .param-description {
        color: rgb(110, 110, 110);
        margin-top: 4px;
    }

}