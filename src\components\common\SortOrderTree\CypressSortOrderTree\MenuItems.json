{"data": [{"id": 37, "parent_id": null, "module_id": 37, "company_id": 2, "sort_order": 16, "icon": "far fa-cash-register", "text": "Registers", "is_disabled": 0, "module": {"id": 37, "name": "Registers", "url": null, "description": "", "module_type_id": 4}}, {"id": 40, "parent_id": 37, "module_id": 40, "company_id": 2, "sort_order": 3, "icon": "", "text": "Front Desk (Impact-04)", "is_disabled": 0, "module": {"id": 40, "name": "Front Desk (Impact-04)", "url": "/p/pos/4", "description": "", "module_type_id": 1}}, {"id": 41, "parent_id": 37, "module_id": 41, "company_id": 2, "sort_order": 4, "icon": "", "text": "Courtside Grille (Impact-07)", "is_disabled": 0, "module": {"id": 41, "name": "Courtside Grille (Impact-07)", "url": "/p/pos/5", "description": "", "module_type_id": 1}}, {"id": 50, "parent_id": null, "module_id": 50, "company_id": 2, "sort_order": 24, "icon": "", "text": "Release & Demos", "is_disabled": 0, "module": {"id": 50, "name": "Release & Demos", "url": null, "description": "", "module_type_id": 4}}, {"id": 309, "parent_id": 328, "module_id": 59, "company_id": 2, "sort_order": 3, "icon": "", "text": "All Orders", "is_disabled": 0, "module": {"id": 59, "name": "All Orders", "url": "/p/all-orders", "description": "", "module_type_id": 1}}, {"id": 340, "parent_id": 3, "module_id": 5, "company_id": 2, "sort_order": 2, "icon": null, "text": "Subscriptions Dashboard", "is_disabled": 1, "module": {"id": 5, "name": "Subscriptions Dashboard", "url": "/p/users/subscriptions", "description": "", "module_type_id": 1}}, {"id": 3, "parent_id": null, "module_id": 3, "company_id": null, "sort_order": 11, "icon": "far fa-users", "text": "Users", "is_disabled": 0, "module": {"id": 3, "name": "Users - Folder", "url": null, "description": "", "module_type_id": 4}}, {"id": 415, "parent_id": null, "module_id": 144, "company_id": 2, "sort_order": 8, "icon": "", "text": "Assign Permissions (Group)", "is_disabled": 0, "module": {"id": 144, "name": "Assign <PERSON><PERSON><PERSON> Permissions to Group", "url": "/p/module/assign/group/:group_id", "description": "Assign permissions to users of a Group", "module_type_id": 1}}, {"id": 47, "parent_id": 45, "module_id": 47, "company_id": null, "sort_order": 2, "icon": "", "text": "New Discount", "is_disabled": 0, "module": {"id": 47, "name": "New Discount", "url": "/p/discount/create", "description": "", "module_type_id": 1}}, {"id": 45, "parent_id": null, "module_id": 45, "company_id": null, "sort_order": 15, "icon": "fas fa-percent", "text": "Discount Management", "is_disabled": 0, "module": {"id": 45, "name": "Discount Management", "url": null, "description": "", "module_type_id": 4}}]}