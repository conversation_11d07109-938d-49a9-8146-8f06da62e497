@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';


.myinput-checkbox-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 0 5px;

    input[type="checkbox"] {
        display: none;
    }

    input[type="checkbox"] + span {
        display: flex;
        position: relative;
        top: -1px;
        width: 16px;
        height: 16px;
        margin: -1px 0px 0 0;
        vertical-align: middle;
        background: white left top no-repeat;
        border: $neutral-border;
        cursor: pointer;
    }

    input[type="checkbox"]:checked + span {
        background: $primary-color -19px top no-repeat;
        border: 1px solid $primary-color;
    }

    input[type="checkbox"] + span {
        margin-right: 4px;
    }

    // checkbox wrapper
    span {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    // these are the two icons for checkmark and for partial checkmark
    .checkbox-checked {
        display: none;
        color: $primary-inverse-color;
        font-size: 13px;
    }
    
    .checkbox-partial {
        display: none;
        color: $primary-inverse-color;
        font-size: 10px;
    }
    

    &.checked {
        .checkbox-checked {
            display: inline-block;
        }
    }

    &.checked.partial {

        .checkbox-checked {
            display: none;
        }
    
        .checkbox-partial {
            display: inline-block;
        }
    }

    .checkbox-label {
        //
    }

    &:hover {
        cursor: pointer;
        background-color: $table-row-hover-background-color;
    }

    &.disabled input[type="checkbox"] + span {
        background-color: $disabled-color;
        border: 1px solid $disabled-color;
    }
}
