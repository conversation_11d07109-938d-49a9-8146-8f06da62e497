@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/variables';

.email-text-editor-wrapper{
    input, select{
        @include basic-input-select;
    }
    button{ 
        @include basic-button;
    }
    label{
        @include basic-label;
        font-weight: 800;
    }
    label{
        margin-right: 10px;
        width: 115px;
    }
    .split-div{
        display: flex; 
        flex-direction: column;
        .save-btn-div{
            display: flex;
        }
    }
    .brand-wrapper-div{
        display: flex;
        flex-direction: column;
        
        select{
            width: 150px;
            margin-left: 115px;
        }
    }
    .input-group{
        display: flex;
        flex-direction: row;
        input{
            max-width: 350px;
            width: 350px;
        }
    }
}