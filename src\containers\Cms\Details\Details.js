import React,{useEffect, useState, Suspense} from 'react';
import { useParams,useLocation,Link  } from "react-router-dom";
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import {Container,Row,Col,Card,Breadcrumb,ListGroup} from 'react-bootstrap';

import APICms from '../../../api/Cms';
import SubHeader from '../../../components/common/SubHeader';

import BasicInfo from '../BasicInfo';
import Pages from '../Pages';
import Urls from '../Urls';

import './Details.scss';

const Details = (props) => {    
    const location = useLocation();
    const { id } = useParams();

    const [websiteInfo,setWebsiteInfo]=useState();

	useEffect(() => {

        const _getWebsite = async () => {
            try {
                let res=await APICms.websites.get({id:id});
                if (res.data.length>0 && mounted) {
                    if (Array.isArray(res.data)) res.data=res.data[0];
                    setWebsiteInfo(res.data);
                    setPagePart(
                        <Suspense fallback={             
                            <SkeletonTheme color="#e0e0e0">
                                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                                <Skeleton height={12} count={5} />
                            </SkeletonTheme>
                        }>
                            <BasicInfo website={res.data} website_id={res.data.id} referer={location.pathname} />
                        </Suspense>
                    );
                }
            } catch (e){
                console.error(e);
            }
        }

        let mounted = true;
        _getWebsite();

        return () => {
            mounted = false;
        }
	}, [id, location.pathname]);
    
    const [pagePart,setPagePart]=useState(
        <Suspense fallback={             
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
            </SkeletonTheme>
        }>
            
        </Suspense>
    );

    const loadPagePartHandler= (e) => {
        let component;
        const page_props={website_id:websiteInfo.id,theme_id:websiteInfo.theme_id || 1,referer:location.pathname};
        switch (e.target.hash.substr(1)){
            case "Pages":
                component=<Pages {...page_props} type={1} />;
                break;
            case "ContentBlock":
                component=<Pages {...page_props} type={2} />;
                break;
            case "Article":
                component=<Pages {...page_props} type={3} />;
                break;
            case "BlogPost":
                component=<Pages {...page_props} type={4} />;
                break;
            case "KnowledgeBase":
                component=<Pages {...page_props} type={5} />;
                break;
            case "CSS":
                component=<Pages {...page_props} type={7} />;
                break;
            case "JS":
                component=<Pages {...page_props} type={8} />;
                break;
            case "Template":
                component=<Pages {...page_props} type={9} />;
                break;
            case "Form":
                component=<Pages {...page_props} type={10} />;
                break;
            case "Wizard":
                component=<Pages {...page_props} type={12} />;
                break;
            case "URLs":
                component=<Urls {...page_props} />;
                break;
            case "BasicInfo":
            default:
                component=<BasicInfo website={websiteInfo} {...page_props} />;
                break;
        }
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {component}
            </Suspense>
        );
    }

    if (!websiteInfo) return (
        <Container fluid>
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
                <Skeleton height={30} style={{marginBottom:"1rem",marginTop:"2rem"}} />
                <Skeleton height={12} count={10} />
            </SkeletonTheme>
        </Container>
    );

    return (websiteInfo &&
        <Container fluid>
            <SubHeader items={[
                {linkAs:Link,linkProps:{to:"/p/home"},text:"Home"},
                {linkAs:Link,linkProps:{to:"/p/cms"},text:"CMS Dashboard"},
                {text:websiteInfo.name}
            ]} />
            <Row className="body content-card">
                <Col>
                    <Card>
                        <Row>
                            <Col sm="auto" className="order-1 order-lg-2">
                                <ListGroup className="profileMenu" variant="flush">
                                    <ListGroup.Item action href="#BasicInfo" onClick={loadPagePartHandler}>
                                        <i className="far fa-globe"></i> Edit Website
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#URLs" onClick={loadPagePartHandler}>
                                        <i className="far fa-link"></i> URLs
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#URLs" onClick={loadPagePartHandler}>
                                    <hr/>
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#Template" onClick={loadPagePartHandler}>
                                        <i className="far fa-clone"></i> Templates
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#Pages" onClick={loadPagePartHandler}>
                                        <i className="far fa-browser"></i> Pages
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#ContentBlock" onClick={loadPagePartHandler}>
                                        <i className="far fa-object-group"></i> Content Blocks
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#Form" onClick={loadPagePartHandler}>
                                        <i className="far fa-clipboard-list"></i> Forms
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#Wizard" onClick={loadPagePartHandler}>
                                        <i className="far fa-wand-magic"></i> Wizards
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#CSS" onClick={loadPagePartHandler}>
                                        <i className="far fa-brackets-curly"></i> CSS
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#JS" onClick={loadPagePartHandler}>
                                        <i className="far fa-code"></i> JS
                                    </ListGroup.Item>
                                    <hr/>
                                    <ListGroup.Item action href="#Article" onClick={loadPagePartHandler}>
                                        <i className="far fa-newspaper"></i> Articles
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#BlogPost" onClick={loadPagePartHandler}>
                                        <i className="far fa-newspaper"></i> Blog Posts
                                    </ListGroup.Item>
                                    <ListGroup.Item action href="#KnowledgeBase" onClick={loadPagePartHandler}>
                                        <i className="far fa-lightbulb"></i> Knowledge Base
                                    </ListGroup.Item>
                                </ListGroup>
                            </Col>
                            <Col className="order-2 order-lg-1">
                                {pagePart /*this is where the magic is happening :-O */ }
                            </Col>
                        </Row>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Details;