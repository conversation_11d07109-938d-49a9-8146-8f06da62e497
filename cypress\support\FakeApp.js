import React, { useState, useEffect } from 'react'
import 'bootstrap/dist/css/bootstrap.min.css';
import '../../src/assets/css/all.min.css';
import '../../src/assets/css/scss/pages.scss';

import { FileURLContext } from '../../src/contexts/FileURLContext';
import { injectSASSVars } from '../../src/utils/cms';
// import { injectSASSVars } from '../../src/utils/pos';
// import '../mock_theme.json'

const FakeApp = ({content}) => {

    let variables = require('../fixtures/mock_theme.json').variables;
    const [context, setContext] = useState('')

    useEffect(()=>{
        let {primaryColor,secondaryColor,tertiaryColor,backgroundColor,primaryFontFamily,secondaryFontFamily} = injectSASSVars(variables);
        let link = document.querySelector("link[rel~='icon']");
        if (!link) {
            link = document.createElement('link');
            link.rel = 'icon';
            document.getElementsByTagName('head')[0].appendChild(link);
        }
        link.crossOrigin = 'anonymous';
        setContext({
            get primaryColor(){ return primaryColor.replace(/^"(.*)"$/, '$1')},
            get secondaryColor(){ return secondaryColor.replace(/^"(.*)"$/, '$1')},
            get tertiaryColor(){ return tertiaryColor.replace(/^"(.*)"$/, '$1')},
            get backgroundColor(){ return backgroundColor.replace(/^"(.*)"$/, '$1')},
            get primaryFontFamily(){ return primaryFontFamily.replace(/^"(.*)"$/, '$1')},
            get secondaryFontFamily(){ return secondaryFontFamily.replace(/^"(.*)"$/, '$1')},
        })
    //we don't need eslint warnings for a fake file T.T
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[])

    return (
        <FileURLContext.Provider value={context}>
            <div className="main-content p-5">
                {content}
            </div>
        </FileURLContext.Provider>
    )
}

export default FakeApp