@import '../../../assets/css/scss/themes.scss';

.rdw-option-wrapper {
    border: 0px solid #eee;
    padding: 5px;
    min-width: 25px;
    height: 25px;
    border-radius: $button-border-radius;
    margin: 0 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background-color: transparent;
    color: $primary-font-color;
    text-transform: $button-text-transform;
    text-decoration: none;
  }
  .rdw-option-wrapper:hover {
    background-color: $neutral-hover-background-color;
    color: $neutral-color;
    text-decoration: none;
    box-shadow: none;
  }
  .rdw-option-wrapper:active {
    box-shadow: none;
    border: 1px solid $button-active-background-color;
  }
  .rdw-option-active {
    box-shadow: none;
    border: 1px solid $button-active-background-color;
  }
  .rdw-option-disabled {
    opacity: 0.3;
    cursor: default;
  }
  
  .rdw-dropdown-wrapper {
    height: 30px;
    background: transparent;
    cursor: pointer;
    border: $form-control-border;
    border-radius: 0;
    margin: 0 3px;
  }
  .rdw-dropdown-wrapper:focus {
    outline: none;
  }
  .rdw-dropdown-wrapper:hover {
    background-color: $neutral-hover-background-color;
    color: $neutral-color;
    text-decoration:none;
  }
  .rdw-dropdown-wrapper:active {
    border: 1px solid $button-active-background-color;
  }
  .rdw-dropdown-carettoopen,
  .rdw-dropdown-carettoclose {
    height: 12px;
    width: 12px;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);

    &:after{
        font-family: 'Font Awesome 5 Pro';
        font-weight: 600;
        content: "\f078";
        font-size: 0.75rem;
        color: $primary-font-color;
        position: absolute;
        top: 0;
        right: 0;
        padding: 0 5px;
    }

  }
  .rdw-dropdown-carettoclose {
    &:after{
        content: "\f077";
    }    
  }
  .rdw-dropdown-selectedtext {
    display: flex !important;
    position: relative;
    height: 100%;
    align-items: center;
    padding: 0 5px;

    >span{
        margin-left: unset !important;
        font-family: 'Outfit', sans-serif;
    }
  }

  .rdw-dropdown-optionwrapper {
    z-index: 100;
    position: relative;
    border: 1px solid #eee;
    width: 98%;
    background: white;
    border-radius: 0;
    margin: 0;
    padding: 0;
    max-height: 250px;
    overflow-y: scroll;
  }
  .rdw-dropdown-optionwrapper:hover {
    background-color: #FFFFFF;
  }
  
  .rdw-dropdownoption-default {
    min-height: 25px;
    display: flex;
    align-items: center;
    padding: 0 5px;
  }
  .rdw-dropdownoption-highlighted {
    background-color: $neutral-background-color;
  }
  .rdw-dropdownoption-active {
    background: $primary-hover-color;
  }
  .rdw-dropdownoption-disabled {
    opacity: 0.3;
    cursor: default;
  }
  
  .rdw-inline-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap
  }
  .rdw-inline-dropdown {
    width: 50px;
  }
  .rdw-inline-dropdownoption {
    height: 40px;
    display: flex;
    justify-content: center;
  }
  
  .rdw-block-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap
  }
  .rdw-block-dropdown {
    width: 110px;
    max-height: 25px;
  }
  
  .rdw-fontsize-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap
  }
  .rdw-fontsize-dropdown {
    min-width: 40px;
    max-height: 25px;
  }
  .rdw-fontsize-option {
    display: flex;
    justify-content: center;
  }
  
  .rdw-fontfamily-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap
  }
  .rdw-fontfamily-dropdown {
    width: 115px;
    max-height: 25px;
  }
  .rdw-fontfamily-placeholder {
    white-space: nowrap;
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .rdw-fontfamily-optionwrapper {
    width: 140px;
  }
  
  .rdw-list-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap
  }
  .rdw-list-dropdown {
    width: 50px;
    z-index: 90;
  }
  .rdw-list-dropdownOption {
    height: 40px;
    display: flex;
    justify-content: center;
  }
  
  .rdw-text-align-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap
  }
  .rdw-text-align-dropdown {
    width: 50px;
    z-index: 90;
  }
  .rdw-text-align-dropdownOption {
    height: 40px;
    display: flex;
    justify-content: center;
  }
  .rdw-right-aligned-block {
    text-align: right;
  }
  .rdw-left-aligned-block {
    text-align: left !important;
  }
  .rdw-center-aligned-block {
    text-align: center !important;
  }
  .rdw-justify-aligned-block {
    text-align: justify !important;
  }
  .rdw-right-aligned-block > div {
    display: inline-block;
  }
  .rdw-left-aligned-block > div {
    display: inline-block;
  }
  .rdw-center-aligned-block > div {
    display: inline-block;
  }
  .rdw-justify-aligned-block > div {
    display: inline-block;
  }
  
  .rdw-colorpicker-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    position: relative;
    flex-wrap: wrap
  }
  .rdw-colorpicker-modal {
    position: absolute;
    top: 35px;
    left: 5px;
    display: flex;
    flex-direction: column;
    width: 225px;
    height: 175px;
    border: 1px solid #eee;
    padding: 15px;
    border-radius: 0;
    z-index: 100;
    background: white;
    box-shadow: rgba(#000, .2) 0px 3px 3px -2px, rgba(#000, .14) 0px 3px 4px 0px, rgba(#000, .12) 0px 1px 8px 0px;
  }
  .rdw-colorpicker-modal-header {
    display: flex;
    padding-bottom: 5px;
  }
  .rdw-colorpicker-modal-style-label {
    width: 50%;
    text-align: center;
    cursor: pointer;
    padding: 0 10px 5px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    font-family: 'Outfit', sans-serif;
  }
  .rdw-colorpicker-modal-style-label-active {
    border-bottom: 2px solid #662d91;
  }
  .rdw-colorpicker-modal-options {
    margin: 5px auto;
    display: flex;
    width: 100%;
    height: 100%;
    flex-wrap: wrap;
    overflow: hidden;
    overflow-y: auto;
  }
  .rdw-colorpicker-cube {
    width: 30px;
    height: 30px;
    border: 1px solid #eee;
  }
  .rdw-colorpicker-option {
    margin: 3px;
    padding: 0;
    border: none;
  }
  .rdw-colorpicker-option:active {
    border:1px solid #662d91;
  }
  .rdw-colorpicker-option-active {
    border:1px solid #662d91;
  }
  
  .rdw-link-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    position: relative;
    flex-wrap: wrap
  }
  .rdw-link-dropdown {
    width: 50px;
  }
  .rdw-link-dropdownOption {
    height: 40px;
    display: flex;
    justify-content: center;
  }
  .rdw-link-dropdownPlaceholder {
    margin-left: 8px;
  }
  .rdw-link-modal {
    position: absolute;
    top: 35px;
    left: 5px;
    display: flex;
    flex-direction: column;
    width: 235px;
    border: 1px solid #eee;
    padding: 15px;
    border-radius: 0;
    z-index: 100;
    background: white;
    box-shadow: rgba(#000, .2) 0px 3px 3px -2px, rgba(#000, .14) 0px 3px 4px 0px, rgba(#000, .12) 0px 1px 8px 0px;
  }
  .rdw-link-modal-label {
    font-size: 0.75rem;
  }
  .rdw-link-modal-input {
    margin-top: 5px;
    margin-bottom: 15px;
  }
  .rdw-link-modal-input:focus {
    outline: none;
  }
  .rdw-link-modal-buttonsection {
    margin: 0 auto;
  }
  .rdw-link-modal-target-option {
    margin-bottom: 20px;
  }
  .rdw-link-modal-target-option > span {
    margin-left: 5px;
  }
  .rdw-link-modal-btn {
    margin-left: 10px;
    width: 75px;
    height: 30px;
    border: 1px solid #eee;
    border-radius: 0;
    cursor: pointer;
    background: white;
    text-transform: capitalize;
  }
  .rdw-link-modal-btn:active {
    border:1px solid #662d91;
  }
  .rdw-link-modal-btn:focus {
    outline: none !important;
  }
  .rdw-link-modal-btn:disabled {
    background: #ece9e9;
  }
  .rdw-link-dropdownoption {
    height: 40px;
    display: flex;
    justify-content: center;
  }
  .rdw-history-dropdown {
    width: 50px;
  }
  
  .rdw-embedded-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    position: relative;
    flex-wrap: wrap
  }
  .rdw-embedded-modal {
    position: absolute;
    top: 35px;
    left: 5px;
    display: flex;
    flex-direction: column;
    width: 235px;
    border: 1px solid #eee;
    padding: 15px;
    border-radius: 0;
    z-index: 100;
    background: white;
    justify-content: space-between;
    box-shadow: rgba(#000, .2) 0px 3px 3px -2px, rgba(#000, .14) 0px 3px 4px 0px, rgba(#000, .12) 0px 1px 8px 0px;
  }
  .rdw-embedded-modal-header {
    font-size: 0.75rem;
    display: flex;
  }
  .rdw-embedded-modal-header-option {
    width: 50%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    font-family: 'Outfit', sans-serif;
  }
  .rdw-embedded-modal-header-label {
    width: 95px;
    border: 1px solid #eee;
    margin-top: 5px;
    background: #6EB8D4;
    border-bottom: 2px solid #662d91;    
  }
  .rdw-embedded-modal-link-section {
    display: flex;
    flex-direction: column;
  }
  .rdw-embedded-modal-link-input {
    width: 88%;
    height: 35px;
    margin: 10px 0;
    border: 1px solid #eee;
    border-radius: 0;
    font-size: 0.75rem;
    padding: 0 5px;
  }
  .rdw-embedded-modal-link-input-wrapper {
    display: flex;
    align-items: center;
  }
  .rdw-embedded-modal-link-input:focus {
    outline: none;
  }
  .rdw-embedded-modal-btn-section {
    display: flex;
    justify-content: center;
  }
  .rdw-embedded-modal-btn {
    margin: 0 3px;
    width: 75px;
    height: 30px;
    border: 1px solid #eee;
    border-radius: 0;
    cursor: pointer;
    background: white;
    text-transform: capitalize;
  }
  .rdw-embedded-modal-btn:active {
    border: 1px solid #662d91;
  }
  .rdw-embedded-modal-btn:focus {
    outline: none !important;
  }
  .rdw-embedded-modal-btn:disabled {
    background: #ece9e9;
  }
  .rdw-embedded-modal-size {
    align-items: center;
    display: flex;
    margin: 8px 0;
    justify-content: space-between;
  }
  .rdw-embedded-modal-size-input {
    width: 80%;
    border: 1px solid #eee;
    border-radius: 0;
    font-size: 12px;
  }
  .rdw-embedded-modal-size-input:focus {
    outline: none;
  }
  
  .rdw-emoji-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    position: relative;
    flex-wrap: wrap
  }
  .rdw-emoji-modal {
    overflow: auto;
    position: absolute;
    top: 35px;
    left: 5px;
    display: flex;
    flex-wrap: wrap;
    width: 235px;
    height: 180px;
    border: 1px solid #eee;
    padding: 15px;
    border-radius: 0;
    z-index: 100;
    background: white;
    box-shadow: rgba(#000, .2) 0px 3px 3px -2px, rgba(#000, .14) 0px 3px 4px 0px, rgba(#000, .12) 0px 1px 8px 0px;
  }
  .rdw-emoji-icon {
    margin: 2.5px;
    height: 24px;
    width: 24px;
    cursor: pointer;
    font-size: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .rdw-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
  }
  .rdw-spinner > div {
    width: 12px;
    height: 12px;
    background-color: #333;
  
    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  }
  .rdw-spinner .rdw-bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
  }
  .rdw-spinner .rdw-bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
  }
  @-webkit-keyframes sk-bouncedelay {
    0%, 80%, 100% { -webkit-transform: scale(0) }
    40% { -webkit-transform: scale(1.0) }
  }
  @keyframes sk-bouncedelay {
    0%, 80%, 100% {
      -webkit-transform: scale(0);
      transform: scale(0);
    } 40% {
      -webkit-transform: scale(1.0);
      transform: scale(1.0);
    }
  }
  
  .rdw-image-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    position: relative;
    flex-wrap: wrap
  }
  .rdw-image-modal {
    position: absolute;
    top: 35px;
    left: 5px;
    display: flex;
    flex-direction: column;
    width: 235px;
    border: 1px solid #eee;
    padding: 15px;
    border-radius: 0;
    z-index: 100;
    background: white;
    box-shadow: rgba(#000, .2) 0px 3px 3px -2px, rgba(#000, .14) 0px 3px 4px 0px, rgba(#000, .12) 0px 1px 8px 0px;
  }
  .rdw-image-modal-header {
    font-size: 0.75rem;
    margin: 10px 0;
    display: flex;
  }
  .rdw-image-modal-header-option {
    width: 50%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .rdw-image-modal-header-label {
    width: 80px;
    background: #eee;
    border: 1px solid #eee;
    margin-top: 5px;
  }
  .rdw-image-modal-header-label-highlighted {
    background: #6EB8D4;
    border-bottom: 2px solid #662d91;
  }
  .rdw-image-modal-upload-option {
    width: 100%;
    color: gray;
    cursor: pointer;
    display: flex;
    border: none;
    font-size: 0.75rem;
    align-items: center;
    justify-content: center;
    background-color: #eee;
    outline: 2px dashed gray;
    outline-offset: -10px;
    margin: 10px 0;
    padding: 9px 0;
  }
  .rdw-image-modal-upload-option-highlighted {
    outline: 2px dashed #662d91;
  }
  .rdw-image-modal-upload-option-label {
    cursor: pointer;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
  }
  .rdw-image-modal-upload-option-label span{
    padding: 0 20px;
  }
  .rdw-image-modal-upload-option-image-preview {
    max-width: 100%;
    max-height: 200px;
  }
  .rdw-image-modal-upload-option-input {
      width: 0.1px;
      height: 0.1px;
      opacity: 0;
      overflow: hidden;
      position: absolute;
      z-index: -1;
  }
  .rdw-image-modal-url-section {
    display: flex;
    align-items: center;
  }
  .rdw-image-modal-url-input {
    width: 90%;
    height: 35px;
    margin: 15px 0 12px;
    border: 1px solid #eee;
    border-radius: 0;
    font-size: 0.75rem;
    padding: 0 5px;
  }
  .rdw-image-modal-btn-section {
    margin: 10px auto 0;
  }
  .rdw-image-modal-url-input:focus {
    outline: none;
  }
  .rdw-image-modal-btn {
    margin: 0 5px;
    width: 75px;
    height: 30px;
    border: 1px solid #eee;
    border-radius: 0;
    cursor: pointer;
    background: white;
    text-transform: capitalize;
  }
  .rdw-image-modal-btn:active {
    border: 1px solid #662d91;
  }
  .rdw-image-modal-btn:focus {
    outline: none !important;
  }
  .rdw-image-modal-btn:disabled {
    background: #ece9e9;
  }
  .rdw-image-modal-spinner {
    position: absolute;
    top: -3px;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
  }
  .rdw-image-modal-alt-input {
    width: 70%;
    height: 20px;
    border: 1px solid #eee;
    border-radius: 0;
    font-size: 12px;
    margin-left: 5px;
  }
  .rdw-image-modal-alt-input:focus {
    outline: none;
  }
  .rdw-image-modal-alt-lbl {
    font-size: 12px;
  }
  .rdw-image-modal-size {
    align-items: center;
    display: flex;
    margin: 8px 0;
    justify-content: space-between;
  }
  .rdw-image-modal-size-input {
    width: 40%;
    height: 20px;
    border: 1px solid #eee;
    border-radius: 0;
    font-size: 12px;
  }
  .rdw-image-modal-size-input:focus {
    outline: none;
  }
  .rdw-image-mandatory-sign {
    color: red;
    margin-left: 3px;
    margin-right: 3px;
  }
  
  .rdw-remove-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    position: relative;
    flex-wrap: wrap
  }
  
  .rdw-history-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap
  }
  .rdw-history-dropdownoption {
    height: 40px;
    display: flex;
    justify-content: center;
  }
  .rdw-history-dropdown {
    width: 50px;
  }
  
  .rdw-link-decorator-wrapper {
    position: relative;
  }
  .rdw-link-decorator-icon {
    position: absolute;
    left: 40%;
    top: 0;
    cursor: pointer;
    background-color: white;
  }
  
  .rdw-mention-link {
    text-decoration: none;
    color: #1236ff;
    background-color: #f0fbff;
    padding: 1px 2px;
    border-radius: 0;
  }
  
  .rdw-suggestion-wrapper {
    position: relative;
  }
  .rdw-suggestion-dropdown {
    position: absolute;
    display: flex;
    flex-direction: column;
    border: 1px solid #eee;
    min-width: 100px;
    max-height: 150px;
    overflow: auto;
    background: white;
    z-index: 100;
  }
  .rdw-suggestion-option {
    padding: 7px 5px;
    border-bottom: 1px solid #eee;
  }
  .rdw-suggestion-option-active {
    background-color: #eee;
  }
  
  .rdw-hashtag-link {
    text-decoration: none;
    color: #1236ff;
    background-color: #f0fbff;
    padding: 1px 2px;
    border-radius: 0;
  }
  
  .rdw-image-alignment-options-popup {
    position: absolute;
    background: white;
    display: flex;
    padding: 5px 2px;
    border-radius: 0;
    border: 1px solid #eee;
    width: 105px;
    cursor: pointer;
    z-index: 100;
  }
  .rdw-alignment-option-left {
    justify-content: flex-start;
  }
  .rdw-image-alignment-option {
    height: 15px;
    width: 15px;
    min-width: 15px;
  }
  .rdw-image-alignment {
    position: relative;
  }
  .rdw-image-imagewrapper {
    position: relative;
  }
  .rdw-image-center {
    display: flex;
    justify-content: center;
  }
  .rdw-image-left {
    display: flex;
  }
  .rdw-image-right {
    display: flex;
    justify-content: flex-end;
  }
  .rdw-image-alignment-options-popup-right {
    right: 0;
  }
  
  .rdw-editor-main {
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
  }
  .rdw-editor-toolbar {
    padding: 6px 5px 0;
    border-radius: 0;
    border: 1px solid #eee;
    display: flex;
    justify-content: flex-start;
    background: white;
    flex-wrap: wrap;
    font-size: 0.75rem;
    margin-bottom: -1px;
    user-select: none;
  }
  .public-DraftStyleDefault-block {
    margin: 1em 0;
  }
  .rdw-editor-wrapper:focus {
    outline: none;
  }
  .rdw-editor-wrapper {
    box-sizing: content-box;
  }
  .rdw-editor-main blockquote {
    border-left: 5px solid #eee;
    padding-left: 5px;
  }
  .rdw-editor-main pre {
    background: #eee;
    border-radius: 3px;
    padding: 1px 10px;
  }
  /**
   * Draft v0.9.1
   *
   * Copyright (c) 2013-present, Facebook, Inc.
   * All rights reserved.
   *
   * This source code is licensed under the BSD-style license found in the
   * LICENSE file in the root directory of this source tree. An additional grant
   * of patent rights can be found in the PATENTS file in the same directory.
   */
  .DraftEditor-editorContainer,.DraftEditor-root,.public-DraftEditor-content{height:inherit;text-align:initial}.public-DraftEditor-content[contenteditable=true]{-webkit-user-modify:read-write-plaintext-only}.DraftEditor-root{position:relative}.DraftEditor-editorContainer{background-color:rgba(255,255,255,0);border-left:.1px solid transparent;position:relative;z-index:1}.public-DraftEditor-block{position:relative}.DraftEditor-alignLeft .public-DraftStyleDefault-block{text-align:left}.DraftEditor-alignLeft .public-DraftEditorPlaceholder-root{left:0;text-align:left}.DraftEditor-alignCenter .public-DraftStyleDefault-block{text-align:center}.DraftEditor-alignCenter .public-DraftEditorPlaceholder-root{margin:0 auto;text-align:center;width:100%}.DraftEditor-alignRight .public-DraftStyleDefault-block{text-align:right}.DraftEditor-alignRight .public-DraftEditorPlaceholder-root{right:0;text-align:right}.public-DraftEditorPlaceholder-root{color:#9197a3;position:absolute;z-index:0}.public-DraftEditorPlaceholder-hasFocus{color:#bdc1c9}.DraftEditorPlaceholder-hidden{display:none}.public-DraftStyleDefault-block{position:relative;white-space:pre-wrap}.public-DraftStyleDefault-ltr{direction:ltr;text-align:left}.public-DraftStyleDefault-rtl{direction:rtl;text-align:right}.public-DraftStyleDefault-listLTR{direction:ltr}.public-DraftStyleDefault-listRTL{direction:rtl}.public-DraftStyleDefault-ol,.public-DraftStyleDefault-ul{margin:16px 0;padding:0}.public-DraftStyleDefault-depth0.public-DraftStyleDefault-listLTR{margin-left:1.5em}.public-DraftStyleDefault-depth0.public-DraftStyleDefault-listRTL{margin-right:1.5em}.public-DraftStyleDefault-depth1.public-DraftStyleDefault-listLTR{margin-left:3em}.public-DraftStyleDefault-depth1.public-DraftStyleDefault-listRTL{margin-right:3em}.public-DraftStyleDefault-depth2.public-DraftStyleDefault-listLTR{margin-left:4.5em}.public-DraftStyleDefault-depth2.public-DraftStyleDefault-listRTL{margin-right:4.5em}.public-DraftStyleDefault-depth3.public-DraftStyleDefault-listLTR{margin-left:6em}.public-DraftStyleDefault-depth3.public-DraftStyleDefault-listRTL{margin-right:6em}.public-DraftStyleDefault-depth4.public-DraftStyleDefault-listLTR{margin-left:7.5em}.public-DraftStyleDefault-depth4.public-DraftStyleDefault-listRTL{margin-right:7.5em}.public-DraftStyleDefault-unorderedListItem{list-style-type:square;position:relative}.public-DraftStyleDefault-unorderedListItem.public-DraftStyleDefault-depth0{list-style-type:disc}.public-DraftStyleDefault-unorderedListItem.public-DraftStyleDefault-depth1{list-style-type:circle}.public-DraftStyleDefault-orderedListItem{list-style-type:none;position:relative}.public-DraftStyleDefault-orderedListItem.public-DraftStyleDefault-listLTR:before{left:-36px;position:absolute;text-align:right;width:30px}.public-DraftStyleDefault-orderedListItem.public-DraftStyleDefault-listRTL:before{position:absolute;right:-36px;text-align:left;width:30px}.public-DraftStyleDefault-orderedListItem:before{content:counter(ol0) ". ";counter-increment:ol0}.public-DraftStyleDefault-orderedListItem.public-DraftStyleDefault-depth1:before{content:counter(ol1) ". ";counter-increment:ol1}.public-DraftStyleDefault-orderedListItem.public-DraftStyleDefault-depth2:before{content:counter(ol2) ". ";counter-increment:ol2}.public-DraftStyleDefault-orderedListItem.public-DraftStyleDefault-depth3:before{content:counter(ol3) ". ";counter-increment:ol3}.public-DraftStyleDefault-orderedListItem.public-DraftStyleDefault-depth4:before{content:counter(ol4) ". ";counter-increment:ol4}.public-DraftStyleDefault-depth0.public-DraftStyleDefault-reset{counter-reset:ol0}.public-DraftStyleDefault-depth1.public-DraftStyleDefault-reset{counter-reset:ol1}.public-DraftStyleDefault-depth2.public-DraftStyleDefault-reset{counter-reset:ol2}.public-DraftStyleDefault-depth3.public-DraftStyleDefault-reset{counter-reset:ol3}.public-DraftStyleDefault-depth4.public-DraftStyleDefault-reset{counter-reset:ol4}
  
  
  /*# sourceMappingURL=react-draft-wysiwyg.css.map*/