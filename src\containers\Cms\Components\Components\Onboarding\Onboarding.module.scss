@import '../../../../../assets/css/scss/variables';
@import '../../../../../assets/css/scss/themes';

.wrapper{
    display: flex;
    height: 100vh;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
    transition: all 0.25s ease-in-out;

    .wizard{
        /*display: flex;
        flex-direction: column;
        justify-content: center;*/
        position: relative;
        overflow: hidden;
        overflow-y: auto;
        height: 100vh;
        padding: 3rem;
        padding-bottom: calc(3rem + 60px);
        font-size: 0.85rem;
        transition: all 0.25s ease-in-out;

        .toolbar{
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            bottom:0;
            left:50%;
            width: 50%;
            padding: 1rem;
            background-color: $background-color;
            border-top: $neutral-border;
        }
    
    }

    .quote{
        font-size: 0.75rem;
        font-family: $secondary-font-family;
        font-weight: 400;
        font-style: italic;
        margin: 0;
        margin-top: 0.5rem;
        color: #616161;
        display: block;
    }

    .line{
        background-image: url(../../../../../assets/images/line.svg);
        background-size: contain;
        background-position: 0.5rem center;
        background-repeat: no-repeat;
        width: 100%;
        height: 80px;
        margin: 1rem;
        margin-bottom: 1.5rem;
    }

    .step-description{
        margin-bottom:1rem;

        h4{
            font-size: 1.5rem;
            font-weight: 500;
            font-family: $secondary-font-family;
            width:100%;
        }

        h5{
            font-size: 1rem;
            font-weight: 400;
            font-family: $secondary-font-family;
            
            &::after{
                content: '';
                display: block;
                width: 2rem;
                height: 1px;
                background-color: $primary-font-color;
                margin: 0.5rem 0;
            }            
        }
    }

    .sidebar{
        position:relative;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-start;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        background-color: $primary-color;
        height: 100%;
        position: relative;
        color: #fff;
        padding: 3rem;
        transition: all 0.25s ease-in-out;

        .logo {
            position: fixed;
            top: 2rem;
            left: 2rem;
            width: 120px;
            height: 50px;
            background-image: url("https://siteboss.s3.amazonaws.com/themes/1/logo.svg");
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            filter: grayscale(1) brightness(1000%);
        }


        &.step-1{
            background-image: url("./images/Membership Program Isometric.svg");
        }
        &.step-2{
            background-image: url("./images/Web SEO Isometric.svg");
        }
        &.step-3{
            background-image: url("./images/Brainstorming Ideas Illustration.svg");
        }
        &.step-4{
            background-image: url("./images/Payment Succesful Illustration.svg");
        }
        &.step-5{
            background-image: url("./images/Business Strategy Illustration.svg");
        }
        
        &::after{
            content: '';
            position: absolute;
            top:0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(to top, $secondary-color, $primary-color);
            opacity: 0.75;
        }

        > * {
            position: relative;
            z-index: 9999;
        }


        .steps{
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;            

            li {
                position: relative;
                display:flex;
                flex-direction: row;
                padding: 0.25rem 0;
                align-items: center;

                span{
                    display:flex;
                    justify-content: center;
                    align-items: center;
                    width: 2rem;
                    height: 2rem;
                    border-radius: 50%;
                    border: 2px solid #fff;
                    background-color: transparent;
                    margin-right: 1rem;
                }

                &.active{
                    span {
                        background-color: #fff;
                        color: $primary-color;
                        font-weight: 500;
                    }
                }

            }
        }

    }
}