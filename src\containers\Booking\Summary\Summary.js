import React from 'react';
import { useSelector } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Accordion from 'react-bootstrap/Accordion';
import Card from 'react-bootstrap/Card';
import { format } from 'date-fns';

export const Summary = (props) => {
    const data=useSelector(state => state.map);
    return (
        <React.Fragment>
            <Row>
                <Col sm="12" lg="8">
                    <h1>Ready. Set. Book!</h1>
                    <p>
                        Before you press that button:
                    </p>
                    <ul className="mb-4">
                        <li>Check that your event details are correct.</li>
                        <li>Once submitted, we will remind you before the event starts.</li>
                        <li>You'll be able to cancel the event up to 48hrs before its starting date.</li>
                    </ul>
                </Col>
            </Row>
            <Row>
                <Col sm="12">
                    <Accordion defaultActiveKey="0">
                        <Card className="p-0">
                            <Accordion.Toggle as={Card.Header} eventKey="0">Event Summary</Accordion.Toggle>
                            <Accordion.Collapse eventKey="0">
                                <Card.Body>
                                    {data.event_type &&
                                        <Row>
                                            <Col sm="12">
                                                <label>Event Type</label>
                                                <span>{data.event_type.name}</span>
                                            </Col>
                                        </Row>
                                    }
                                {/* </Card.Body>
                            </Accordion.Collapse>
                            <Accordion.Toggle as={Card.Header} eventKey="1">Locations</Accordion.Toggle>
                            <Accordion.Collapse eventKey="1">
                                <Card.Body> */}
                                    {data.selected_items.map((item,i)=>{
                                        return (
                                            <div className="summary_item" key={`summr-${i}`}>
                                                <span className="bold">{item.name}</span>
                                                <Row>
                                                    <Col className="col-auto">
                                                        <label>From</label>
                                                        <span>{format(new Date(item.booking.start_datetime), "eee, MM/dd/yyyy hh:mm aa")}</span>
                                                    </Col>
                                                    <Col className="col-auto">
                                                        <label>To</label>
                                                        <span>{format(new Date(item.booking.end_datetime), "eee, MM/dd/yyyy hh:mm aa")}</span>
                                                    </Col>
                                                    {item.booking.event_name &&
                                                        <Col sm="12">
                                                            <label>Event Name</label>
                                                            {item.booking.event_name}
                                                        </Col>
                                                    }
                                                    {item.booking.event_description &&
                                                        <Col sm="12">
                                                            <label>Event Description</label>
                                                            <div dangerouslySetInnerHTML={{ __html: item.booking.event_description }} />
                                                        </Col>
                                                    }
                                                </Row>
                                            </div>
                                        );
                                    })}
                                </Card.Body>
                            </Accordion.Collapse>
                            <Accordion.Toggle as={Card.Header} eventKey="1">{data.event_type.group_invites?"Attending Groups":"Attendees"}</Accordion.Toggle>
                            <Accordion.Collapse eventKey="1">
                                <Card.Body>
                                    {data.attendees.map((item,i) => (
                                        data.event_type.group_invites
                                        ? (
                                            <div key={"invitee"+i}>
                                                {item.name}
                                                <label className="mt-0 mb-3">{item.description}</label>
                                            </div>
                                        )
                                        : (
                                            <div key={"invitee"+i}>
                                                {item.first_name+" "+item.last_name}
                                                <label className="mt-0 mb-3">{item.email}</label>
                                            </div>
                                        )
                                    ))}
                                </Card.Body>
                            </Accordion.Collapse>
                        </Card>
                    </Accordion>
                </Col>
            </Row>
        </React.Fragment>
    );
}