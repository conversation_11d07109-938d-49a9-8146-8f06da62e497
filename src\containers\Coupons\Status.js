import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Status = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    function onSwitch(event) {
        let tempEvent = { target:
            {
                id: event.target.id,
                name: event.target.name,
                type: "switch",
                value: event.target.checked
            }
        }
        onChangeInput(tempEvent);
    }

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Coupon is Active?</span>
                <Form.Check
                    type="switch"
                    id="status"
                    name="status"
                    checked={coupon.status === 1}
                    onChange={onChangeInput}
                />
                <div className={`err ${!!errors.status ? "" : "hidden"}`}>
                    {errors.status}
                </div>
            </Col>
        </Row>
    );
}

export default Status;