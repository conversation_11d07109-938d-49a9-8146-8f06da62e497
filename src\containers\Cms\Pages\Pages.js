import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import {Container,Row,Col,ListGroup} from 'react-bootstrap';
import ReactTooltip from 'react-tooltip';
import { useSelector, useDispatch } from 'react-redux';

import {confirm} from '../../../components/Confirmation';
import Table from '../../../components/common/Table';
import APICms from '../../../api/Cms';
import APIThemes from '../../../api/Themes';
import { emptyPage } from '../../../utils/cms';
import * as actions from '../../../store/actions';

import './Pages.scss';

const Pages = (props) => {
	const history = useHistory();
	const dispatch = useDispatch();
	//const cmsSelector = useSelector(state => state.cms);

	const [pages, setPages] = useState();
	const [pageType, setPageType] = useState();
	const [listColumns, setListColumns] = useState([])
		
	const _getPages = useCallback(async () => {
		try {
			const res = await APICms.pages.get({website_id:props.website_id,page_type_id:props.type});
			if (res) {
				setPages(res?.data || []);
			}
		} catch (e) {
			console.error(e);
		}
	},[props.website_id,props.type]);

	useEffect(() => {
		let mounted = true;

		const _getPageTypes=async () => {
            try {
                const res=await APICms.pageTypes.get({id:props.type});
                if (res.data && mounted) {
                    setPageType(res.data[0]);
                }
            } catch (e){
                console.error(e);
            }
        }

		_getPages();
		_getPageTypes();

		return () => {
			mounted = false;
		}
	}, [props.website_id,props.type,_getPages]);

	useEffect(() => {
		let _columns = [
			{
				Header: "Type",
				id: "type",
				accessor: d => d.page_type.name,
				className: "align-middle",
			}
		];
	
		if (props.type === 1){
			_columns.push({
				Header: "Title",
				id: "title",
				accessor: "title",
				className: "align-middle",
			},{
				Header: "Slug",
				id: "slug",
				accessor: "slug",
				className: "align-middle",
			});
		} else {
			_columns.push({
				Header: "Name",
				id: "title",
				accessor: "title",
				className: "align-middle",
			});
		}
		setListColumns(_columns);
	}, [props.type]);


	useEffect(() => {
		return () => {
			setPages(null);
			setPageType(null);
			setListColumns([]);
		}
	}, []);

	const deletePage = useCallback(async (page_id) => {
		try {
			const res = await APICms.pages.delete({id:page_id,website_id:props.website_id});
			if (res) {
				_getPages();
			}
		} catch (e) {
			console.error(e);
		}
	},[_getPages,props.website_id]);

	const confirmHandler = useCallback((props) => {
		confirm("Are you sure you want to " + props.text, {
			title:props.title || "Confirm",
			okText:"Yes",
			cancelText:"No",
			cancelButtonStyle:"light"
		}).then(result =>{
			if (result===true) props.click();
		}).catch(e => console.error(e));
	},[]);

	const designClickHandler = useCallback(async page_id => {
		for (let i = 0; i < localStorage.length; i++) {
			const key = localStorage.key(i);
			if (key.startsWith("cms")) localStorage.removeItem(key);
		}
		dispatch(actions.CMSSetCurrentWebsite(props.website_id));
		dispatch(actions.CMSSetCurrentPage(page_id));
		dispatch(actions.CMSReset([]));

		// sets the website theme for the builder to be the current theme
		const res=await APIThemes.get({my:1});
		if (res.data){
			dispatch(actions.CMSSetCurrentWebsiteTheme(res.data[0]?.content?.variables || null));
			dispatch(actions.CMSSetCurrentWebsiteCss(res.data[0]?.css_ids || []));			
			//dispatch(actions.CMSSetCurrentPageProps({..._props}));
		}

		history.push(`/p/cms/${props.website_id}/builder/${page_id}`);
	},[dispatch,history,props.website_id]);

	const newClickHandler = async (website_id) => {
		for (let i = 0; i < localStorage.length; i++) {
			const key = localStorage.key(i);
			if (key.startsWith("cms")) localStorage.removeItem(key);
		}
		const res = await APICms.pages.create({website_id:website_id,page_type_id:props.type,title:"New "+pageType.name});
		if (res?.data?.length>0){
			const _props = {
				page_id: res.data[0].id,
				title: "New "+pageType.name,
				slug: "",
				description: "",
				keywords: "",
				page_type: props.type || pageType.id,
				page_type_name: pageType.name || "",
				themes: [],
				config: pageType.config || {},
				elements: [emptyPage(pageType.config || {})],
			}
			//localStorage.setItem("cms",JSON.stringify({..._props}));
			//localStorage.setItem("cms_config",JSON.stringify(pageType.config || {}));
			dispatch(actions.CMSSetCurrentWebsite(website_id));
			dispatch(actions.CMSSetCurrentPage(null));
			dispatch(actions.CMSSetCurrentPageProps({..._props}));
			dispatch(actions.CMSReset([emptyPage(pageType.config || {})]));
			history.push(`/p/cms/${props.website_id}/builder/${res.data[0].id}`);
		}
	}

	const duplicateClickHandler = useCallback(async (page_id) => {
		const res = await APICms.pages.copy({id:page_id,website_id:props.website_id});
		if (res.data.length){
			_getPages();
		}
	},[_getPages, props.website_id]);

	const columns = React.useMemo(() => [
		{
			id: "table",
			columns: [...listColumns,
				{
					id: "controls",
					accessor: (d) => "",
					maxWidth: 10,
					minWidth: 0,
					className: "controls",
					disableFilters: true,
					disableSortBy: true,
					Cell: (props) => (
						<ListGroup horizontal>
							<ListGroup.Item className="user-buttons" onClick={()=>designClickHandler(props.row.original.id)}>
								<i className="far fa-pencil-ruler" data-tip="Design"/>
							</ListGroup.Item>

							<ListGroup.Item className="user-buttons">
								<i className="far fa-copy" data-tip="Duplicate" onClick={()=>duplicateClickHandler(props.row.original.id)}/>
							</ListGroup.Item>

							<ListGroup.Item className="user-buttons">
								<i className="far fa-trash-alt" data-tip="Delete" onClick={()=>confirmHandler({
									title: "Delete Page",
									text: "delete this page", 
									click: () => deletePage(props.row.original.id), 
								})}/>
							</ListGroup.Item>

							<ReactTooltip
								globalEventOff="click"
								effect="solid"
								backgroundColor="#262D33"
								arrowColor="#262D33"
							/>
						</ListGroup>
					),
				},
                {
                    id: 'id',
					accessor: "id",
                    show:false,
                },
                {
                    id: 'website_id',
					accessor: "website_id",
                    show:false,
                },								
			],
		}
	],[designClickHandler, deletePage, confirmHandler, duplicateClickHandler, listColumns]);

	if (!pages || !pageType) return (<div>Loading...</div>);

	return (
		<Container fluid>
			<Row>
				<Col>            
					<Table columns={columns} data={pages} label={true} newClick={()=>newClickHandler(props.website_id)} newLabel={`New ${pageType.name || ""}`} />
				</Col>
			</Row>
		</Container>
	);
}

export default Pages;