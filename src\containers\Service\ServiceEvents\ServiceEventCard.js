import React, { useState, useEffect } from 'react';
import { Link } from "react-router-dom";
import { format, addMinutes } from 'date-fns'
import { <PERSON><PERSON>, Card } from 'react-bootstrap';

const requestStatuses = {
    1: "Requested",
    2: "Approved",
    3: "Denied"
};

const DateTime = ({ start, end }) => {
    const windowSize = window.innerWidth;
    return (
        <div className="se-date-time">
            {windowSize < 420 ? format(start, "MM-dd-yyyy") : format(start, "EEEE MMM d, yyyy")}
            <br />
            {format(start, "h:mm a")} {format(end, "- h:mm a")}
        </div>
    )
}

const UserList = ({ event, listManagers=false, userMaxRole }) => {
    let roleToShow = listManagers ? 2 : 3;
    console.log("listManagers", listManagers);
    return (
        <div className="se-user-list">
            {listManagers ? //making sure that patrons do not have a link to access any else's profile
                <>
                    {event.users && event.users
                    .map(user => (
                        (parseInt(user.event_role_id) === roleToShow) &&<span> {user.first_name} {user.last_name} </span>
                    ))}
                </>
            :
                <>
                    {event.users && event.users
                    .map(user => (
                        (parseInt(user.event_role_id) === roleToShow) && <Link key={`event-${event.id}-user-${user.id}`} to={`/p/users/${user.id}`}>{user.first_name} {user.last_name}</Link>
                    ))}
                </>
            }
        </div>
    )
}

const Name = ({ name, className="se-name" }) => {
    return (
        <div className={className}>
            {name}
        </div>
    )
}

const RemoveButton = ({ event, onCancel, userEvent, managerView=false, className="" }) => {
    if (new Date(event.start_datetime) < new Date()) {
        return <div className={className}></div>
    }

    if (userEvent?.requests?.length>0) {
        let request = userEvent.requests[0];
        let content = (
            <>
                Cancellation Request { request.request_status_id===1 ? "Submitted" : "Completed" }
                { request.request_status_id!==1 && !managerView &&
                    <><br/>Status: {requestStatuses[request.request_status_id]}</>
                }
            </>
        );
        if (managerView) {
            content = (
                <Link to={`/p/services/request-respond/${userEvent.user_event_id}`} className={request.request_status_id===1 ? "highlight" : ""}>
                    {content}
                </Link>
            );
        }
        return (<div className={className}>{content}</div>);
    }

    return (
        <div className={className}>
            <Button className={`btn-danger ${[1,2].includes(event.event_status_id) ? '' : 'hidden'}`} onClick={() => onCancel(event.id)}>
                Cancel
            </Button>
        </div>
    )
}

const ServiceEventCard = ({ event, onCancel, locationNames, managerView=false, showServiceName=true, showCancel=true, showLocation=false, showUserName=true, showManagerName=true, userMaxRole=7 }) => {

    const windowSize = window.innerWidth;

    let [pagePart, setPagePart] = useState();
    let [userEvent, setUserEvent] = useState();

    console.log("managerView", managerView);

    useEffect(() => {
        let start = new Date(event.start_datetime);
        let end = (addMinutes(new Date(event.end_datetime),1));

        if (event.user_event && !userEvent) {
            setUserEvent(event.user_event);
        } else if (event.users && !userEvent) {
            let patronUsers = event.users.filter(user => user.event_role_id===3);
            if (patronUsers.length>0) setUserEvent(patronUsers[0]);
        }

        setPagePart(
            <>
                <DateTime start={start} end={end} />
                {showUserName &&
                    <UserList event={event} />
                }
                {showManagerName &&
                    <UserList event={event} listManagers={true} userMaxRole={userMaxRole} />
                }
                {showServiceName &&
                    <Name name={event.services ? event?.services[0]?.name : ''} />
                }
                {showLocation && 
                    <Name name={locationNames[event?.location_id]} />
                }
                {windowSize < 420 ? 
                    <>
                        {event?.event_status_name === "Cancelled" ?
                            <i className="far fa-ban success-text" />
                        :
                            <i className="far fa-check-circle fail-text" />
                        } 
                    </>
                : 
                    <div className="se-name">
                        {event?.event_status_name}        
                    </div>
                }
                {showCancel &&
                    <RemoveButton event={event} onCancel={onCancel} userEvent={userEvent} managerView={managerView} className="se-name" />
                }
            </>
        );
    },[event, locationNames, userEvent, managerView, showCancel, showLocation, showManagerName, showServiceName, showUserName, onCancel, windowSize, userMaxRole]);

    return (
        <div className={`table-row service-booking-card status-${event.event_status_name}`}>

            {pagePart}

            {/* TODO: Display more details when card is clicked on: description, managers contact info */}

        </div>
    );
};

export default ServiceEventCard;