/*eslint-disable*/
let qaDevSB = Cypress.env('impact_sb_user');
let qaDevAdmin = Cypress.env('impact_admin_user');
let password = Cypress.env('login_password');
let baseUrl = "https://portal-qa.impactathleticsny.com/p/"

describe("It will create an event (Sky Flyer's) that is used in other tests and test the create wizard for those circumstances", {scrollBehavior: "center", testIsolation:false}, ()=>{
    let local;
    let skyFlyersExists=false

    before("It will log in a user, save local state, and access React",()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', 'api/user/menu').as('getMenu');
        cy.loginLocal(baseUrl, qaDevAdmin, password)
        cy.wait('@getUserUser').then(()=>{
            local = localStorage.getItem('user');
        });
    });

    beforeEach("It will renew the user in local storage and viewport size",()=>{
        cy.viewport(1920, 1080);
        cy.restoreLocalUser(JSON.parse(local));
    });

    context("It will check to see if an event exists before going through the event wizard, saving if need be", ()=>{
        
        it("Will see if this event already exists",()=>{
            cy.visit(`${baseUrl}events`)
            cy.intercept('POST', '/api/event').as('getEvents');
            cy.wait('@getEvents')
            cy.get('#searchTerm')
                .type('Sky Flyers', {force: true});
            cy.wait('@getEvents').then((response)=>{
                cy.log(response.response.body)
            });
            cy.get('tbody')
                .children()
                .invoke('text')
                .as('rowText')
            cy.get('@rowText').then(text=>{
                if(text.includes('Sky Flyers')) {
                    skyFlyersExists=true;
                    cy.log(`${String.fromCodePoint(0x1F92F)} Sky Flyer Event Exists`)
                }
            })
        })
    
        it("will navigate to the event wizard",()=>{
            cy.visit(`${baseUrl}events/wizard`);
            cy.wait(250);
            cy.wait(250);
        });
        
        it("will check that the heading changes when you name it and that you are able to name the event",()=>{
            cy.url().should('contain',"events/wizard");
            cy.get('.event-breadcrumb > .breadcrumb > .breadcrumb-item')
                .should('contain', "Untitled Event");
            cy.get('#name')
                .type('Sky Flyers')
            cy.get('#short_description')
                .type('This Event was created by Cypress!!!')
            cy.get('.event-breadcrumb > .breadcrumb > .breadcrumb-item')
                .should('contain', "Sky Flyers");
            cy.get('[data-cy="multistep-next"]')
                .click();
            cy.wait(500);
        });
    
        it("will pick an event type, age, location, recurring",()=>{
            cy.get('.title')
                .should('contain', "Event Type")
            cy.get('#event_type_2')
                .click();
                cy.get('[data-cy="multistep-next"]')
                .should('contain', 'Next')
                .click();
            cy.wait(500);
            cy.get('.title')
                .should('contain', "Is there an age requirement?");
            cy.get(':nth-child(1) > .form-check-label')
                .click();
            cy.get('[data-cy="multistep-next"]')
                .should('contain', 'Next')
                .click();
            cy.get('.title')
                .should('contain', 'Choose Location');
            cy.get('#location_id_53')
                .click();
            cy.get('[data-cy="multistep-next"]')
                .should('contain', 'Next')
                .click();
            cy.wait(500);
            cy.get('.title')
                .should('contain', "Is this a recurring event?");
            cy.get('#is_recurring_0')
                .click()
            cy.get('[data-cy="multistep-next"]')
                .click();
            cy.wait(500);
        });
    
        it("will select the time slots for the event",()=>{
            cy.get('.react-datepicker-wrapper > .react-datepicker__input-container > .datepicker-calendar')
                .click();
            cy.get('.react-datepicker__month-read-view--down-arrow')
                .click();
            cy.get('.react-datepicker__month-dropdown > :nth-child(5)')
                .click();
            cy.get('.react-datepicker__year-read-view')
                .click();
            cy.get('.react-datepicker__year-dropdown > :nth-child(6)')
                .click();
            cy.get('.react-datepicker__day--017')
                .click();
            if(!skyFlyersExists){
                cy.get('#cell-20-56')
                    .click();
                cy.get('.time-display')
                    .should('contain', "2:00 PM")
                    .and('contain', "Basketball Court 4");
            }else {
                cy.get('#cell-20-59')
                    .click();
                cy.get('.time-display')
                    .should('contain', "2:59 PM")
                    .and('contain', "Basketball Court 4");
            }
            cy.get('[data-cy="multistep-next"]')
                .click();
            cy.wait(500);
        });
    
        it("will add registration and price",()=>{
            cy.get('.title')
                .should('contain', "Requires Registration")
            cy.get('#requires_registration_1')
                .click();
            cy.get('[data-cy="multistep-next"]')
                .click();
            cy.wait(500);
    
            cy.get('.title')
                .should('contain', 'Does it require a fee?')
            cy.get('#has_fee_1')
                .click();
            cy.get('#fee')
                .type('10')
            cy.get('[data-cy="multistep-next"]')
                .click();
            cy.wait(500);
        });
    
        it("will skip to summary and save if need be",()=>{
            cy.get('[data-cy="event-wizard-skip-btn"]')
                .click()
            cy.wait(500);
            if(!skyFlyersExists) cy.get('.submit').click()
        });
    })

})