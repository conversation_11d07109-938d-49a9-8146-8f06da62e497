import React, {useState, useEffect} from 'react';

import styles from './Hamburger.module.scss';

/** 
 * @param buttons should be object with name, function, id 
 * @param type should be either "list" or "html".  If html, it should be contained within an array to make React happy
 * @param maxWidth should be in pixels, passed in as a style
 * @param orientation currently accepts "left" and "right"
*/
export const Hamburger =({
    buttons,
    type="html", 
    maxWidth="200px",
    orientation="right",
})=>{

    const [isOpen, setIsOpen]=useState(false);

    useEffect(()=>{
        const handleEscKey=(e)=>{ if(e.key === "Escape" && isOpen) setIsOpen(false)}

        document.addEventListener('keydown', handleEscKey);

        return()=>{document.removeEventListener('keydown', handleEscKey)}
    //isOpen is only conditional and not required - just preventing it from rerunning thigns when not necessary    
    //eslint-disable-next-line
    },[]);

    const handleBackdropClick=()=>{
        if(isOpen) setIsOpen(false)
    }

    return(
        <div   
            className={`
                ${styles["hamburger-wrapper"]}
                ${orientation === "left" && styles["left"]} 
                ${orientation === "right" && styles["right"]}
            `}
        >
            <i className="fas fa-bars" onClick={()=>setIsOpen(!isOpen)}/>

            {isOpen &&
                <>
                    <div 
                        style={{maxWidth: maxWidth}}
                        className={`
                            ${styles[`opened-burger`]} 
                            ${orientation === "left" && styles["left"]} 
                            ${orientation === "right" && styles["right"]}
                        `}
                        onClick={(e)=>e.stopPropagation()}
                    >
                        {type==="list" &&
                            <>
                                {buttons?.map((each)=>(
                                    <p 
                                        key={`buger-button-${each.id}`}
                                        className={styles['each-entry']}
                                        onClick={()=>each.function(each.id)} 
                                    >
                                        {orientation === "left" && <i className="fas fa-chevron-right" />}
                                        <span>{each.name}</span>
                                        {orientation === "right" && <i className="fas fa-chevron-left" />}
                                    </p>
                                ))}
                            </>
                        }
                        {type==="html" &&
                            <>
                                {buttons.map(button=>(
                                    <>
                                        {button}
                                    </>
                                ))}
                            </>
                        }
                    </div>
                    <div className={styles["backdrop"]} onClick={handleBackdropClick}>
                        {" "}
                    </div>
                </>
            }
        </div>
    )
}