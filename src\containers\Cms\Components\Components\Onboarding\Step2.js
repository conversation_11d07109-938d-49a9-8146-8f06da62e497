import React, {useState, useEffect, useCallback} from 'react';
import { Row, Col } from 'react-bootstrap';

import FormGroupText from '../FormGroupText';
import FormGroupTextarea from '../FormGroupTextarea';

//import Tree from './Step2/Tree';

import styles from './Onboarding.module.scss';

export const Step2 =  props => {
    const {saveStepValues, stepValues} = props;
    const [selectedIndustries, setSelectedIndustries] = useState([]);
    const [showTree, setShowTree] = useState(false);
    const [fieldValues, setFieldValues] = useState();
    
    useEffect(() => {
        if (stepValues?.company){
            setFieldValues(stepValues.company);
        }
    }, [stepValues]);

    useEffect(() => {
        return () => {
            setSelectedIndustries([]);
            setShowTree(false);
            setFieldValues(null);
        }
    }, []);

    const treeSelectHandler = (selected) => {
        setSelectedIndustries(selected);
    }

    const changeHandler = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        const {name, value} = e.target;
        const required = e.target?.required || false;
        
        const newFieldValues = {...(fieldValues || {})};
        newFieldValues[name] = value;

        setFieldValues(newFieldValues);
        saveStepValues({company: newFieldValues});
    }, [fieldValues, saveStepValues]);    

    return (
        <>
            <div className={styles["step-description"]}>
                <h5>Step 2</h5>
                <h4>Who said business can't be fun?</h4>
                <p>Tell us about your business so we can taylor your SiteBoss experience to your needs.</p>
            </div>
            <Row>
                <Col sm={12}>
                    <FormGroupText page_id={props.page_id} name="company_name" type="text" label="Company Name" feedback="Please enter your company's name" disabled={props.submitting} onChange={changeHandler} value={fieldValues?.["company_name"] || ""} />
                </Col>
                <Col sm={12}>
                    <FormGroupTextarea page_id={props.page_id} name="description" rows={5} label="In your words, add a brief description of the company and its business" feedback="Please enter a description for the company" disabled={props.submitting} onChange={changeHandler} value={fieldValues?.["description"] || ""} />
                </Col>
                {/*
                <Col sm={12}>
                    <Form.Label>Select one or more industries that best describe your company</Form.Label>
                    <Row>
                        <Col sm={12}>
                            <Button variant="light" size="sm" href="#!" onClick={()=>setShowTree(!showTree)}>{showTree ? 'Hide' : 'Show'} industries</Button>
                        </Col>
                        <Col sm={12}>
                            {selectedIndustries.map(a=> <Badge pill key={`industry-${a.id}`} className="badge-chip">{a.name}</Badge>)}
                        </Col>
                    </Row>                        
                    {showTree && <Tree page_id={props.page_id} onSelect={treeSelectHandler} selected={selectedIndustries} />}
                </Col>
                */}
                <Col sm={12} lg={8}>
                    <FormGroupText page_id={props.page_id} name="subdomain" type="text" label="Your company's unique access URL" append="siteboss.net" feedback="Please enter a subdomain" disabled={props.submitting} onChange={changeHandler} value={fieldValues?.["subdomain"] || ""} />
                </Col>
            </Row>
        </>
    );
}