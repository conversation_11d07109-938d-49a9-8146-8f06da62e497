import React, {useState, useEffect, useCallback} from 'react';
import {useSelector} from 'react-redux';

import CCPayment from '../../CCPayment';

export const Step6 = (props) => {
    const {click, saveStepValues} = props;
    const user = useSelector(state => state.auth.user.profile);
    
    const [loading, setLoading] = useState(true);
    const [collectJS, setCollectJS] = useState();
    const [selectedUsers, setSelectedUsers] = useState([]);

    useEffect(() => {
        if (props.stepValues?.selectedUsers) setSelectedUsers(props.stepValues.selectedUsers);
        else setSelectedUsers([{id: user.id, first_name: user.first_name, last_name: user.last_name}]);
    }, [user, props.stepValues]);

    useEffect(() => {
        if (!props.default_variant_price){
            click({
                preventDefault: () => {},
                stopPropagation: () => {},
            }, props.referrerStep === "back" ? 5 : 7);
        } else setLoading(false);
    }, [ click, props.referrerStep, props.default_variant_price]);
    
    useEffect(() => {
        return () => {
            setLoading(false);
            setCollectJS(null);
            setSelectedUsers([]);
        }
    }, []);

    const collectJsHandler = useCallback(response => {
        if (response.isToken && response.token) {
            saveStepValues({token: response.token});
        }
        if (!collectJS){
            setCollectJS(response);
            saveStepValues({collectJS: response});
        }
    }, [saveStepValues, collectJS]);

    if (loading || !selectedUsers.length) return (<p>Loading...</p>);

    return (
        <div>
            <p>
                <label className="form-label">Payment information:</label>
            </p>
            <CCPayment {...props} forceMobile hideButton callback={collectJsHandler} price={+props.default_variant_price*selectedUsers.length} />
        </div>
    );
}