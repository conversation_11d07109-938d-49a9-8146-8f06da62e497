{"userGroups": {"errors": null, "data": [{"group_id": 1015, "group_type_id": 7, "group_status_id": 1, "name": "The Galavanting Unicorns", "description": "", "is_admin": 1, "group_member_role_id": 18, "group_member_role": "Admin", "group_member_status_id": 2, "group_member_id": 2418}, {"group_id": 1002, "group_type_id": 4, "group_status_id": 1, "name": "The Midnights", "description": "A fun family full of people who know <PERSON>.  Maybe....\n\nRawr.", "is_admin": 0, "group_member_role_id": 12, "group_member_role": "Guardian", "group_member_status_id": 2, "group_member_id": 2419}, {"group_id": 373, "group_type_id": 4, "group_status_id": 1, "name": "Gero Family", "description": "", "is_admin": 0, "group_member_role_id": 12, "group_member_role": "Guardian", "group_member_status_id": 1, "group_member_id": 2420}]}, "group373": {"errors": null, "data": [{"id": 373, "parent_id": null, "company_id": 2, "name": "Gero Family", "description": "", "group_type_id": 4, "group_type_name": "Family", "group_status_id": 1, "group_status_name": "Active", "group_members": [{"group_member_id": 846, "user_id": 2, "first_name": "<PERSON>", "last_name": "Gero II", "group_member_role_id": 9, "group_member_role_name": "Admin", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 1120, "user_id": 1028, "first_name": "Victoria", "last_name": "Gero", "group_member_role_id": 9, "group_member_role_name": "Admin", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 2420, "user_id": 3075, "first_name": "Midders", "last_name": "<PERSON><PERSON>", "group_member_role_id": 12, "group_member_role_name": "Guardian", "group_member_status_id": 1, "group_member_status_name": "Invited"}], "group_events": [], "group_member_roles": [{"id": 9, "name": "Admin", "description": "Can ban users and edit the group", "is_admin": 1}, {"id": 10, "name": "Parent", "description": "Has authority over children", "is_admin": 0}, {"id": 11, "name": "Child", "description": "A member of a familiy group with no special permissions", "is_admin": 0}, {"id": 12, "name": "Guardian", "description": "Has authority over children", "is_admin": 0}]}]}, "group1002": {"errors": null, "data": [{"id": 1002, "parent_id": null, "company_id": 2, "name": "The Midnights", "description": "A fun family full of people who know <PERSON>.  Maybe....\n\nRawr.", "group_type_id": 4, "group_type_name": "Family", "group_status_id": 1, "group_status_name": "Active", "group_members": [{"group_member_id": 2350, "user_id": 3590, "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON>", "group_member_role_id": 6, "group_member_role_name": "Player", "group_member_status_id": 1, "group_member_status_name": "Invited"}, {"group_member_id": 2380, "user_id": 3605, "first_name": "RoRo", "last_name": "<PERSON><PERSON>", "group_member_role_id": 8, "group_member_role_name": "Friend", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 2381, "user_id": 3603, "first_name": "<PERSON><PERSON>", "last_name": "RRR", "group_member_role_id": 11, "group_member_role_name": "Child", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 2388, "user_id": 3602, "first_name": "<PERSON>", "last_name": "R", "group_member_role_id": 12, "group_member_role_name": "Guardian", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 2389, "user_id": 3598, "first_name": "<PERSON><PERSON>", "last_name": "Oob", "group_member_role_id": 12, "group_member_role_name": "Guardian", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 2393, "user_id": 3604, "first_name": "Midders", "last_name": "M", "group_member_role_id": 12, "group_member_role_name": "Guardian", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 2407, "user_id": 3593, "first_name": "Mid", "last_name": "Night", "group_member_role_id": 9, "group_member_role_name": "Admin", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 2419, "user_id": 3075, "first_name": "Midnight", "last_name": "<PERSON><PERSON>", "group_member_role_id": 12, "group_member_role_name": "Guardian", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}, {"group_member_id": 2422, "user_id": 2691, "first_name": "Natalie", "last_name": "St Jean", "group_member_role_id": 12, "group_member_role_name": "Guardian", "group_member_status_id": 2, "group_member_status_name": "Confirmed"}], "group_events": [], "group_member_roles": [{"id": 9, "name": "Admin", "description": "Can ban users and edit the group", "is_admin": 1}, {"id": 10, "name": "Parent", "description": "Has authority over children", "is_admin": 0}, {"id": 11, "name": "Child", "description": "A member of a familiy group with no special permissions", "is_admin": 0}, {"id": 12, "name": "Guardian", "description": "Has authority over children", "is_admin": 0}]}]}}