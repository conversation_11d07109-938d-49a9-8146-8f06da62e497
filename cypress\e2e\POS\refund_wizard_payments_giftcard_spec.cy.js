/*eslint-disable*/

let baseUrl = "https://portal-qa.impactathleticsny.com/p/"
// let baseUrl = "http://localhost:3000/p/"
let username = Cypress.env('impact_admin_user');
let password = Cypress.env('login_password');
// let giftCard = Cypress.env('gift_card_dev_one');
let giftCard = Cypress.env('gift_card_qa_one');

describe("It will do multiple payments, one of which is giftcards, and then refund by payments", {testIsolation: false, scrollBehavior: "center"}, ()=>{
    let orderNumber;
    let gcTransactionNumber;
    let cashTransactionNumber;
    let local;
    let subtotal = '3.89'; //amount of the two items without anything added
    let tax = '.27';
    let fullTotal = '4.16'; //subtotal + tax shown on preview pane
    let cashAmount = "2.80"

    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, username, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        });
    });

    beforeEach(()=>{
        cy.viewport(1920, 1080);
    });

    context("it will add multiple items to the order",()=>{
        it("will visit the POS",()=>{
            cy.visit(`${baseUrl}pos/1`);
            cy.wait(1000);
        });

        it("will add multiple items to the order",()=>{
            cy.get('[data-cy="product-card"]')
                .contains('Arizona Gummy')
                .click({force:true});
            cy.wait(1500);
            cy.get('[data-cy="product-card"]')
                .contains('Coffee Cake')
                .click({force:true});
            cy.get('[data-cy="order-number"]')
                .invoke('text').as('orderNumber')
            cy.get('@orderNumber').then((text)=>{
                orderNumber = text
            });
            cy.get('.item-name')
                .children()
                .should('have.length', 2)
        });

        it('will check the totals',()=>{
            cy.get('[data-cy="order-subtotal"]')
                .invoke('text')
                .should('include', subtotal);

            cy.get('[data-cy="order-tax"]')
                .invoke('text')
                .should('include', tax);

            cy.get('[data-cy="order-total"]')
                .invoke('text')
                .should('include', fullTotal)
        });

    });

    context("it will make a split payment between cash and giftcard and verify transactions",()=>{
        it("should check data",()=>{
            cy.get('[data-cy="pos-button-checkout"]')
                .click();
            cy.get('[data-cy="payment-type-2"]')
                .click();
            cy.get('[data-cy="checkout-totals-subtotal"]')
                .invoke('text')
                .should('include', subtotal)
            cy.get('[data-cy="checkout-totals-container"]') //data-cy was removed when this was moved...so.....long complicated selector
                .children()
                .contains('Taxes')
                .next()
                .invoke('text')
                .should('include', tax)
                // cy.get('[data-cy="checkout-totals-taxes"]')
            //     .invoke('text')
            //     .should('include', tax)
            // cy.get('[data-cy="checkout-totals-cash-discount"]')
            //     .invoke('text')
            //     .should('include', cashDiscount)
            cy.get('[data-cy="checkout-totals-outstanding"]')
                .invoke('text')
                .should('include', fullTotal);
        });
        
        it("should make a cash payment for 2.80",()=>{
            cy.intercept('POST', '/api/payment/process').as('processPayment')
            cy.get('[data-cy="checkout-totals-payment-0"]')
                .should('not.exist');
            cy.get('[data-cy="payment-other-input"]')
                .click()
                .type(`{selectAll}{backspace}${cashAmount}`);
            cy.get('#memo')
                .click()
                .type("Partial Cypress payment");
            cy.get('[data-cy="details-add-to-cart"]')
                .click();
            cy.get('[data-cy="checkout-totals-outstanding"]')
                .invoke('text')
                .should('include', '1.36');
            cy.get('[data-cy="checkout-totals-container"]').within(()=>{
                cy.get('button')
                    .invoke('text')
                    .should('contain', cashAmount)
            })
                // .contains(cashAmount)
        });

        it('will make a payment with a manager discount',()=>{
            cy.get('[data-cy="payment-type-5"]')
                .click();
            cy.get('#username')
                .click()
                .type(username);
            cy.wait(500)
            cy.get('#password')
                .click()
                .type(password);
            cy.wait(500)
            cy.get('#amount')
                .click()
                .type('{selectall}{backspace}1.00');
            cy.get('[data-cy="details-add-to-cart"]')
                .click();
        });

        it("should make a gift card payment",()=>{
            cy.get('[data-cy="payment-type-4"]')
                .click()
            cy.get('[data-cy="gift-card-code"]')
                .should('not.exist')
            cy.get('#card_code')
                .click()
                .type(giftCard)
            cy.get('.input-group-append')
                .click()
            cy.get('#memo')
                .click()
                .type('{selectAll}{backspace}Rawr')
            cy.get('[data-cy="details-add-to-cart"]')
                .click();
            cy.get('[data-cy="gift-card-code"]')
                .invoke('text')
                .should('include', giftCard)
            cy.get('[data-cy="gift-card-code"]')
                .should('exist')
        });

        it("will make sure there are successful details",()=>{
            // cy.stubPrint();
            cy.get('[data-cy="success-title"]')
                .should('exist')
            cy.get('[data-cy="success-title"]')
                .invoke('text')
                .should('include', "Payment Successful");

            cy.get('[data-cy="order-success-all-transactions"]')
                .children()
                .should('have.length', 9) //children include <p> <button> and <hr>
            cy.get('[data-cy="order-success-all-transactions"]')
                .children()
                .eq(0)
                .within(()=>{
                    cy.get('[data-cy="success-transaction-details"]')
                        .invoke('text')
                        .should('include', 'Gift Card')
                });
            cy.get('[data-cy="order-success-all-transactions"]')
                .children()
                .eq(3)
                .within(()=>{
                    cy.get('[data-cy="success-transaction-details"]')
                        .invoke('text')
                        .should('include', 'Admin')
                });
            cy.get('[data-cy="order-success-all-transactions"]')
                .children()
                .eq(6)
                .within(()=>{
                    cy.get('[data-cy="success-transaction-details"]')
                        .invoke('text')
                        .should('include', 'Cash')
                });
            cy.get('[data-cy="payment-cancel-button"]')
                .click();
            cy.stubPrint();
            cy.wait(1000)
            cy.get('[data-cy="order-number"]')
                .should('not.exist');
        });
    });

    context("go to the order page and initiate a refund by payment", ()=>{

        it("will navigate via back and menu to get to the order page",()=>{
            cy.intercept('POST', "/api/order").as('waitOrders');
            cy.intercept('GET', '/api/order/order/**').as('getOrder');
            cy.get('.btn-back')
                .click();
            cy.get('[data-cy="menu-item-All Orders-309"]')
                .click({force:true});
            cy.wait('@waitOrders')
            cy.get('[data-cy="orders-table"]')
                .within(()=>{
                    cy.get('tr')
                        .contains(orderNumber)
                        .parent()
                        .within(()=>{
                            cy.get('[data-cy="handle-detail-click-td"]')
                                .click();
                            cy.wait('@getOrder')
                        })
                })
            
        });

        it("will check and see that the order is the correct one and begin the refund",()=>{
            cy.get('.py-1')
                .should('not.exist')
            cy.get('.section-title')
                .invoke('text')
                .should('include', orderNumber)
            cy.get('[data-cy="transaction-histories"]')
                .children()
                .should('have.length', 6); //the collapse of the accordion also counts as a child
            cy.get('[data-cy="advanced-refund-btn"]')
                .click()
            cy.wait(500)
        });

        it("will begin the refund",()=>{
            cy.get('[data-cy="adv-refund-payments-btn"]')
                .click();
            cy.get('[data-cy="refund-sum-type"]')
                .invoke('text')
                .should('include', 'By Payments');
            cy.get('[data-cy="refund-next"]')
                .click();
            cy.wait(200)
        });

        it("will uncheck gift card transactions",()=>{
            cy.wait(1000)
            cy.get(':nth-child(3) > #refund-accordion-toggle').within(()=>{
                cy.get('i')
                    .eq(0)
                    .click()

            });
            cy.get('[data-cy="refund-next"]')
                .click();
            cy.wait(200)
        });

        it("will select a register and add a memo", ()=>{
            cy.get('[data-cy="adv-refund-register-select"]')
                .contains('Internal Testing Only')
                .click();
            cy.get('[data-cy="refund-next"]')
                .click();
            cy.wait(200);
            cy.get('#refund-memo')
                .click()
                .type("Memo stuff");
            cy.get('[data-cy="refund-next"]')
                .click();
            cy.wait(200)
        });

        it("will check the amount",()=>{
            cy.get('[data-cy="adv-refund-summary-amount"]')
                .invoke('text')
                .should('include', cashAmount);
        });

        it("will finish the refund and check the page has changed",()=>{
            cy.intercept('POST', '/api/payment/process/refund').as('processRefund');
            cy.get('[data-cy="refund-submit"]')
                .click();
            cy.wait('@processRefund');
            cy.get('[data-cy="refund-cancel-button"]')
                .click();
            cy.wait(500)
            cy.get('.py-1')
                .should('exist')
            cy.get('[data-cy="transaction-histories"]')
                .children()
                .should('have.length', 8);
            cy.get('[data-cy="advanced-refund-btn"]')
                .should('have.attr', 'disabled')
        })
    })

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    });
})