import React, { useState,useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Form from 'react-bootstrap/Form';
import Card from 'react-bootstrap/Card';

import * as actions from '../../../store/actions';

import Events from '../../../api/Events';

export const EventTypes = (props) => {

    const user = useSelector(state => state.auth.user);

    const dispatch = useDispatch();    
    const [eventTypes,setEventTypes]=useState([]);

    const setTypeHandler = (event) => {
        dispatch(actions.eventType(eventTypes.filter(item => item.id===+event.target.value)?.[0]));
    }

    useEffect(() => {
        const _getEventTypes = async () => {
            try{
                const response = await Events.Types.get({"company_id": user.company_id});
                if (response.data) setEventTypes(response.data);
            } catch(e){
                console.error(e);
            }
        }

        _getEventTypes();

    },[user.company_id]);

    return (
        <React.Fragment>
            <Row>
                <Col sm="12" lg="8">
                    <h1>Let's get Started!</h1>
                    <p>
                        Our event wizard will let you book anything you want, whether it be a tournament, a team practice, a class, a massage table, or a spot at our juice bar.
                    </p>
                    <p>
                        First, select the event type you want to book, and press CONTINUE.
                    </p>
                </Col>
            </Row>
            <Row>
                <Col sm="12" md="6" lg="3" className="event-types">
                    <Card>
                        <Card.Body>
                            <Card.Title>Event Type</Card.Title>
                            {eventTypes && eventTypes.map((event,i)=>(
                                <Form.Group key={`event-type-${i}`} controlId={`event-type-${event.id}`}>
                                    <Form.Check required type="radio" name="event_type" label={event.name} value={event.id} onClick={setTypeHandler} />
                                </Form.Group>
                            ))}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </React.Fragment>
    );
}