import React from 'react'
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton'
import {Row } from 'react-bootstrap';

/** Card skeleton will create a grid of loading card placeholders
 * @param {number} columns - Number of columns in the grid (default: 4)
 * @param {number} rows - Number of rows in the grid (default: 1)
 * @param {string} className - Additional CSS classes to apply
 */
export const CardSkeleton = ({columns=4, rows = 1}) => {
    return (
        <>
            {Array(rows).fill("card").map((val, i) => (
                <SkeletonTheme color="#e0e0e0" width={100} key={`skele-${val}-${i}`}>
                    <Row key={`cs-${val}-${i}`}>
                        {Array(columns).fill("col").map((val, j)=>(
                            <div key={`ls-${val}-${j}`} style={{width: "175px"}}>
                                <Skeleton height={50} style={{borderRadius: "50px", width: "50px"}}/> 
                                <div>
                                    <Skeleton height={20} width={75} /> 
                                    <br />
                                    <Skeleton height={16} width={150} />
                                    <br />
                                    <Skeleton height={16} width={150} />
                                </div>
                            </div>
                        ))}
                    </Row>
                </SkeletonTheme>
            ))}
        </>
    )
}