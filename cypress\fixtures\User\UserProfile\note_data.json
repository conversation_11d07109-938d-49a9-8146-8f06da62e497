{"fullDataNotes": {"errors": null, "data": [{"id": 12, "author_user_id": 3075, "author_first_name": "Midnight", "author_last_name": "<PERSON><PERSON>", "note": "This is an admin only note ", "status": 2, "created_at": "2022-04-04T14:44:02.000000Z"}, {"id": 13, "author_user_id": 3075, "author_first_name": "Midnight", "author_last_name": "<PERSON><PERSON>", "note": "This is \"all\"", "status": 1, "created_at": "2022-04-04T14:44:11.000000Z"}, {"id": 14, "author_user_id": 3075, "author_first_name": "Midnight", "author_last_name": "<PERSON><PERSON>", "note": "This is \"only me\" (Midnight Main account)", "status": 3, "created_at": "2022-04-04T14:44:33.000000Z"}, {"id": 15, "author_user_id": 3605, "author_first_name": "RoRo", "author_last_name": "<PERSON><PERSON>", "note": "This is a note written by staff. And now it should be viewable by all!", "status": 2, "created_at": "2022-05-24T21:58:04.000000Z"}, {"id": 16, "author_user_id": 3605, "author_first_name": "RoRo", "author_last_name": "<PERSON><PERSON>", "note": "\"only me\"", "status": 3, "created_at": "2022-05-24T21:58:22.000000Z"}, {"id": 17, "author_user_id": 3605, "author_first_name": "RoRo", "author_last_name": "<PERSON><PERSON>", "note": "\"all\"", "status": 1, "created_at": "2022-05-24T21:58:31.000000Z"}, {"id": 21, "author_user_id": 3075, "author_first_name": "Midnight", "author_last_name": "<PERSON><PERSON>", "note": "author", "status": 3, "created_at": "2022-06-23T17:55:06.000000Z"}, {"id": 22, "author_user_id": 3075, "author_first_name": "Midnight", "author_last_name": "<PERSON><PERSON>", "note": "Viewable by Staff", "status": 5, "created_at": "2022-07-14T08:00:11.000000Z"}, {"id": 22, "author_user_id": 3075, "author_first_name": "Midnight", "author_last_name": "<PERSON><PERSON>", "note": "<PERSON>r", "status": 5, "created_at": "2022-05-24T08:00:11.000000Z"}, {"id": 23, "author_user_id": 3075, "author_first_name": "Midnight", "author_last_name": "<PERSON><PERSON>", "note": "Viewable by IT/Tech", "status": 6, "created_at": "2022-07-14T08:00:22.000000Z"}, {"id": 24, "author_user_id": 3605, "author_first_name": "RoRo", "author_last_name": "<PERSON><PERSON>", "note": "<PERSON><PERSON> should see it because he wrote it! Otherwise some staff may try to leave a note for IT or something and never know they actually left it or not because there are no visual cues!", "status": 6, "created_at": "2022-07-14T08:05:29.000000Z"}]}, "noDataNotes": {"errors": null, "data": []}, "errorNoUser": {"errors": ["User not Found"], "data": null}, "errorNoAuth": {"errors": ["Not authorized to access user notes"], "data": null}}