import React, { useState, useEffect } from 'react';
import { ListGroup, Row, Col, Button, Modal } from 'react-bootstrap';
import { format, parse, differenceInYears } from 'date-fns';

import Events from '../../../api/Events';

import '../ManageUsers/ManageUsers.scss';

/**
* Creates a button for viewing a user's registration info for an event. This will show a modal containing the info.
* 
* @param {string} userId The user's id
* @param {string} eventId The event's id
* @param {string} btnIcon The icon for the button
*/
const InfoButton = (props) => {

    const [userId, setUserId] = useState("");
    const [eventId, setEventId] = useState("");
    const [userInfo, setUserInfo] = useState({});
    const [icon, setIcon] = useState("");
    const [showModal, setShowModal] = useState(false);
    const [parsedDoB, setParsedDoB] = useState("");
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (props.userId) { setUserId(props.userId); }
        else { console.error("User info button received no user ID"); }
        if (props.eventId) { setEventId(props.eventId); }
        else { console.error("User info button received no event ID"); }
        if (props.btnIcon) { setIcon(props.btnIcon); }
        else { setIcon("fas fa-question-square"); }
    },[props]);

    const closeModalHandler = () => {setShowModal(false)};

    const openModalHandler = () => {
        Events.get_user_responses({ "user_id": userId, "event_id": eventId})
        .then( response => {
            setUserInfo(response.data[0]);
            setParsedDoB(format(new Date(response.data[0]?.dob.substring(0,10)), 'MM/dd/yyyy'));
            setLoading(false);
        }).catch(e => console.error(e));
        setShowModal(true);
    };

    return (
        <React.Fragment>
            <ListGroup.Item className="user-buttons" action href="#RegistrationInfo" onClick={openModalHandler}>
                <i className={icon} data-tip={`View Registration Info`}></i>
            </ListGroup.Item>
            <Modal show={showModal} onHide={closeModalHandler} size={"m"} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Event Registration Info</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Row className="role-modal-row shaded">
                        <Col lg="3"><span>Name:</span></Col>
                        <Col><span>{!loading ? userInfo.first_name + " " + userInfo.last_name : "loading"}</span></Col>
                    </Row>
                    <Row className="role-modal-row">
                        <Col lg="3"><span>Email:</span></Col>
                        <Col><span>{userInfo.email}</span></Col>
                    </Row>
                    <Row className="role-modal-row shaded">
                        <Col lg="3"><span>Date of Birth:</span></Col>
                        <Col><span>{parsedDoB}</span></Col>
                    </Row>
                    <Row className="role-modal-row">
                        <Col lg="3"><span>Age:</span></Col>
                        <Col><span>{parsedDoB ? differenceInYears(new Date(), parse(userInfo.dob, 'yyyy-MM-dd HH:mm:ss', new Date())) : ""}</span></Col>
                    </Row>
                    {userInfo.custom_responses?.map((item, i) => {
                        return(
                            <Row className={`role-modal-row ${i % 2 === 0 ? "shaded" : ""}`} key={"response_" + i}>
                                <Col lg="3"><span className="custom-response">{item.placeholder_text.replace("_", " ")}:</span></Col>
                                <Col><span className="custom-response">{item.value}</span></Col>
                            </Row>
                        )
                    })}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="primary" action href="#ManageUsers" onClick={closeModalHandler}>
                        OK
                    </Button>
                </Modal.Footer>
            </Modal>
        </React.Fragment>
    );
}

export default InfoButton;