import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Button } from 'react-bootstrap';
import { randomUUID } from '../../../../../utils/cms';
import SortableTreeView from '../../../../../components/common/SortableTreeView';

import Item from './Item';
import Element from './Item/Element';

import styles from './AddItems.module.scss';


export const AddItems = (props) => {
    const {selection} = props;
    const [triggerSave, setTriggerSave] = useState(false);
    const [items, setItems] = useState([]);
    const [selectedItem, setSelectedItem] = useState(props.selectedItem || null);

    const findObj = useCallback((items, value, remove = false) => {
        let obj = null;
        items.forEach((item, i) => {
            if (item.id === value) {
                if (remove) {
                    items.splice(i, 1);
                    obj = items;
                } else obj = item;
                return;
            } else if (item.children && item.children.length) {
                const _obj = findObj(item.children, value, remove);
                if (_obj) {
                    if (remove){
                        item.children = _obj;
                        obj = items;
                    } else obj = _obj;
                    return;
                }
            }
        });
        return obj;
    },[]);

    const addHandler = useCallback(e => {
        e.preventDefault();
        let _items = [];
        if (props?.items && props?.items.length > 0){
            _items = [...props.items];
            _items.forEach(item => {
                if (!item?.id) item.id = randomUUID(); // adds an id to each item if it doesn't have one
            });
        }
        setItems(prev=> {
            // type 0 = group, type 1 = normal item (btw items may have items inside them, which are the objects to be rendered... for example, it may be a field (for a simple list), or a group of fields (for a menu item that needs a url, description and icon))        
            const _id = (prev?.filter(a=>a.type===1)?.length || 0) + 1;
            setSelectedItem(_id);
            return [...prev, {id: _id, type: 1, parent_id: null, index: (prev.length || 0) + 1, items: _items}];
        });
        setTriggerSave(true);
    }, [props?.items]);


    const saveHandler = useCallback((id, value) => {
        setSelectedItem(id);
        setItems(prev=>{
            const obj = findObj(prev, id);
            if (obj){
                const idx = obj.items.findIndex(a=>a.type === value.type && a.display_name === value.display_name);
                if (idx > -1){
                    if (value.type==="checkbox") obj.items[idx].checked = value.checked || false;
                    obj.items[idx].value = value.value;
                }
            }
            return prev;
        });
        setTriggerSave(true);
    },[findObj]);

    const deleteHandler = useCallback((e,id) => {
        e.preventDefault();
        setItems(prev=>{
            return findObj(prev, id, true);
        });
        setTriggerSave(true);
    },[findObj]);

    const sortHandler = useCallback((data)=>{
        setItems(data);
        setTriggerSave(true);
    },[]);

    const _groupByParentId = useCallback((items, parentId = null) => {
        const filteredItems = items.filter(item => item.parent_id === parentId);
        const groupedItems = filteredItems.map(item => {
            const children = _groupByParentId(items, item.id);
            const _item = JSON.parse(JSON.stringify(item));
            if (children.length) {
                return {..._item, children};
            }
            return {..._item};
        });
        return groupedItems;
    },[]);

    /* 
    - props.data is an array of arrays containing the items and values. These items are the fields. 
    - if the element is a container the items it contains are stored in the children property.
    - so we need to transform this array of arrays into a flat array of objects, where each object contains an array of items. These items are field definitions which we need to build the form.
    */
    useEffect(() => {
        let struct = props?.items || [];
        let __items = [];
        let __index = 0;

        const _loadItems = (_items, parent_id = null) => {
            if (typeof _items === "string") _items = JSON.parse(_items);
            if (!Array.isArray(_items)) return null;
            return _items.map(_item => {
                __index ++;
                const _id = randomUUID();
                const _st = {id: _id, text: _item?.[0].value || "Item" , parent_id: parent_id, type: 1, index: __index, items: [], children: []};
                Array.isArray(_item) && _item.forEach((item , j) => {
                    const __st = JSON.parse(JSON.stringify(struct));
                    const __id = props?.groupingType === 1 ? [...__items].filter(a=>a.type===1).length + 1 : __index;
                    if (item?.value !== undefined) {
                        _st.id = __id;
                        if (__st?.[j]) {
                            if (__st[j].type === "checkbox") __st[j].checked = item.checked || false;
                            else __st[j].value = item.value;
                            _st.items.push(__st[j]);
                        }
                    }
                    if (item?.children) {
                        _st.type = props?.groupingType === 1 ? 0 : 1; // sets the item as a container if groupingType is 1 (grouping)
                        _st.children = _loadItems(item.children, props?.groupingType === 1 ? _id : __id);
                    } 
                });
                return _st;
            });
        }

        if (props?.data) setItems(_loadItems(props.data));
    }, [props?.data, props?.items, props?.groupingType]);


    /*
    - we need to transform our flat array of objects that has form elements and stuff into an array of arrays that we can store in our json object.
    - so we group by parent_id then, if the item is a container, add the items to the children property.
    - we also save the value that was entered in the form field.    
    */
    useEffect(() => {
        const transformData = (input) => {
            const output = [];
            input.forEach(element => {
                const { id, value, children, items } = element;
                const node = [];
          
                if (items && items.length) {
                    items.forEach(item => {
                        const itemNode = { id: item.id || randomUUID(), value: item.value || null, checked: item.checked || undefined, children: null };
                        node.push(itemNode);
                    });
                }
          
                if (children && children.length){
                    let _node = {id, value, children: null};
                    _node.children = (_node.children || []).concat(transformData(children));
                    node.push(_node);
                }
                output.push(node);
            });
          
            return output;
        }

        if (triggerSave === true){
            const value = transformData(_groupByParentId([...items]));
            selection(value, selectedItem);
            setTriggerSave(false);
        }
    }, [triggerSave, items, selection, _groupByParentId, selectedItem]);


    const toggleItemHandler = useCallback(id => {
        setSelectedItem(prev => prev === id ? null : id);
    },[]);

    const fields = useMemo(() => {       
        const foundObj = findObj(items, selectedItem);
        
        let _fields = [];
        if (foundObj) {
            foundObj?.items?.forEach((field, j)=>{
                _fields.push(<Element key={`item-${selectedItem}-${j}`} item={field} element_id={selectedItem} update={saveHandler} delete={deleteHandler} />)
            });
        }
        return _fields;
    },[selectedItem, items, saveHandler, deleteHandler, findObj]);

    return (
        <>
            <div className={items.length > 0 ? styles["item-container"] : ""}>
                <SortableTreeView data={items} selectedItem={selectedItem} onSelect={toggleItemHandler} onSort={sortHandler} >
                    {fields.length> 0 && selectedItem &&
                        <Item key={`item-${selectedItem}`} id={selectedItem} delete={deleteHandler} type={items[0].type}>
                            {fields}
                        </Item>
                    }
                </SortableTreeView>
            </div>
            <Button variant="primary" onClick={addHandler}>Add Item</Button>
        </>
    );
}
