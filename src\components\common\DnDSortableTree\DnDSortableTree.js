import React, { useState, useEffect, useCallback, useRef } from 'react';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { Container, Card, Button, Col, Row } from 'react-bootstrap'
import SortableTree, { toggleExpandedForAll,getFlatDataFromTree, getTreeFromFlatData } from '@nosferatu500/react-sortable-tree';
import { useHistory } from 'react-router-dom'

import Toast from '../../Toast'
import ErrorCatcher from '../ErrorCatcher';

import './DnDSortableTree.scss'
import '@nosferatu500/react-sortable-tree/style.css'

/**Component to utilise drag and drop trees.  Required props are {data}, {id}, {endpoint}, and {refreshUrl}
 * Data params should be {.name}, {.sort_order}, {.parent_id}, {.children (as [])}, optional {.description}
 * Other params needed are {props.id} for the name of the Id needing to be returned to the DB (such as "module_id")
 * More info can be found at https://nyspsp.atlassian.net/wiki/spaces/IA/pages/2095185921/DnD+Sortable+Tree+Common+Component
 */
export const DnDSortableTree = (props) => {

    const { endpoint, setModules, propData } = props;

    const history = useHistory();
    const mountedRef = useRef(false);
    const [ loading, setLoading ]=useState(true);
    const [ treeData, setTreeData ] =useState([]);
    const [ searchTerm, setSearchTerm ]=useState("");
    const [ apiData, setApiData ]=useState([]);
    const [ success, setSuccess ]=useState(null);
    const [ errors, setErrors ]=useState(null);
    const [ expanded, setExpanded ]=useState(false)
    const [ searchFocusIndex, setSearchFocusIndex ]=useState(0)
    const [ searchFoundCount, setSearchFoundCount ]=useState(null)
    const [ hasSearch, setHasSearch ]=useState(false)
    const [ activeMatch, setActiveMatch ]=useState();

    /** Function to check and see if there is already a copy of the child within the parent.  If not, allows it to be pushed in. */
    const childAlreadyThere =useCallback((children, childId)=>{
        let alreadyThere = false
        for( let i = 0; i < children.length; i++){
            if(children[i].id === childId)
            alreadyThere = true
        }
        return alreadyThere
    },[])

    /** If parent ID is not at the highest level, checks children for the parent_id match */
    const checkForGrandkids=useCallback((parent, children)=>{
        for(let i =0; i < children.length; i++){
            if(children[i].id === parent.parent_id){
                children[i].children.push(parent)
            }
            else(checkForGrandkids(parent, children[i].children))
        }
    },[])    

    /** Function to make sure if an object has a parent ID that it is properly applied as a child to its parent.
     * Takes in the parent object as a parameter and the whole data set as a second
     */
     const checkProperParenting=useCallback((parent, data)=>{
        let deletes = []
        if(parent.parent_id !== null){
            for(let i = 0; i < data.length; i++){
                if(data[i].id === parent.parent_id){
                    let alreadyThere = childAlreadyThere(data[i].children, parent.id)
                    if(alreadyThere === false){
                        data[i].children.push(parent)
                    }
                }
                else{
                   checkForGrandkids(parent, data[i].children)
                }
                if(data[i].id === parent.id){
                    deletes.push(i);
                }
            }
        }
        return deletes
    },[childAlreadyThere, checkForGrandkids])

    /** Sorts children of the object based on having a sort_order */
    const reorderChildren=useCallback(children=>{
        children.sort((a,b)=>a.sort_order - b.sort_order)
        for(let i = 0; i <children.length; i++){
            if(children[i].children.length>0){
                for(let i = 0; i < children[i].children.length; i++){
                    reorderChildren(children.children);
                }
            }
        }
    },[])

    /**Sorts data based on having a .sort_order. */
    const reorder=useCallback(data=>{
        data.sort((a,b)=>a.sort_order - b.sort_order);
        for(let i = 0; i < data.length; i++){
            if(data[i].children.length >0){
                reorderChildren(data[i].children)
            }
        }
    },[reorderChildren])

    /** Adds title (based on {.name} and subtitle (based on {.description}) to children to conform to tree data structure).  Also checks children for more children. */
    const convertChildren=useCallback((child, parent)=>{
        child.title = child.name; //Adding title
        child.expanded = false //Ensuring collapsed by default
        child.parent_id = parent.id //Assinging kids to their parents
        if(child.description){ //Adding subtitle
            child.subtitle = (`${child.description.substring(0,30)} ...`)
        }
        if(child.children.length>0){
            let children = []
            child.children.forEach((kid)=>{
                let newKid = convertChildren(kid, child)//Recursion to get those other kids.
                children.push(newKid)
            })
            child.children = children
        }else{
            child.children=[];
        }
        return child
    },[])

    /**Adds title (based on {.name}), subtitle (based on {.description}), expanded, and children to items to conform to tree structure */
    const convertData=useCallback(data=>{
        let newData = []
        data.forEach((item)=>{
            item.title = item.name;  //Adding title
            item.expanded = false; //Adding collapsed property by default
            if(item.description){  //Adding subtitle
                item.subtitle = (`${item.description.substring(0,30)} ...`)
            }
            if(item.children.length>0){ //Adding title/subtitle to children
                let children = []
                item.children.forEach((child)=>{
                    let newChild = convertChildren(child, item)
                    children.push(newChild)
                })
                item.children = children
            }
            if(!item.parent_id){  //Checking to ensure object should not be a child
                item.parent_id = null
                newData.push(item)
            }
            else{
               checkProperParenting(item, data)  //Finding a child's parents
            }
        })
        reorder(newData) //Calls reorder function
        newData.forEach((item)=>{ //Reorders children after having children assigned
            if(item.children.length > 0){
                reorderChildren(item.children)
            }
        })
        setTreeData(getTreeFromFlatData({
            flatData: newData,
            getKey: node => node.id,
            getParentKey: node=>node.parent_id,
            rootKey: null
        }))
        setSearchFoundCount(newData.length)//set initial "out of" search matches to the length of the tree
    },[checkProperParenting, reorder, reorderChildren, convertChildren]);

    /** Adds children array if the data does not have the property. */
    const addChildren=useCallback(data=>{
        data.forEach((item)=>{
            if(item.hasOwnProperty('children')===false){
                item.children = []
            }
        })
    },[])

    const resetComponent=useCallback(()=>{
        setLoading(true)
        addChildren(propData)
        convertData(propData)
        setLoading(false)
    },[propData, addChildren, convertData])

    //#region State/Use Effect

    useEffect(()=>{
        mountedRef.current = true;

        return ()=> mountedRef.current = false;
    })

    useEffect(()=>{
        setApiData([])

        if(propData?.length > 0 && mountedRef.current === true){
            addChildren(propData)
            convertData(propData)
            setLoading(false)
        }
    },[propData,  convertData, addChildren])

    useEffect(()=>{ //Clears search results if there is no search term so that nothing is highlighted
        if(searchTerm === ""){
            setActiveMatch([])
        }
    },[searchTerm])

    const sendApiCall=useCallback(async()=>{
        setErrors(null);
        try{
            if(mountedRef.current === true){
                let response = await endpoint({sort:apiData})
                if(!response.errors && response.status===200){
                    setSuccess(<Toast>Your data has been saved successfully!</Toast>)
                    setModules([]);
                    // history.go(props.refreshUrl) //reloads page because all data has changed and temp data has been removed
                }
                else{
                    setErrors(<ErrorCatcher error={response.errors} />)
                    // history.go(props.refreshUrl) //reloads page because all data has changed and temp data has been removed
                    //reset state
                    resetComponent()
                }
            }
        }catch(ex){
            console.error(ex)
        }
        setApiData([])
    },[apiData, endpoint, resetComponent, setModules])

    useEffect(()=>{
        mountedRef.current = true
        
        if(apiData.length>0){
            sendApiCall() 
        }
        return()=>{
            mountedRef.current = false
            
        }
    },[apiData, sendApiCall])

    //#endregion State/UseEffect


    //#region Save Utils
    
    /** Applies the sort order to each item before it's flattened back for sending. Also calls the function to apply parent Id to children */
    const onClickSave=()=>{
        const newOrder = treeData
        for(let i = 0; i<treeData.length; i++){
            newOrder[i].sort_order = i +1
            newOrder[i].parent_id = null
            if(newOrder[i].children.length>0){
                modifyChildrenProperties(newOrder[i])
            }
            let flatData = getFlatDataFromTree({
                treeData: newOrder,
                getNodeKey: ({ node })=>node.id,
                ignoreCollapsed: false,
            })
            convertFlatDataToApiData(flatData)
        }
    }

    /**Adds parent and sort order to children */
    const modifyChildrenProperties=(treeData)=>{
        for(let i = 0; i< treeData.children.length; i++){
            treeData.children[i].parent_id = treeData.id
            treeData.children[i].sort_order = i + 1;
            if(treeData.children[i].children.length >0){
                modifyChildrenProperties(treeData.children[i])//recursion to allow for children to have kids to.  This can go on for generations!
            }
        }
    }
    
    /** End point takes in an ID as props {props.id}, {parent_id}, and {sort_order} so this function reduces all the flat data to that format. */
    const convertFlatDataToApiData=(flatData)=>{
        const apiData =[]
        for(let i = 0; i < flatData.length; i++){
            let newItem = {}
            newItem[props.id] = flatData[i].node.id
            newItem.parent_id = flatData[i].node.parent_id
            newItem.sort_order = flatData[i].node.sort_order
            apiData.push(newItem)
        }
        setApiData(apiData)
    }

    //#endregion Save Utils
    

    //#region Tree Utils
    const expandAll=()=>{
        setExpanded(!expanded)
        setTreeData(prevState=> toggleExpandedForAll({treeData: prevState, expanded: expanded}))
    }

    const matchHandler=(matches)=>{
        if(matches.length > 0){
            setHasSearch(true)
            let isMatch = []
            for(let i = 0; i < matches.length; i++){
                isMatch.push(matches[i].node.id)
            }
            setActiveMatch(isMatch)
            setSearchFoundCount(matches.length)
            setSearchFocusIndex(matches.length > 0 ? searchFocusIndex % matches.length : 0)
        }else{
            setHasSearch(false)
            setSearchFoundCount(treeData.length)
            setSearchFocusIndex(0)
        }
    }

    const searchNext=()=>{
        setSearchFocusIndex(
            searchFocusIndex !== null ?
            (searchFocusIndex + 1 ) % searchFoundCount :0
        )
    }
    const searchPrevious=()=>{
        setSearchFocusIndex(
            searchFocusIndex !== null ?
            (searchFoundCount + searchFocusIndex -1 )% searchFoundCount : searchFoundCount -1
        )
    }

    //#endregion Tree Utils


  return (
    <Container fluid>
        {success}
        <Row className="DnD-top-row-p">
        <h6>Reorder your items to assign a tree hierarchy.  You must click the save button at the bottom to confirm the new order.  Search results will be highlighted and expanded for ease of viewing.</h6>
        </Row>
            <Row>
                <Col xs={6} sm={4} lg={3}>
                    <input className="DnD-search" type="text" placeholder="search" value={searchTerm} onChange={(e)=>setSearchTerm(e.currentTarget.value)} />
                </Col>
                <Col xs={6} sm={8} lg={9}>
                    <Button onClick={expandAll}>{expanded ? <span>Expand All</span> : <span>Collapse All</span> } </Button>
                </Col>
            </Row>
            {hasSearch ? 
                <div className="search-matches">
                    <span className="DnD-f-b-btn" onClick={searchPrevious}> <i className="far fa-chevron-circle-left"></i></span>
                    <span className="match-text">{searchFocusIndex +1}/{searchFoundCount} Matching Results</span>
                    <span className="DnD-f-b-btn" onClick={searchNext}> <i className="far fa-chevron-circle-right"></i></span>
                </div>
            :null}
        <Card className="DnD-tree-wrapper">
            <SortableTree
                id="DnD-sortable-tree"
                treeData={treeData}
                onChange={treeData => setTreeData( treeData )}
                getNodeKey={({ node }) => node.id}
                scaffoldBlockPxWidth = {75}
                searchQuery={searchTerm}
                onlyExpandSearchedNodes={true}
                searchMethod={
                    ({node, searchQuery})=> 
                    searchQuery && node.title.toLowerCase().indexOf(searchQuery.toLowerCase())> -1 
                }
                searchFocusOffset={searchFocusIndex}
                searchFinishCallback={(matches)=>{
                        matchHandler(matches)
                    }
                }
                generateNodeProps={(row)=>{
                    const { node } = row;
                    if(activeMatch?.includes(node.id)){
                        return({
                            isSearchFocus:true,
                            buttons:[
                                <button className="module-edit-btn" onClick={()=>history.push(`/p/module/${node.id}`)}>
                                    Edit
                                </button>
                            ]
                        })
                    }
                    return({
                        buttons:[
                            <button className="module-edit-btn" onClick={()=>history.push(`/p/module/${node.id}`)}>
                                Edit
                            </button>
                        ]
                    })
                }}
            />
        </Card> 
        {errors}
        <Button onClick={onClickSave} className="module-save-btn">Save Order</Button>
        
    </Container>
  );
};
