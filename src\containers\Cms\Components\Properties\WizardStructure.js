import React,{useState, useEffect, useCallback} from 'react';
import { Row, Col ,Button } from 'react-bootstrap';

const svgWidth = 50;
const svgHeight = 50;
const svgGap = 1.5;

const WizardStructure = React.forwardRef((props, _) => {
    const {selection, steps} = props;
    const [svg, setSvg] = useState([]);

    const clickHandler = useCallback(item => {
        selection(item);
    },[selection]);

    useEffect(() => {
        let _svg=[];
        for (let i = 1; i <= (steps || 1); i++){
            _svg.push(
                <svg key={`svg-${i}`} className="layout-structure-svg" width={svgWidth} height={svgHeight}>
                    <rect x="0" y="0" width={svgWidth - svgGap} height={svgHeight - svgGap} />
                </svg>
            );
        }    
        setSvg(_svg);
    },[steps, clickHandler]);

    useEffect(() => {   
        return () => {
            setSvg([]);
        }
    }, []);

    return (
        <Row noGutters className="mx-1">
            {steps && +steps>1 &&
                <Col sm="auto" className="d-flex align-items-center p-0">
                    <Button className="btn rounded my-auto me-0" variant="light" href="#!" onClick={() => clickHandler(+steps - 1)}><i className="far fa-minus"/></Button>
                </Col>
            }
            <Col sm="auto" className="d-flex justify-content-center px-3">
                <Row noGutters style={{maxWidth:"300px"}}>
                    {svg.map((item,i) => (
                        <Col sm="auto" key={`layout-structure-${i}`} className="d-flex justify-content-center p-0">
                            {item}
                        </Col>
                    ))}
                </Row>
            </Col>
            {steps && +steps<24 &&
                <Col sm="auto" className="d-flex align-items-center p-0">
                    <Button className="btn rounded my-auto mx-0" variant="light" href="#!" onClick={() => clickHandler((+steps || 1) + 1)}><i className="far fa-plus"/></Button>
                </Col>
            }
        </Row>
    );
});

export default WizardStructure;