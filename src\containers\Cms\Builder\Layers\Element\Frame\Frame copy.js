import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { useSelector } from 'react-redux';
import { Container } from 'react-bootstrap';
import { getPageById } from '../../../../../../utils/cms';

export const Frame = ({children,...props}) => {
    const cmsSelector = useSelector(state => state.cms);
    
    const [loading, setLoading] = useState(true);
    const [contentRef, setContentRef] = useState(null);
    const [customCss, setCustomCss] = useState([]);
    const mountNode = contentRef?.contentWindow?.document?.body;
    

    // get the current custom css    
    useEffect(() => {
        const _getPageById = async id => {
            if (id){
                const css = await getPageById({id: id});
                if (css) setCustomCss(prev => [...prev, {order: 999, content: css}]);
            }
        }
    
        if (cmsSelector?.currentPageProps?.css){
            setLoading(true);
            _getPageById(cmsSelector.currentPageProps.css);
        } 
    }, [cmsSelector?.currentPageProps?.css]);

    /*
    useEffect(() => {
        fs.readFile('../../../../assets/css/scss/pages.scss', 'utf8', (err, data) => {
            if (err) {
                console.error(err);
                return;
            }
            sass.render({
                data,
                outputStyle: 'compressed'
            }, (err, result) => {
                if (err) {
                    console.error(err);
                    return;
                }
                setCustomCss(prev => [...prev, {order: 2, content: result.css.toString()}]);
            });
        });
        fs.readFile('../../../../assets/css/all.min.css', 'utf8', (err, data) => {
            if (err) {
                console.error(err);
                return;
            }
            setCustomCss(prev => [...prev, {order: 1, content: data}]);
        });
    }, []);   
    */

    // add the css to the iframe
    useEffect(() => {
        if (mountNode){
            const style = document.createElement('style');            
            style.innerHTML = `
                @import url('https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css');
                `;
            mountNode.appendChild(style);
            if (customCss){
                customCss.sort((a,b) => a.order - b.order).forEach(css => {
                    const style = document.createElement('style');
                    style.innerHTML = css.content;
                    mountNode.appendChild(style);
                });
                setLoading(false);
            }    
        }
    }, [mountNode, customCss]);

    useEffect(() => {
        console.log(mountNode);
    }, [mountNode]);
    

    useEffect(() => {
        setLoading(false);

        return () => {
            //setLoading(true);
            setCustomCss([]);
            setContentRef(null);
        }
    }, []);


    if (!props.height) props.height = "100%";
    if (!props.width) props.width = "100%";

    return (
        <Container fluid className="frame-container">
            <iframe title={props?.title || "_"} {...props} style={{height: props.height, width: props.width}} ref={contentRef} /*src="../../../../assets/css/scss/includes.html"*/>
                {!loading && createPortal(children, mountNode.querySelector("#content-holder"))}
            </iframe>
        </Container>
    );
}