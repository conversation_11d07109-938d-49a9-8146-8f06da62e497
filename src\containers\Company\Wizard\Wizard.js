import React, { useState, useEffect, useCallback } from 'react';
import { useHist<PERSON>, Link } from "react-router-dom";
import {Container, Col, Row, Breadcrumb, ListGroup, Card, Button} from 'react-bootstrap';
import SubHeader from '../../../components/common/SubHeader';

import Toast from '../../../components/Toast';
import ErrorCatcher from '../../../components/common/ErrorCatcher';

import {Step1, Step2, Step3} from './Steps';

import APICompanies from '../../../api/Companies';
import styles from './Wizard.module.scss';

const Wizard = (props) => {
    const history = useHistory();
    const adminDash = JSON.parse(localStorage.getItem("adminDash"));

    const [step, setStep] = useState(1);
    const [pagePart, setPagePart] = useState(<></>);
    const [values, setValues] = useState({});
    const [submitting, setSubmitting] = useState(false);
    const [errors, setErrors] = useState({});
    const [success, setSuccess] = useState();

    const saveHandler = useCallback(e => {
        const {name, value} = e.target;
        setValues(prev => {
            return {...prev, [name]: value};
        });
    }, []);

    useEffect(() => {
        const _props = {
            values: values,
            change: saveHandler,
            errors: errors,
            setErrors: setErrors,
        }
        switch(step){
            case 3:
                setPagePart(<Step3 {..._props}/>);
                break;
            case 2:
                setPagePart(<Step2 {..._props}/>);
                break;
            case 1:
            default:
                setPagePart(<Step1 {..._props}/>);
                break;
        }
    }, [step, values, errors, saveHandler]);

	useEffect(() => {
        return () => {
        }

	}, []);

    const nextClickHandler = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (step<3) setStep(step+1);
    }

    const prevClickHandler = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (step>1) setStep(step-1);
    }

    // form submission
    const submitHandler = async (e) => {
        e.preventDefault();
        e.stopPropagation();

        setSubmitting(true);
        setSuccess(null);

        let _errors={};
        if (!values.name) {
            _errors.name = "Company name is required";
            setStep(1);
        }
        if (!values.email) {
            _errors.email = "Email is required";
            setStep(1);
        }
        if (!values.subdomain) {
            _errors.subdomain = "Subdomain is required";
            setStep(1);
        }
        if (!values.owner_first_name) {
            _errors.owner_first_name = "First name is required";
            setStep(2);
        }
        if (!values.owner_last_name) {
            _errors.owner_last_name = "Last name is required";
            setStep(2);
        }
        if (!values.owner_email) {
            _errors.owner_email = "Email is required";
            setStep(2);
        }
        if (!values.owner_user_name) {
            _errors.owner_user_name = "User name is required";
            setStep(2);
        }
        if (!values.owner_password) {
            _errors.owner_password = "Password is required";
            setStep(2);
        }

        setErrors(_errors);

        if (Object.keys(_errors).length === 0){
            const formData = new FormData();
            for (let key in values) {
                let _val = values[key];
                if ((Array.isArray(_val) || typeof _val === "object") && !(_val instanceof File)) _val = JSON.stringify(_val);
                formData.append(key, _val);
            }
            if (props.company_id) formData.append("id", props.company_id);
            //const formDataObj = Object.fromEntries(formData.entries());

            try{
                const res = await APICompanies.wizard(formData);
                setSubmitting(false);
                if (res.errors) {                    
                    let _errs = {};
                    for (const field in res.errors) {
                        _errs[field] = res.errors[field];
                        if (field.startsWith("owner_")) setStep(2);
                        else setStep(1);                        
                    }
                    setErrors(_errs);
                }
                else if (res.data){
                    setSuccess(<Toast>Company created successfully!</Toast>);
                    history.push(props.referer || "/p/companies/dashboard");
                }
            } catch (err) {
                console.log(err);
                setSubmitting(false);
                setErrors({_general: err.message});
            }
        } else setSubmitting(false);
    };


    return (
        <Container fluid>
            {success}
            <SubHeader items={[
                {linkAs:Link,linkProps:{to:"/p/home"},text:"Home"},
                {linkAs:Link,linkProps:{to:"/p/companies/dashboard"},text:"Company Dashboard"},
                {text:"Company Wizard"}
            ]} />
            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <Row>
                            <Col sm="auto" className="order-1 order-lg-2">
                                <ListGroup className="profileMenu" variant="flush">
                                    <ListGroup.Item action className={step===1?"active":null} onClick={(e)=>setStep(1)}>
                                        <i className="far fa-store-alt"></i> Company Info
                                    </ListGroup.Item>
                                    <ListGroup.Item action className={step===2?"active":""} onClick={(e)=>setStep(2)}>
                                        <i className="far fa-user"></i> Owner Info
                                    </ListGroup.Item>
                                    <ListGroup.Item action className={step===3?"active":""} onClick={(e)=>setStep(3)}>
                                        <i className="far fa-ballot-check"></i> Features
                                    </ListGroup.Item>
                                </ListGroup>
                            </Col>
                            <Col className="order-2 order-lg-1">
                                {pagePart /*this is where the magic is happening :-O */ }
                            </Col>
                        </Row>
                    </Card>
                    <Row>
                        <Col>
                            <div className={styles.buttons}>
                                <Button variant="primary" disabled={submitting || step<=1} className={`${submitting?" submitting":""}`} onClick={prevClickHandler}>Previous</Button>
                                <Button variant="primary" disabled={submitting || step>=3} className={`${submitting?" submitting":""}`} onClick={nextClickHandler}>Next</Button>
                                <Button variant="primary" disabled={submitting} className={`${submitting?" submitting":""}`} onClick={submitHandler}>Save</Button>
                            </div>
                        </Col>
                    </Row>
                </Col>
            </Row>
            {errors._general && <ErrorCatcher error={errors._general} />}
        </Container>
    );
}

export default Wizard;