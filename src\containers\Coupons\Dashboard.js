import React, { useState, useEffect } from 'react';
import { useH<PERSON><PERSON>, Link } from "react-router-dom";
import Container from 'react-bootstrap/Container';
import { Col, Row, DropdownButton, Button, Breadcrumb, Card } from 'react-bootstrap';
import SubHeader from '../../components/common/SubHeader';
import Stack from '../../components/common/Stack';
import Table from '../../components/common/Table';
import Coupons from '../../api/Coupons';

import './Coupon.scss';

const Dashboard = (props) => {

    let history = useHistory();

    const [loading,setLoading]=useState(true);
    const [coupons,setCoupons]=useState([]);

    // first load, get companies from api
    useEffect(() => {
        let mounted = true;

        setLoading(true);
        Coupons.get()
        .then(response => {
            if(mounted) setCoupons(response.data?.map( (cmp, index) => {
                // format some data
                cmp.discount_type = cmp.discount_type===1 ? 'Fixed' : 'Percentage';
                cmp.status = cmp.status===1 ? 'Active' : 'Inactive';
                return cmp;
            }));
            setLoading(false);
        }).catch(e => console.error(e));

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
        }        
    },[]);

    const columns = React.useMemo(() => [{
        id: 'table',
        columns: [
            {
                Header: 'Name',
                id: 'name',
                accessor: 'name',
                className: "align-middle",
            },
            {
                Header: 'Coupon Code',
                id: 'coupon_code',
                accessor: 'coupon_code',
                className: "align-middle",
            },
            {
                Header: 'Type',
                id: 'discount_type',
                accessor: 'discount_type',
                className: "align-middle",
            },
            {
                Header: 'Amount',
                id: 'discount_amount',
                accessor: 'discount_amount',
                className: "align-middle",
            },
            {
                Header: 'Status',
                id: 'status',
                accessor: 'status',
                className: "align-middle",
            },
            {
                id: 'id',
                url:"/discount/edit/:id",
                show:false,
            },
        ],
    }],[]);

    return (
        <Container fluid>
            
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Discount Dashboard" }
            ]} />
            <Row className="body">
                <Col>
                    <Card className={`content-card ${loading?" loading":""}`}>
                        <Stack direction="horizontal" gap={2}>
                            <h4 className="tm-1 section-title order-2 order-lg-1">Discount Dashboard</h4>
                            <div className="ms-sm-auto order-1 order-lg-2">
                                <Button variant="primary" onClick={() => history.push("/p/discount/create")}>New Discount</Button>
                            </div>
                        </Stack>
                        <hr/>
                        <Table columns={columns} data={coupons} />
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Dashboard;