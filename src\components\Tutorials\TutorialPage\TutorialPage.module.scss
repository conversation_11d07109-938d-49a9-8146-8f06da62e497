@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';

.tutorials-wrapper, .accordion-div{
    max-width: 700px;
    button{
        min-width: 350px;
        width: 100%;
        padding: 5px 0;
        border-radius: $card-border-radius;
        background-color: transparent;
        // color: $primary-inverse-color;
        border-left: 15px;
        border-color: $primary-color;
        border-style: solid;
        border-radius: 4px 4px 0 0;
        margin-top: 4px;
    }
    table{
        width: 100%;
    }
    .each-collapse{
        padding: 10px;
        border-left: 15px;
        border-right: 0;
        border-top: 0;
        border-bottom:0;
        border-color: $primary-color;
        border-style: solid;
        border-radius: 0 0 4px 4px;
        
    }
    p{
        padding-top:2px;
    }
    ul{
        margin-left: .5rem;
        li{
            margin-bottom: .5rem;
            list-style-type: disc;
        }
    }
}