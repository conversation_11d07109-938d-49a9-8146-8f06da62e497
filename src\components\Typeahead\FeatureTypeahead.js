import React, {useState, useCallback} from 'react'

import { Typeahead } from './Typeahead'
import Permissions from '../../api/Permissions'

const FeatureTypeahead = ({async=false, multiple=false, paginated=false, ...props}) => {
    
    const [ loading, setLoading ] = useState("Loading Features...");

    const makeRequest = useCallback(async () => {
        let responseObject;
        try{
            let response = await Permissions.Features.getAll();
            responseObject={
                data: response.data || null,
                errors: response.errors || null
            }
            setLoading();
        }catch(ex){
            console.error(ex);
            setLoading("Unable to load features")
        }
        return responseObject;
    },[]);
    
    return (
        <Typeahead 
            {...props}
            id={"feature-search"}
            makeRequest={makeRequest}
            async={async}
            paginated={paginated}
            multiple={multiple}
            placeholder={loading ? `${loading}` : "Search Features"}
        />
    )
}

export default FeatureTypeahead