import React, { useState, useEffect, useCallback } from 'react';
import { Container, Card, Accordion } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';

import { authUserHasModuleAccessMany } from '../../../utils/auth';
import styles from './TutorialPage.module.scss';
import { Subsections } from './TutorialPageComponents/Subsections';
import SubHeader from '../../common/SubHeader/SubHeader';
import DetailSubpage from '../../common/DetailSubPage';

import TutorialsJson from '../Tutorials.json';

export const TutorialPage=()=>{

    const location = useLocation();
    const currentTab = location?.hash
    const [modulePermission, setModulePermission]=useState([]);
    const [tutorialSubnav, setTutorialSubnav]=useState([]);

    //the subpage below already handles module permissions so long as we supply the module ids
    const createSubnav=useCallback(()=>{
        let tutorialObjects = []
        const topics = Object.keys(TutorialsJson);
        for(let i = 0; i < topics.length; i ++){
            let originalData = TutorialsJson[topics[i]];
            let item = {};
            item.id = i;
            item.moduleId = originalData?.content?.module_id;
            item.displayName = topics[i];
            item.icon = originalData.icon;
            item.hash=topics[i]?.replace(/\s/g,'')
            item.component = <Subsections topic={originalData} />
            tutorialObjects.push(item);
        }
        setTutorialSubnav(tutorialObjects);
    },[])


    useEffect(()=>{
        createSubnav();
        
    },[createSubnav])

    
    return(
        <Container fluid>
            <SubHeader 
                items={[
                    {linkAs: Link, linkProps: {to: "/p/home"}, text: "Home" },
                    {text: "Tutorials"}
                ]}
            />
            <Card className="content-card">
                <h4 className="section-title">
                    Tutorials
                </h4>
                {tutorialSubnav?.length > 1 &&
                    <DetailSubpage 
                        allTabs={tutorialSubnav}
                        itemId={null}
                        loading={false}
                        currentTab={currentTab || "UserProfile"}
                    />
                }
            </Card>
        </Container>
    )
}