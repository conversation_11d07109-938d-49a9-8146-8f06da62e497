import React, {useState, useEffect} from 'react';
import {useSelector} from 'react-redux';


export const Step5 = (props) => {
    const user = useSelector(state => state.auth.user.profile);

    const [loading, setLoading] = useState(false);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [customFields, setCustomFields] = useState();

    useEffect(() => {
        if (props.stepValues?.selectedUsers) setSelectedUsers(props.stepValues.selectedUsers);
        else setSelectedUsers([{id: user.id, first_name: user.first_name, last_name: user.last_name}]);
        if (props.stepValues?.customFields) setCustomFields(props.stepValues.customFields);
    }, [user, props.stepValues]);
    
    useEffect(() => {
        return () => {
            setLoading(false);
            setSelectedUsers([]);
            setCustomFields(null);
        }
    }, []);

    if (loading) return (<p>Loading...</p>);

    return (
        <div>
            <p>
                <label className="form-label">Registering for this event:</label>
            </p>
            {selectedUsers.map((user, i) => (
                <div key={`selected-user-${user.id}-${i}`}>
                    <p>
                        <span className="bold">{user.first_name} {user.last_name} {user?.group_member_role}</span>
                        <br/>
                        {customFields && customFields?.[user.id] && customFields[user.id].map((field, i) => (
                            <div key={`custom-f-${i}-${user.id}-${field.custom_field_id}`}>
                                {field.custom_field_label}: {field.label}
                                <br/>
                            </div>
                        ))}
                        {props.default_variant_price && <>Fee: ${props.default_variant_price}</>}
                    </p>
                    <hr/>
                </div>
            ))}
            {props.default_variant_price &&
                <p className="bold">Subtotal: ${(+props.default_variant_price*selectedUsers.length).toFixed(2)}</p>
            }
        </div>
    );
}