import React, { useState, useEffect } from 'react';
import { useHistory } from "react-router-dom";
import {Container,Col,Row,Form,InputGroup,Button} from 'react-bootstrap';

import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import Toast from '../../../../components/Toast';

import {toNormalCase,sassToJson} from '../../../../utils/cms';
import APICms from '../../../../api/Cms';

const Styles = (props) => {
    let history = useHistory();

    const [theme, setTheme] = useState();
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

	useEffect(() => {
        const _getTheme=async () => {
            try {
                const res=await APICms.themes.get({id:props.theme_id});
                if (res.data && mounted) {
                    setTheme(res.data[0].content);
                }
            } catch (e){
                console.error(e);
            }
        }

        let mounted = true;
        if (props.theme) setTheme(props.theme);
        else _getTheme();

        return () => {
            mounted = false;
        }

	}, [props.theme_id, props.theme]);

    useEffect(() => {
        return () => {
            setTheme(null);
        }
    }, []);

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            const formDataObj = Object.fromEntries(formData.entries());
            
            let data={};
            if (props.theme_id) data.id=props.theme_id;
            data.content=JSON.stringify(sassToJson(formDataObj));

            const response=await APICms.themes.create(data);
            if (!response.errors) {
                setSubmitting(false);
                setValidated(false);
                setSuccess(<Toast>Theme saved successfully!</Toast>);
                history.push(props.referer || "/p/themes/dashboard"); // pushes to profile again to avoid resubmission
            } else { // api returned errors
                setSubmitting(false);
                setError(<ErrorCatcher error={response.errors} />);
            } 
        } else setSubmitting(false);
    };


    return (
        <Container fluid>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Row>
                    {theme && theme?.variables.length>0 && theme?.variables.map((variable, i) => (
                        <Col sm="12" lg="6">
                            <InputGroup className="mb-3" controlId={variable.name}>
                                <InputGroup.Prepend>
                                    <InputGroup.Text id={variable.name}>{toNormalCase(variable.name)}</InputGroup.Text>
                                </InputGroup.Prepend>
                                <Form.Control type="text" aria-describedby={variable.name} name={variable.name} defaultValue={variable?.value || variable?.compiledValue} />
                            </InputGroup>
                        </Col>
                    ))}
                </Row>
                <Row>
                    <Col sm="12" className="mt-4 mb-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={`${submitting?" submitting":""}`}>Save</Button>
                    </Col>
                </Row>
            </Form>
            {error}
        </Container>
    );
}

export default Styles;