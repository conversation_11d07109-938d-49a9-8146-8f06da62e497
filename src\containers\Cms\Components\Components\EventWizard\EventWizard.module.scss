@import '../../../../../assets/css/scss/variables';
@import '../../../../../assets/css/scss/themes';

.wrapper{
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    overflow: auto;
    position: relative;

    >div:first-child{
        //padding-bottom: calc($modal-body-padding + 70px);
    }
    
    .toolbar{
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        background-color: $modal-body-background-color;
        bottom:0;
        left:0;
        width: 100%;
        padding: 1rem;
        padding-bottom:0.5rem;
        margin-top: calc($modal-border-radius * -1);
    }
}