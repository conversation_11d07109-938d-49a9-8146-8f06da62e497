import React, {useState, useEffect, useCallback} from 'react';
import { Row, Col } from 'react-bootstrap';

import { StepExtra } from './StepExtra';
import SignUp from '../SignUp';

import styles from './Onboarding.module.scss';

export const Step1 =  props => {
    const {click, disableNext, saveStepValues, stepValues} = props;
    const [body, setBody] = useState();

    const setUserInfo = useCallback((user) => {
        if (user){
            disableNext(false);
            saveStepValues({user: user});
            //click({preventDefault: () => {}, stopPropagation: () => {},}, 2);
        } else disableNext(true);
    }, [saveStepValues, disableNext]);

    const getUserInfo = useCallback((fields) => {
        if (fields){
            if (fields.errors){
                if (fields.errors.filter(a=> a.field === "email" || a.field === "username")){
                    disableNext(true);
                    setBody(<StepExtra value={1} callback={setUserInfo}/>);
                }
            } else {
                setUserInfo({fields});
                setBody(<StepExtra value={3} extra_text={[fields.first_name]} callback={setUserInfo}/>);
            }
        }
    }, [disableNext, setUserInfo]);

    useEffect(() => {
        disableNext(true);
    }, [disableNext]);

    useEffect(() => {
        if (stepValues?.user){
            setBody(<StepExtra value={3} extra_text={[stepValues.user.first_name]} callback={setUserInfo}/>);
            disableNext(false);
        }
    }, [stepValues, disableNext , setUserInfo]);

    useEffect(() => {
        return () => {
            setBody(null);
        }
    }, []);    

    return (
        <>
            <div className={styles["step-description"]}>
                <h5>Step 1</h5>
                <h4>We're so happy to see you!</h4>
                <p>To get you started we need some information from you. Please fill it to continue.</p>
            </div>
            {body && <>{body}</>}
            {!body && <SignUp {...props} forceMobile onboarding callback={getUserInfo} />}
        </>
    );
}