/*eslint-disable*/
// let baseUrl = 'http://portal-qa.impactathleticsny.com/p/'
// let baseUrl = 'http://portal-dev.impactathleticsny.com/p/'
let baseUrl = 'http://localhost:3000/p/'
let staffUserName = Cypress.env('impact_staff_user')
let patronUserName = Cypress.env('impact_patron_user')
let password = Cypress.env('login_password')

describe('Patrons', {scrollBehavior: "center"}, ()=>{
    let local;

    before('login as patron', ()=>{
        // we should probably have this pulled into a function somewhere as loginPatron, as this is going to be used on every e2e test
        // same with loginStaff, loginCompanyAdmin
        cy.visit(baseUrl)
        cy.get('[data-cy="login-username').type(patronUserName)
        cy.get('[data-cy="login-password"]').type(password)
        cy.get('[data-cy="login-submit"]').click()

        cy.intercept('POST', '/api/user/login').as('loginCall')
        cy.intercept('GET', "/api/user/menu").as('getMenus')
        // cy.wait('@getMenus')

        cy.intercept('GET', "/api/user/user/**").as('getUserUser');
        cy.wait('@getUserUser').then(()=>{
            local = (localStorage.getItem('user'))
        })
    })

    //can only set the user after sure that the calls are all done
    it("will make sure the user is a patron and only a patron",()=>{
        let pLocal = JSON.parse(local)
        expect(local).to.include("roles")
        expect(pLocal.roles)
            .to.have.lengthOf(1)
            .deep.equal([{id: 7, name: "Patron"}])
    });

// ***Will need to make a new test for sidenav that takes permissions into account ***

    // it('will have correct items in menu', ()=>{
    //     cy.get('#side-bar-menu')
    //         .children().each($child)
    //     cy.get("[data-cy='menu-item']").contains('Upcoming Events').should('exist')
    //     cy.get("[data-cy='menu-item']").contains('Profile').should('exist')
    //     cy.get("[data-cy='menu-item']").contains('My Services').should('exist')
    //     cy.get("[data-cy='menu-item']").contains('My Transactions').should('exist')
    //     cy.get("[data-cy='menu-item']").contains('Sign Out').should('exist')
        
    //     // registers do not exist
    //     cy.get("[data-cy='menu-item']").contains('Registers').should('not.exist')
    //     cy.get('#menu-item-39').should('not.exist')
    // })

    it('will show correct header for full screen size', ()=>{
        cy.get('[data-cy="header-mobile-logo-img"]').should('not.be.visible')
        cy.get('[data-cy="header-fullsize-logo-img"]').should('be.visible')
        cy.get('[data-cy="header-shopping-cart"]').should('be.visible')
        cy.get('[data-cy="header-profile-img"]').should('be.visible')
        cy.get('[data-cy="header-profile-name"]').should('be.visible')
        cy.get('[data-cy="header-sign-out"]').should('be.visible')

        cy.get('[data-cy="header-menu-open-btn"]').should('not.be.visible')
    })

    it('will show correct header for mobile screen size', ()=>{
        cy.viewport('iphone-6')
        cy.get('[data-cy="header-mobile-logo-img"]').should('be.visible')
        cy.get('[data-cy="header-fullsize-logo-img"]').should('not.be.visible')
        cy.get('[data-cy="header-shopping-cart"]').should('be.visible')
        cy.get('[data-cy="header-profile-img"]').should('be.visible')
        cy.get('[data-cy="header-profile-name"]').should('not.be.visible')
        cy.get('[data-cy="header-sign-out"]').should('be.visible')

        cy.get('[data-cy="header-menu-open-btn"]').click()
        cy.get('#side-bar-menu').should('be.visible')
        // BUG: cypress is not seeing the contents of the menu in mobile view!
        // cy.get('#menu-item-2').should('be.visible')
        // open menu takes up full screen size
        cy.get('[data-cy="header-menu-close-btn"]').click()
        cy.get('#side-bar-menu').should('not.be.visible')
    })

    it('will show correct header for tablet screen size', ()=>{
        cy.viewport('ipad-2')
        cy.get('[data-cy="header-mobile-logo-img"]').should('not.be.visible')
        cy.get('[data-cy="header-fullsize-logo-img"]').should('be.visible')
        cy.get('[data-cy="header-shopping-cart"]').should('be.visible')
        cy.get('[data-cy="header-profile-img"]').should('be.visible')
        cy.get('[data-cy="header-profile-name"]').should('not.be.visible')
        cy.get('[data-cy="header-sign-out"]').should('be.visible')
        cy.wait(250)
        cy.get('[data-cy="header-menu-close-btn"]').click()
        cy.get('#side-bar-menu').should('not.be.visible')
    })

    // it('will close menu when shopping cart is clicked on', ()=>{
    //     cy.viewport('ipad-2')
    //     cy.get('[data-cy="header-menu-open-btn"]').click()
    //     cy.get('#side-bar-menu').should('be.visible')
    //     cy.get('[data-cy="header-shopping-cart"]').click()
    //     cy.get('#side-bar-menu').should('not.be.visible')
    // })

    // it('will close menu when profile is clicked on', ()=>{
    //     cy.viewport('ipad-2')
    //     cy.get('[data-cy="header-menu-open-btn"]').click()
    //     cy.get('#side-bar-menu').should('be.visible')
    //     cy.get('[data-cy="header-profile-img"]').click()
    //     cy.get('#side-bar-menu').should('not.be.visible')
    // })
})

describe('Staff',()=>{
    let local;
    let user;

    before(()=>{
        cy.fixture('User/userResponse.json').then((data)=>{
            user = data;
        });
    })

    beforeEach('login as staff', ()=>{
        // we should probably have this pulled into a function somewhere as loginPatron, as this is going to be used on every e2e test
        // same with loginStaff, loginCompanyAdmin
        cy.visit(baseUrl)
        cy.get('[data-cy="login-username').type(staffUserName)
        cy.get('[data-cy="login-password"]').type(password)
        cy.get('[data-cy="login-submit"]').click()

        cy.intercept('POST', '/api/user/login').as('loginCall')
        cy.intercept('GET', "/api/user/menu").as('getMenus')
        // cy.wait('@getMenus')

        cy.intercept('GET', "/api/user/user/**").as('getUserUser');
        cy.wait('@getUserUser').then(()=>{
            local = (localStorage.getItem('user'))
        })
    })

    //can only set the user after sure that the calls are all done
    it("will make sure the user's highest role is staff",()=>{
        let pLocal = JSON.parse(local)
        expect(local).to.include("roles")
        let roleIds = pLocal.roles.map(role => {return role.id})
        //changed to account for a user that has more than one role as many of the Impact staff do.  
        let maxRole = Math.min(...roleIds)
        expect(maxRole).to.equal(5);
    });

    // it('will have correct items in menu', ()=>{
    //     cy.get('.menu-item').contains('Upcoming Events').should('exist')
    //     cy.get('.menu-item').contains('My Profile').should('exist')
    //     cy.get('.menu-item').contains('My Services').should('exist')
    //     cy.get('.menu-item').contains('My Transactions').should('exist')
    //     cy.get('.menu-item').contains('Sign Out').should('exist')

    //     cy.get('#menu-item-3').click()  // Users
    //     cy.get('#menu-item-4').should('be.visible') // User Dashboard
    //     cy.get('#menu-item-14').should('be.visible') // Groups
    //     cy.get('.menu-item').contains('Products').click()
    //     cy.get('#menu-item-13').should('be.visible') // Products Dashboard
    //     cy.get('#menu-item-21').should('be.visible') // Categories
    //     cy.get('#menu-item-15').contains('Events').click()
    //     cy.get('#menu-item-24').should('be.visible') // Add Event
    //     cy.get('#menu-item-25').should('be.visible') // Event Management
    //     cy.get('#menu-item-32').should('be.visible') // Calendar
    //     cy.get('#menu-item-51').contains('Services').click()
    //     cy.get('#menu-item-52').should('be.visible') // Services Dashboard

    //     cy.get('.menu-item').contains('Registers').click()
    //     cy.get('#side-bar-menu').scrollTo('0%', '100%', {ensureScrollable: false})  // the menu is usually only scrollable on hover without turning this off
    //     cy.get('#menu-item-38').should('be.visible')  // Smack Shack
    //     cy.get('#menu-item-39').should('be.visible')  // Front Desk Impact-03
    //     cy.get('#menu-item-40').should('be.visible')  // Front Desk Impact-04
    //     cy.get('#menu-item-41').should('be.visible')  // Courtside Grille Impact-07
    //     cy.get('#menu-item-42').should('be.visible')  // Courtside Grille Impact-08
    // })

})