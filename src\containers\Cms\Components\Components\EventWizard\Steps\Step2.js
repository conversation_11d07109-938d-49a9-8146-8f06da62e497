import React, {useState, useEffect} from 'react';
import {useSelector} from 'react-redux';
import {Tabs, Tab} from 'react-bootstrap';

import Login from '../../Login';
import SignUp from '../../SignUp';

export const Step2 = (props) => {
    const {click, disableNext, disableBack} = props;
    const user = useSelector(state => state.auth.user);

    const [showRegister, setShowRegister] = useState(false);

    useEffect(() => {
        if (user?.profile?.id){
            disableBack(false);
            disableNext(false);
            click({
                preventDefault: () => {},
                stopPropagation: () => {},
            }, props.referrerStep === "back" ? 1 : 3);
        } else {
            disableBack(true);
            disableNext(true);
        }
    }, [user?.profile?.id, click, props.referrerStep, disableBack, disableNext]);

    useEffect(() => {
        return () => {
            setShowRegister(false);
        }
    }, []);

    if (user?.profile?.id) return null;

    return (
        <Tabs defaultActiveKey="login" id="step2-login" className="tabs">
            <Tab eventKey="login" title="Sign In">
                <Login {...props} />                        
            </Tab>
            <Tab eventKey="register" title="Register">
                <p className="bolder">Note: You are now creating a parent account, if enrolling in an event for your child, you will add your child's account after registering.</p>
                <SignUp {...props} forceMobile /> 
            </Tab>
        </Tabs>
    );
}