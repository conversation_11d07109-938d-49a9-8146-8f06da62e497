import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Card, Container, Breadcrumb, Modal } from 'react-bootstrap';
import { Link } from 'react-router-dom'
import SubHeader from '../../../components/common/SubHeader';
import { CompanyTypeahead } from '../../../components/Typeahead/CompanyTypeahead';
import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';
import Config from '../../../api/Config';
import ConfigParams from '../ConfigComponents/ConfigParams';
import CreateConfig from '../ConfigComponents/CreateConfig';
import ViewConfigTypes from '../ConfigComponents/ViewConfigTypes';

import '../Configs.scss'

export const SBConfig = () => {

    const mountedRef = useRef(false);
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();

    const [activeConfig, setActiveConfig] = useState();
    const [activeParameters, setActiveParameters] = useState();
    const [configTypes, setConfigTypes] = useState([]);
    const [showDelete, setShowDelete]=useState(false);
    const [showView, setShowView]=useState(false);
    const [showEdit, setShowEdit]=useState(false);
    const [showCreate, setShowCreate]=useState(false);
    const [highestSortOrder, setHighestSortOrder]=useState(0);
    const [selectedCompany, setSelectedCompany]=useState([]);
    const adminDash = useRef(JSON.parse(localStorage.getItem("adminDash")));

    const getConfigTypes = useCallback(async()=>{
        try{
            let response = await Config.ConfigTypes.get();
            if(response.status === 200){
                setConfigTypes(response.data);
                findHighestSortOrder(response.data);
                setLoading(false)
            }
            else{
                setError(setErrorCatcher(response.errors, false));
                setLoading(false)
            }
        }catch(ex){
            setLoading(false);
            console.error(ex);
        }
    },[])

    useEffect(()=>{
        mountedRef.current = true;

        getConfigTypes();

        return mountedRef.current = false;
    },[getConfigTypes])

    useEffect(()=>{
        if(activeConfig){
            let objectEntries = Object.entries(activeConfig.config_params);
            setActiveParameters(objectEntries)
        }
    },[activeConfig]);

    const handleDelete =(config)=>{
        setActiveConfig(config);
        setShowDelete(true)
    }

    const yesDelete = async()=>{
        try{
            let response = await Config.ConfigTypes.delete({id: activeConfig.id});
            if(response.status === 200){
                setSuccessToast(`You successfully deleted ${activeConfig.name}`);
                setLoading(true);
                getConfigTypes();
                handleHide();
            }
        }catch(ex){console.error(ex)}
    }

    const handleView=(config)=>{
        setActiveConfig(config);
        setShowView(true)
    }

    const handleEdit=(config)=>{
        setActiveConfig(config);
        setShowEdit(true);
    }

    const handleHide=()=>{
        setActiveConfig();
        if(showDelete) setShowDelete(false);
        if(showView) setShowView(false)
        if(showEdit) setShowEdit(false);
        if(showCreate) setShowCreate(false)
        if(showCreate || showEdit) getConfigTypes();
    }

    const findHighestSortOrder=(data)=>{
        let sortIds=data.map(config=>config.sort_order);
        let highest = Math.max(...sortIds);
        setHighestSortOrder(highest+1);
    }
    // create array of breadcrumbs
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" }
    ];

    // if adminDash.current is true, add the permission dashboard breadcrumb
    if (adminDash.current) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" })
    }

    // add the current page to the breadcrumbs
    breadcrumbs.push({ text: "Configs" });

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} />
        
            <Card className="content-card">
                <div className="section-btn-header">
                    <h4 className="section-title">
                        Configs
                    </h4>
                    <button onClick={()=>setShowCreate(true)}>Create New Config Type</button>
                </div>
                <div className="sb-config-wrapper">
                    <div>
                        <p>
                            Select a company to view the company configs.
                        </p>
                        <p className="select-company">
                            <CompanyTypeahead 
                                passSelection={(selection)=>setSelectedCompany(selection)} 
                                multiple={false}
                            />
                            <Link to={{
                                pathname: '/p/config/owner',
                                search: `?company=${selectedCompany[0]?.id}`
                            }}>
                                <button
                                    className="btn-basic"
                                    disabled={selectedCompany.length === 0}
                                >
                                    Edit Company Config
                                </button>
                            </Link>
                        </p>
                    </div>
                    <h5>
                        Config Types
                    </h5>
                    {loading ?
                        "...loading"
                        :
                        <ViewConfigTypes 
                            configTypes={configTypes} 
                            handleDelete={handleDelete}
                            handleEdit={handleEdit}
                            handleView={handleView}
                        />
                    }
                    
                    <Modal size="lg" show={showDelete || showView || showCreate || showEdit} onHide={handleHide}>
                        <Modal.Header closeButton>
                            {showDelete && 
                                <span>
                                    Delete Config - {activeConfig?.name}
                                </span>
                            }
                            {showView && 
                                <span>
                                    View Config - {activeConfig?.name}
                                </span>
                            }    
                        </Modal.Header>
                        <Modal.Body>
                            {showDelete &&
                                <div className="config-delete-modal">
                                    Are you sure you want to delete this config?
                                    <p>
                                        <button onClick={yesDelete}>Yes</button>
                                        <button onClick={handleHide}>No</button>
                                    </p>
                                </div>
                            }
                            {showView && activeParameters &&
                                <div className="config-edit-modal">
                                    <div className="border-box">
                                        <h5>
                                            Basic Info
                                        </h5>
                                        <div className="two-col">
                                            <p>
                                                Name 
                                                <br />
                                                Id 
                                                <br />
                                                Active 
                                                <br />
                                                Sort Order 
                                            </p>
                                            <p>
                                                {activeConfig?.name}
                                                <br />
                                                {activeConfig?.id}
                                                <br />
                                                {activeConfig?.is_active ===1 ? "Yes" : "No"}
                                                <br />
                                                {activeConfig?.sort_order}        
                                            </p>

                                        </div>
                                    </div>
                                    <div className="border-box">
                                        <h5>
                                            Parameters
                                        </h5>
                                        {activeParameters.map((parameters)=>(
                                            <div className="view-config-parameters">
                                                <ConfigParams param={parameters} />
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            }
                            {(showCreate || (activeConfig && showEdit)) &&
                                <CreateConfig 
                                    activeConfig={activeConfig}
                                    setSuccess={setSuccess}
                                    setError={setError}
                                    highestSortOrder={highestSortOrder}
                                    handleHide={handleHide}
                                />
                            }
                        </Modal.Body>
                    </Modal>
                </div>
            </Card>
        </Container>
    )
}