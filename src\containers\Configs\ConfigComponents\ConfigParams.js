import React, { useState } from 'react'

import Tooltip from '../../../components/common/Tooltip'

import './ConfigComponents.scss';

const ConfigParams = ({view=true, param, fullConfig, configType, ...props}) => {

    const [visibility, setVisibility] = useState(false);

    console.log(param, "param")
    console.log(configType, "configType")
    
    return (
        // <>
        // </>
        <div>
            {/* This is to view an already defined type */}
            {view &&
                <div className="view-config-wrapper">
                    <h6>
                        <span>
                            {/* in both instances, param[0] should be the config parameter name (i.e api_key, password, etc) */}
                            {param[0]}  
                        </span>
                        <span>
                            {/* if param not an object, it's coming from the view where configs are already defined and they need to be displayed */}
                            {(typeof param[1]==="string" || typeof param[1]==="number") && 
                                <>
                                    {(param[0].includes("password")
                                    || param[0].includes("key")
                                    || param[0].includes("token")
                                    || param[0].includes("id") ) 
                                    && param[1].length > 0
                                    ?
                                        <>
                                            {visibility ? 
                                                <>
                                                    <span>
                                                         -- {param[1]} {" "}
                                                    </span>
                                                    <span onClick={()=>setVisibility(!visibility)}>
                                                        <i className="far fa-eye-slash" /> 
                                                    </span>
                                                </>
                                                    
                                            :
                                                <>
                                                    <span>
                                                        <i>
                                                            -- hidden text {" "}
                                                        </i>
                                                    </span>
                                                    <span onClick={()=>setVisibility(!visibility)}>
                                                        <i className="far fa-eye" />
                                                    </span>
                                                </>
                                            }
                                        </>
                                    :
                                        ` -- ${param[1]}`
                                    }
                                </>
                            }
                            {typeof param[1]==="boolean" && ` -- ${param[1] ? "Yes" : "No"}`}
                        </span>
                    </h6>
                    <div className="two-col">        
                        <p>
                            Type
                            <br />
                            Required
                            <br />
                            Description
                        </p>
                        <p>
                            {/* if it's coming from the config types, it has its own object to display data, otherwise we need to pull it from the config type */}
                            {param[1]?.type ?
                                param[1]?.type
                            :
                                configType && configType[0]?.config_params[param[0]]?.type
                            }
                            <br />
                            {param[1]?.hasOwnProperty("required") ?
                                param[1]?.required ? "Yes" : "No"
                            :
                                configType && configType[0]?.config_params[param[0]]?.required ? "Yes" : "No"
                            }
                            <br />
                            {param[1]?.hasOwnProperty("description") ? 
                                param[1]?.description ? param[1]?.description : "No Description"
                            :
                                configType && configType[0]?.config_params[param[0]]?.description ? 
                                    configType[0]?.config_params[param[0]]?.description
                                : 
                                    "No Description"
                            }
                        </p>
                    </div>
                </div>
            }
            {/* This is to edit the values of an already defined type */}
            {!view &&
                <div className="edit-config-wrapper">
                    <h6>
                        {param[0]}
                        {" "}
                        {configType[0]?.config_params[param[0]]?.required ? <span className="required-star">*</span> : null}
                    </h6>
                    {configType[0]?.config_params[param[0]]?.type === "bool" &&
                        <>
                            <select name={param[0]}>
                                <option value={true} selected={param[1] === "true" || param[1]===true ? true: false}>Yes</option>
                                <option value={false} selected={param[1] === "false" || param[1]===false ? true: false}>No</option>
                            </select>
                        </>
                    }
        
                    {configType[0]?.config_params[param[0]]?.type === "text" &&
                        <>
                            <input 
                                required={configType[0]?.config_params[param[0]]?.required}
                                name={param[0]}
                                defaultValue={typeof param[1] ==="string" || typeof param[1] ==="number" ? param[1] :  ""}
                                autoComplete="off"
                                type={
                                    ( param[0].includes("password")
                                    || param[0].includes("key")
                                    || param[0].includes("token") 
                                    || param[0].includes("id"))
                                    && !visibility ?
                                        "password"
                                        :
                                        "text"
                                }
                            />
                            {param[0].includes("password") 
                            || param[0].includes("key") 
                            || param[0].includes("token") 
                            || param[0].includes("id") ?
                                <span onClick={()=>setVisibility(!visibility)}>
                                    {visibility ? 
                                        <i className="far fa-eye-slash" /> 
                                    : 
                                        <i className="far fa-eye" />
                                    }
                                </span>
                                :
                                ""
                            }
                        </>
                    }
                    {param[1]?.description &&
                        <Tooltip
                            direction="top"
                            text={configType[0]?.config_params[param[0]]?.description}
                            width="200px"
                            height="auto"
                        >
                            <span>
                                <i className="far fa-question-circle"></i>
                            </span>
                        </Tooltip>
                    }
                </div>
            }
        </div>
    )
}

export default ConfigParams