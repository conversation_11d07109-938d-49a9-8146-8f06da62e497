import React from 'react'
import Datepicker from 'react-datepicker'
import EventTypesTypeahead from '../../../components/Typeahead/EventTypesTypeahead';
import EventStatusTypeahead from '../../../components/Typeahead/EventStatusTypeahead';
import { LocationTypeahead } from '../../../components/Typeahead';

export const SearchFilters = ({
    styles, 
    rangeStart, 
    handleDate, 
    rangeEnd, 
    dateRange, 
    handleDateRange, 
    testNumeric, 
    handleSearchFilters, 
    searchRef, 
    handleIsChild, 
    setSelectedEventTypes, 
    setSelectedStatus
}) => {
    return (
        <div className={styles["search-filters"]}>
            <div className={styles["each-input"]}>
                <label htmlFor="date-picker">Select New Date</label>
                <Datepicker 
                    name="date-picker"
                    placeholderText="Change Week"
                    dateFormat="MM/dd/yyyy"
                    minDate={new Date('01/01/2020')}
                    showMonthDropdown
                    showYearDropdown
                    selected={rangeStart}
                    onChange={(e)=>handleDate(e, null)}
                    className="form-control"
                    startDate = {rangeStart}
                    endDate = {dateRange !== "day" ? rangeEnd : null}
                    selectsRange={dateRange === "custom" ? true : false}
                    shouldCloseOnSelect={dateRange === "custom" ? !(!rangeStart || Boolean(rangeStart && rangeEnd)) : null}
                />
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor="search-range">
                    Date Range
                </label>
                <select 
                    defaultValue="week"
                    onChange={(e)=>handleDateRange(e.target.value)}
                >
                    <option value="day">The Day</option>
                    <option value="week">The Week</option>
                    <option value="month">The Month</option>
                    <option value="custom">Custom</option>
                </select>
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor="search">
                    Search Term or Id
                </label>
                <input 
                    id="search"
                    placeholder="Search..."
                    onChange={(e)=>{
                        let name;
                        if(testNumeric(e.target.value)) name="id"
                        else name = "search"
                        handleSearchFilters(e.target.value, name)}
                    }
                />
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor = "min-age">
                    Min Age
                </label>
                <input 
                    type="number" 
                    id="min-age" 
                    onChange={(e)=>handleSearchFilters(e.target.value, "min_age")}
                />
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor="max-age">
                    Max Age
                </label>
                <input 
                    type="number" 
                    id="max-age" 
                    onChange={(e)=>handleSearchFilters(e.target.value,"max_age")}
                />
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor="max-records">
                    Max Records
                </label>
                <input
                    placeholder={searchRef.current.max_records}
                    type="number"
                    id="max-records"
                    onChange={(e)=>handleSearchFilters(e.target.value, "max_records")}
                />
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor="parent-events">Include</label>
                <select id="parent-events" onChange={(e)=>handleIsChild(e.target.value)}>
                    <option value="all">
                        All Events
                    </option>
                    <option value="parent">
                        Parent Events Only
                    </option>
                    <option value="child">
                        Child Events Only
                    </option>
                </select>
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor="type-search">Select Event Types</label>
                <EventTypesTypeahead
                    async={false}
                    name="type-search"
                    multiple={true}
                    placeholder="All Events Selected"
                    passSelection={(selected)=>{setSelectedEventTypes(selected)}}
                />
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor="event-status">Event Status</label>
                <EventStatusTypeahead 
                    multiple={true} 
                    async={false} 
                    placeholder={"All Statuses"} 
                    passSelection={setSelectedStatus}
                />
            </div>
            <div className={styles["each-input"]}>
                <label htmlFor="location">
                    Location
                </label>
                <LocationTypeahead 
                    multiple={true}
                    async={false}
                    placeholder="All Locations"
                    passSelection={(e)=>handleSearchFilters(e, "locations")}
                />
            </div>
        </div>
    )
}

export default SearchFilters