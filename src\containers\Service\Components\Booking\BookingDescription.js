import React from 'react';
import { Form, Button } from 'react-bootstrap';

import { convertMinutesToHoursDays } from '../../../../utils/dates';
import styles from './BookingDescription.module.scss';

export const BookingDescription = ({ 
    service, 
    onClickBack,
    tokens, 
    linkToBundles
}) => {

    let minToken = Math.round(service?.min_timeslots / service?.timeslots_for_token);
    let maxToken = Math.round(service?.max_timeslots / service?.timeslots_for_token);
    let bulletIcon = (<span className={styles.faLi}><i className="far fa-caret-right"></i></span>);

    return(
        <>
            <Form.Row className={`${styles.serviceSelection} mb-3`}>
                <div><Button onClick={onClickBack}>Back to Services</Button></div>
                <div className="ml-4">
                    <Form.Label className={styles.question} data-cy="service-name">{service?.name}</Form.Label>
                    <br/>
                    Offered by: {service?.managers.map(manager => manager.first_name + " " + manager.last_name + " ")}
                </div>
            </Form.Row>
            {service?.description!=="" &&
                <Form.Row className={`${styles.serviceSelection} no-wrap mb-2`}>
                    <div><Form.Label className={styles.description}>Description:</Form.Label></div>
                        <div className="pl-2" data-cy="service-description">{service?.description}</div>
                </Form.Row>
            }
            <ul className={styles.serviceDescriptionList} data-cy="service-details">
                <li>{bulletIcon}
                    Each timeslot is <span className={styles.boldText}>{convertMinutesToHoursDays(service?.block_minutes*service?.timeslots_for_token)}</span> long
                    and <span className={styles.boldText}>${service?.default_price}</span>
                </li>
                <li>{bulletIcon}
                    {service?.max_timeslots===1 &&
                        <>You can book only <span className={styles.boldText}>1 timeslot</span></>
                    }
                    {service?.max_timeslots>1 &&
                        <>You can book a minimum of
                        <span className={styles.boldText}> {convertMinutesToHoursDays(service?.block_minutes*service?.min_timeslots)}
                        ({minToken} timeslots = ${minToken*service?.default_price}) </span>
                        and a maximum of
                        <span className={styles.boldText}> {convertMinutesToHoursDays(service?.block_minutes*service?.max_timeslots)}
                        ({maxToken} timeslots = ${maxToken*service?.default_price})</span></>
                    }
                </li>
                <li>{bulletIcon}
                    There is a maximum of
                    <span className={styles.boldText}> {service?.max_participants} registered user
                    {service?.max_participants===1 ? '' : 's'} </span>
                    per timeslot
                </li>
                <li>{bulletIcon}
                    Reservations must be made at least <span className={styles.boldText}>{convertMinutesToHoursDays(service?.min_booking_notice)}</span> in advance,
                    and can be made up to <span className={styles.boldText}>{convertMinutesToHoursDays(service?.max_booking_notice)}</span> in advance
                </li>
                <li>{bulletIcon}Tokens accepted for this service: {" "} 
                    {service?.products.map((product, i) =>(
                        <span key={`tokens-accepted-list-${product.id}`}>
                            {product.name} - ${product.variants[0].price} 
                            {i === service?.products.length-1 ? "" : ", "}
                        </span>
                    ))}
                </li>
            </ul>
            {linkToBundles &&parseFloat(service?.default_price)>0 &&
                <div className={`${styles.serviceSelection} ${styles.tokensAvail} ${tokens.available===0 ? styles.none : ''} mt-2`}>
                    You have {tokens.available} token{tokens.available===1 ? "" : "s"} in your wallet that could be used for this service.{' '}
                    {linkToBundles}
                </div>
            }
            <div className={`${styles.serviceSelection} ${styles.info} mt-2 mb-1`}>
                Choose your week and location to see the availabilities for this service.
            </div>
        </>
    )
}