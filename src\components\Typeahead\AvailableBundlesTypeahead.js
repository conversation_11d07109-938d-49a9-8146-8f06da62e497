import React, {useState, useCallback} from 'react';

import { Typeahead } from './Typeahead';
import Products from '../../api/Products';

export const AvailableBundlesTypeahead = (props) => {

    const [loading, setLoading]=useState("Loading...")

    const makeRequest=useCallback(async()=>{
        let responseObj;
        try{
                let response = await Products.get({
                product_type_id: 6,
                bundle_includes_product_type_id: [9]
            })
            responseObj={
                data: response.data.products || null,
                errors: response.errors || null
            }
            setLoading("");
        }catch(ex){
            console.error(ex)
            setLoading("Currently No Bundles")
        }
        return responseObj;
    },[])

  return (
    <Typeahead
        {...props}
        id="available-bundle-search"
        makeRequest={makeRequest}
        async={false}
        paginated={false}
        placeholder={loading ? `${loading}` : "Search Available Bundles"}
        initialData={props.initialData}
    />
  )
}
