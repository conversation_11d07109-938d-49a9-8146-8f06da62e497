import React, { useCallback } from 'react';
import { Form, Container } from 'react-bootstrap';

const Video = React.forwardRef((props, _) => {
    const {input, selection} = props;

    const parseVideoInput = useCallback(input => {
        let videoId = '';
        let embedUrl = ''

        // Regex patterns for YouTube and Vimeo URL formats
        const patterns = [
            { source: 'youtube', pattern: /youtu\.be\/([\w\-_]+)/ },                               // YouTube - Shortened URL
            { source: 'youtube', pattern: /youtube\.com\/embed\/([\w\-_]+)/ },                     // YouTube - Embed URL
            { source: 'youtube', pattern: /youtube\.com\/watch\?v=([\w\-_]+)/ },                   // YouTube - Standard watch URL
            { source: 'youtube', pattern: /youtube\.com\/watch\?.*v=([\w\-_]+)/ },                 // YouTube - Watch URL with parameters
            { source: 'vimeo', pattern: /vimeo\.com\/(\d+)/ },                                     // Vimeo - Standard URL
            { source: 'vimeo', pattern: /vimeo\.com\/channels\/[\w]+\/(\d+)/ },                    // Vimeo - Channel URL
        ];

        for (const {source, pattern} of patterns) {
            const match = input.match(pattern);
            if (match && match[1]) {
                videoId = match[1];
                if (source === 'youtube') {
                    const ampersandPosition = videoId.indexOf('&');
                    if (ampersandPosition !== -1) videoId = videoId.substring(0, ampersandPosition);
                    embedUrl = `https://www.youtube.com/embed/${videoId}`;
                } else if (source === 'vimeo') embedUrl = `https://player.vimeo.com/video/${videoId}`;
                break;
            }
        }

        selection(embedUrl);
    },[selection]);

    return (
        <Form.Group>
            <Form.Label>Video URL</Form.Label>
            <Form.Control placeholder="YouTube or Vimeo video link" defaultValue={input || ""} onBlur={(e)=>parseVideoInput(e.target.value)} />
        </Form.Group>
    );
});

export default Video;