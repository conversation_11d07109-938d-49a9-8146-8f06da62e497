import React, { useCallback } from 'react';

import { Typeahead } from './Typeahead';
import CMS from '../../api/Cms';

/**Requires props.websiteId */
export const UrlPagesTypeahead = (props)=>{
    
    const getPages = useCallback(async()=>{
        let respObj = {
            data: null,
            errors: null
        }
        if(props.websiteId){
            try{
                let response = await CMS.pages.get({website_id: props?.websiteId, page_type_id: 1});
                if(response.status === 200 && response.data) respObj.data = response.data;
                else if(response.error) response.errors = response.error;
            }catch(ex){
                console.error(ex)
                respObj.errors = ex;
            }
        }

        return respObj;
    },[props.websiteId])

    return(
        <Typeahead
            {...props}
            id="url-pages-search"
            makeRequest={getPages}
            placeholder={props.placeholder || "Search for a page"}
            paginated={false}
        />
    )
}
