import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Form } from 'react-bootstrap';
import { format, startOfToday, subMinutes, set as setTime, formatISO } from 'date-fns';

import * as actions from '../../../../store/actions';
import { formatTimeAmpm } from '../../../../utils/dates';
import Tooltip from '../../../../components/common/Tooltip';

import './TimeslotGrid.scss';

const dayNames = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'June', 'July', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'];

const TimeslotGrid = ({ 
    location, 
    startDate, 
    endDate, 
    minHour, 
    maxHour, 
    conflictEvents, 
    service, 
    showConflictInfo=false, 
    onSlotChange=(e)=>console.log(e) 
}) => {
    const dispatch = useDispatch();

    const [pagePart, setPagePart] = useState();
    const [dateTimes, setDateTimes] = useState({});
    // const [selectedSlots, setSelectedSlots] = useState([]);
    const selectedSlots = useSelector(state => state.serviceBooking.current_booking.selected_slots);

    // generate the daysList (array of dates) to loop through to make grid
    // save the info into a new blockslist
    useEffect(() => {
        let day = startDate;
        let list = [];
        while (day <= endDate) {
            let date = new Date(Date.parse(day))
            let dateObj = {
                date: date,
                blocks: []
            }
            for (let i=0; i<service.blocks.length; i++) {
                let block = service.blocks[i];
                if (date.getDay()===block.day_of_week) {
                    // Filter out blocks that have timeslots outside the minHour/maxHour range
                    let filteredBlock = {
                        ...block,
                        timeslots: block.timeslots?.filter(timeslot => {
                            let start_time = timeslot.start_time.split(":");
                            let startHour = parseInt(start_time[0]);
                            return startHour >= minHour && startHour < maxHour;
                        }) || []
                    };

                    // Only add the block if it has valid timeslots within the hour range
                    if (filteredBlock.timeslots.length > 0) {
                        dateObj.blocks.push(filteredBlock);
                    }
                }
            }
            list.push(dateObj);
            day.setDate(day.getDate()+1);
        }
        setDateTimes(list);
    },[startDate, endDate, service, minHour, maxHour]);


    // grid generation loop
    useEffect(() => {

        const isSelected = (date, block, timeslot) => {
            // use for loop so we can bail and return true if it finds a match
            for (let i=0; i<selectedSlots.length; i++) {
                let selected = selectedSlots[i];
                if (selected.date===date && selected.block===block && selected.timeslot===timeslot) {
                    return true;
                }
            }
            return false;
        }

        const isAdjacent = (date, block, timeslot) => {
            // use for loop so we can bail and return true if it finds a match
            for (let i=0; i<selectedSlots.length; i++) {
                let selected = selectedSlots[i];
                if (selected.date===date && selected.block===block && (selected.timeslot===timeslot-1 || selected.timeslot===timeslot+1)) {
                    return true;
                }
            }
            return false;
        }

        const removeSlot = (timeslot) => {
            let slots = selectedSlots;
            let slot_nums = selectedSlots.map(slot => slot.timeslot);
            if (timeslot===Math.min(...slot_nums) || timeslot===Math.max(...slot_nums)) {
                // remove the first or last slot
                slots = selectedSlots.filter(slot => slot.timeslot!==timeslot);
            }
            return slots;
        }

        const addSlot = (newSlot) => {
            let slots = selectedSlots;
            if (selectedSlots.length<service.max_timeslots) {
                slots = [...selectedSlots, newSlot];
            }
            return slots;
        }

        const setSlots = (newSlot, timeslot_index, timeslots) => {
            let slots = [newSlot];
            // how many additional slots need to be added?
            let numToAdd = service.min_timeslots-1;   // we already added the first one
            let found = false;
            // find the new slot in array, then add the slots immediately following
            if (numToAdd>0) {
                let date = new Date(newSlot.start);
                let conflict_events = findConflictEventsForDay(date);
                timeslots.some((slot, i) => {
                    if (found) {    // if the timeslot was already found, then check the countdown
                        // check if this new slot is valid
                        // if not valid, bail
                        let times = getTimeslotStartEnd(slot, date);
                        if (!!blockHasConflicts(conflict_events, times.timeslot_start, times.timeslot_end)) return true;
                        // add this one to the array
                        slots.push({
                            date: newSlot.date,
                            block: newSlot.block,
                            timeslot: i,
                            start: formatISO(times.timeslot_start),
                            end: formatISO(times.timeslot_end)
                        });
                        numToAdd--;
                        if (numToAdd===0) return true;  // if there's no more left to find bail
                    } else if (timeslot_index===i) found = true;
                    return false;
                });
            };
            
            return slots;
        }
    
        function onChangeTimeslot (date, block_index, timeslot_index, start, end, timeslots) {
            let slots = [];
            let newSlot = { date: date, block: block_index, timeslot: timeslot_index, start: start, end: end};

            if (isSelected(date, block_index, timeslot_index)) {
                // if selected cell is already in our selected list, try to remove it - will only remove first or last in group, not slots in the middle
                slots = removeSlot(timeslot_index);
            } else if (service.max_timeslots>1 && isAdjacent(date, block_index, timeslot_index)) {
                // if a cell is adjacent to current cells, add it to the array
                slots = addSlot(newSlot);
            } else {
                // if the cell clicked on is not adjacent, replace the currently selected cells with the new one 
                slots = setSlots(newSlot, timeslot_index, timeslots);
            }
    
            if(!onSlotChange) {
                dispatch(actions.setServiceBooking({ selected_slots: slots }));
            }else onSlotChange({ selected_slots: slots})
        }

        function findConflictEventsForDay (datetime) {
            let day_events = conflictEvents?.find(dateGroup => dateGroup.start_datetime.slice(0,10)===format(datetime, "yyyy-MM-dd"));
            return day_events ? day_events.events : [];
        }

        function getTimeslotStartEnd(timeslot, date) {
            let start_time = timeslot.start_time.split(":");
            let start = (start_time[0]<10 ? "0" : "") + start_time[0] + start_time[1] + "";
            let end_time = timeslot.end_time.split(":");
            let end = (end_time[0]<10 ? "0" : "") + end_time[0] + end_time[1] + "";

            // figure out if it's disabled or not - if there is a conflicting event
            let timeslot_start = setTime(date, { hours: start_time[0], minutes: start_time[1] });
            // subtract 1 minute from timeslot_end
            let timeslot_end = subMinutes(setTime(date, { hours: end_time[0], minutes: end_time[1] }), 1);

            return {
                start: start,
                end: end,
                timeslot_start: timeslot_start,
                timeslot_end: timeslot_end
            }
        }

        // determines if the given slot 
        function blockHasConflicts (day_events, timeslot_start, timeslot_end) {
            let eventConflicts = [];
            let service_events_count = 0;
            // go through the conflict events and set disabled to true for any overlap
            for (let i=0; i<day_events.length; i++) {
                let event = day_events[i];
                let event_start = new Date(event.start_datetime);
                let event_end = new Date(event.end_datetime);
                if ((event_start>=timeslot_start && event_start<=timeslot_end)
                    || (event_end>=timeslot_start && event_end<=timeslot_end)
                    || (event_start<=timeslot_start && event_end>=timeslot_end) ) {
                        // if it's a matching service, then add to count
                        // TODO change matching service to matching any group service
                        // currently hardcoded to check for location=7, ignore conflicts for Team Training Room
                        let is_same_service = event.service.includes(service.id) || location===7;
                        if (is_same_service) service_events_count++;
                        // if the count is >= max_participants or it's not a matching service
                        if (!is_same_service || service_events_count>=service.max_participants) {
                            eventConflicts.push(event);
                        }
                }
            }
            return eventConflicts.length>0 ? eventConflicts : false;
        }
    
        const makeCells = () => {
            let cells = [];

            // grey out past days
            let date = dateTimes[0].date;
            debugger
            if (dateTimes.length>0 && date<startOfToday()) {
                // make a semi-transparent block for the day so it's clear it's not available?
                let start = (minHour<10 ? "0" : "") + minHour + "00";
                let end = (maxHour<10 ? "0" : "") + maxHour + "00";
                cells.push(
                    <div className="cell-past-day"
                        style={{gridRow: `header-date / h-${end}`, gridColumn: `day-${format(date, "yyyy-MM-dd")} / day-${format(new Date(), "yyyy-MM-dd")}`}}
                    ></div>
                );
            }
    
            console.log(dateTimes)
            dateTimes.forEach((day, date_index) => {
                let date = day.date;
                // ignore dates that are in the past
                if (date>=startOfToday()) {

                    // find conflict events for this particular day
                    let day_events = findConflictEventsForDay(date);
                    
                    day.blocks.forEach((block, block_index) => {
                        block.timeslots?.forEach((timeslot, timeslot_index) => {
                            // Additional safety check: ensure timeslot is within hour bounds
                            let start_time = timeslot.start_time.split(":");
                            let startHour = parseInt(start_time[0]);

                            if (startHour >= minHour && startHour < maxHour) {
                                let cell_id = `${format(date, "yyyy-MM-dd")}-${block_index}-${timeslot_index}`;

                                let timeslotTime = getTimeslotStartEnd(timeslot, date);
                                let timeslot_start = timeslotTime.timeslot_start;
                                let timeslot_end = timeslotTime.timeslot_end;

                            // don't show timeslot if it's already started or if there is not enough lead time for min_booking_notice or more lead time than max_booking_notice
                            let dateWithMin = subMinutes(timeslot_start, service.min_booking_notice);
                            let dateWithMax = subMinutes(timeslot_start, service.max_booking_notice);

                            if (new Date()<=dateWithMin && new Date()>=dateWithMax) {

                                // check if this block has conflicts with other events = will mark this cell disabled or enabled
                                let conflicts = blockHasConflicts(day_events, timeslot_start, timeslot_end);
                                console.log(conflicts)
                                // TODO: add conflicts to cell so we can show a tooltip with the conflicting events
                                // but tooltip isn't working inside the form check component

                                cells.push(
                                    <div key={cell_id}
                                        data-cy="time-cell"
                                        className={`cell-block ${!!conflicts ? 'reserved' : ''}`}
                                        style={{gridRow: `h-${timeslotTime.start} / h-${timeslotTime.end}`, gridColumn: `day-${format(date, "yyyy-MM-dd")}`}}
                                    >
                                        <Form.Check
                                            type="checkbox"
                                            id={cell_id}
                                            name={cell_id}
                                            className="form-radio event-type grid"
                                        >
                                            <Form.Check.Input
                                                type="checkbox"
                                                disabled={!!conflicts}
                                                checked={isSelected(format(date, "yyyy-MM-dd"), block_index, timeslot_index)}
                                                onChange={(e) => onChangeTimeslot(
                                                    format(date, "yyyy-MM-dd"),
                                                    block_index,
                                                    timeslot_index,
                                                    formatISO(timeslot_start),
                                                    formatISO(timeslot_end),
                                                    block.timeslots
                                                )}
                                            />
                                            <Form.Check.Label>{!conflicts ? formatTimeAmpm(timeslot.start_time)
                                                : <i className="far fa-times"></i>}
                                            </Form.Check.Label>
                                        </Form.Check>
                                    </div>
                                );
                            }
                        });
                    });
                }
            });
    
            return cells;
        }


        if (location && dateTimes.length>0) {
            // make the grid-template-columns dynamically with named lines
            // if the service increment is 15 or 30 minutes these boxes will have to be larger to accomodate a button in each cell
            let cellDivisions = ["00", "15", "30", "45"];
            let cell_string = "[header-date] auto";
            let cell_height = service.block_minutes>=30 ? "15px" : "34px";
            let grid_lines_cells = [];
            if (conflictEvents) for (let i=minHour; i<maxHour; i++) {
                let start = (i<10 ? "0" : "") + i + "00";
                let end = ((i+1)<10 ? "0" : "") + (i+1) + "00";
                grid_lines_cells.push(
                    <React.Fragment key={`grid-lines-${i}`}>
                        <div
                            className={`cell-gridlines`}
                            style={{gridRow: `h-${start} / h-${end}`, gridColumn: `column-time / ${dateTimes.length+2}`}}
                        ></div>
                        <div
                            style={{gridRow: `h-${start} / h-${end}`, gridColumn: `column-time`}}
                            className="cell-hourname"
                        >{formatTimeAmpm(i+":00", false)}</div>
                    </React.Fragment>
                );
                // create the vertical grid line names
                for (let j=0; j<cellDivisions.length; j++) {
                    cell_string += ' [h-' + (i<10 ? "0"+i : i) + cellDivisions[j] + '] ' + cell_height;
                }
            } 

            // create the horizontal grid line names
            let horizontal_cell_string = "[column-time] auto"
            dateTimes.forEach(day => {
                horizontal_cell_string += ' [day-' + format(day.date, "yyyy-MM-dd") + '] 1fr';
            });

            setPagePart(
                <>
                    <div
                        className="range-display-grid"
                        id="range-display"
                        style={{gridTemplateRows: cell_string, gridTemplateColumns: horizontal_cell_string}}
                        key={`timeslot-grid-${location}`}
                    >
                        {dateTimes.map(day => (
                            <div
                                key={`header-date-${format(day.date, "yyyy-MM-dd")}`}
                                className="header-date"
                                style={{gridRow: `header-date`, gridColumn: `day-${format(day.date, "yyyy-MM-dd")}`}}
                            >
                                <span className="header-day-of-week">{dayNames[day.date.getDay()].slice(0,3)}</span>
                                <span className="header-month-name">{monthNames[day.date.getMonth()].slice(0,3)}</span>
                                <span className="header-date-num">{day.date.getDate()}</span>
                            </div>
                        ))}
                        {conflictEvents && grid_lines_cells }
                        {makeCells()}
                    </div>
                    {!conflictEvents &&
                        <div
                            className="none-available"
                            key={`timeslot-none-${location}`}
                        >
                            No available timeslots for these dates.
                        </div>
                    }
                </>
            );
        } else {
            setPagePart(
                <div
                    className="none-available"
                    key={`timeslot-none-${location}`}
                >
                    No available options for these dates and location.
                </div>
            );
        }
    },[location, dateTimes, service, selectedSlots, conflictEvents, showConflictInfo, dispatch, maxHour, minHour]);

    return (
        <>
            {pagePart}
        </>
    );
}

export default TimeslotGrid;