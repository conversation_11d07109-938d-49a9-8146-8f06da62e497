import React, { useState } from 'react';

import './Checkbox.scss';

// This is a wrapper so we can use it wherever we want a checkbox so they will all be styled the same
// onchange is a callback function that will be called when the checkbox is clicked, return values are (identifier, isChecked)
export const Checkbox = ({
    id,
    checked,
    onChange,
    partial=false,
    label=null,
    disabled=false,
    partialIcon='far fa-star-of-life',
    checkedIcon='far fa-check'
}) => {

    return (
        <span
            className={`myinput-checkbox-wrapper ${!!checked && ' checked'} ${!!partial && ' partial'} ${!!disabled && ' disabled'}`}
            onClick={() => {
                if (!disabled) onChange(id, !checked)
            }}
        >
            <input type="checkbox" className={`myinput-checkbox`}
                value={id}
                checked={checked}
                disabled={disabled}
            />
            <span>
                <i className={`checkbox-checked ${checkedIcon}`}></i>
                <i className={`checkbox-partial ${partialIcon}`}></i>
            </span>
            {label &&
                <span className="checkbox-label">
                    {label}
                </span>
            }
        </span>
    )
}