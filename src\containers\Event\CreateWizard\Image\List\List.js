import React, {useEffect, useState, useCallback} from 'react';
import ImageCard from './ImageCard';

export const List = (props) => {
    const {update, setPreview} = props;

    const [images, setImages] = useState(props.images || []);

    const moveImageHandler = useCallback((dragIndex, hoverIndex) => {
        setImages(prev => {
            const updatedImages = [...prev];
            const [draggedElement] = updatedImages.splice(dragIndex, 1);
            updatedImages.splice(hoverIndex, 0, draggedElement);
          
            update(updatedImages);
            return updatedImages;
        });
    }, [update]);

    const removeImageHandler = useCallback((e, id) => {
        e.preventDefault();
        e.stopPropagation();

        const updatedImages = [...images];
        const index = updatedImages.findIndex(a=>a.id === id);
        updatedImages.splice(index, 1);

        setPreview(null);
        setImages(updatedImages);
        update(updatedImages);
    }, [update, setPreview, images]);

    const updateDescriptionHandler = (e, id) => {
        e.preventDefault();
        e.stopPropagation();

        const value = e.target.value;
        let _images = [...images];
        _images.find(a=>a.id === id).description = value || "";

        update(_images);
    }    

    useEffect(() => {
        setImages(props.images || []);
    }, [props.images]);

    return (
        <div>
            {images.map((image, i) => (
                <ImageCard 
                    id={image.id}
                    key={image.id}
                    index={i}
                    url={image.preview_url}
                    moveImage={moveImageHandler}
                    removeImage={removeImageHandler}
                    description={image.description}
                    setDescription={updateDescriptionHandler}
                    setPreview={setPreview}
                />
            ))}
        </div>
    );
}