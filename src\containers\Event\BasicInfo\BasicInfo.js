import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useHistory } from "react-router-dom";
import { Container, Row, Col, Form, Button } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import { format, utcToZonedTime } from 'date-fns-tz';
import { useDispatch } from 'react-redux';

import { updateEvents, resetEvents } from "../../../store/actions/calendar"
import { roundTimes } from '../../../utils/dates'
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';
import { Typeahead } from '../../../components/Typeahead';

import Events from '../../../api/Events';
import Locations from '../../../api/Locations';
import { eventType } from '../../../store/actions';

const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

const BasicInfo = (props) => {
    let history = useHistory();
    const dispatch = useDispatch();

    const [event, setEvent] = useState({});
    const [possibleChildEvents, setPossibleChildEvents] = useState();
    const [currentStatusValue, setCurrentStatusValue] = useState();
    const [eventStatuses, setEventStatuses]=useState();
    const [currentTypeValue, setCurrentTypeValue] = useState("");
    const [locations, setLocations] = useState([]);
    const [validLocations, setValidLocations] = useState([]);
    const [currentLocation, setCurrentLocation] = useState();
    const [startDate, setStartDate] = useState();
    const [endDate, setEndDate] = useState();
    const [currentChildEvents, setCurrentChildEvents] = useState([]);
    const [initialChildEvents, setInitialChildEvents] = useState([]);
    const [parentEvents, setParentEvents] = useState([]);
    const [parentId, setParentId] = useState(null);
    const [requiresRegistration, setRequiresRegistration] = useState(1);


    const currentStatusHandler = e => {
        setCurrentStatusValue(e.target.value);
    }

    const currentTypeHandler = e => {
        setCurrentTypeValue(e.target.value);
    }

    const currentLocationHandler = e => {
        setCurrentLocation(e.target.value);
    }

    const parentHandler = (e) => {
        setParentId(e.currentTarget.value);
    }

    const toggleRequiresRegistration = () => {
        requiresRegistration === 1 ? setRequiresRegistration(0) : setRequiresRegistration(1);
    }

    //init data from props
	useEffect(() => {
        let mounted = true;

        if (props.event) {
            setEvent(props.event);
            setCurrentTypeValue(props.event.event_type_id);
            setCurrentStatusValue(props.event.event_status_id);
            setStartDate(new Date(props.event.start_datetime));
            setEndDate(new Date(props.event.end_datetime));
            setInitialChildEvents(props.event.children);
            setCurrentChildEvents(props.event.children);
            setParentId(props.event.parent_id || 0);
            setRequiresRegistration(props.event.requires_registration);
            setParentEvents(props.event.possible_parents || []);
            setPossibleChildEvents(props.event.possible_children || []);
        } else {
            Events.getSimple({ 
                id: props.event_id,
                include_possible_parents: 1,
                include_possible_children: 1
             })
            .then(response => {
                if(mounted && response.data) {
                    setEvent(response.data.events[0]);
                    setCurrentTypeValue(response.data[0].event_type_id);
                    setCurrentStatusValue(response.data[0].event_status_id);
                    setStartDate(utcToZonedTime(new Date(response.data[0].start_datetime), browserTimeZone));
                    setEndDate(utcToZonedTime(new Date(response.data[0].end_datetime), browserTimeZone));
                    setInitialChildEvents(response.data[0].children);
                    setCurrentChildEvents(response.data[0].children);
                    setParentId(response.data[0].parent_id || 0);
                    setRequiresRegistration(response.data[0].requires_registration);
                    setParentEvents(response.data[0].possible_parents || []);
                    setPossibleChildEvents(response.data[0].possible_children || []);
                }
            }).catch(e => console.error(e));
        }

        return () => {
            mounted = false;
            dispatch(resetEvents())
        }

	}, [props, props.event, props.event_id, dispatch]);

    //set lists of events and locations
    useEffect(() => {
        let mounted = true;

        getStatusForDD(mounted);

        Locations.get()
        .then(response => {
            if(mounted) setLocations(response.data);
        }).catch(e => console.error(e));

        return () => mounted = false;
    },[])

    //set valid locations for the selected event type
    useEffect(() => {
        if(parseInt(currentTypeValue) !== 5) setValidLocations(locations?.filter(location => location.selectable === 1));
        else setValidLocations(locations?.filter(location => location.id === 1));
    },[currentTypeValue, locations])

    //set current event location when locations have been gotten
    useEffect(() => {
        setCurrentLocation(locations?.filter(location => location.id === event?.location_id)[0]?.id);
    },[event, locations]);

    //edit end time if it ends in 4 or 9
    useEffect(()=>{
        if(endDate){
            roundTimes(endDate);
        }
    },[endDate])


    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

    // form submission
    const SubmitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();

        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            if (props.event.id) formData.append("id", props.event.id);
            const formDataObj = Object.fromEntries(formData.entries());
            formDataObj.requires_registration = requiresRegistration.toString();
            formDataObj.parent_id = parseInt(formDataObj.parent_id) === 0 ? null : formDataObj.parent_id;

            if (props.event.id) await Events.edit(formDataObj) // existing event, send edit call
            .then(response => {
                if (!response.errors) {
                    dispatch(updateEvents())
                    setSubmitting(false);
                    setValidated(false);
                    setSuccess(<Toast>Event saved successfully!</Toast>);
                    history.push(props.referer || "/p/events"); // pushes to profile again to avoid resubmission
                    if (props.hasOwnProperty('handleClose')) props.handleClose();
                    if (props.onCancel) props.onCancel();
                } else {
                    setSubmitting(false);
                    setValidated(false);
                    setError(<ErrorCatcher error={response.errors} />);
                }
            })
            .catch(e => console.error(e));
            else await Events.create(formDataObj) // new event, send create call
            .then(response => {
                if (!response.errors) {
                    setSubmitting(false);
                    setValidated(false);
                    setSuccess(<Toast>Event saved successfully!</Toast>);
                    history.push(props.referer || "/p/events"); // pushes to profile again to avoid resubmission
                } else {
                    setSubmitting(false);
                    setError(<ErrorCatcher error={response.errors} />);
                }
            }).catch(e => console.error(e));

            // if nothing failed, then send calls to update child/parent relationships
            if(!error && ( currentChildEvents.length > 0 || initialChildEvents.length > 0 )) {
                setSubmitting(true);
                let errors = 0;
                // identify all children in currentChild events that do not exist in initial child events
                let initialChildIds = initialChildEvents.map(child => child.id);
                let currentChildIds = currentChildEvents.map(child => child.id);
                let childrenToAdd = currentChildIds.filter(
                    currentChild => !initialChildIds.includes(currentChild));
                // all children in initial child evends that does not exist in current child events
                let childrenToRemove = initialChildIds.filter(
                    initialChild => !currentChildIds.includes(initialChild));
                childrenToAdd.forEach( async childId =>
                    await Events.edit({id: childId, parent_id: event.id})
                    .catch(e => {
                        errors += 1;
                        console.error(e);
                    })
                );
                childrenToRemove.forEach( async childId => 
                    await Events.edit({id: childId, parent_id: null})
                    .catch(e => {
                        errors += 1;
                        console.error(e);
                    })
                );
                setSubmitting(false);
                if (errors === 0) setInitialChildEvents(currentChildEvents);
                history.push(props.referer || "/p/events");
            }
        } else setSubmitting(false);
    };

    // //child event input
    // const renderInput = ({ inputClassName, inputRef, referenceElementRef, ...props },{ onRemove, selected }) => (
    //     currentChildEvents &&
    //     <React.Fragment>
    //         <input
    //             {...props}
    //             className="form-control"
    //             ref={input => {
    //                 referenceElementRef(input);
    //                 inputRef(input);
    //             }}
    //             type="text"
    //         />
    //         <div style={{ marginTop: '10px' }}>
    //             {currentChildEvents.map((option, i) => (
    //                 option &&
    //                 <Token key={`tkn-${i}`} onRemove={() => onRemove(option)}>
    //                     {option.name}
    //                 </Token>
    //             ))}
    //         </div>
    //     </React.Fragment>
    // );

    const getChildEvents = useCallback(async (query, perPage, page=1) => {
        //console.log(possibleChildEvents);
        let responseObj = {
            data: possibleChildEvents || null,
            errors: null
        }
        return responseObj;
    },[possibleChildEvents]);

    // each item in responseObj.data is an option
    const formatForLabel = (option) => {
        let returnString = `${option?.name}`;
        if (option.start_datetime && option.end_datetime) {
            let startDate = format(new Date(option.start_datetime), "MM/dd/yyyy");
            let endDate = format(new Date(option.end_datetime), "MM/dd/yyyy");
            returnString += ` (${startDate} - ${endDate})`;
        }
        return returnString;
    };

    const getStatusForDD=async(mounted)=>{
        try{
            let response = await Events.getEventStatus();
            if(!response.errors && mounted){
                setEventStatuses(response.data.filter(status => status.name!=="In Cart" && status.name !=="Expired"));
            }
        }catch(ex){console.error(ex)}
    }

    return (
        <Col className="order-lg-1">
            <Container fluid>
                {success}
                <Form noValidate validated={validated} onSubmit={SubmitHandler}>
                    <Row>
                        <Col sm="12">
                            <Form.Group controlId="name">
                                <Form.Label>Event Name</Form.Label>
                                <Form.Control required type="text" name="name" defaultValue={event.name || ""} />
                            </Form.Group>
                        </Col>
                    </Row>
                    <Row>
                        <Col m="6">
                            <Form.Group>
                                <Form.Label>Start Date</Form.Label>
                                <DatePicker
                                    disabled
                                    dateFormat="MM/dd/yyyy"
                                    minDate={new Date()}
                                    maxDate={new Date(new Date().getFullYear()+100,12,31)}
                                    showMonthDropdown
                                    showYearDropdown
                                    selected={startDate}
                                    onChange={setStartDate}
                                    customInput={
                                        <Button disabled variant="light" className="datepicker-calendar" type="button">
                                            {startDate?.toLocaleDateString("en-US",{weekday:"short",day:"2-digit",month:"2-digit",year:"numeric"})}
                                        </Button>
                                    }
                                />
                            </Form.Group>
                        </Col>
                        <Col m="6">
                            <Form.Group className="endtime">
                                <Form.Label>End Date</Form.Label>
                                <DatePicker
                                    disabled 
                                    dateFormat="MM/dd/yyyy"
                                    minDate={new Date()}
                                    maxDate={new Date(new Date().getFullYear()+100,12,31)}
                                    showMonthDropdown
                                    showYearDropdown
                                    selected={endDate}
                                    onChange={setEndDate}
                                    customInput={
                                        <Button variant="light" className="datepicker-calendar" type="button">
                                            {endDate?.toLocaleDateString("en-US",{weekday:"short",day:"2-digit",month:"2-digit",year:"numeric"})}
                                        </Button>
                                    }
                                />
                            </Form.Group>
                        </Col>
                    </Row>
                    <Row>
                        <Col m="6">
                            <Form.Group> 
                                <Form.Label>Start Time</Form.Label>
                                <DatePicker disabled customInput={
                                    <Button disabled variant="light" className="datepicker-calendar" type="button">
                                        {startDate ? format(startDate, "hh:mm:ss aa") : ""}
                                    </Button>
                                }/>
                            </Form.Group> 
                        </Col>
                        <Col m="6">
                            <Form.Group> 
                                <Form.Label>End Time</Form.Label>
                                <DatePicker disabled customInput={
                                    <Button disabled variant="light" className="datepicker-calendar" type="button">
                                        {endDate ? format(endDate, "hh:mm:ss aa") : ""}
                                    </Button>
                                }/>
                            </Form.Group> 
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="12">
                            <Form.Group controlId="description">
                                <Form.Label>Description</Form.Label>
                                <Form.Control as="textarea" name="description" rows={6} defaultValue={event.description || ""} />
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="event_type">
                                <Form.Label>Event Type</Form.Label>
                                {props.eventTypes && currentTypeValue ?
                                    <Form.Control required custom as="select" name="event_type_id" value={currentTypeValue} onChange={currentTypeHandler}>
                                        {props.eventTypes?.map(eventType =>
                                            <option key={`event-type-dd-${eventType.id}`} value={eventType.id}>{eventType.name}</option>
                                        )}
                                    </Form.Control>
                                : <><br /> <span>Loading...</span></>
                                }
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="event_status">
                                <Form.Label>Status</Form.Label>
                                {eventStatuses && currentStatusValue ?
                                    <Form.Control required custom as="select" name="event_status_id" value={currentStatusValue} onChange={currentStatusHandler}>
                                        {eventStatuses?.map(status =>
                                            <option key={`event-status-dd-${status.id}`} value={status.id}>{status.name}</option>
                                        )}
                                    </Form.Control>
                                : <><br /> <span>Loading...</span></>
                                }
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="event_location">
                                <Form.Label>Location</Form.Label>
                                <Form.Control custom as="select" name="location_id" value={currentLocation || ""} onChange={currentLocationHandler}>
                                    {validLocations.map((location, i) =>
                                        <option key={`location_${i}`} value={location.id}>{location.name}</option>
                                    )}
                                </Form.Control>
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="event_requires_registration">
                                <Form.Label>Requires Registration</Form.Label>
                                <Form.Switch name="requires_registration" checked={requiresRegistration === 1} onChange={toggleRequiresRegistration} />
                            </Form.Group>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="col-parent-child" sm="12">
                            <Form.Group controlId="event_parent">
                                <Form.Label>Parent Event</Form.Label>
                                {parentEvents ?
                                    <Form.Control required custom as="select" name="parent_id" value={parentId || ""} onChange={parentHandler}>
                                        <option key="default_option" value={"0"}>None</option>
                                        {parentEvents?.map( (parent, i) => {
                                            let dates = '';
                                            if (parent.start_datetime && parent.end_datetime) {
                                                dates = ` (${format(new Date(parent.start_datetime), "MM/dd/yyyy")} - ${format(new Date(parent.end_datetime), "MM/dd/yyyy")})`;
                                            }
                                            return <option key={`option_${i}`} value={parent.id}>{parent.name}{dates}</option>
                                        })}
                                    </Form.Control>
                                : "loading..."
                                }
                                <sub>An event must occur within the duration of its parent. Only valid parents are shown.<br/></sub>
                            </Form.Group>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="col-parent-child" sm="12">
                            {parseInt(currentTypeValue) === 5 ?
                                <Form.Group controlId="child_events" className="events-typeahead">
                                    <Form.Label>Child Events</Form.Label>
                                    {possibleChildEvents && event ?
                                        <Typeahead
                                            id="event-child-search"
                                            formatForLabel={formatForLabel}
                                            makeRequest={getChildEvents}
                                            placeholder={"Enter an event name..."}
                                            async={false}
                                            paginated={false}
                                            multiple={true}
                                            passSelection={(selection) => setCurrentChildEvents(selection)}
                                            initialDataIds={event.children.map(child => child.id)}
                                        />
                                    : <div>loading...</div>
                                    }
                                </Form.Group>
                                : null}
                        </Col>
                    </Row>
                    <Row>
                        <Col className="mt-4 mb-3 col-sm-auto">
                            <Button variant="primary" type="submit" disabled={submitting} className={`${submitting?" submitting":""}`}>Save</Button>
                        </Col>
                        {props.fromModal ? 
                            <>
                                <Col className="mt-4 mb-3 col-sm-auto">
                                    <Button variant="light" type="button" onClick={props.onCancel} >Cancel Edit</Button>
                                </Col>
                            </>
                            :
                            null
                        }
                    </Row>
                </Form>
                {error}
            </Container>
        </Col>
    );
}

export default BasicInfo;