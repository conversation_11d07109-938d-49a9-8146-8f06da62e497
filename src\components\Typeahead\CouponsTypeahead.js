import React, {useState, useCallback} from 'react';

import { Typeahead } from './Typeahead';
import Coupons from '../../api/Coupons';

export const CouponsTypeahead = ({
    multiple=true, 
    async=false, 
    paginated=false, 
    placeholder="Search Available Coupons", 
    ...props}) => {

    const [loading, setLoading]=useState("Loading...");

    const makeRequest=useCallback(async()=>{
        let responseObj;
        try{
            let response = await Coupons.get();
            responseObj={
                data: response.data || null,
                errors: response.errors || null
            }
            setLoading("")
        }catch(ex){
            console.error(ex)
            setLoading("Currently No Coupons");
        }
        return responseObj;
    },[]);

    return (
        <Typeahead
            {...props}
            id="available-coupon-search"
            makeRequest={makeRequest}
            async={async}
            paginated={paginated}
            multiple={multiple}
            placeholder={loading ? `${loading}` : placeholder}
        />
    )
}