import React, { useState, useEffect } from 'react';
import { useH<PERSON><PERSON>, Link } from "react-router-dom";
import {Card, Container, Col, Row, Button, Breadcrumb}  from 'react-bootstrap';
import Table from '../../../../components/common/Table';
import Stack from '../../../../components/common/Stack';
import SubHeader from '../../../../components/common/SubHeader';

import './Dashboard.scss';

import APICms from '../../../../api/Cms';

const Dashboard = (props) => {
    let history = useHistory();

    //let user = authCheck(history);

    const newButtonHandler = (event) => {
        history.push("/p/themes/new");
    }

    const [loading,setLoading]=useState(true);
    const [themes,setThemes]=useState([]);

    // first load, get companies from api
    useEffect(() => {

        const _getThemes = async () => {
            try {
                setLoading(true);
                const res=await APICms.themes.get();
                if (res.data && mounted) setThemes(res.data?.map( cmp => cmp));
                setLoading(false);
            } catch (e){
                console.error(e);
            } 
        }

        let mounted = true;

        _getThemes();

        
        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
            setThemes([]);
        }        
    },[]);


    const columns = React.useMemo(
        () => [{
            id: 'table',
            columns: [
                {
                    Header: 'Theme',
                    id: 'name',
                    accessor: 'name',
                },
                {
                    Header: 'Description',
                    id: 'description',
                    accessor: 'description',
                },
                {
                    id: 'company_id',
                    accessor: "company_id",
                    show:false,
                },
                {
                    id: 'id',
                    url:"/themes/:id",
                    condition: [{
                        field:"company_id",
                        operator: "!=",
                        value: null
                    }],
                    show:false,
                },
            ],
        }],[]
    );
        
    return (
        <Container fluid>
            <SubHeader items={[
                {linkAs:Link,linkProps:{to:"/p/home"},text:"Home"},
                {text:"Themes Dashboard"}
            ]} />
            <Row className="body">
                <Col>
                    <Card className={`${loading?" loading":""} content-card`}>
                        <Stack direction="horizontal" gap={2}>
                            <h4 className="tm-1 section-title order-2 order-lg-1 ">Themes Dashboard</h4>
                            <div className="ms-sm-auto order-1 order-lg-2">
                                <Button variant="primary" onClick={newButtonHandler}>New Theme</Button>
                            </div>
                        </Stack>
                        <hr/>
                        <Table columns={columns} data={themes} />
                    </Card>
                </Col>
            </Row>
        </Container>
    );
}

export default Dashboard;