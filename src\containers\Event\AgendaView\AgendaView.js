import React, {useState, useEffect, useRef, useCallback} from 'react'
import { useHistory, Link } from 'react-router-dom';
import ReactToPrint from 'react-to-print';
import { 
    endOfWeek, startOfWeek, 
    startOfMonth, endOfMonth, 
    startOfDay, endOfDay, 
    add, format, getDay, 
    getDaysInMonth, differenceInCalendarDays } from 'date-fns';

import DateHeader from './DateHeader';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import Datepicker from 'react-datepicker';
import SubHeader from '../../../components/common/SubHeader';
import EventTypesTypeahead from '../../../components/Typeahead/EventTypesTypeahead';
import EventStatusTypeahead from '../../../components/Typeahead/EventStatusTypeahead';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Events from '../../../api/Events';
import Services from '../../../api/Services';
import styles from './AgendaView.module.scss';
import SearchFilters from './SearchFilters';
import { authUserHasModuleAccess } from '../../../utils/auth';

const MANAGE_EVENTS_MODULE_ID = 118;

const pageStyle = `@media print{
    font-size: 8px;
    .date{
        font-size: 10px;
    }
}`
export const AgendaView = () => {
    const history = useHistory();
    const mountedRef = useRef(false);
    const printRef = useRef();
    const searchRef = useRef({
        max_records: 100,
    });

    const [loading, setLoading]=useState(true);
    const [error, setError]=useState();
    const [rangeStart,setRangeStart]=useState(startOfWeek(new Date(), {weekStartsOn: 1 }));
    const [rangeEnd, setRangeEnd]=useState(endOfWeek(new Date(), {weekStartsOn: 1 }));
    const [weekDates, setWeekDates ]=useState([]);
    const [events, setEvents]=useState([]);
    const [combinedDatesEvents, setCombinedDatesEvents]=useState([]);
    const [sortedDatesEvents, setSortedDatesEvents]=useState([]);
    const [selectedEventTypes, setSelectedEventTypes]=useState([]);
    const [services, setServices]=useState([]);
    const [eventInts, setEventInts]=useState([1,2,3,4,6,7,8]);
    const [selectedStatus, setSelectedStatus]=useState();
    const [searchHasChanged, setSearchHasChange]=useState(0)
    const [dateRange, setDateRange]=useState("week")
    const [bothDone, setBothDone]=useState(false); //so it can't continue without running through both events and services, in case one is not searched
    const [modulePermission, setModulePermission]=useState(null)

//#region useCallback    
    const resetStartAndEndDate=useCallback(()=>{
        if(mountedRef.current){
            setRangeStart(startOfWeek(new Date(), {weekStartsOn: 1 }));
            setRangeEnd(endOfWeek(new Date(), {weekStartsOn: 1 }));
        }
    },[]);

    const servicesToEvents=useCallback(sortEvents=>{
        let tempEvents=[];
        sortEvents.forEach((event)=>{
          event.blocks.forEach((block)=>{
            let endTime = calculateEndTime(block)
            block={
              start_date: event.start_date,
              end_date: event.end_date,
              name: event.name,
              days_of_week:block.day_of_week,
              startTime: timesToDates(block.start_time, new Date()),
              endTime: timesToDates(endTime, new Date()),
              duration: block.duration,
            }
            tempEvents.push(block)
          });
        });
        return tempEvents;
    },[]);

    const getEvents=useCallback(async()=>{
        let filterParams = {
            start_datetime: startOfDay(rangeStart).toISOString(),
            end_datetime: startOfDay(rangeEnd).toISOString(),
            event_types: eventInts,
            ...searchRef.current
        }
        if(selectedStatus && selectedStatus.length > 0){
            let tempIds = [];
            for(let i=0; i<selectedStatus.length; i++){
                tempIds.push(selectedStatus[i].id)
            }
            if(tempIds.length > 0)
            filterParams.event_status_id = tempIds
        }
        try{
            let response = await Events.getSimple(filterParams)
            if(!response.errors && mountedRef.current){
                setEvents(response.data.events);
            }else if(response.errors){
                setError(<ErrorCatcher errors={response.errors} />)
            }
        }catch(ex){console.error(ex)}
        if(eventInts.includes(9)){
            try{
                let response = await Services.get({
                    start_datetime: startOfDay(rangeStart).toISOString(),
                    end_datetime: startOfDay(rangeEnd).toISOString(),
                    max_records: 100,
                })
                if(!response.errors){
                    let resp = servicesToEvents(response.data.services)
                    setServices(resp)
                }
            }catch(ex){console.error(ex)}
        }
        setBothDone(true);
    },[rangeStart, rangeEnd, eventInts, servicesToEvents, selectedStatus]);    

    const getDatesForDisplay=useCallback(()=>{
        let tempDays = [];
        tempDays.push(rangeStart);
        let repeatInterval;
        if(dateRange === "day") repeatInterval = 0; //adds the start date before this point
        else if (dateRange === "week") repeatInterval = 5; //adds start and end date
        else if (dateRange === "month") repeatInterval = getDaysInMonth(new Date(rangeStart)) - 2;
        else if (dateRange === "custom") repeatInterval = differenceInCalendarDays(rangeEnd, rangeStart) - 1; //the value returned is the total days -1 - need to account for first and last date
        let newDate = new Date(rangeStart);
        for(let i = 0; i < repeatInterval; i++){
            newDate = add(new Date(newDate), {days: 1})
            tempDays.push(newDate);
        }
        if(dateRange !== "day") tempDays.push(rangeEnd);
        if(mountedRef.current) {
            setWeekDates(tempDays);
            getEvents();
        }
    },[rangeStart, rangeEnd, getEvents, dateRange]);

    //an event should be shown if it occurs on that day.  For some that spans multiple days.
    const sortDatesEvents=useCallback(async()=>{
        let tempCombined=[];
        let eventCopy = [...events]
        weekDates.forEach((date)=>{
            let tempServices;
            let numeralForWeek = getDay(date)
            let day = convertNumeralToDay(numeralForWeek);
            let tempEvents=eventCopy.filter(event=> startOfDay(new Date(event.start_datetime)) <= startOfDay(new Date(date)) && endOfDay(new Date(event.end_datetime)) >= endOfDay(new Date(date)));
            if(eventInts.includes(9)){
                tempServices=services.filter((service)=> new Date(service.start_date) <= new Date(date) && new Date(service.end_date) >= new Date(date) && service.days_of_week === numeralForWeek);
            }
            tempCombined.push({date: date, day: day, events: tempEvents || [], services: tempServices || []})
        })
        setCombinedDatesEvents(tempCombined);
    },[events, services, weekDates, eventInts]);

    const convertTypesToInts=useCallback(()=>{
        let tempIds=[];
        selectedEventTypes.forEach((type)=>{
            tempIds.push(type.id);
        })
        if(mountedRef.current) setEventInts(tempIds);
    },[selectedEventTypes]);

    const sortTimes=useCallback(()=>{
        if(combinedDatesEvents.length > 0){
            let combinedCopy = [...combinedDatesEvents]
            for(let i =0; i < combinedCopy.length; i++){
                let newEvents = combinedCopy[i].events.sort((a,b)=>{
                    if(a.start_datetime > b.start_datetime) return 1
                    if(a.start_datetime < b.start_datetime) return -1
                    else return 0
                })
                combinedDatesEvents[i].events = newEvents
                let newServices = combinedCopy[i].services.sort((a,b)=> {
                    if(a.startTime > b.startTime) return 1
                    if(a.startTime < b.startTime) return -1
                    return 0
                })
                combinedDatesEvents[i].services = newServices
            }
            setSortedDatesEvents(combinedCopy)
            setLoading(false)
        }
    },[combinedDatesEvents]);

//#endregion useCallback

//#region useEffect
    useEffect(()=>{
        mountedRef.current = true;
        
        const checkPermissions = async()=>{
            try{
                let response = await authUserHasModuleAccess(MANAGE_EVENTS_MODULE_ID)
                setModulePermission(response)
            }catch(ex){console.error(ex)}
        }

        checkPermissions()

        return()=> mountedRef.current = false;
    },[]);
    
    useEffect(()=>{
        if(rangeStart && rangeEnd && mountedRef.current) getDatesForDisplay();
    },[getDatesForDisplay, rangeStart, rangeEnd]);

    useEffect(()=>{
        resetStartAndEndDate();
    },[resetStartAndEndDate]);

    useEffect(()=>{
        if(mountedRef.current && bothDone) sortDatesEvents();
    },[sortDatesEvents, bothDone]);

    useEffect(()=>{
        if(selectedEventTypes.length === 0) setEventInts([1,2,3,4,6,7,8]);
        else convertTypesToInts();
    },[selectedEventTypes, convertTypesToInts]);

    useEffect(()=>{
        if(mountedRef.current) {
            setLoading(true);
            setBothDone(false);
        }
        getEvents();
    },[getEvents, eventInts, searchHasChanged]);

    useEffect(()=>{
        if(mountedRef.current) sortTimes()
    }, [sortTimes, combinedDatesEvents])

//#endregion useEffect

    const convertNumeralToDay=(num)=>{
        switch(num){
            case 0: 
                return 'Sunday';
            case 1:
                return 'Monday';
            case 2:
                return 'Tuesday';
            case 3:
                return 'Wednesday';
            case 4:
                return 'Thursday';
            case 5:
                return 'Friday';
            case 6:
                return 'Saturday';
            default:
                break;
        }
    }

    const calculateEndTime=(block)=>{
        let hours = Math.floor(block.duration/60)
        let remainingMinutes = block.duration%60;
        let startHours=block.start_time.substr(0,2);
        let startMinutes=block.start_time.substr(3,2);
        let endHours = +hours + +startHours;
        let endMinutes = +remainingMinutes + +startMinutes;
        if(endMinutes.toString().length===1) endMinutes = "0" + endMinutes;
        let endTime = `${endHours}:${endMinutes}:000`
        if(endHours > 24) endTime="24:00:00"
        return endTime
    }

    const timesToDates=(time, date)=>{
        let splitTime = time.split(':');
        let hours = +splitTime[0];
        let minutes = +splitTime[1];
        let newDate= add(startOfDay(date), {hours: hours, minutes: minutes})
        return newDate;
    }

    const handleSearchFilters = (e, name)=>{
        //handling specific criteria cases
        if(name === "max_records" && (isNaN(e) || e < 1 )) e = 1; 

        if(e){
            searchRef.current[name] = e;
            setSearchHasChange(searchHasChanged + 1)
        }
        else searchRef.current[name] = null;
        console.log(searchRef.current)
    }

    const handleIsChild=(e)=>{
        if(e === "parent") searchRef.current.is_child = 0;
        else if(e === "child") searchRef.current.is_child = 1;
        else {
            if(searchRef.current.hasOwnProperty("is_child")) delete searchRef.current.is_child;
        }
        setSearchHasChange(searchHasChanged + 1)
    }

    const testNumeric=(searchTerm)=>{
        return /^[-+]?(\d+)$/.test(searchTerm);
    }

    const handleDate=(dates, range)=>{
        let start;
        let end;
        let localRange;
        if(range) localRange = range;
        else localRange = dateRange;
        if(Array.isArray(dates)){
            start = dates[0];
            end = dates[1];
        }else start = dates;
        if(localRange === "custom"){
            setRangeStart(start);
            setRangeEnd(end);
        }
        if(localRange === "day"){
            setRangeStart(startOfDay(start));
            setRangeEnd(endOfDay(start));
        }
        if(localRange === "week"){
            setRangeStart(startOfWeek(start, {weekStartsOn: 1 }));
            setRangeEnd(endOfWeek(start, {weekStartsOn: 1 }));
        }
        if(localRange === "month"){
            setRangeStart(startOfMonth(start));
            setRangeEnd(endOfMonth(start));
        }
    }

    const handleDateRange =(range)=>{
        if(range === "custom") setRangeEnd(null);
        setDateRange(range);
        let dates = rangeStart;
        if(range === "custom") dates=[rangeStart, rangeEnd];
        handleDate(dates, range);
    }

    return (
    <Container fluid>
        
        <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/events" }, text: "Event Management" },
                { linkAs: Link, linkProps: { to: "/p/calendar" }, text: "Calendar" },
                { text: "Event Agenda" },
            ]} />
        <Row>
            <Col>
                <Card className="content-card">
                <div className={styles["agenda-date-pick-wrapper"]}>
                    <div className={styles["title-row"]}>
                        <DateHeader 
                            styles={styles}
                            rangeStart={rangeStart}
                            rangeEnd={rangeEnd}
                            dateRange={dateRange}
                        />
                        <div>
                            <ReactToPrint
                                trigger={()=><Button variant="primary"><i style={{fontSize: "18px"}} className="far fa-print"/> Print</Button> }
                                documentTitle={`Agenda for ${rangeStart} to ${rangeEnd}`}
                                content={()=>printRef.current}
                                pageStyle="margin:10px"
                                bodyClass={styles["bodyClassPrint"]}
                            />
                        </div>
                    </div>
                </div>
                <SearchFilters 
                    styles={styles}
                    rangeStart={rangeStart}
                    rangeEnd={rangeEnd}
                    dateRange={dateRange}
                    handleDate={handleDate}
                    handleDateRange={handleDateRange}
                    testNumeric={testNumeric}
                    handleSearchFilters={handleSearchFilters}
                    searchRef={searchRef}
                    handleIsChild={handleIsChild}
                    setSelectedEventTypes={setSelectedEventTypes}
                    setSelectedStatus={setSelectedStatus}
                />
                {sortedDatesEvents && !loading && 
                    <div ref={printRef}>
                        {sortedDatesEvents?.map((date, i)=>(
                            <div key={`day-of-week-${i}`} className={styles["agenda-each-day"]}>
                                <div>
                                    <div className={styles["agenda-date"]}>
                                        <div>
                                            <span>{format(new Date(date.date), 'dd')}</span>
                                        </div>
                                        <div className={styles["date-secondary"]}>
                                            <p>{date.day}</p>
                                            <p>{format (new Date(date.date), "MM/dd/yyyy")}</p>
                                        </div>
                                    </div>
                                </div>
                                <div className={styles["agenda-wrapper"]}>
                                    {date.events.length > 0 || date.services.length >0 ?
                                        <>
                                            {date?.events?.map((event,j)=>(
                                                <div key={`event-display-${date}-${i}-${j}`} className={styles["agenda-event"]}>
                                                    <p className={styles["date"]}>
                                                        {event.event_type_id ===5 ?
                                                            <>
                                                                <span className={styles["start-end"]}>Start :</span>
                                                                {format (new Date(event.start_datetime), "MM/dd/yyyy")} - {" "}
                                                                All Day
                                                                <br/>
                                                                <span className={styles["start-end"]}>{" "}End {" "} : {" "}</span>
                                                                <span style={{paddingLeft: "3px"}}>{format(new Date(event.end_datetime), "MM/dd/yyyy")} - {" "}</span>
                                                                Meta Event
                                                            </>
                                                        :
                                                            <>
                                                                <span className={styles["start-end"]}>Start :</span>
                                                                {format (new Date(event.start_datetime), "MM/dd/yyyy")} - {" "}
                                                                <span className={styles["each-time"]}>
                                                                    {new Date(event.start_datetime).toLocaleString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                                </span>
                                                                <span className={styles["event-name"]}>
                                                                    {event.name} 
                                                                    {modulePermission && modulePermission[MANAGE_EVENTS_MODULE_ID] &&
                                                                        <span>
                                                                            - <Link to={`/p/events/${event.id}`} target="_blank" rel="noreferrer">{" "}Event Details{" "}<i className="fas fa-external-link"/></Link>
                                                                        </span>
                                                                    }    
                                                                    </span>
                                                                <br/>
                                                                <span className={styles["start-end"]}>
                                                                    {" "}End {" "} : {" "}
                                                                </span>
                                                                <span style={{paddingLeft: "3px"}}>
                                                                    {format(new Date(event.end_datetime), "MM/dd/yyyy")} - {" "}
                                                                </span>
                                                                <span className={styles["each-time"]}>
                                                                    {new Date(event.end_datetime).toLocaleString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                                </span>
                                                            </>
                                                        }
                                                    </p>
                                                </div>
                                            ))}
                                            {date?.services?.map((event,j)=>(
                                                <div key={`event-display-${date}-${i}-${j}`} className={styles["agenda-event"]}>
                                                    <p className={styles["date"]}>
                                                        <span className={styles["start-end"]}>Start :</span>
                                                        {/* keeping the dates here so that the spacing is the same */}
                                                        <span style={{visibility: "hidden"}}>{format (new Date(event.start_date), "MM/dd/yyyy")} - {" "}</span>
                                                        <span className={styles["each-time"]}> 
                                                            {new Date(event.startTime).toLocaleString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                        </span>
                                                        <span className={styles["event-name"]}>
                                                            {event.name}
                                                        </span>
                                                        <br/>
                                                        <span className={styles["start-end"]}>End {" "} : </span>
                                                        <span style={{visibility: "hidden"}}>{format(new Date(event.end_date), "MM/dd/yyyy")}{" "} - {" "} </span>
                                                        <span className={styles["each-time"]} style={{paddingLeft: "3px"}}>
                                                            {new Date(event.endTime).toLocaleString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                        </span>
                                                    </p>
                                                </div>
                                            ))}
                                        </>
                                        :
                                        <div className={`h-100 d-flex align-items-center px-2 ${styles["no-event"]}`}>
                                            No events matching the search criteria for this day.
                                        </div>
                                    }
                                </div>
                            </div>
                        ))}
                    </div>
                }
                </Card>
            </Col>
        </Row>
        {error}
    </Container>
    )
}
