import React, { useState, useEffect } from 'react';
import { useHistory } from "react-router-dom";
import {Container,Col,Row,Form,InputGroup,Button} from 'react-bootstrap';

import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';
import Selector from '../../../components/common/Selector';

import Companies from '../../../api/Companies';
import Cms from '../../../api/Cms';
import {stringToUUID} from '../../../api/Common';

const states=[
    {id:"AL",name:"Alabama"},
    {id:"AK",name:"Alaska"},
    {id:"AZ",name:"Arizona"},
    {id:"AR",name:"Arkansas"},
    {id:"CA",name:"California"},
    {id:"CO",name:"Colorado"},
    {id:"CT",name:"Connecticut"},
    {id:"DE",name:"Delaware"},
    {id:"DC",name:"District Of Columbia"},
    {id:"FL",name:"Florida"},
    {id:"GA",name:"Georgia"},
    {id:"HI",name:"Hawaii"},
    {id:"ID",name:"Idaho"},
    {id:"IL",name:"Illinois"},
    {id:"IN",name:"Indiana"},
    {id:"IA",name:"Iowa"},
    {id:"KS",name:"Kansas"},
    {id:"KY",name:"Kentucky"},
    {id:"LA",name:"Louisiana"},
    {id:"ME",name:"Maine"},
    {id:"MD",name:"Maryland"},
    {id:"MA",name:"Massachusetts"},
    {id:"MI",name:"Michigan"},
    {id:"MN",name:"Minnesota"},
    {id:"MS",name:"Mississippi"},
    {id:"MO",name:"Missouri"},
    {id:"MT",name:"Montana"},
    {id:"NE",name:"Nebraska"},
    {id:"NV",name:"Nevada"},
    {id:"NH",name:"New Hampshire"},
    {id:"NJ",name:"New Jersey"},
    {id:"NM",name:"New Mexico"},
    {id:"NY",name:"New York"},
    {id:"NC",name:"North Carolina"},
    {id:"ND",name:"North Dakota"},
    {id:"OH",name:"Ohio"},
    {id:"OK",name:"Oklahoma"},
    {id:"OR",name:"Oregon"},
    {id:"PA",name:"Pennsylvania"},
    {id:"RI",name:"Rhode Island"},
    {id:"SC",name:"South Carolina"},
    {id:"SD",name:"South Dakota"},
    {id:"TN",name:"Tennessee"},
    {id:"TX",name:"Texas"},
    {id:"UT",name:"Utah"},
    {id:"VT",name:"Vermont"},
    {id:"VA",name:"Virginia"},
    {id:"WA",name:"Washington"},
    {id:"WV",name:"West Virginia"},
    {id:"WI",name:"Wisconsin"},
    {id:"WY",name:"Wyoming"}
];

const BasicInfo = (props) => {
    let history = useHistory();

    const [company, setCompany] = useState({});
    const [currentStateValue, setCurrentStateValue] = useState();
    const [currentApiKey, setCurrentApiKey] = useState(stringToUUID(Math.random()));
    const [currentSubdomain, setCurrentSubdomain] = useState();
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

    const currentStateHandler = (e) => {
        setCurrentStateValue(e.target.value);
    }

    const generateApiKeyHandler = () => {
        setCurrentApiKey(stringToUUID(Math.random()));
    }

	useEffect(() => {
        const _getCompany=async () => {
            try {
                const res=await Companies.get(props.company_id);
                if (res.data && mounted) {
                    setCompany(res.data);
                    setCurrentApiKey(res.data.api_key);
                }
            } catch (e){
                console.error(e);
            }
        }

        const _getUrl=async () => {
            try {
                const res=await Cms.urls.get({company_id:props.company_id});
                if (res.data && mounted) {
                    setCurrentSubdomain(res.data?.[0]?.subdomain);
                }
            } catch (e){
                console.error(e);
            }
        }

        let mounted = true;
        if (props.company) {
            setCompany(props.company);
            setCurrentApiKey(props.company.api_key);
        } else _getCompany();
        if (props.company_id) _getUrl();

        return () => {
            mounted = false;
        }

	}, [props.company_id, props.company]);


    useEffect(() => {
        return () => {
            setCompany({});            
            setCurrentStateValue(null);
            setCurrentApiKey(null);
            setCurrentSubdomain(null);
        }
    }, []);

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            if (props.company_id) formData.append("id", props.company_id);
            const formDataObj = Object.fromEntries(formData.entries());

            let response;
            if (props.company_id) response=await Companies.update(formDataObj);
            else response=await Companies.create(formDataObj);
            
            if (!response.errors) {
                // updates or creates the url
                if (formDataObj.subdomain){
                    let cid;
                    let create_url=true;
                    if (props.company_id){
                        cid=props.company_id;
                        const res = await Cms.urls.get({company_id:props.company_id});
                        if (res.data && res.data.length) {
                            create_url=false;
                            await Cms.urls.create({id:res.data[0].id,company_id:props.company_id,subdomain:formDataObj.subdomain});
                        }
                    } else cid=response.data[0].id;

                    if (create_url === true) await Cms.urls.create({company_id:cid,subdomain:formDataObj.subdomain}); 
                }

                setSubmitting(false);
                setValidated(false);
                setSuccess(<Toast>Company saved successfully!</Toast>);
                history.push(props.referer || "/p/companies/dashboard"); // pushes to profile again to avoid resubmission
            } else { // api returned errors
                setSubmitting(false);
                setError(<ErrorCatcher error={response.errors} />);
            }

        } else setSubmitting(false);
    };


    return (
        <Container fluid>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Row>
                    <Col sm="12">
                        <Form.Group controlId="name">
                            <Form.Label>Company Name</Form.Label>
                            <Form.Control required type="text" name="name" defaultValue={company?.name || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="owner_name">
                            <Form.Label>Owner Full Name</Form.Label>
                            <Form.Control type="text" name="owner_name" defaultValue={company?.owner_name || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="email">
                            <Form.Label>Email</Form.Label>
                            <Form.Control required type="email" name="email" defaultValue={company?.email || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="primary_number">
                            <Form.Label>Phone</Form.Label>
                            <Form.Control type="tel" name="primary_number" defaultValue={company?.primary_number || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="fax_number">
                            <Form.Label>Fax</Form.Label>
                            <Form.Control type="tel" name="fax_number" defaultValue={company?.fax_number || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="address_street">
                            <Form.Label>Address Line 1</Form.Label>
                            <Form.Control type="text" name="address_street" defaultValue={company?.address_street || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="address_street2">
                            <Form.Label>Address Line 2</Form.Label>
                            <Form.Control type="text" name="address_street2" defaultValue={company?.address_street2 || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="4">
                        <Form.Group controlId="address_city">
                            <Form.Label>City</Form.Label>
                            <Form.Control type="text" name="address_city" defaultValue={company?.address_city || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="4">
                        <Form.Group controlId="address_state">
                            <Form.Label>State</Form.Label>
                            <Selector required custom as="select" name="address_state" options={states} value={currentStateValue || ""} onChange={currentStateHandler} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="4">
                        <Form.Group controlId="address_postcode">
                            <Form.Label>Zip Code</Form.Label>
                            <Form.Control type="text" name="address_postcode" defaultValue={company?.address_postcode || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="subdomain">
                            <Form.Label>Subdomain</Form.Label>
                            <InputGroup>
                                <Form.Control type="text" max="155" name="subdomain" defaultValue={currentSubdomain || ""} style={{textAlign:"right"}} />
                                <InputGroup.Text>.siteboss.net</InputGroup.Text>
                            </InputGroup>
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="api_key">
                            <Form.Label>API Key</Form.Label>
                            <InputGroup>
                                <Form.Control required type="text" max="36" name="api_key" readOnly value={currentApiKey || company?.api_key  || ""} />
                                <InputGroup.Append>
                                    <Button variant="outline-secondary" onClick={generateApiKeyHandler}>New</Button>
                                </InputGroup.Append>
                            </InputGroup>
                        </Form.Group>
                    </Col>
                </Row>
                <Row>
                    <Col sm="12" lg="4" className="mt-4 mb-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={`${submitting?" submitting":""}`}>Save</Button>
                    </Col>
                </Row>
            </Form>
            {error}
        </Container>
    );
}

export default BasicInfo;