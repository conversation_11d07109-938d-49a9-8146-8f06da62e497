import React, {useState, useEffect} from 'react'

const CreateEachParam = ({createdParam, removeParam, activeConfig = false, ...props}) => {
  
    const [paramName, setParamName] = useState(createdParam.paramName || "");
    const [paramType, setParamType] = useState(createdParam.type || "text")
    const [isRequired, setIsRequired]=useState(createdParam.required || 0);
    const [description, setDescription]=useState(createdParam.description || "");

    useEffect(()=>{
        if(activeConfig && createdParam){
            setParamName(createdParam.paramName);
            setParamType(createdParam.type);
            setIsRequired(createdParam.required);
            setDescription(createdParam.description);
        }
    },[activeConfig, createdParam])

    useEffect(()=>{
        let mounted = true;
        if(mounted){
            createdParam.paramName = paramName;
            createdParam.type = paramType;
            createdParam.isRequired = isRequired;
            createdParam.description = description;
        }

        return ()=> mounted = false;

    },[paramName, paramType, isRequired, description, createdParam]);

    return (
        <div className="each-param-wrapper">
            <div className="label-input-pair">
                <label htmlFor="param-name">
                    Param Name <span className="required-star">*</span>
                </label>
                <input
                    name="param-name"
                    value={paramName}
                    onChange={(e)=>setParamName(e.target.value)}
                    required
                />
            </div>
            <div className="label-input-pair">
                <label htmlFor="param-type">
                    Param Type
                </label>
                <select name="param-type" defaultValue={paramType} onChange={(e)=>setParamType(e.target.value)}>
                    <option value="text" selected={paramType==="text" ? true: false}>Text</option>
                    <option value="bool" selected={paramType==="bool" ? true: false}>True/False</option>
                </select>
            </div>
            <div className="label-input-pair">
                <label htmlFor="is-required">
                    Is Required?
                </label>
                <select name="is-required" defaultValue={isRequired} onChange={(e)=>setIsRequired(+e.target.value)}>
                    <option value="1" selected={isRequired===1 ? true : false}>Yes</option>
                    <option value="0" selected={isRequired===0 ? true: false}>No</option>
                </select>
            </div>
            <div className="label-input-pair">
                <label htmlFor="description">
                    Description
                </label>
                <input 
                    name="description"
                    value={description}
                    onChange={(e)=>setDescription(e.target.value)}
                />
            </div>
            <span 
                className="cp fake-btn"
                onClick={()=>removeParam(createdParam.tempId)}
            >
                X
            </span>
        </div>
    )
}

export default CreateEachParam