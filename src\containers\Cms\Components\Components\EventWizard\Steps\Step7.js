import React, {useState, useEffect} from 'react';
import {useSelector, useDispatch} from 'react-redux';

import APIUsers from '../../../../../../api/Users';
import APIProducts from '../../../../../../api/Products';
import APIPOS from '../../../../../../api/Pos';
import { addToCart } from "../../../../../../utils/thunks";

export const Step7 = (props) => {
    const {disableNext, disableBack} = props;

    const dispatch = useDispatch();
    const user = useSelector(state => state.auth.user.profile);
    const defaultPatronRegister = useSelector(state => state.pos.register)

    const [loading, setLoading] = useState(true);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [customFields, setCustomFields] = useState();
    const [error, setError] = useState([]);
    const [success, setSucess] = useState([]);

    useEffect(() => {
        if (props.stepValues?.selectedUsers) setSelectedUsers(props.stepValues.selectedUsers);
        else setSelectedUsers([{id: user.id, first_name: user.first_name, last_name: user.last_name}]);
        if (props.stepValues?.customFields) setCustomFields(props.stepValues.customFields);
    }, [user, props.stepValues]);    

    // save info
    useEffect(() => {
        const _saveInfo = async () => {
            setLoading(true);
            let _errors = false;
            
            let items = [];
            for (let seluser of selectedUsers){
                let data = {
                    for_user_id: seluser.id,
                    by_user_id: user.id,
                    event_id: props.id,
                };

                items.push({
                    variant_id: props.default_variant_id,
                    qty: 1,
                    event: {
                        event_id: props.id,
                        for_user_id: seluser.id
                    }
                });

                if (customFields){
                    if (customFields?.[seluser.id]){
                        customFields[seluser.id].forEach(field=>{
                            data[`custom_${field.custom_field_id}`] = `${field.value}`;
                        });
                    }
                }

                // if a payment token is present, create the order
                if (props.stepValues.token){
                    const res = await APIPOS.order.save({
                        register_id: defaultPatronRegister,
                        user_id: user.id,
                        items: items // defined up there ^
                    });
                    if (res?.errors){
                        setError(prev=>[...prev,"Error while saving the order."]);
                        _errors = true;
                    } else if (res?.data){
                        const payments = [{
                            payment_method_id: 1,
                            amount: res.data[0].total_price,
                            type: 'sale',
                            description: props.name || "Online Credit Card Payment",
                                payment_token: props.stepValues.token,
                                first_name: props.stepValues.first_name || user.first_name,
                                last_name: props.stepValues.last_name || user.last_name,
                                bill_address1: props.stepValues.address1 || user.address1,
                                bill_address2: props.stepValues.address2 || user.address2,
                                bill_city: props.stepValues.city || user.city,
                                bill_state: props.stepValues.state || user.state,
                                bill_postalcode: props.stepValues.postal_code || user.postal_code,
                        }];
        
                        const res2 = await APIPOS.payment.process({order_id: res.data[0].id, payments});

                        /*const res2 = await APIPOS.payment.card({
                            order_id: res.data[0].id,
                            type: 'sale',
                            description: props.name,
                            first_name: props.stepValues.first_name || user.first_name,
                            last_name: props.stepValues.last_name || user.last_name,
                            bill_address1: props.stepValues.address1 || user.address1,
                            bill_address2: props.stepValues.address2 || user.address2,
                            bill_city: props.stepValues.city || user.city,
                            bill_state: props.stepValues.state || user.state,
                            bill_postalcode: props.stepValues.postal_code || user.postal_code,
                            payment_token: props.stepValues.token
                        });*/
                        if (res2?.errors){
                            setError(prev=>[...prev,<b className="error">Payment Failed</b>]);
                            console.log(res2.errors);
                            _errors = true;
                        } else {
                            setSucess(prev=>[...prev,"Payment Successful"]);
                        }
                    }
                } else {
                    setError(prev=>[...prev,"No payment token."]);
                    _errors = true;
                }

                if (!_errors){
                    const res = await APIUsers.eventRegister(data);
                    if (res?.errors){
                        setError(prev=>[...prev,"Error while saving the event."]);
                    } else if (res?.data){
                        if (props.product_id && !props.stepValues.token){
                            const res2 = await APIProducts.get({id: props.product_id});
                            if (res2?.data?.length>0){
                                const item = res2.data[0];
                                dispatch(addToCart([{
                                    category_id: item.categories[0]?.id,
                                    discounts: 0,
                                    hash: null,
                                    id: item.id,
                                    product_type_id: item.product_type_id,
                                    is_taxable: item.is_taxable,
                                    original_price: +item.product_variants[0].price,
                                    parent_id: null,
                                    product_name: item.name,
                                    product_price: +item.product_variants[0].price,
                                    qty: 1,
                                    type: 1,
                                    variant_id: item.product_variants[0].id,
                                    variant_name: item.product_variants[0].name,
                                    event: {
                                        event_id: props.id,
                                        for_user_id: seluser.id
                                    }
                                }]));
                            }
                        }
                    }
                }
            }

            setLoading(false);
            disableNext(true);
            if (!_errors) disableBack(true);
        }

        _saveInfo();
    }, [selectedUsers, customFields, user, props.id, props.name, props.product_id, props.token, props.default_variant_id, props.stepValues, dispatch, disableNext, disableBack, defaultPatronRegister]);

    useEffect(() => {
        return () => {
            setLoading(false);
        }
    }, []);

    if (loading) return (<p>Processing...</p>);
    if (error.length>0) return (
        <>
            <p>
                {success.length>0 && "Your payment was successful. However an error ocurred while processing your registration. Please contact us for more information."}
                {!success.length>0 && "An error ocurred while processing your registration. Please try again later."}
            </p>            
            Error details:
            <ul>
                {error.map((err, i)=>(
                    <li><small key={`err-${i}`}>{err}</small></li>
                ))}
            </ul>
        </>
    );

    return (
        <>
            <p>
                You successfully registered for this event.<br/>
                Please check your email for a confirmation.
            </p>
        </>
    );
}