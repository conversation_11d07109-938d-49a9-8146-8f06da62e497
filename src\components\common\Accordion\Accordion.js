import React, { useState } from 'react';
import { Accordion as BootstrapA<PERSON>rdi<PERSON>, Card } from 'react-bootstrap';
import styles from './Accordion.module.scss';

/**
* 
*
* @param {Array} items - Array of objects with the following structure:
*       @param {string} items[].title - Header text for the accordion item
*       @param {ReactNode} items[].content - Content to be displayed when item is expanded
* @param {string} activeKey default null - Key of the initially expanded accordion item
* @param {boolean} primary default false - Use primary styling instead of neutral
* @param {Function} onToggle - Callback fired when an item is toggled
 */
export const Accordion = ({ items, activeKey = null, primary=true, onToggle=null }) => {

    const [localKey, setLocalKey]=useState(activeKey);

    const handleSelect=(item, index)=>{
        if(localKey===index) setLocalKey(null)
        else setLocalKey(index.toString())
        if(onToggle) onToggle(item, index);
    }

    return (
        <BootstrapAccordion defaultActiveKey={localKey} className={styles["accordion-wrapper"]}>
            {items?.map((item, index) => (
                <React.Fragment key={`accordion-item-${index}`} >
                    {item?.title && item?.content &&
                        <Card className={`${styles["overall-item"]} ${primary ? styles["primary"] : styles["neutral"]}`}>
                            <BootstrapAccordion.Toggle 
                                onClick={()=>handleSelect(item, index.toString())}
                                as={Card.Header} 
                                eventKey={index.toString()}
                                className={`${styles["card-toggle"]} ${primary ? styles["primary"] : styles["neutral"]}`}
                            >
                                <span>{item?.title}</span>
                                <i className={`far fa-chevron-${localKey === index.toString() ? "up" : "down"}`} />
                            </BootstrapAccordion.Toggle>
                            <BootstrapAccordion.Collapse eventKey={index.toString()} className={styles["collapse-portion"]}>
                                <Card.Body>
                                    {item?.content}
                                </Card.Body>
                            </BootstrapAccordion.Collapse>
                        </Card>
                    }
                </React.Fragment>
            ))}
        </BootstrapAccordion>
    );
};
