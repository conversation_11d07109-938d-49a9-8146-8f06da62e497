/* eslint-disable*/
//CTRL+F "Choose a" to change parameters of the test
//In Firefox, the loading skeleton tests will fail.  Can be commented out for FF CTRL+F "FF LS"

//visit home page
// click on sign up now
// fill out the form
// attempt to fill it out with a username that's invalid
// try to fill it out without putting in a birthday
// try to use a username that's already taken
// fill it out with proper information
// register for an account
// signin with the account

// click on My Profile
// click on edit profile
// make sure the birthday is the same one added when registering
// change the phone number to something new
// check that local storage updated with new name

// sign the user's waiver
// make sure that banner disappears
// make sure the document tab includes the signed waiver for download
// balance due shows 0
// profile image can be clicked on
// opens the upload window
// clicks on tokens and opens the window for it
// make sure contact info includes the email and phone number that were registered with

let devPatron = Cypress.env('impact_patron_user')
let qaPatron = Cypress.env('impact_patron_user')
let qaDevStaff = Cypress.env('impact_staff_user')
let qaDevAdmin = Cypress.env('impact_admin_user')
let qaDevSB = Cypress.env('impact_sb_user')
let password = Cypress.env('login_password')
// let baseUrl = "http://localhost:3000/p/";
let baseUrl = "https://portal-qa.impactathleticsny.com/p/";

const randomWordArray=["curious","trap","lead","glad","iron","event","phrase","electricity","mother","sell","half","failed","division","sale","chose","three","acres","brush","finally","managed","ice","tree","mail","education","might","light","machine","importance","coast","protection","piece","tiny","simplest","combination","previous","fed","establish","said","heart","contain","kids","job","block","compound","uncle","fresh","replace","fireplace","sit","badly","iron","rock","yesterday","examine","light","trouble","automobile","lie","strong","alone","trick","breeze","quick","smooth","diagram","ruler","similar","army","carry","month","is","rubbed","material","traffic","mood","operation","cost","itself","calm","motor","week","fine","particular","go","transportation","source","sitting","tie","perhaps","bark","sad","upon","chest","tobacco","look","poetry","driven","small","bone","pony"]
const randomeWordsTwo=["log","hard","burn","honor","beauty","station","raise","long","question","division","dress","remain","silk","nearly","swam","rise","upward","press","unhappy","material","slow","lesson","direct","pain","live","equipment","wall","willing","simply","title","glad","needle","solid","coast","concerned","pink","came","stretch","breeze","escape","stiff","depend","roof","typical","design","plant","thumb","from","idea","grew","silver","blew","supper","everything","why","eager","cell","load","sea","wheel","twelve","dog","fun","carry","difficult","passage","nuts","high","after","sky","bottom","medicine","pour","maybe","season","chosen","even","desk","bush","source","ordinary","excellent","powder","wealth","stay","wagon","cell","nose","age","example","feed","page","faster","kids","before","needed","these","cat","bank","freedom"]
const randomNumberArray=[];


describe("Check that a patron has the appropriate profile tabs", {scrollBehavior: "center", testIsolation: false}, ()=>{
    let local;
    before(()=>{

    //Choose a test environment
        
        cy.loginLocal(baseUrl, devPatron, password).then(()=>{
            local = localStorage.getItem('user');
        })
        
    })

    beforeEach("restore local storage", ()=>{
        cy.viewport(1920,1080)
        cy.wait(500)
        cy.restoreLocalUser(JSON.parse(local))
    }); //end restore local user

    context("Checking the User Profile from the User's perspective",()=>{

        it("should be a patron user and go to the profile page",()=>{
            cy.get(JSON.parse(local).profile.roles).then($roles=>{
                cy.get($roles).should('deep.include', [{id: 7, name: 'Patron'}])
            })
        

            //Choose a tab based on environment
                //the profile tab on the side nav
                // cy.get('#menu-item-49').click() //QA - not nested, only needs one click
                cy.get('#menu-item-48').click() //Dev/local (it's nested so need both clicked)
                cy.get('#menu-item-19').click() //Dev/local
                
                cy.url().should('include','/profile')
            //FF LS
                cy.get('.react-loading-skeleton').should('exist')
                cy.intercept('GET', '/api/user/user/**').as('getUserUser');
                cy.wait('@getUserUser');
                cy.wait(1000)
        })//end check patron
    
        it('should conditionally show the waiver banner based upon the response from the user call',()=>{
           
            cy.log(`${String.fromCodePoint(0x1F92F)} If a user has a signed waiver, has_signed_waiver should be 1, if not, it should be 0`)
            let hasWaiver = JSON.parse(local).profile.has_signed_waiver;
            if(hasWaiver === 0){
                cy.get('[data-cy="waiver-banner-unsigned"]').should('exist')
                cy.get('button').contains('Sign Your Waiver').click()
                cy.get('.waiver-iframe').should('exist')
                cy.get('.close').click()
            }else{
                cy.get('[data-cy="waiver-banner-unsigned"]').should('not.exist')
            }
        })//end waiver check
    
        it("should load the appropriate tabs for a patron",()=>{
            let patronTabs = ['Edit Profile', 'Groups', 'Family Members', 'Subscriptions', 'Outstanding Charges', 'Event Schedule', 'Reset Password', 'Documents', 'Notification Settings']
            cy.get('.profileMenu')
                .children()
                .its('length')
                .as('tabAmounts')
            cy.get('@tabAmounts').then(($tabAmounts)=>{
                expect($tabAmounts).to.eq(patronTabs.length)
            })
            cy.get('.profileMenu').children().each(($tab, i)=>{
                cy.get($tab).contains(patronTabs[i])
            })
        })//end access check
    
        it("should prevent a user from changing to a username in use", ()=>{
            cy.intercept('POST', '/api/user/check-username-availability').as('checkUsername');
            cy.intercept('/api/user/user/**', cy.spy().as('updateUser'));
    
            cy.get('#username')
                .type('{selectAll}')
                .type('{backspace}')
                .type(qaDevAdmin)
            cy.get('button')
                .contains('Save')
                .click()
            cy.get('div')
                .contains('Username is unavailable.')
                .should('exist')
            cy.get('@updateUser').should('not.have.been.called');
        })//end prevent username change if taken
    
        it("will click on the group tab and check it's data",()=>{
            let groups;
            cy.intercept('GET', 'api/user/groups/**').as('getGroups');
            cy.get('[data-cy="Groups"]').click()
            cy.wait('@getGroups').then((response)=>{
                groups = (JSON.parse(response.response.body)).data
                cy.log(groups)
            }).then(()=>{
                cy.get('h4')
                    .invoke('text')
                    .should('contain', 'Groups')
                if(groups.length === 0){
                    cy.get('p').contains('No groups to display.')
                }else{
                    cy.get('[data-cy="group-row"]')
                        .children()
                        .should('have.lengthOf', groups.length);
                        //have to add one because the header is also considered a child of this container
                }
            })
        })//end group tab

        it("will click on the Family Member tab",()=>{
            let groups;
            let family;
            cy.intercept('GET', '/api/user/groups/**').as('getGroups');
            cy.intercept('GET', '/api/group/**').as('getFamily');
            cy.get('[data-cy="Family"]').click()
        //FF LS
            cy.get('.react-loading-skeleton').should('exist')
            cy.wait('@getGroups').then((response)=>{
                groups = (JSON.parse(response.response.body)).data
                groups = groups.filter((group)=>{
                    if(group.group_type_id === 4) return group
                })
                cy.log(groups)
            }).then(()=>{
                if(groups.length > 0){
                    cy.wait('@getFamily').then((response)=>{
                        family = (JSON.parse(response.response.body)).data
                        cy.log(family)
                    }).then(()=>{
                        if(groups.length === 0){
                            cy.get('p').contains('No Family Registered.')
                        }else{
                            let filteredFamily = family[0].group_members.filter((member)=>{
                                if(member.group_member_status_id === 2) return member
                            })
                            cy.get('[data-cy="family-card"]')
                                .should('have.lengthOf', groups.length)
                                .eq(0)
                                .within(($section)=>{
                                    cy.get($section)
                                    .children()
                                    .get('table')
                                    .get('tbody')
                                    .children()
                                    .should('have.lengthOf', filteredFamily.length)

                                })
                        }
                    })
                }
            })
        })//end family tab

        it("will check the subscription tab",()=>{
            let subscriptions;
            cy.intercept('GET', '/api/user/user/**').as('getSubscriptions');
            cy.get('[data-cy="Subscriptions"]').click()
        //FF LS
            cy.get('.react-loading-skeleton').should('exist')
            cy.wait('@getSubscriptions').then((response)=>{
                subscriptions = (JSON.parse(response.response.body)).data[0].subscriptions
                cy.log(subscriptions)
            }).then(()=>{
                cy.get('h4')
                    .should('contain', 'Subscriptions');
                cy.get('.react-loading-skeleton')
                    .should('not.exist');
                if(subscriptions.length === 0){
                    cy.get('p')
                        .contains('No Current Subscriptions');
                }else{
                    cy.get('.profile-subscriptions-wrapper')
                        .children()
                        .should('have.lengthOf', subscriptions.length);
                }
            })
        })//end subscriptions

        it("will show patron's their charges and the charges of their family",()=>{
            let charges;
            cy.intercept('POST', '/api/user/outstanding_charges').as('getCharges')
            cy.get('[data-cy="Outstanding"]').click()
            cy.wait('@getCharges').then((response)=>{
                let allCharges = []
                let allChargesNest = (JSON.parse(response.response.body)).data
                allChargesNest.outstanding_charges.forEach((charge)=>{
                    allCharges.push(charge)
                })
                allChargesNest.family.forEach((charge)=>{
                    charge.outstanding_charges.forEach((charge)=>{
                        allCharges.push(charge)
                    })
                })
                charges = allCharges;
            }).then(()=>{
                cy.get('h4')
                    .should('contain', 'Outstanding Charges');
                cy.get('button')
                    .contains('Record Payment')
                    .should('not.exist')
                cy.get('tbody')
                    .children()
                    .should('have.lengthOf', charges.length)
                    .first()
                    .should('contain', charges[0].event_name)
                cy.get('tbody')
                    .children()
                    .last()
                    .should('contain', charges[charges.length-1].event_name)
            })
        })//end outstanding charges

        // Test is working, events need to be updated
        it("will make sure the event schedule shows nothing before today's date",()=>{
            let events;
            let today = new Date();
            let startAfter = false;
            let endAfter = false;
            let upcoming = true;
            cy.intercept('POST', '/api/event').as('getSchedule');
            cy.get('[data-cy="Schedule"]').click()
        //FF LS
            cy.get('.react-loading-skeleton').should('exist')
            cy.wait('@getSchedule').then((response)=>{
                events = (JSON.parse(response.response.body)).data
            }).then(()=>{
                cy.get('h4')
                    .should('contain', 'Event Schedule');
                cy.log(events)
                if(events.events.length > 0){
                    cy.get('table')
                    .get('tbody').within(()=>{
                        cy.get('tr').each(($row)=>{
                            cy.get($row).within(()=>{
                                cy.get('td')
                                    .eq(1) //start date column
                                    .invoke('text').as('startdate')
                                cy.get('@startdate').then(($date)=>{
                                    if(new Date($date) > today){
                                        startAfter = true
                                    }
                                })
                                cy.get('td')
                                    .eq(2) //end date column
                                    .invoke('text').as('endDate')
                                cy.get('@endDate').then(($date)=>{
                                    if(new Date($date) > today){
                                        endAfter = true
                                    }
                                })
                                if(startAfter && endAfter === false) upcoming = false
                                expect(upcoming).to.equal(true)
                                cy.get('td')
                                    .last()
                                    .invoke('text')
                                    .should('contain', 'Confirmed')
                            })
                        })
                    })
                }else{
                    cy.get('p')
                        .contains('Currently No Scheduled Events')
                }
            })
        })//end event schedule

        it("will check the document tab",()=>{
            cy.get('[data-cy="Documents"]').click()
        //FF LS
            //loading skeleton happens so fast on this page that they may not be seen, disabling check FOR skeleton, just making sure its gone
            // cy.get('.react-loading-skeleton').should('exist')
            cy.wait(500) //there is no call here, it only waits for props to load
            cy.get('.react-loading-skeleton').should('not.exist')
            cy.get('h4').invoke('text').should('contain','Documents');
            if(JSON.parse(local).profile.has_signed_waiver === 0){
                cy.get('.col-sm-12 > .text-center')
                    .invoke('text')
                    .should('contain', 'This user has no signed documents on file.')
            }else{
                const downloadsFolder = Cypress.config('downloadsFolder')
                const userId = JSON.parse(local).profile.id
                cy.get('.wav-card')
                    .should('contain', 'Registration Waiver')
                cy.get('.wav-a-tag')
                    .should('contain', 'Download Copy')
            }
            cy.log(JSON.parse(local).profile.has_signed_waiver)
        })
    })

})//end patron check for Profile


//doing admin seperately as there will be other tabs to test (there will be some duplication of tests for the other tabs)
describe("It will check functionality for admin on profile", {scrollBehavior: "center"}, ()=>{

    let local;
    let roles;
    let isSiteBoss = false;
  
    before(()=>{

    
        cy.loginLocal(baseUrl, qaDevAdmin, password).then(()=>{
            local = localStorage.getItem('user');
        })
    })

    beforeEach("restore local storage", ()=>{
        //added buffers of time to set the local storage as the only timing errors this test is running into are not having local storage in tact
        cy.viewport(1920,1080)
        cy.wait(500)
        cy.restoreLocalUser(JSON.parse(local))
        cy.wait(500)
    }); //end restore local user

    it("will check the roles for following tests",()=>{
        roles=(JSON.parse(local).roles)
        
        roles.forEach(role=>{
            if(role.id === 1) isSiteBoss = true
        })
        cy.log(`${String.fromCodePoint(0x1F92F)} Is this user a site boss admin?? ${isSiteBoss}`)
    })

    context("It will check an admin profile for functionality as well", ()=>{
        it("navigate to the profile",()=>{
            cy.intercept('GET', '/api/user/user/**').as('getUserUser');
            cy.intercept('GET', '/api/user/wallet').as('getWallet');
            cy.intercept('POST', '/api/user/tokens').as('getTokens');
            cy.intercept('GET', '/api/user/groups/**').as('getGroups');


            //Choose a tab tab according to environment
            // cy.get('#menu-item-49').click() //QA - not nested, only needs one click
            cy.get('#menu-item-48').click() //Dev/local (it's nested so need both clicked)
            cy.get('#menu-text-19').click() //Dev/local
        //FF LS
            cy.get('.react-loading-skeleton').should('exist')
            cy.url().should('include','/profile')
            cy.wait('@getUserUser');
            cy.wait('@getWallet');
            cy.wait('@getTokens');
            cy.wait('@getGroups');
        }) //end log in admin
        
        it("should have the appropriate tabs for an admin/above user",()=>{
            // let adminTabs = ['Edit Profile', 'Groups', 'Notes', 'Family Members', 'Roles & Permissions', 'Subscriptions', 'Outstanding Charges', 'Event Schedule', 'Reset Password', 'Documents', 'Notification Settings', 'Check In History', 'Transactions', "Print QR Label"]
            let adminTabs = ['Edit Profile', 'Groups', 'Notes', 'Family Members', 'Roles & Permissions', 'Subscriptions', 'Outstanding Charges', 'Event Schedule', 'Reset Password', 'Documents', 'Notification Settings', 'Check In History', 'Transactions']
            cy.get('.profileMenu')
                .children()
                .its('length')
                .as('tabAmounts')
            cy.get('@tabAmounts').then(($tabAmounts)=>{
                expect($tabAmounts).to.eq(adminTabs.length)
            })
            cy.get('.profileMenu')
                .children()
                .each(($tab, i)=>{
                    cy.get($tab).contains(adminTabs[i])
                })
        })//end profile tab check

        it("should be a staff or above person and go to the profile page",()=>{
            
        //Choose a check for the chosen user
            if(!isSiteBoss){
                cy.get(JSON.parse(local).profile.roles).then($roles=>{
                    cy.get($roles).should('deep.include', [{id: 4, name: 'Company Super Admin'}])
                    // cy.get($roles).should('deep.include', {id: 4, name: 'Company Staff'})
                })
            }
            
                cy.url().should('include','/profile')
                cy.get('.react-loading-skeleton').should('not.exist')
            })//end check patron
    
        it('should conditionally show the waiver banner based upon the response from the user call',()=>{
            cy.log(`${String.fromCodePoint(0x1F92F)} If a user has a signed waiver, has_signed_waiver should be 1, if not, it should be 0`)
            let hasWaiver = JSON.parse(local).profile.has_signed_waiver;
            if(hasWaiver === 0){
                cy.get('[data-cy="waiver-banner-unsigned"]').should('exist')
                cy.get('button').contains('Sign Your Waiver').click()
                cy.get('.waiver-iframe').should('exist')
                cy.get('.close').click()
            }else{
                cy.get('[data-cy="waiver-banner-unsigned"]').should('not.exist')
            }
        })//end waiver check
    
        it("should prevent a user from changing to a username in use", ()=>{
            cy.intercept('POST', 'api/user/check-username-availability').as('checkUsername');
            cy.intercept('/api/user/user/**', cy.spy().as('updateUser'));
    
            if(!isSiteBoss){
                //the username is the sb user name, so cannot do this check with that user
                cy.get('#username')
                    .type('{selectAll}')
                    .type('{backspace}')
                    .type(qaDevSB)
                    cy.get('button')
                        .contains('Save')
                        .click()
                    cy.wait('@checkUsername')
                    cy.get('.invalid-feedback')
                        .contains('Username is unavailable. Please pick another.')
                        .should('exist')
                    cy.get('@updateUser').should('not.have.been.called');
            }
        })//end prevent username change if taken
    
        it("will click on the group tab and check it's data",()=>{
            let groups;
            cy.intercept('GET', 'api/user/groups/**').as('getGroups');
            cy.get('[data-cy="Groups"]').click()
            cy.wait('@getGroups').then((response)=>{
                groups = (JSON.parse(response.response.body)).data
                cy.log(groups)
            }).then(()=>{
                cy.get('h4')
                    .invoke('text')
                    .should('contain', 'Groups')
                if(groups.length === 0){
                    cy.get('p').contains('No groups to display.')
                }else{
                cy.get('[data-cy="group-row"]')
                    .children()
                    .should('have.lengthOf', groups.length);
                    //have to add one because the header is also considered a child of this container
            }
            })
        })//end group tab

        it("will click on the Family Member tab",()=>{
            let groups;
            let family;
            cy.intercept('GET', '/api/user/groups/**').as('getGroups');
            cy.intercept('GET', '/api/group/**').as('getFamily');
            cy.get('[data-cy="Family"]').click()
        //FF LS
            cy.get('.react-loading-skeleton').should('exist')
            cy.wait('@getGroups').then((response)=>{
                groups = (JSON.parse(response.response.body)).data
                groups = groups.filter((group)=>{
                    if(group.group_type_id === 4) return group
                })
                cy.log(groups)
            }).then(()=>{
                if(groups.length > 0){
                    cy.wait('@getFamily').then((response)=>{
                        family = (JSON.parse(response.response.body)).data
                        cy.log(family)
                    }).then(()=>{
                        if(groups.length === 0){
                            cy.get('p').contains('No Family Registered.')
                        }else{
                            let filteredFamily = family[0].group_members.filter((member)=>{
                                if(member.group_member_status_id === 2) return member
                            })
                            cy.get('[data-cy="family-card"]')
                                .should('have.lengthOf', groups.length)
                                .last()
                                .children()
                                .get('table')
                                .get('tbody')
                                .children()
                                .should('have.lengthOf', filteredFamily.length)
                        }
                    })
                }
            })
        })//end family tab

        it("will check the subscription tab",()=>{
            let subscriptions;
            cy.intercept('GET', '/api/user/user/**').as('getSubscriptions');
            cy.get('[data-cy="Subscriptions"]').click()
        //FF LS
            cy.get('.react-loading-skeleton').should('exist')
            cy.wait('@getSubscriptions').then((response)=>{
                subscriptions = (JSON.parse(response.response.body)).data[0].subscriptions
                cy.log(subscriptions)
            }).then(()=>{
                cy.get('h4')
                    .should('contain', 'Subscriptions');
                cy.get('.react-loading-skeleton')
                    .should('not.exist');
                if(subscriptions.length === 0){
                    cy.get('p')
                        .contains('No Current Subscriptions');
                }else{
                    cy.get('.profile-subscriptions-wrapper')
                        .children()
                        .should('have.lengthOf', subscriptions.length);
                }
            })
        })//end subscriptions

        it("will show charges and the charges of their family",()=>{
            let charges;
            cy.intercept('POST', '/api/user/outstanding_charges').as('getCharges')
            cy.get('[data-cy="Outstanding"]').click()
            cy.wait('@getCharges').then((response)=>{
                let allCharges = []
                let allChargesNest = (JSON.parse(response.response.body)).data
                allChargesNest.outstanding_charges.forEach((charge)=>{
                    allCharges.push(charge)
                })
                allChargesNest.family.forEach((familyMember)=>{
                    familyMember.outstanding_charges.forEach((charge)=>{
                        allCharges.push(charge)
                    })
                })
                charges = allCharges;
            }).then(()=>{
                cy.get('h4')
                    .should('contain', 'Outstanding Charges');
                //initially checked first vs last here (hence the 0 and -1), but the order is being switched somewhere.  
                //Order isn't important here, though, just that the right charges are there
                if(charges.length > 0){
                    cy.get('tbody')
                        .children()
                        .should('have.lengthOf', charges.length)
                        // .should('contain', charges[0].event_name)
                    cy.get('tbody')
                        .children()
                        // .should('contain', charges[charges.length-1].event_name)
                    if(isSiteBoss){
                        cy.get('button')
                            .contains('Record Payment')
                            .should('exist')
                    }else{
                        cy.get('button')
                            .contains('Record Payment')
                            .should('not.exist')
                    }
                }
                else{
                    cy.get(':nth-child(2) > .col').should('contain', "No Outstanding Charges");
                }
            })
        })//end outstanding charges

        it("will make sure the event schedule shows nothing before today's date",()=>{
            let events;
            let today = new Date();
            cy.intercept('POST', '/api/event').as('getSchedule');
            cy.get('[data-cy="Schedule"]').click()
            cy.get('.react-loading-skeleton').should('exist')
            cy.wait('@getSchedule').then((response)=>{
                events = (JSON.parse(response.response.body)).data
            }).then(()=>{
                cy.get('h4')
                    .should('contain', 'Event Schedule');
                cy.log(events)
                if(events.events.length > 0){
                    let isOkay=false;
                    cy.get('table')
                    .get('tbody')
                        .children()
                        .each(($row)=>{
                            cy.get($row)
                                .children()
                                .eq(1)
                                .invoke('text').as('startDate')
                            cy.get('@startDate').then(($date)=>{
                                cy.log($date)
                                if(new Date($date) > today) isOkay = true;
                                else isOkay = false;
                                cy.log(isOkay);
                            })
                            if(!isOkay){
                                cy.get($row)
                                    .children()
                                    .eq(2)
                                    .invoke('text').as('endDate')
                                cy.get('@endDate').then(($date)=>{
                                    cy.log($date) 
                                    if(new Date($date) > today) isOkay = true;
                                    else isOkay = false;
                                    cy.log(isOkay);   
                                })

                            }
                            cy.get('td')
                                .last()
                                .invoke('text')
                                .should('contain', 'Confirmed')
                           
                        }).then(()=>{
                            expect(isOkay).to.be.true;
                        })
                }else{
                    cy.get('p')
                        .contains('No scheduled events')
                }
            })
        })//end event schedule

        it("will check the document tab",()=>{
            cy.get('[data-cy="Documents"]').click()
            cy.wait(500) //there is no call here, it only waits for props to load
            cy.get('.react-loading-skeleton').should('not.exist')
            cy.get('h4').invoke('text').should('contain','Documents');
            if(JSON.parse(local).profile.has_signed_waiver === 0){
                cy.get('.col-sm-12 > .text-center')
                    .invoke('text')
                    .should('contain', 'This user has no signed documents on file.')
            }else{
                const downloadsFolder = Cypress.config('downloadsFolder')
                const userId = JSON.parse(local).profile.id
                cy.get('.wav-card')
                    .should('contain', 'Registration Waiver')
                cy.get('.wav-a-tag')
                    .should('contain', 'Download Copy')
            }
            cy.log(JSON.parse(local).profile.has_signed_waiver)
        })//end documents tab
    })//end context for checking most of admin profile

    context("checkins && reset password",()=>{

        let checkin; //results of the call that limits checkins to one
        let multipleCheckins; //results of the full call on the checkin tab
        let checkinLeftDate; //date of the checkin on the left panel
        let originalCount; //number of fields in the check in profile tab before checking in
        let afterClickCount; 

        it('will check the checkins displayed',()=>{
            cy.intercept('POST', '/api/checkins').as('getCheckins');
            cy.get('[data-cy="Checkin"]')
                .click()
        //FF LS
            cy.get('.react-loading-skeleton').should('exist');
            cy.wait('@getCheckins').then((response)=>{
                //this call is used twice (for the left panel to get the recent checkin and for the body of the profile tab)
                multipleCheckins = (JSON.parse(response.response.body)).data
                
            }).then(()=>{
                cy.wait(1000)
                cy.log(multipleCheckins)
                cy.get('.react-loading-skeleton')
                    .should('not.exist');
                cy.get('h4')
                    .invoke('text')
                    .should('contain','Check In History');
            
                cy.get('[data-cy="checkin-last"]')
                    .invoke('text').as('checkinText')
                cy.get('@checkinText').then($text=>{
                    cy.log(`${String.fromCodePoint(0x1F92F)} have to manually check dates as even when I set seconds, Cypress doesn't see this as equalling one another.`);
                    cy.log(`${String.fromCodePoint(0x1F92F)} left date - ${new Date($text)}`);
                    cy.log(`${String.fromCodePoint(0x1F92F)} response[0] - ${new Date(multipleCheckins[0].checkin_at)}`)
                })

                //check tab display
                cy.get(".checkin-grid")
                    .children()
                    .its('length')
                    .as('checkinRows')
                cy.get('@checkinRows').then(($rows)=>{
                    //divide the children by 2 because there are two columns in each
                    expect($rows).to.equal(multipleCheckins.length)
                    originalCount = multipleCheckins.length
                })
            })
        }) //end check the checkin displays

        it('check the reset password tab',()=>{
            cy.get('[data-cy="Password"]')
                .click();
            cy.wait(1000) //there are no calls to wait on for this page and the load doesn't take near this long, but just in case
            cy.get('h4')
                .should('contain', 'Reset Password');
            cy.get('p')
                .should('contain', "By clicking this button, an email with a password reset link will be sent to the user's email account.");
            cy.get('.d-flex > .btn')
                .contains('Reset Password')
                .click();
            cy.get('.order-2').within(()=>{
                cy.get('p')
                    .should('contain', "A password reset link has been sent to the user's email.");
            })
        }) //end check the reset tab

        it("will check the user in and verify changes to the time",()=>{
            let newCheckinFromBE;
            cy.intercept('POST', '/api/user/checkin').as('checkin');
            cy.get('button')
                .contains('Check In This User')
                .click()
            cy.get('.modal-header')
                .should('contain', 'Confirm Check In')
            cy.get('.modal-body')
                .should('contain', `Check in ${JSON.parse(local).profile.first_name} ${JSON.parse(local).profile.last_name} to the facility?`)
            cy.get('.modal-footer > button')
                .contains('Yes')
                .click()
            cy.wait('@checkin').then((response)=>{
                newCheckinFromBE = new Date (JSON.parse(response.response.body).data.checkin_at)
            }).then(()=>{
                cy.get('.checked-in-today')
                    .invoke('text')
                    .as('checkinText')
                cy.get('@checkinText').then(($text)=>{
                    let leftSideText = $text.split(": ")
                    cy.log(`${String.fromCodePoint(0x1F92F)} Manual verification needed!! The original text should NOT equal the current text.  The current text should equal from the backend`)
                    cy.log(`${String.fromCodePoint(0x1F92F)} Original Check In Text from the Left Side Display, ${new Date(checkinLeftDate)}`)
                    cy.log(`${String.fromCodePoint(0x1F92F)} Current Check In Text from the Left Side Display, ${new Date(leftSideText[1])}`)
                    cy.log(`${String.fromCodePoint(0x1F92F)} Check In From the back end, ${new Date(newCheckinFromBE)}`)
                })
            })
        })//end checking in the user and verifying the time change

        it("will make sure the checkin tab has been updated",()=>{
            let newCheckins;
            cy.intercept('POST', '/api/checkins').as('getCheckins');
            cy.get('[data-cy="Checkin"]').click()
            cy.wait('@getCheckins').then((response)=>{
                //nested checks likes above
                if(!response.request.body.limit) newCheckins = (JSON.parse(response.response.body)).data 
            }).then(()=>{
                //since we're checking the user in, the checkins should never be 0, so no conditional statement
                cy.get(".checkin-grid")
                        .children()
                        .its('length')
                        .as('checkinRows')
                cy.get('@checkinRows').then(($rows)=>{
                    cy.log(`${String.fromCodePoint(0x1F92F)} Making sure the number of rows matches the backend`)
                    expect($rows).to.equal(newCheckins.length)
                    cy.log(`${String.fromCodePoint(0x1F92F)} Making sure the number of rows is more than it was the first time checked`)
                    expect(originalCount).to.not.equal(newCheckins.length)
                })
            })
        })//end it will make sure the checkin tab has been updated
    })//end check in context

    context("Will check the rest of admin profile (notes, roles/permissions)",()=>{
        
        it("will check the roles and permissions tab", ()=>{
            cy.intercept('GET', '/api/user/roles').as('getRoles');
            cy.get('[data-cy="Settings"]').click();
            cy.wait('@getRoles').then(()=>{
                cy.get('h4').should('contain', 'Roles and Permissions');
                roles.forEach(role=>{
                    if(role.id === 1) isSiteBoss = true
                })
                if(isSiteBoss === true){
                    cy.get('.form-control')
                        .click()
                        .get('#roles_autocomplete')
                        .children()
                        .its('length')
                        .should('be.gt',  5).and('be.lt', 8)
                    cy.get('.form-control')
                        .type('P')
                        .get('#roles_autocomplete')
                        .children()
                        .its('length')
                        .should('equal', 4)
                    cy.get('.form-control')
                        .type('{esc}')
                    cy.get('.rbt-token')
                        .should('contain', 'SiteBoss Master Admin')
                    cy.get('button')
                        .contains('Save')
                        .should('be.visible')
                }else{
                    //unless they're SB, no one should be able to edit roles/permissions on their own profile
                    cy.get('p').should('contain', "You do not have permission to edit this user's roles.")
                }
            })
        })//end it will check roles/permissions

        it("will check the notes tab",()=>{
            cy.log(`${String.fromCodePoint(0x1F92F)} This is going to check the functionality of notes, more indepth component test will be made for viewability of all the different note types and conditions`)
            cy.intercept('GET', '/api/user/notes/**').as('getNotes');
            cy.intercept('POST', '/api/user/notes/add').as('addNote');
            cy.intercept('POST', '/api/user/notes/edit').as('editNote');
            cy.get('[data-cy="Notes"]').click()
            cy.wait('@getNotes').then(()=>{
                if(isSiteBoss === true){
                    cy.get('button').contains('New Note')
                        .should('be.visible')
                        .click()
                        let randomMessage = createRandomMessage()
                        let secondOne = createSecondRandomWords()
                    cy.get('.modal').should('be.visible')
                    cy.get('#note')
                        .type(`This test note was made by Cypress!! ${randomMessage}`)
                    cy.get('#status')
                        .select('All')
                    cy.get('button')
                        .contains('Save Note')
                        .click()
                        .get('.toast-body').should('be.visible')
                        .wait('@addNote')
                        .then(()=>{
                            cy.wait('@getNotes').then(()=>{
                            //puttting newest note at the top
                            cy.wait(500)
                            cy.get('[data-cy="note-desc-btn"]')
                                .click()
                            //get the top note
                            cy.get('[data-cy="note-map-div"] > :nth-child(1)')
                                .children()
                                .invoke('text')
                                .should('include', "viewable by All")
                                .and('include', randomMessage)
                            cy.get(':nth-child(1) > .d-flex > :nth-child(1)')
                                .contains('Edit')
                                .click()
                            .get('#note')
                                .type(`${secondOne}`)
                            .get('#status')
                                .select('Only Me')
                            .get('button')
                                .contains('Save Note')
                                .click()
                                .get('.toast-body').should('be.visible')
                                .wait('@editNote').then(()=>{
                                    cy.wait('@getNotes').then(()=>{
                                        cy.wait(1000)
                                        cy.get('[data-cy="note-desc-btn"]')
                                            .click()
                                        cy.get('[data-cy="note-map-div"] > :nth-child(1)')
                                            .children()
                                            .invoke('text')
                                            .should('contain', secondOne)
                                        cy.get(':nth-child(1) > .d-flex > :nth-child(2)')
                                            .contains('Delete')
                                            .click()
                                        cy.get('.modal-body').within(()=>{
                                            cy.get('h5').should('contain', 'Are you sure you want to ')
                                            cy.get('button')
                                                .contains('Yes')
                                                .click()
                                            })
                                        cy.wait('@editNote').then(()=>{
                                            cy.wait('@getNotes')
                                            cy.wait(2000)
                                            cy.get('.container-fluid > :nth-child(4) > :nth-child(1)')
                                                .children()
                                                .invoke('text')
                                                .should('not.contain', randomMessage)
                                                .and('not.contain', secondOne)
                                        })
                                    })
                                })
                            })
                        }) 
                } //end note check (if SB account to check them)
                else{
                    //unless it's a sb account, should not be able to add notes on own profile
                    cy.get('button')
                        .contains('New Note')
                        .should('not.exist')
                }
            })
           
        })

        // it("will check that the transactions takes you to a new page",()=>{
        //     cy.get('[data-cy="profile-transaction-link"]').click()
        // })
    }) //end notes and roles/permission tabs
})

const createRandomMessage=()=>{
    createRandomNumbers(6, 50)
    let message = "";
    randomNumberArray.forEach(number=>{
        message += randomWordArray[number]
        message += " ";
    })
    return message;
}
const createSecondRandomWords=()=>{
    createRandomNumbers(5, 50)
    let message = "";
    randomNumberArray.forEach(number=>{
        message += randomeWordsTwo[number]
        message += " ";
    })
    return message;
}

const createRandomNumbers=(howMany, max)=>{
    for(let i = 0; i<howMany; i++){
        randomNumberArray.push(Math.floor(Math.random()*max));
    }
}