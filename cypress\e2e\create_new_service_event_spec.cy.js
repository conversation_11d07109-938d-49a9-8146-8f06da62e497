/*eslint-disable*/
let qaDevAdmin = Cypress.env('impact_admin_user');
let password = Cypress.env('login_password');
let baseUrl = "https://portal-qa.impactathleticsny.com/p/"

describe("It will create the service 'Midnight's Party' that is used in other tests", {testIsolation: false, scrollBehavior: "center"}, ()=>{
    let local;
    let serviceExists=false;

    before("It will log in a user", ()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, qaDevAdmin, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach("It will renew the user in local storage and viewport size",()=>{
        cy.viewport(1920, 1080);
        cy.restoreLocalUser(JSON.parse(local));
    });

    it("will see if the service exists",()=>{
        cy.visit(`${baseUrl}services/dashboard`);
        cy.get('#searchbar-input')
            .type("Midnight's Party")
        cy.wait(1000) //the call is made repeatedly during the search - try for time instead
        cy.get('tbody')
            .invoke('text')
            .as('tableText');
        cy.get('@tableText').then(text=>{
            if(text.includes("Midnight's Party")){
                serviceExists=true;
            }
        })
    })

    it("will navigate to the service create wizard",()=>{
        cy.get('.btn-primary')
            .should('contain', "New Service")
            .click();
    });

    it("will check the manager is the user",()=>{
        cy.get('.doing')
            .invoke('text')
            .should('contain', "Manager");
        cy.get('.rbt-token')
            .invoke('text')
            .should('contain', 'Skyler R');
        cy.get('[data-cy="multistep-next"]')
            .should('contain', 'Next')
            .click();
    })//end manager

    it("will put in service name and description",()=>{
        cy.get('.doing')
            .invoke('text')
            .should('contain', "Name");
        cy.get('[data-cy="multistep-next"]')
            .should('contain', "Next")
            .click();
        cy.get('.err')
            .invoke('text')
            .should('contain', 'Name is required.');
        cy.get('#name')
            .type("Midnight's Party");
        cy.get('#description')
            .type('A party that Midnight hosts.');
        cy.get('[data-cy="multistep-next"]')
            .should('contain', "Next")
            .click();
    })//end name/description

    it("will select the location of the service",()=>{
        cy.get('.doing')
            .invoke('text')
            .should('contain', 'Location');
        cy.get('[data-cy="multistep-next"]')
            .click();
        cy.get('.err')
            .should('contain', "At least one location must be selected.");
        cy.get('.btn-column > :nth-child(23)')
            .should('contain', "Arcade")
            .click();
        cy.get('[data-cy="multistep-next"]')
            .click();
    }); //end select location

    it("will select the availablilty Sun-Sat",()=>{
        cy.get('.doing')
            .invoke('text')
            .should('include', "Availability");
        cy.get('[data-cy="multistep-next"]')
            .click();
        cy.get('.err')
            .should('contain', "At least one day must be selected")
        cy.get('.stacked > .form-row > :nth-child(1)') //Sun
            .click()
        cy.get('.stacked > .form-row > :nth-child(2)') //Mon
            .click()
        cy.get('.stacked > .form-row > :nth-child(3)') //Tues
            .click()
        cy.get('.stacked > .form-row > :nth-child(4)') //Wed
            .click()
        cy.get('.stacked > .form-row > :nth-child(5)') //Thurs
            .click()
        cy.get('.stacked > .form-row > :nth-child(6)') //Fri
            .click()
        cy.get('.stacked > .form-row > :nth-child(7)') //Sat
            .click()
        cy.get('[name="end_time_0"]')
            .select('12:00 PM')
        // cy.get('.long-button-tiny')
        //     .click()
        cy.get('[data-cy="multistep-next"]')
            .click();
    }); //end select availibility

    it("will select the increments",()=>{
        cy.get('.doing')
            .invoke('text')
            .should('contain', "Increments")
        cy.get(':nth-child(6) > .custom-select')
            .select("4 tokens (4 timeslots) = 1 hour");
        // cy.get(':nth-child(7) > .custom-select')
        //     .select('4 tokens (4 timeslots) = 1 hour');
        cy.get(':nth-child(1) > .react-numeric-input > input')
            .type('{backspace}30');
        cy.get('[data-cy="multistep-next"]')
            .click();
    }); //end increments

    it("will select the token",()=>{
        cy.get('.doing')
            .invoke('text')
            .should('contain', "Payment");
        // cy.get('#has_fee')
        //     .click();
        // cy.get('.flex > .input-group > .form-control')
        //     .type('.02');
        // cy.get('.react-numeric-input > input')
        //     .type('{backspace}{backspace}5');
        cy.get('[data-cy="multistep-next"]')
            .click();
    });//end select token

    it("will select cancellation",()=>{
        cy.get('.doing')
            .invoke('text')
            .should('contain', "Cancellation")
        cy.get('.mb-2 > :nth-child(3)')
            .click();
        cy.get('[data-cy="multistep-next"]')
            .click();
    });//end cancellation

    it("will save the service if need be",()=>{
        cy.log(serviceExists);
        if(!serviceExists){
            cy.get('.submit')
                .click({force: true})
        }
    }); //end saving service

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
})