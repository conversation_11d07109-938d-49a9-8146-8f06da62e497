import React, { useState, useEffect, useCallback, Suspense } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, Button } from 'react-bootstrap';
import DatePicker from "react-datepicker";
import { format, formatISO } from 'date-fns';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';

import * as actions from '../../store/actions';

import "react-datepicker/dist/react-datepicker.css";

import './Coupon.scss';

const defaultUntil = new Date()
function addYears(year) {
    let date = new Date();
    date.setFullYear(date.getFullYear() + year);
    return date;
}

const Dates = ({onChangeInput=()=>{}}) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    const [pagePart, setPagePart] = useState();
    const locations=useSelector(state => state.map.selected_items);
    const dayNames = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

    const changeDateHandler = useCallback((name, e) =>{
        let event = {
            target: {
                type: "calendar",
                name: name,
                value: formatISO(new Date(e.toLocaleDateString("en-US",{year:"numeric", month:"2-digit", day:"2-digit"}))),
            }
        };
        onChangeInput(event);
    },[onChangeInput]);

    useEffect(() => {
        // don't try to load the datepicker unless a valid date has been loaded
        if (coupon.valid_from) {
            setPagePart(
                <Suspense fallback={             
                    <SkeletonTheme color="#e0e0e0">
                        <Skeleton height={30} style={{marginBottom:"1rem"}} />
                        <Skeleton height={12} count={5} />
                    </SkeletonTheme>
                }>
                    <Row>
                        <Col className="col-sm-auto">
                            <Form.Label>Start Date</Form.Label>
                            <DatePicker 
                                dateFormat="MM/dd/yyyy"
                                minDate={new Date()}
                                maxDate={new Date(new Date().getFullYear()+100,12,31)}
                                showMonthDropdown
                                showYearDropdown
                                selected={new Date(coupon.valid_from)}
                                onChange={(e) => { changeDateHandler('valid_from', e) }}
                                customInput={
                                    <Button variant="light" className="datepicker-calendar" type="button">{format(new Date(coupon.valid_from), "MM/dd/yyyy")}</Button>
                                }
                            />
                        </Col>
                        <Col className="col-sm-auto">
                            <Form.Label>End Date</Form.Label>
                            <DatePicker 
                                dateFormat="MM/dd/yyyy"
                                minDate={new Date(coupon.valid_from)}
                                maxDate={new Date(new Date().getFullYear()+100,12,31)}
                                showMonthDropdown
                                showYearDropdown
                                selected={new Date(coupon.valid_until)}
                                onChange={(e) => { changeDateHandler('valid_until', e) }}
                                disabled={coupon.no_end_date}
                                customInput={
                                    <Button variant="light" className="datepicker-calendar" type="button">{format(new Date(coupon.valid_until), "MM/dd/yyyy")}</Button>
                                }
                            />
                            <Form.Check 
                                type="checkbox"
                                id="unlimited-0"
                                label="No End Date"
                                name="no_end_date"
                                checked={!!coupon.no_end_date}
                                isInvalid={!!errors.no_end_date}
                                onChange={onChangeInput}
                                className="px-0 mt-2"
                            />
                        </Col>
                    </Row>
                </Suspense>                                                                                                                                                                                 
            );
        }
    },[coupon, errors.no_end_date, onChangeInput, changeDateHandler]);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">When is it valid from and until?</span>
                {pagePart}
                <div className={`err ${!!errors.valid_from || !!errors.valid_until || !!errors.no_end_date ? "" : "hidden"}`}>
                    {errors.valid_from}
                    {errors.valid_until}
                    {errors.no_end_date}
                </div>
            </Col>
        </Row>
    );
}

export default Dates;