import React, { useRef, useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Col, Row, Form, Button, InputGroup } from 'react-bootstrap';

//import { selectCurrentEvent } from '../../../../store/selectors';

import styles from './Recurring.module.scss';

const Recurring = ({ variant_idx=0, variant = null, onRemove=()=>{}, onSave=()=>{} }) => {
    const ref = useRef();
    const firstRender = useRef(true);

    //const currentEvent = useSelector(selectCurrentEvent);
    const errors = useSelector(state => state.eventwizard.errors);

    const [values, setValues] = useState(variant || {});
    const [isRecurring, setIsRecurring] = useState(variant?.bill_num_times > 0 || variant?.interval_quantity > 0);

    const updateFee = (e, recurring = null) => {
        if (recurring === null) recurring = isRecurring;
        let value = e.target.value;
        if (e.target.name === "price" || e.target.name === "activation_fee" || e.target.name === "bill_num_times" || e.target.name === "interval_quantity" ) value = +value;
        setValues(prev=>({
            ...prev, 
            ...(e.target.name ? {[e.target.name]: value} : {}),
            ...(!recurring ? {activation_fee: 0, bill_num_times: 0, interval_quantity: 0, bill_interval: ""} : {}),
        }));
    }

    const intervalChangeHandler = (e) => {
        if (ref.current){
            if (e.target.value === "y") ref.current.innerHTML = "years";
            else ref.current.innerHTML = "months";
        }
        updateFee(e);
    }

    const toggleRecurring = (e) => {
        setIsRecurring(e.target.checked);
        updateFee(e, e.target.checked);
    }

    const removeHandler = () => {
        onRemove(variant_idx);
    }

    useEffect(() => {
        if (firstRender.current === false) onSave(values, variant_idx);
    }, [values, variant_idx, onSave]);

    useEffect(() => {
        firstRender.current = false;
    }, []);

    //if (!currentEvent.has_fee===1) return null;

    return (
        <div className={styles.variant}>
            <Row>
                <Col>
                    <Form.Group controlId="name">
                        <Form.Label>Description</Form.Label>
                        <Form.Control
                            name="name"
                            value={variant?.name || ""}
                            onChange={updateFee}
                            required
                        />
                    </Form.Group>
                </Col>
                <Col sm={3} style={{display:'flex', alignItems:'center'}}>
                    <Form.Check 
                        className="form-switch mt-2"
                        type="checkbox"
                        id="recurring"
                        label="Recurring Billing" 
                        onChange={toggleRecurring}
                        checked={isRecurring}
                    /> 
                </Col>
            </Row>
            <Row>
                <Col sm={3} style={{display: isRecurring ? undefined : "none"}}>
                    <Form.Group controlId="activation_fee" className={`fee-input`}>
                        <Form.Label>Upfront Fee</Form.Label>
                        <InputGroup>
                            <InputGroup.Prepend>
                                <InputGroup.Text>$</InputGroup.Text>
                            </InputGroup.Prepend>
                            <Form.Control
                                value={+variant?.activation_fee || 0}
                                name="activation_fee"
                                type="number"
                                onChange={updateFee}
                            />
                        </InputGroup>
                    </Form.Group>
                </Col>
                <Col sm={3}>
                    <Form.Group controlId="price" className={`fee-input`}>
                        <Form.Label>Charge Fee</Form.Label>
                        <InputGroup>
                            <InputGroup.Prepend>
                                <InputGroup.Text>$</InputGroup.Text>
                            </InputGroup.Prepend>
                            <Form.Control
                                value={+variant?.price || 0}
                                name="price"
                                type="number"
                                onChange={updateFee}
                                required
                            />
                        </InputGroup>
                    </Form.Group>
                </Col>
                <Col sm={3} style={{display: isRecurring ? undefined : "none"}}>
                    <Form.Group controlId="interval_quantity">
                        <Form.Label>Every</Form.Label>
                        <div className="d-flex">
                            <Form.Control
                                name="interval_quantity"
                                type="number"
                                min="0"
                                max="12"
                                value={+variant?.interval_quantity || 0}
                                onChange={updateFee}
                            />
                            <Form.Control 
                                as="select" 
                                custom 
                                value={variant?.bill_interval || ""}
                                name="bill_interval"
                                onChange={intervalChangeHandler}
                            >
                                <option value=""></option>
                                <option value="m">Month(s)</option>
                                <option value="y">Year(s)</option>
                            </Form.Control>
                        </div>
                    </Form.Group>
                </Col>
                <Col sm={3} style={{display: isRecurring ? undefined : "none"}}>
                    <Form.Group controlId="bill_num_times">
                        <Form.Label>For</Form.Label>
                        <div className="d-flex align-items-center">
                            <Form.Control
                                name="bill_num_times"
                                type="number"
                                min="0"
                                max="99"
                                value={+variant?.bill_num_times || 0}
                                onChange={updateFee}
                            />
                            <span className="ms-2">cycles</span>
                        </div>
                    </Form.Group>
                </Col>
            </Row>            
            {variant_idx>0 &&
                <Button className="btn rounded my-0 me-0 align-self-end" variant="outline-light" onClick={removeHandler} ><i className="far fa-trash-alt m-0"/></Button>
            }

            <div className={`err ${!!errors?.[variant?.variant_id] ? "" : "hidden"}`}>
                {errors?.[variant?.variant_id]}
            </div>
        </div>
    );
}

export default Recurring;