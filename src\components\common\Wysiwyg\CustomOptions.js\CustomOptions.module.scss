@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/mixins';

.dropdown-title{
    text-decoration: $link-text-decoration;
    font-size: $link-font-size;
    font-weight: $link-font-weight;
    color: inherit;
    margin: $link-margin;
    cursor: pointer;
    transition: all .15s ease-in-out;
}
.hover-item{
    &:hover{
        background-color:  $neutral-background-color;
    }
}