import React,{ useState} from 'react';
import Card from 'react-bootstrap/Card';
import Nav from 'react-bootstrap/Nav';
import {News} from './News';
import {Notifications} from './Notifications';
import './Tabs.css';


export const Tabs = (props) => {

    // selected tab
    const [selectedTab, setSelectedTab] = useState(<News key="news" />);

   

    // load content based on the tab selected
    const tabClickHandler = (e) =>{
        switch(e.target.hash){
            case "#news":
                setSelectedTab(<News key="news" />);
                break;
            case "#notifications":
                setSelectedTab(<Notifications key="notifications" />);
                break;
            
            default:
                setSelectedTab(<News key="news" />);
                break;
        }        
    }
    
  

    return (
        <React.Fragment>
            <div>
                <Nav defaultActiveKey={`#${props.includes?.[0].hash}`} as="ul">
                    {props.includes.map((item,i)=> (
                        <Nav.Item key={item.hash+"-"+i} as="li">
                            <Nav.Link href={`#${item.hash}`} onClick={(event)=>tabClickHandler(event)}>
                                {item.text}
                            </Nav.Link>
                        </Nav.Item>
                    ))}
                </Nav>
                <hr className="prof-he-line"/>
            </div>
            <div>
                {selectedTab}
            </div>
        </React.Fragment>
    );
}