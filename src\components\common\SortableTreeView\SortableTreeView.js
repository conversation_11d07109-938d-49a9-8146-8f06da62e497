import React, { useRef, useEffect, useState } from 'react';
import Dropzone from './Dropzone';
import Branch from './Branch';

import styles from './SortableTreeView.module.scss';

const flatten = (members) => {
    let children = [];
    const flattenMembers = members.map(m => {
      if (m.children && m.children.length) {
        children = [...children, ...m.children];
      }
      return m;
    });
  
    return flattenMembers.concat(children.length ? flatten(children) : children);
}  

/**/
const groupByParentId = (elements) => {
    let index = 0
    // Function to build the nested structure recursively
    const buildTree = (parentId, map) => {
      return map.get(parentId).map((element, i) => ({
        ...element,
        index: ++index,
        children: map.has(element.id) ? buildTree(element.id, map) : []
      }));
    };
  
    // Organize elements into a map
    const map = new Map();
    elements.forEach(element => {
      if (!map.has(element.parent_id)) {
        map.set(element.parent_id, []);
      }
      map.get(element.parent_id).push(element);
    });
  
    // Start the recursive building with null parent_id
    return buildTree(null, map);
}
  
export const SortableTreeView = (props) => {
    const {onSelect} = props;
    const [treeData, setTreeData] = useState(props.data);

    const ref = useRef(0);

    useEffect(() => {
        if (props.data?.length) {
            setTreeData(props.data);
        }
    }, [props.data]);

    const handleDrop = (draggedItem, dropTarget, isSubBranchZone) => {
        const { id: draggedId } = draggedItem;
        const { id: dropTargetId, parent_id: dropTargetParentId } = dropTarget;

        if (draggedId === dropTargetId) return;
        const _flat = flatten(JSON.parse(JSON.stringify(treeData)));
        let _targetidx = _flat.findIndex(a=>a.id===dropTargetId);
        if (_targetidx < 0) _targetidx = 0;
        for (let i=0; i<_targetidx; i++){
            if (_flat[i].parent_id===draggedId){
                return;
            }
        }

        const dropIndex = _flat[_targetidx].index;
        const _draggedidx = _flat.findIndex(a=>a.id===draggedId);

        let to = -1, from = -1, increment = 0;
        if (_targetidx === 0){
            from = 0;
            to = _flat.length;
            increment = 1;
        } else if (_targetidx >= _flat.length - 1){
            from = 0;
            to = _flat.length;
            increment = -1;
        } else {
            if (dropTarget.index > draggedItem.index){
                from = 0;
                to = _targetidx;
                increment = -1;
            } else {
                from = _targetidx;
                to = _flat.length;
                increment = 1;
            }
        }

        for (let i=from; i<to; i++){
            _flat[i].index = _flat[i].index + increment;
        }

        if (_flat[_draggedidx].parent_id !== isSubBranchZone ? dropTargetId : dropTargetParentId){
            const _parent = _flat.find(a=>a.id===_flat[_draggedidx].parent_id);
            if (_parent){
                const _idx = _parent.children.findIndex(a=>a.id===draggedId);
                if (_idx > -1){
                    _parent.children.splice(_idx, 1);
                }
            }
        }

        _flat[_draggedidx].parent_id = isSubBranchZone ? dropTargetId : dropTargetParentId;
        _flat[_draggedidx].index = dropIndex;

        _flat.sort((a, b)=>a.index-b.index);
        _flat.forEach((item, i) => item.index = i + 1);

        setTreeData(groupByParentId(_flat));
        if (props.onSort){
            props.onSort(_flat);
        }
        
        return;
    }

    ref.current = 0;
    const renderTree = (nodes) => {
        if (!nodes || nodes.length === 0) return null;
        return nodes.map((node, i) => {
            ref.current++;
            return (
                <div key={`sortable-treeview-node-${node.id}`} className={styles.container}>
                    <Dropzone 
                        data={{id: node.id, parent_id: node.parent_id, index: ref.current}} 
                        onDrop={handleDrop} 
                        isSubBranch={false} 
                    />
                    <Branch 
                        id={node.id} 
                        parent_id={node.parent_id} 
                        text={node.text} 
                        index={++ref.current} 
                        selected={node.id === props.selectedItem} 
                        onSelect={onSelect} 
                        onDrop={handleDrop} data={node}
                    >
                        {node.id === props.selectedItem && props.children}
                    </Branch>
                    {node.children && 
                        <div className={styles.child}>
                            {renderTree(node.children)}
                            {node.children.length === 0 &&
                                <Dropzone 
                                    onDrop={handleDrop} 
                                    data={{id: node.id, parent_id: node.parent_id, index: ++ref.current}} 
                                    isSubBranch 
                                />
                            }
                        </div>
                    }
                    {i === nodes.length - 1 &&
                        <Dropzone 
                            onDrop={handleDrop}
                            data={{id: node.id, parent_id: node.parent_id, index: ++ref.current}}
                            isSubBranch={false} 
                        />
                    }
                </div>
            );
        });
    }

    if (!treeData || treeData.length === 0) return null;

    return (
        <>
            {renderTree(treeData)}
        </>
    );
}