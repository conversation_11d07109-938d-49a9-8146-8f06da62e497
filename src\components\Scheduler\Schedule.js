import React from 'react';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import './Scheduler.css';

// schedule view
const Schedule = (props) =>{
    let date;

    // since indexes are dates, create an iterable array
    const tasks=[];
    for (var k in props.tasks) {
        if (props.tasks.hasOwnProperty(k)){
            if (k==="everyday" || new Date(k).getTime()===new Date(props.date).getTime()){
                tasks.push({
                    date:k,
                    tasks:[...props.tasks[k]]
                });
            }
        }
    }

    let dayTask=[];
    let dayTaskTimes=[];
    let taskClass="";

    return (
        <Container className="mt-2">
            {tasks.map((task,i)=>{
                if (task.date==="everyday") {
                    if (!date) {
                        date=new Date().toLocaleDateString("en-US",{weekday:"long",day:"2-digit",month:"2-digit",year:"numeric"});
                        dayTask=[];
                        dayTaskTimes=[];
                    }
                } else {
                    date=new Date(task.date).toLocaleDateString("en-US",{weekday:"long",day:"2-digit",month:"2-digit",year:"numeric"});
                    dayTaskTimes=[];
                    dayTask=[];
                }
                

                dayTaskTimes = task.tasks.map((t)=>{
                    dayTask.push(<li className="bold" key={`dg${t.id}`}>{t.title}</li>);
                    
                    let start=new Date(date+" "+t.time_start).toLocaleTimeString("en-US", { hour12: true, hour: '2-digit', minute: '2-digit' })
                    let end=new Date(date+" "+t.time_end).toLocaleTimeString("en-US", { hour12: true, hour: '2-digit', minute: '2-digit' })

                    switch(t.type){
                        case 1:
                            taskClass=" task-everyday";
                            break;
                        default:
                            taskClass="";
                            break;
                    }
                    
                    return <li key={`tm${t.id}`}><i className={`task${taskClass}`}></i>{start} - {end}</li>
                });

                return (
                    <React.Fragment key={`scr${i}`}>
                        <Row>
                            <Col lg="3"><span className="schedule-date">{date}</span></Col>
                            <Col lg="9" className="schedule">
                                <ul>{dayTaskTimes}</ul>
                                <ul>{dayTask}</ul>
                            </Col>
                        </Row>
                        <hr/>
                    </React.Fragment>
                )
            })}
        </Container>
    )
}

export default Schedule;