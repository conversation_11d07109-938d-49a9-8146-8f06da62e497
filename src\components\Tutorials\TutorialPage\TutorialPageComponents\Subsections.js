import React, { useState, useEffect, useCallback } from 'react';
import { Accordion } from 'react-bootstrap';
import styles from '../TutorialPage.module.scss';
import { authUserHasModuleAccessMany } from '../../../../utils/auth';

export const Subsections = ({topic, ...props})=>{

    let userRole = JSON.parse(localStorage.getItem("user")).roles[0].id
    const [ currentTopic, setCurrentTopic ]=useState(null)
    const [ allowedTutorials, setAllowedTutorials ]=useState(null);
    const [ basicInfo, setBasicInfo ]=useState(null);
    const [ navigationInfo, setNavigationInfo]=useState(null);

    //if each role meets the permission, does it meet the role? We're using roles to keep SOME semblance of continuity between companies and available information
    const roleCheck = useCallback(async()=>{
        let subsections = topic?.content?.subsections;
        let approvedSections = []
        if(topic.content.access_and_above >= userRole && topic.content.basic) setBasicInfo(topic.content.basic);
        else setBasicInfo(null);
        if(topic.content.access_and_above >= userRole && topic.content.navigation) setNavigationInfo(topic.content.navigation);
        else setNavigationInfo(null);
        for(let i = 0; i < subsections?.length; i++){
            if(subsections[i].access_and_above >= userRole) approvedSections.push(subsections[i]); 
        }
        return approvedSections;
    },[topic, userRole])
    
    //does each section meet the permission?
    const authCheck = useCallback(async()=>{
        let approvedSections = await roleCheck();
        if(approvedSections){
            let response = await authUserHasModuleAccessMany(topic?.content?.module_id_array)
            if(response){
                let approvedSections = []
                let subsections = topic?.content?.subsections;
                for(let i = 0; i < subsections.length; i ++){
                    if(response[subsections[i].module_id]) approvedSections.push(subsections[i])
                }
                setAllowedTutorials(approvedSections);
                setCurrentTopic(topic) //used as a refresher instead of using props;
            }
        }
        
    },[topic, roleCheck]);

    useEffect(()=>{
        //this is all to prevent a loop
        let doIt = false; //should we?
        if(topic?.content?.subsections.length > 1 && !allowedTutorials) doIt = true; //if there aren't any allowed tutorials, we need to do it.  Running through the functions will at least set it to an empty array
        if(topic && topic?.id !== currentTopic?.id) doIt = true; //if the topic is new, we gotta do it again
        if(!currentTopic && !allowedTutorials) doIt = true; //if there isn't anything set currently, we need to do it.  

        if(topic && doIt){ //need to make sure we also HAVE a topic passed in
            authCheck()
        }

    },[authCheck, topic, allowedTutorials, currentTopic])

    const parseHtml=(htmlData, topic, section)=>{
        if(htmlData){
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlData, 'text/html');
            const body = doc.body;
            // const jsxContent = Array.from(body.childNodes).map(createJSX, index);
            const jsxContent = Array.from(body.childNodes).map((node, index) => createJSX(node, topic, section, index));
            
            return jsxContent.length > 0 ? <>{jsxContent}</> : <></>;
        }
        else return <></>
    }
    
    const createJSX = (node, topic, section, index) => {
        // Check if it's a text node and return the text
        if (node.nodeType === Node.TEXT_NODE || node.nodeType===3) {
          const textContent =  node?.textContent?.trim()
            return textContent ? textContent : null;
        }
    
        // Map tag names to corresponding JSX elements
        const tagName = node?.tagName?.toLowerCase()
        if(!tagName) return null;
        const children = Array.from(node?.childNodes).map((childNode, index)=>createJSX(childNode, topic, section, index ));
        const childrenContent = children.length === 1 ? children[0] : <>{children}</>
    
        return  React.createElement(tagName, {key: `${tagName}- ${topic}-${section?.id}-${index}`}, childrenContent);
    };

    const EachDropdown = ({htmlBlock, section}) =>{
        return(
            <Accordion.Collapse eventKey={`${topic.id}-${section.id}`} className={styles["each-collapse"]}>
                <div>
                    {parseHtml(htmlBlock, topic, section)}
                </div>
            </Accordion.Collapse>
        )
    }

    return(
        <div key={`tutorial-subsection-${topic.id}`} className={styles["accordion-div"]}>
            <Accordion>    
                {basicInfo && 
                    <>
                        <Accordion.Toggle eventKey={`${topic.id}-basic`}>
                            Basic Info
                        </Accordion.Toggle>
                        <Accordion.Collapse eventKey={`${topic.id}-basic`} className={styles["each-collapse"]}>
                            {parseHtml(basicInfo, topic.id, "basics")}
                        </Accordion.Collapse>    
                    </>
                }
                {navigationInfo &&
                    <>
                        <Accordion.Toggle eventKey={`${topic.id}-navigation`}>
                            Navigation Info
                        </Accordion.Toggle>
                        <Accordion.Collapse eventKey={`${topic.id}-navigation`} className={styles["each-collapse"]}>
                            {parseHtml(navigationInfo, topic, "nav")}
                        </Accordion.Collapse>    
                    </>
                }
                {allowedTutorials && allowedTutorials?.length > 0 && allowedTutorials?.map((section)=>(
                    <div key={`tutorial-subsection-${topic.id}-${section.id}`} className={styles["accordion-div"]}>
                        {(section.patron_block || section.staff_block || section.admin_block) &&
                            <>
                                <Accordion.Toggle eventKey={`${topic.id}-${section.id}`}>
                                    {section.title}
                                </Accordion.Toggle>   
                                {section.patron_block && 
                                    <EachDropdown htmlBlock={section.patron_block} section={section} />
                                }
                                {section.staff_block && 
                                    <EachDropdown htmlBlock={section.staff_block} section={section} />
                                }
                                {section.admin_block && 
                                    <EachDropdown htmlBlock={section.admin_block} section={section}/>
                                }
                            </>
                        }
                    </div>
                ))}
            </Accordion>
        </div>
    )
}