import React, { useRef } from 'react';
import {Container, Col, Row, Form, InputGroup, Button} from 'react-bootstrap';

import APIUsers from '../../../../api/Users';

const Step2 = (props) => {
    const {change, values, errors, setErrors} = props;

    const ref = useRef();
    const iconRef = useRef();

    const autoGeneratePasswordHandler = () => {
        const length = 15;
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-!@#$%^&*(){}[]<>?";
        let retVal = "";
        for (let i = 0, n = charset.length; i < length; ++i) {
            retVal += charset.charAt(Math.floor(Math.random() * n));
        }        
        ref.current.value = retVal;
        change({target: { name: "owner_password", value: retVal}});
    }

    const togglePasswordFieldHandler = () => {
        const type = ref.current.type;
        ref.current.type = type === "password" ? "text" : "password";
        iconRef.current.className = type === "password" ? "far fa-eye-slash" : "far fa-eye";
    }

    const checkUserHandler = (e) => {
        e.preventDefault();
        APIUsers.checkUsername({username: e.target.value}).then(response => {
            if (response.data && response.data?.[0]?.available === true) {
                setErrors({owner_user_name: null});
                change(e);
            } else {
                setErrors({owner_user_name: "Username is already taken."});
            }
        });
    }


    return (
        <Container fluid>
            <Row>
                <Col sm="12" lg="8">
                    <Row>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="owner_first_name">
                                <Form.Label>First Name</Form.Label>
                                <Form.Control required type="text" name="owner_first_name" defaultValue={values?.owner_first_name || ""} onChange={change} />
                                {errors?.owner_first_name && <Form.Text bsPrefix="error-text">{errors.owner_first_name}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="owner_last_name">
                                <Form.Label>Last Name</Form.Label>
                                <Form.Control required type="text" name="owner_last_name" defaultValue={values?.owner_last_name || ""} onChange={change} />
                                {errors?.owner_last_name && <Form.Text bsPrefix="error-text">{errors.owner_last_name}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="owner_email">
                                <Form.Label>Email</Form.Label>
                                <Form.Control required type="email" name="owner_email" defaultValue={values?.owner_email || ""} onChange={change} />
                                {errors?.owner_email && <Form.Text bsPrefix="error-text">{errors.owner_email}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="owner_mobile_phone">
                                <Form.Label>Mobile Phone</Form.Label>
                                <Form.Control type="tel" name="owner_mobile_phone" defaultValue={values?.owner_mobile_phone || ""} onChange={change} />
                                {errors?.owner_mobile_phone && <Form.Text bsPrefix="error-text">{errors.owner_mobile_phone}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="owner_home_phone">
                                <Form.Label>Home Phone</Form.Label>
                                <Form.Control type="tel" name="owner_home_phone" defaultValue={values?.owner_home_phone || ""} onChange={change} />
                                {errors?.owner_home_phone && <Form.Text bsPrefix="error-text">{errors.owner_home_phone}</Form.Text>}
                            </Form.Group>
                        </Col>
                    </Row>
                    <hr/>
                    <Row>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="owner_user_name">
                                <Form.Label>User Name</Form.Label>
                                <Form.Control type="text" name="owner_user_name" defaultValue={values?.owner_user_name || ""} onChange={checkUserHandler} autoComplete="new-password" />
                                {errors?.owner_user_name && <Form.Text bsPrefix="error-text">{errors.owner_user_name}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">                            
                            <Form.Group controlId="owner_password">
                                <Form.Label>Password</Form.Label>
                                <InputGroup>
                                    <Form.Control type="password" name="owner_password" defaultValue={values?.owner_password || ""} onChange={change} autoComplete="new-password" ref={ref} />
                                    <InputGroup.Append>
                                        <Button variant="outline-link m-0" onClick={togglePasswordFieldHandler}><i className="far fa-eye" ref={iconRef} /></Button>
                                        <Button variant="outline-link m-0" onClick={autoGeneratePasswordHandler}><i className="far fa-comment-alt-dots" /></Button>
                                    </InputGroup.Append>
                                </InputGroup>
                                {errors?.owner_password && <Form.Text bsPrefix="error-text">{errors.owner_password}</Form.Text>}
                            </Form.Group>
                        </Col>
                    </Row>
                </Col>

                <Col sm="12" lg="4"/>
            </Row>
        </Container>
    );
}

export default Step2;