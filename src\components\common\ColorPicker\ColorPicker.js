import React, { useState, /*useRef,*/ useContext } from 'react';
import chroma from 'chroma-js';
import { TwitterPicker } from 'react-color';
import { InputGroup, FormControl, OverlayTrigger, Popover } from 'react-bootstrap';

import { FileURLContext } from '../../../contexts/FileURLContext';

import styles from './ColorPicker.module.scss';

const getHarmoniousColors = (colors = [], colorCount = 17) => {
    colors = colors.filter((color, index) => colors.indexOf(color) === index);
        
    // Calculate average hue, saturation, and lightness values of colors in array
    const totalHue = colors.reduce((acc, cur) => acc + chroma(cur).get('hcl.h'), 0);
    const averageHue = totalHue / colors.length;    
    const totalSaturation = colors.reduce((acc, cur) => acc + chroma(cur).get('hcl.c'), 0);
    const averageSaturation = totalSaturation / (colors.length * 100);
    const totalLightness = colors.reduce((acc, cur) => acc + chroma(cur).get('hcl.l'), 0);
    const averageLightness = totalLightness / colors.length;
    
    // Generate color scale using HSL color harmony algorithm
    for (let i = 0; i < colorCount; i++) {
        const hue = averageHue;
        const saturation = averageSaturation;
        const lightness = (i / (colorCount - 1) * (1 - averageLightness) + averageLightness) * 0.5;
        const color = chroma.hsl(hue, saturation, lightness).hex(); // Generate color based on HSL values
        colors.push(color);
    }
    colors = colors.filter((color, index) => colors.indexOf(color) === index);
    colors = chroma.scale(colors).mode('lab').colors(colorCount);
    colors.sort((a, b) => chroma(b).get('hcl.h') - chroma(a).get('hcl.h'));

    return colors;
}

export const ColorPicker = React.forwardRef((props, ref) => {
    const { onChange, onBlur } = props;
    //const ref = useRef();
    const company_context = useContext(FileURLContext);
    const [color, setColor] = useState(props?.value || "");
    const scale = getHarmoniousColors([company_context.primaryColor, company_context.secondaryColor, company_context.tertiaryColor, company_context.backgroundColor]); 
    //const scale = chroma.scale([company_context.primaryColor, company_context.secondaryColor, company_context.tertiaryColor, company_context.backgroundColor]).mode('lab').colors(10);

    return (
            <InputGroup>
                <FormControl ref={ref} {...props} value={color} onChange={(e)=>{
                    e.preventDefault();
                    setColor(e.target.value);
                }} />
                <InputGroup.Append>
                    <OverlayTrigger trigger="click" placement="bottom" rootClose={true} overlay={
                        <Popover>
                            <TwitterPicker triangle="top-right" color={color} colors={scale} onChange={color=>setColor(color.hex)} onChangeComplete={(color,e)=>{
                                e.preventDefault();
                                setColor(color.hex);
                                if (onChange) onChange(e, color.hex);
                                if (onBlur) onBlur(e, color.hex);
                            }}/>
                        </Popover>
                    }>
                        <span className={`btn btn-light ${styles.btn}`}><i className="far fa-palette"/></span>
                    </OverlayTrigger>
                </InputGroup.Append>
            </InputGroup>
    );
});