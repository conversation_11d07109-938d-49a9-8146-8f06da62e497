/* eslint-disable */ 
let qaDevSB = Cypress.env('impact_sb_user');
let password = Cypress.env('login_password');

const randomAdjectives=["Halting", "Unwritten", "Tranquil", "Petite", "Abortive", "Incompetent", "Wary", "Shocking", "Exciting", "Guttural", "Dreary", "General", "Flat", "Protective", "Foregoing", "Aggressive", "Direful", "Diligent", "Mammoth", "Nauseating", "Alive", "Gainful", "Outrageous", "Unusual", "Skinny", "Bouncy", "Coordinated", "Resonant", "Alluring", "Ad hoc", "Disgusted", "<PERSON><PERSON>h", "<PERSON><PERSON>", "Absorbed", "Combative", "Tiny", "Vivacious", "Unequal", "Hospitable","Unadvised", "Laughable", "Average", "Spiteful", "<PERSON>y", "Wandering", "Hesitant", "Mountainous", "Tranquil", "<PERSON>y", "Eminent", "Silent", "Young", "Probable", "Domineering", "Resolute", "Reflective", "Healthy ", "Quizzical ", "Gentle " , " Disturbed" , " Mindless" , " Capricious" , " Periodic" , " One" , " Determined" , " Supreme" , " Dazzling" , " Romantic "]
const randomNouns=["Beast" , " Hair" , " Credit" , " Stranger" , " Feeling" , " Blow" , " Meaning" , " Star" , " Collection" , " Bear" , " Ship" , " Gun" , " Note" , " Description" , " Lift" , " Wedding" , " Yarn" , " Bathroom" , " Scale" , " Drink" , " String" , " Mine" , " Feet" , " Performance" , " Sheet" , " Brick" , " Crush" , " Marketing" , " Adjustment" , " Video" , " Situation" , " Afterthought" , " Eggs" , " Bait" , " Flowers" , " Ladybug" , " Wave" , " Flock" , " Wall" , " Depression" , " Attack" , " River" , " Liquid" , " Classroom" , " Yard" , " Control" , " Lunchroom" , " Curve" , " Pocket" , " Muscle" , " Grass" , " Advice" , " Death" , " Homework" , " Church" , " Moon" , " Drop" , " Definition" , " Departure" , " Recording" , " Things" , " Cheese" , " Professor" , " Bird" , " Depth" , " Pen" , " Regret" , " Bonus" , " Twig" , " Negotiation "]
const today = new Date().toLocaleDateString('en-US', {year: 'numeric', month: '2-digit', day: '2-digit'});
let local;

describe("It will check that products are able to be created successfully", { scrollBehavior: 'center', testIsolation: false }, ()=>{

    before("It will log in a user",()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        
        cy.loginLocal('http://localhost:3000/p/', qaDevSB, password)
        cy.wait('@getUserUser').then(()=>{
            local = localStorage.getItem('user');
        });
    });

    beforeEach("It will renew the user in local storage and viewport size",()=>{
        cy.restoreLocalUser(JSON.parse(local));
        cy.viewport(1920, 1080);
        cy.wait(2000)
    });

    it("will check the breadcrumbs and direct page access",()=>{
        cy.visit('http://localhost:3000/p/products/new')
        cy.wait(2000)
        cy.get('.breadcrumb')
            .should('exist')
            .children()
            .contains('Product Dashboard')
            .click();
        cy.get('[data-cy="product-dash-table"]')
            .should('exist');
        cy.get('[data-cy="product-dash-new-prod-btn"]')
            .click();
        cy.wait(1000);
        cy.url().should('include', '/products/new');
    });//end checking breadcrumbs
    
    context("Check the basic elements of the page", ()=>{

        it("will make sure that the header says 'create' and not 'edit'",()=>{
            cy.get('[data-cy="header-wrapper"]')
                .should('exist')
                .and('contain', "Create a New Product");
        });//end create, not edit

        it("will make sure that the product selection is present and causes the rest of the page to render",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Subscription');
            cy.wait(500);
            cy.get('[data-cy="product-details-section"]')
                .should('exist');
            cy.get('[data-cy="product-details-header"]')
                .should('exist');
            cy.get('[data-cy="product-details-title"]')
                .should('exist');
            cy.get('[data-cy="all-product-details-header"]')
                .should('exist');
            cy.get('[data-cy="variant-header-title"]')
                .should('exist');
            cy.get('[data-cy="new-product-variants"]')
                .should('exist');
            cy.get('[data-cy="new-product-add-variant"]')
                .should('exist');
            cy.get('[data-cy="product-changes-header"]')
                .should('not.exist');
            cy.get('[data-cy="new-product-local-errors"]')
                .should('not.exist');
        });//end elements of page sections

        it("will make subscriptions have the proper elements",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('have.value', 1);
            cy.get('[data-cy="new-product-select-bundles-token-bundles"]')
                .should('exist');
            cy.get('[data-cy="variant-type-single-variant"]')
                .should('exist');
            cy.get('[data-cy="add-variant-btn"]')
                .click();
            cy.wait(500);
            cy.get('[data-cy="variant-type-subscription-variant"]')
                .should("exist");
        });//end elements of subscriptions

        it("will make sure the proper elements exist for a physical product",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Physical')
                .should('have.value', 2);
            cy.get('[data-cy="variant-type-itemized-variants"]')
                .should('exist')
            cy.get('[data-cy="variant-type-measurement-sku-variant"]')
                .should('exist');
            cy.get('[data-cy="variant-measurement-details-check"] > input')
                .should('exist')
                .click()
            cy.get('[data-cy="variant-sku-upc-check"] > input')
                .should('exist')
                .click();
            cy.get('[data-cy="variant-type-sku-upc"]')
                .should('exist');
            cy.get('[data-cy="variant-type-meausurement-variants"]')
                .should('exist');
            cy.get('[data-cy="variant-type-single-variant"]')
                .should('not.exist');
            cy.get('[data-cy="add-variant-btn"]')
                .click();
            cy.wait(500);
            cy.get('[data-cy="variant-name"]')
        });//end elements of physical product

        it("will make sure the proper elements exist for a digital product",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Digital')
                .should('have.value', 3);
            cy.basicProductTest();
        });//end elements of digital product

        it("will make sure the proper elements exist for a service product",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Service')
                .should('have.value', 4);
            cy.basicProductTest();
        });//end elements of service product

        it("will make sure the proper elements exist for an event product",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Event')
                .should('have.value', 5);
            cy.basicProductTest();
        });//end elements of class product

        it("will make sure the proper elements exist for a bundle product",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Bundle')
                .should('have.value', 6);
            cy.basicProductTest();
            cy.get('[data-cy="new-product-select-bundles-service-token"]')
                .should('exist')
        });//end elements of bundle product

        it("will make sure the proper elements exist for a rental product",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Rental')
                .should('have.value', 7);
            cy.get('[data-cy="variant-type-itemized-variants"]')
                .should('exist')
            cy.get('[data-cy="variant-type-measurement-sku-variant"]')
                .should('exist');
            cy.get('[data-cy="variant-measurement-details-check"] > input')
                .should('exist')
                .click()
            cy.get('[data-cy="variant-sku-upc-check"] > input')
                .should('exist')
                .click();
            cy.get('[data-cy="variant-type-sku-upc"]')
                .should('exist');
            cy.get('[data-cy="variant-type-meausurement-variants"]')
                .should('exist');
            cy.get('[data-cy="variant-type-single-variant"]')
                .should('not.exist');
            cy.get('[data-cy="add-variant-btn"]')
                .click();
            cy.wait(500);
            cy.get('[data-cy="variant-name"]')
        });//end elements of rental products

        it("will have all the elements for a a food item",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Food & Drink')
                .should('have.value', 8);
            cy.get('[data-cy="variant-type-single-variant"]')
                .should('exist')
            cy.get('[data-cy="add-variant-btn"]')
                .click();
            cy.wait(500);
            cy.get('[data-cy="variant-type-single-variant"]')
                .should('not.exist');
            cy.get('[data-cy="variant-type-each-basic"]')
                .should('exist');
            cy.get('[data-cy="variant-type-measurement-sku-variant"]')
                .should('exist');
        });//end elements of food items

        it("will have all the elemelents for a token item",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Token')
                .should('have.value', 9);
            cy.get('[data-cy="variant-type-token-variant"]')
                .should('exist');
            cy.get('[data-cy="add-variant-btn"]')
                .should('not.exist')
        });//end elements of tokens

        it("will check all the elements of a cancellation fee",()=>{
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Cancellation Fee')
                .should('have.value', 10);
            cy.basicProductTest();
        });//end elements of cancellation fee
    })//end context checking for basic elements

    context("It will check create and verify a subscription product",()=>{
        let randomAdjective = randomAdjectives[Math.floor(Math.random()*randomAdjectives.length)];
        let randomNoun = randomNouns[Math.floor(Math.random()*randomNouns.length)];
        let productName = randomAdjective + randomNoun;
        let productDescription = `This ${randomAdjective.toLowerCase()} product was made by Cypress!`;
        let bundleName;
        let variantPrice = 1;
        let intervalQuantity = 6;
        let activationFee=1;

        it("will make sure creation is prevented when there are errors",()=>{
            cy.get('[data-cy="select-product-type"]')
                .select('Subscription')
                .should('have.value', 1);
            cy.get('[data-cy="create-and-add-new-btn"]')
                .click()
            cy.get('[data-cy="err-product-name"]')
                .should('exist');
            cy.get('[data-cy="err-variant-price"]')
                .should('exist');
            cy.get('[data-cy="err-no-interval-quantity"]')
                .should('exist')
            cy.get('[data-cy="err-warning-map"]')
                .should('contain', "You have not selected any categories for this product.")
            cy.get('[data-cy="add-variant-btn"]')
                .click()
            cy.get('[data-cy="new-product-variants"]')
                .children()
                .eq(0)
                .within(()=>{
                    cy.get('[data-cy="variant-name"]')
                    .clear();
                });
            cy.get('[data-cy="new-product-bd-status"] > select')
                .select('Pending')
                .should('have.value', 2);
            cy.get('[data-cy="create-and-add-new-btn"]')
                .click()
            cy.get('[data-cy="err-product-name"]')
                .should('exist');
            cy.get('[data-cy="err-warning-map"]')
                .should('contain', "You have selected a product status");
        });

        it("will properly fill out the basic info",()=>{
            cy.get('[data-cy="new-product-bd-name"] >input') //product name
                .type(productName);
            cy.get('[data-cy="new-product-bd-description"] > textarea' ) //product description
                .type(productDescription, {force: true});
            cy.get('[data-cy="new-product-bd-printer-select"]')
                .click();
            cy.get('#regular-item-0')
                .should('exist')
            cy.get('textarea')
                .click() //clicking something esle to make the typeahead close without making a selection
            cy.get('[data-cy="new-product-bd-new-categories"] > .form-group > .rbt > .form-control') //select a category
                .type("performance")
            cy.get('#categories_autocomplete-item-1')
                .click()
        });

        it("will properly fill out the variant section",()=>{
            cy.intercept('POST', '/api/product').as('fillTypeahead')
            cy.intercept('POST', '/api/product/add').as('addProduct')

            cy.get('[data-cy="new-product-variants"] > :nth-child(2)') //remove the extra variant
                .within(()=>{
                    cy.get('[data-cy="variant-remove-btn"]')
                        .click()
                });
            cy.get('#sub-bun-y') //yes to bundles
                .click();
            cy.wait('@fillTypeahead')
            cy.get('.rbt-input-main') //select a bundle
                .type('free')
            cy.get('#regular-item-0')
                .invoke('text').as('bundleName')
            cy.get('#regular-item-0')
                .click()
            cy.get('@bundleName').then(tempName=>{
                bundleName = tempName;
            });
            cy.get('[data-cy="variant-price"] > input')
                .type(variantPrice);

            //variant available start
            cy.get('[data-cy="variant-available-start"] > .react-datepicker-wrapper > .react-datepicker__input-container ')
                .click();
            cy.get(':nth-child(5) > .react-datepicker__day--030')
                .click();
            
            //variant end date
            cy.get('[data-cy="variant-available-end"] > .react-datepicker-wrapper > .react-datepicker__input-container ')
                .click();
            cy.get(':nth-child(5) > .react-datepicker__day--030')
                .click();

            //select yearly interval qty to check disabled
            cy.get('[data-cy="varaint-bill-interval"] > select ')
                .select('Yearly')
            // cy.get('[data-cy="variant-bill-interval-quantity"] > input')
            //     .should('be.disabled')
            cy.get('[data-cy="varaint-bill-interval"] > select')
                .select('Monthly')
            // cy.get('[data-cy="variant-bill-interval-quantity"] > input')
            //     .type(intervalQuantity)
            cy.get('[data-cy="variant-activation-fee"] > input')
                .type(variantPrice)

            //confirm we understand the warning
            // cy.get('[data-cy="new-product-confirmation"] > input')
            //     .click();
            cy.get('[data-cy="create-and-exit-btn"]')
                .click();
            cy.wait('@addProduct')
        });

        it("will make sure that the product was created and has the proper details",()=>{
            cy.intercept('POST', '/api/product').as('getProducts');
            cy.intercept('GET', '/api/product/type').as('getTypes');
            cy.intercept('GET', '/api/product/variant/**').as('getVariants');

            
            cy.log(`${String.fromCodePoint(0x1F92F)} Find the product on the dashboard`);
            
            cy.wait(2000);
            cy.get('[data-cy="pd-status-select"]')
                .select('Pending')
            cy.wait('@getProducts');
            cy.get('#search_input')
                .type(productName);
            cy.get('.product-row > :nth-child(2)')
                .should('contain', productName)
                .click();

            cy.wait('@getProducts');
            cy.wait('@getTypes');
            cy.wait("@getVariants");
            cy.get(':nth-child(1) > [data-cy="imported-edit-btn"]')
                .contains("Edit Product")
                .click();

            cy.log(`${String.fromCodePoint(0x1F92F)} Check the data on the edit screen`);
            cy.wait('@getProducts');
            cy.get('[data-cy="new-product-bd-name"] > input ')
                .should('have.value', productName);
            cy.get('[data-cy="new-product-bd-status"] > select')
                .should('have.value', 2);
            cy.get('[data-cy="new-product-bd-description"] > textarea')
                .should('have.value', productDescription);
            cy.get('#sub-bun-y')
                .should('be.checked');
            cy.get('#sub-bun-n')
                .should('not.be.checked');
            cy.get('.rbt-input-main')
                .should('have.value', bundleName);
            cy.get('[data-cy="variant-price"] > input')
                .should('have.value', variantPrice);
            // cy.get('[data-cy="variant-bill-interval-quantity"] > input')
            //     .should('have.value', intervalQuantity); 
            cy.get('[data-cy="variant-activation-fee"] > input')
                .should('have.value', activationFee + ".00");
            cy.get('[data-cy="cancel-btn"]')
                .click()
            cy.wait(1000)
            cy.get('[data-cy="product-dash-new-prod-btn"]')
                .click();
        });
    }) //end creating and checking a subscription product

    context("it will create and check a physical product",()=>{
        let randomAdjective = randomAdjectives[Math.floor(Math.random()*randomAdjectives.length)];
        let randomNoun = randomNouns[Math.floor(Math.random()*randomNouns.length)];
        let productName = randomAdjective + randomNoun;
        let productDescription = `This ${randomAdjective.toLowerCase()} product was made by Cypress!`;
        let variantPrice = 2;
        let SKU = "abcdef";
        let UPC = "123456";
        let weight = 1;
        let length = 2;
        let height = 3;
        let width = 4;
        let secondName = randomNouns[Math.floor(Math.random()*randomNouns.length)];
        let secondPrice = 4.5 

        it("will add the basic details",()=>{
            
            cy.get('[data-cy="select-product-type"]')
                .select('Physical')

            cy.get('[data-cy="new-product-bd-name"] >input')
                .type(productName)
            cy.wait(1000)
            cy.get('[data-cy="new-product-bd-description"] > textarea' ) //product description
                .type(productDescription);
            cy.get('[data-cy="new-product-bd-new-categories"]') //select a category
                .type("Merch Shop");
            cy.get('#categories_autocomplete-item-0')
                .click();

        }) //end physical product basic details
        
        it("will add the variant details",()=>{
            cy.intercept('POST', '/api/product').as('fillTypeahead');
            cy.intercept('POST', '/api/product/add').as('addProduct');

            cy.get('[data-cy="variant-price"] > input')
                .type(variantPrice);
            //variant end date
            cy.get('[data-cy="variant-available-end"] > .react-datepicker-wrapper > .react-datepicker__input-container > input')
                .click();
            cy.get('[data-cy="variant-measurement-details-check"] > input')
                .check();
            cy.get('[data-cy="variant-shippable"] > input')
                .check()
            cy.get('[data-cy="variant-sku-upc-check"] > input')
                .check();
        }); //end variant details

        it("will add the SKU/UPC and measurement details",()=>{
            cy.get('[data-cy="variant-sku"] > .sku-input')
                .type(SKU);
            cy.get('[data-cy="variant-upc"] > .sku-input')
                .type(UPC);
            cy.get('[data-cy="variant-weight"] > .product-flex-row > .measure-select')
                .should('have.value', "oz");
            cy.get('[data-cy="variant-weight"] > .product-flex-row > .measure-value')
                .type(weight);
            // cy.get('[data-cy="variant-height"] > .product-flex-row > .measure-select')
            //     .should('have.value', "in");
            cy.get('[data-cy="variant-height"] > .product-flex-row > .measure-value')
                .type(height);
            // cy.get('[data-cy="variant-length"] > .product-flex-row > .measure-select')
            //     .should("have.value", "in")
            //     .select('ft');
            cy.get('[data-cy="variant-length"] > .product-flex-row > .measure-value')
                .type(length);
            // cy.get('[data-cy="variant-width"] > .product-flex-row > .measure-select')
            //     .should('have.value', "in")
            cy.get('[data-cy="variant-width"] > .product-flex-row > .measure-value')
                .type(width);
        }) //end SKU/UPC and measurement details
        
        it("will add a second variant",()=>{
            cy.intercept('POST', '/api/product').as('getProducts');

            cy.get('[data-cy="add-variant-btn"]')
                .click()
            cy.get('[data-cy="new-product-variants"] > :nth-child(2)').within(()=>{
                cy.get('[data-cy="variant-name"] > input ')
                    .clear();
                cy.get('[data-cy="variant-name"] >input')
                    .type(`${secondName}`);
                cy.get('[data-cy="variant-price"] >input')
                    .type(secondPrice);
                cy.get('[data-cy="variant-status"] > select')
                    .select('Pending');
            }) 

            cy.get('[data-cy="create-and-exit-btn"]')
                .click();
            cy.wait('@getProducts');
        }); //end it will add a second variant

        it("will make sure the physical product has the appropriate data",()=>{
            cy.intercept('POST', '/api/product').as('getProducts');
            cy.intercept('GET', '/api/product/variant/**').as('getVariants')
            cy.intercept('GET', '/api/product/type').as('getTypes');

            cy.restoreLocalUser(JSON.parse(local));
            cy.visit('http://localhost:3000/p/products/dashboard');
            cy.wait(1000)
            cy.wait('@getProducts');
            cy.get('#search_input')
                .type(productName);
            cy.wait(1500)
            cy.get('.product-row > :nth-child(2)')
                .should('contain', productName)
                .click();
            
            cy.wait('@getProducts');
            cy.wait('@getTypes');
            cy.wait("@getVariants");
            cy.get(':nth-child(1) > [data-cy="imported-edit-btn"]')
                .click();

            cy.wait('@getProducts');
            cy.wait('@getVariants');
            cy.wait(1500)
            cy.get('[data-cy="new-product-bd-name"] >input ')
                .should('have.value', productName);
            cy.get('[data-cy="new-product-bd-status"] >select')
                .should('have.value', 1);
            cy.get('[data-cy="new-product-bd-description"] > textarea')
                .should('have.value', productDescription);

            cy.log(`${String.fromCodePoint(0x1F92F)} Check the first variant`);
            cy.get('[data-cy="new-product-variants"] > :nth-child(1)').within(()=>{
                cy.get('[data-cy="variant-name"] > input ')
                    .should('have.value', 'Default');
                cy.get('[data-cy="variant-price"] > input')
                    .should('have.value', variantPrice);
                cy.get('[data-cy="variant-status"] > select')
                    .should('have.value', 1);
                cy.get('[data-cy="variant-shippable"] > input')
                    .should('be.checked');
                cy.get('[data-cy="variant-measurement-details-check"] > input')
                    .should('be.checked');
                cy.get('[data-cy="variant-sku-upc-check"] > input')
                    .should('be.checked');
                cy.get('[data-cy="variant-sku"] > input')
                    .should('have.value', SKU);
                cy.get('[data-cy="variant-upc"] > input')
                    .should('have.value', UPC);
                cy.get('[data-cy="variant-weight"] > .product-flex-row > .measure-select')
                    .should('have.value', "oz");
                cy.get('[data-cy="variant-weight"] > .product-flex-row > .measure-value')
                    .should('have.value', weight + ".000");
                cy.get('[data-cy="variant-height"] > .product-flex-row > .measure-select')
                    .should('have.value', "in");
                cy.get('[data-cy="variant-height"] > .product-flex-row > .measure-value')
                    .should('have.value', height + ".000");
                cy.get('[data-cy="variant-length"] > .product-flex-row > .measure-select')
                    .should('have.value', "in");
                cy.get('[data-cy="variant-length"] > .product-flex-row > .measure-value')
                    .should('have.value', length + ".000");
                cy.get('[data-cy="variant-width"] > .product-flex-row > .measure-select')
                    .should('have.value', "in");
                cy.get('[data-cy="variant-width"] > .product-flex-row > .measure-value')
                    .should('have.value', width + ".000")
            }) //end first variant
            
            cy.log(`${String.fromCodePoint(0x1F92F)} Check the second variant`);
            cy.get('[data-cy="new-product-variants"] > :nth-child(2)').within(()=>{
                cy.get('[data-cy="variant-name"] > input ')
                    .should('have.value', secondName);
                cy.get('[data-cy="variant-price"] > input')
                    .should('have.value', secondPrice);
                cy.get('[data-cy="variant-status"] > select')
                    .should('have.value', 2);
                cy.get('[data-cy="variant-measurement-details-check"] > input')
                    .should('not.be.checked');
                cy.get('[data-cy="variant-measurement-details-check"] > input')
                    .click();
                cy.get('[data-cy="variant-shippable"] > input')
                        .should('not.be.checked');
                cy.get('[data-cy="variant-sku-upc-check"] > input')
                    .should('not.be.checked');
            })
        }) //end checking that the physical product has appropriate data

    }) //end create and check a physical product

    context("It will create a digital product",()=>{
        let randomAdjective = randomAdjectives[Math.floor(Math.random()*randomAdjectives.length)];
        let randomNoun = randomNouns[Math.floor(Math.random()*randomNouns.length)];
        let productName = randomAdjective + randomNoun;
        let productDescription = `This ${randomAdjective.toLowerCase()} product was made by Cypress!`;
        let vPriceOne = 1;
        let vPriceTwo = 2;
        let vPriceThree = 3;
        let vNameTwo = randomNouns[Math.floor(Math.random()*randomNouns.length)];
        let vNameThree = randomNouns[Math.floor(Math.random()*randomNouns.length)];
        
        it("will make sure that the product selection is present and causes the rest of the page to render",()=>{
            cy.intercept('POST', '/api/product').as('getProducts');
            cy.intercept('POST', '/api/product/add').as('addProduct')

            cy.visit('http://localhost:3000/p/products/new')
            cy.wait(2000)
            cy.get('[data-cy="select-product-type"]')
                .should('exist')
                .select('Digital');

            cy.get('[data-cy="new-product-bd-name"] > input ')
                .type(productName);
            cy.get('[data-cy="new-product-bd-description"] > textarea')
                .type(productDescription)
            cy.wait(1000)
            cy.get('[data-cy="basic-product-available-start"] > .react-datepicker-wrapper > .react-datepicker__input-container > input')
                .type(`{selectall}{backspace}${today}`)
            cy.get('[data-cy="new-product-bd-new-categories"] > .form-group > .rbt > .form-control')
                .click();
            cy.get('#categories_autocomplete-item-4')
                .click()

            cy.get('[data-cy="add-variant-btn"]')
                .click()
            cy.get('[data-cy="add-variant-btn"]')
                .click()
            cy.get('[data-cy="new-product-variants"]')
                .children()
                .should('have.length', 3)

            cy.get('.status-tax-print > [data-cy="new-product-bd-add-on-categories"] > .rbt > .form-control')
                .click();
            cy.get('#addon-category-item-3')
                .click();

            cy.get('[data-cy="new-product-variants"] > :nth-child(1)').within(()=>{
                cy.get('[data-cy="variant-name"]')
                    .click()
                    .type("Default")
                cy.get('[data-cy="new-product-bd-add-on-categories"] > .rbt > [style="margin-top: 10px;"]')
                    .should('exist')
                cy.get('[data-cy="variant-description"] ')
                    .type(vPriceOne)
                cy.get('[data-cy="new-product-bd-add-on-categories"] > .rbt > .form-control')
                    .click()
                cy.get('#addon-category-item-4')
                    .click()
                cy.get('[style="margin-top: 10px;"] > :nth-child(2)')
                    .should('exist')
            })
            cy.get('[data-cy="new-product-variants"] > :nth-child(2)').within(()=>{
                cy.get('[data-cy="variant-type-each-basic"] > .product-name-row > [data-cy="new-product-bd-add-on-categories"] > .rbt > [style="margin-top: 10px;"]')
                    .should('exist')
                cy.get('[style="margin-top: 10px;"] > :nth-child(2)')
                    .should('not.exist')
                cy.get('[data-cy="variant-name"] > input')
                    .clear()
                    .type(vNameTwo)
                cy.get('[data-cy="variant-description"] ')
                    .type(vPriceTwo);
            })
            cy.get('[data-cy="new-product-variants"] > :nth-child(3)').within(()=>{
                cy.get('[data-cy="variant-type-each-basic"] > .product-name-row > [data-cy="new-product-bd-add-on-categories"] > .rbt > [style="margin-top: 10px;"]')
                    .should('exist')
                cy.get('[style="margin-top: 10px;"] > :nth-child(2)')
                    .should('not.exist')
                cy.get('[data-cy="variant-name"] > input')
                    .clear()
                    .type(vNameThree);
                cy.get('[data-cy="variant-description"] ')
                    .type(vPriceThree)
            })
            cy.get('[data-cy="create-and-exit-btn"]')
                .click();
            cy.wait("@addProduct")
        })

        it("will make sure the product was created succesfully",()=>{
            cy.intercept("POST", '/api/product').as('getProducts')
            cy.intercept("GET", "/api/product/variant/**").as('getVariants')
            cy.intercept('GET', '/api/product/type').as('getTypes')
            
            cy.get('#search_input')
                .type(productName);
            cy.wait('@getProducts')

            cy.log(productName)
            cy.get('tbody')
                .children() 
                .each($child=>{
                    cy.get($child)
                        .invoke('text')
                        .as('rowText')
                        cy.get('@rowText').then(text=>{
                            if(text.includes(productName)){
                                cy.get($child)
                                    .click()
                            }
                        })
                })

            cy.wait('@getProducts');
            cy.get(':nth-child(1) > [data-cy="imported-edit-btn"]')
                .click();

            cy.wait('@getProducts')
            cy.wait('@getVariants')
            cy.wait(1500)
            cy.get('[data-cy="new-product-bd-name"] > input')
                .invoke('val')
                .as('productName')
            cy.get("@productName").then(text=>{
                expect(productName).to.equal(text)
            })
            cy.get('[data-cy="new-product-bd-description"] > textarea')
                .invoke('val')
                .as('productDescription')
            cy.get('@productDescription').then(text=>{
                expect(productDescription).to.equal(text)
            })
            cy.get('[data-cy="new-product-variants"] > :nth-child(1)').within(()=>{
                cy.get('[data-cy="variant-name"] > input')
                    .invoke('val')
                    .as('variantOne')
                cy.get("@variantOne").then(text=>{
                    expect(text).to.equal("Default")
                })
                cy.get('[data-cy="variant-description"] >input')
                    .invoke('val')
                    .as('price')
                cy.get('@price').then(text=>{
                    expect(text).to.equal(vPriceOne.toString())
                })
                cy.get('[style="margin-top: 10px;"] > :nth-child(1)')
                    .should('exist')
                cy.get('[style="margin-top: 10px;"] > :nth-child(2)')
                    .should('exist')  
            })
            cy.get('[data-cy="new-product-variants"] > :nth-child(2)').within(()=>{
                cy.get('[data-cy="variant-name"] > input')
                    .invoke('val')
                    .as('variantTwo')
                cy.get("@variantTwo").then(text=>{
                    expect(text).to.equal(vNameTwo)
                })
                cy.get('[data-cy="variant-description"]>input')
                    .invoke('val')
                    .as('price')
                cy.get('@price').then(text=>{
                    expect(text).to.equal(vPriceTwo.toString())
                })
                cy.get('[style="margin-top: 10px;"] > :nth-child(1)')
                    .should('exist')
                cy.get('[style="margin-top: 10px;"] > :nth-child(2)')
                    .should('not.exist')  
            })
            cy.get('[data-cy="new-product-variants"] > :nth-child(3)').within(()=>{
                cy.get('[data-cy="variant-name"] > input')
                    .invoke('val')
                    .as('variantThree')
                cy.get("@variantThree").then(text=>{
                    expect(text).to.equal(vNameThree)
                })
                cy.get('[data-cy="variant-description"]>input')
                    .invoke('val')
                    .as('price')
                cy.get('@price').then(text=>{
                    expect(text).to.equal(vPriceThree.toString())
                })
                cy.get('[style="margin-top: 10px;"] > :nth-child(1)')
                    .should('exist')
                cy.get('[style="margin-top: 10px;"] > :nth-child(2)')
                    .should('not.exist')  
            })
        })
    });

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
})