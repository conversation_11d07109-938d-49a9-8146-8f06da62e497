import React, {useState, useEffect} from 'react';
import {<PERSON>, Col, Container, Button} from 'react-bootstrap';


const ComingSoon = (props) => {
    // this should be in every component, its used to forward the click event to the builder if in preview mode
    let preview_click=null;
    if (props.is_preview && props.onClick){
        preview_click = props.onClick;
    }    

    return (
        <Container className={`coming-soon_${props.page_id} ${props.className || ""}`} style={props?.style || null} onClick={preview_click}>
            {props?.title && <h1>{props.title}</h1>}
            {props?.subtitle && <p>{props.subtitle}</p>}
            {props?.image && <img src={props.image} alt={props?.title || ""} />}
        </Container>
    );
}





export default ComingSoon;