import React,{useState,useEffect,Suspense} from 'react';
import { useSelector } from 'react-redux';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import Button from 'react-bootstrap/Button';
import Card from 'react-bootstrap/Card';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Modal from 'react-bootstrap/Modal';
import DatePicker from "react-datepicker";

import { differenceInHours,differenceInMinutes } from 'date-fns';

import "react-datepicker/dist/react-datepicker.css";
import './Timeline.css';

import Create from '../Create';

import Locations from '../../../api/Locations';

const day_start_time=6; // time when the day starts in 24hr format

// load events into the time table
const LoadEvents = (props) => {
    const [newEventModal,setNewEventModal]=useState();
    const selected_items=useSelector(state => state.map.selected_items);
    const [locationEvents, setLocationEvents] = useState([]);

    useEffect( () => {
        let mounted = true;

        Locations.getEvents({id: props.item.id, start_datetime: null, include_sublocations: 0})
        .then( response => {
            if(mounted) {

                setLocationEvents(response.data?.filter( event => //other events at this location
                    event.type_id !== 5 //not meta
                    && event.status_id !== 3 //not postponed
                    && event.status_id !== 4 //not canceled
                ));
            }
        }).catch( e => console.error(e) )

        return () => mounted = false;
    },[props.item.id]);

    const slotHandler = (item,slot,all_slots) =>{

        const modalCloseHandler = () => {
            setNewEventModal(null);
        }

        setNewEventModal(
            <Suspense fallback={
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
                }>
                <Modal show={true} onHide={modalCloseHandler}>
                    <Modal.Body>
                        <Create
                            {...props} 
                            location={item} 
                            slots={all_slots} 
                            slot={slot} 
                            modal={true} 
                            closeHandler={modalCloseHandler} 
                        />
                    </Modal.Body>
                </Modal>                
            </Suspense>
        );
    }

    const curr_date=props.date || new Date();
    let availability=[];
    let time_cell=[];
    let biggest_end=0;

    props.item.general_availabilities.filter( row =>
        row.day_of_week===curr_date.getDay() + 1).forEach( slot => { // getDay() is 0-indexed, day_of_week is 1-indexed

            const start_time=slot.start_time.split(":");
            const end_time=slot.end_time.split(":");
            
            const start_date=new Date(curr_date);
            start_date.setHours(start_time[0],start_time[1],0,0);
            const end_date=new Date(curr_date+(end_time[0]<start_time[0]?1:0));
            end_date.setHours(end_time[0],end_time[1],59,0);
            let hours=differenceInHours(end_date,start_date);
            const minutes=differenceInMinutes(end_date,start_date);

            // get availability and user selection, to set colors
            let slot_class="";
            selected_items.forEach( item => {
                if (+item.id === +props.item.id){
                    if (start_date >= new Date(item.booking.start_datetime) && start_date <= new Date(item.booking.end_datetime)){
                        slot_class="booked";
                    }
                    locationEvents.forEach( event => {
                        if (start_date >= new Date(event.start_datetime) && start_date <= new Date(event.end_datetime)){
                            slot_class="reserved";
                        }
                    })
                }
            });

            if (!Array.isArray(availability[start_date.getHours()])) availability[start_date.getHours()]=[];
            availability[start_date.getHours()].push(
                {
                    start_time:start_date,
                    end_time:end_date,
                    hours: hours,
                    minutes: minutes,
                    class_name: slot_class
                }
            );

            if (end_date>biggest_end) {
                biggest_end=end_date;
            }
        }
    );

    // if previous hours have no slots, create placeholder empty cells
    for (let i=0;i<+Object.keys(availability)?.[0]-day_start_time;i++){
        if (!Array.isArray(availability[i])) {
            time_cell.push(<div key={`con-phb${i}`} />)
        }
    }

    availability.forEach((slots,k) => {
        let cell_style={};
        let slot_style={};
        let biggest_diff=0;
        let slot_divs=[];
        let pos=0;

        // individual slot whithin an hour range
        slots.forEach((slot,l)=>{
            if (slot.minutes<=14){
                slot_style={flexBasis:"25%"};
            } else if (slot.minutes<=29){
                slot_style={flexBasis:"50%"};
            } else if (slot.minutes<=44){
                slot_style={flexBasis:"75%"};
            }
            if (slot.minutes>biggest_diff){
                let cell_start=slot.start_time.getHours()-day_start_time;
                if (cell_start<0) cell_start+=24;
                cell_start+=2;
                pos=cell_start;

                cell_style={gridColumn:cell_start+"/"+(cell_start+slot.hours)};
                biggest_diff=slot.minutes;
            }
            slot_divs.push(<div style={slot_style} className={slot.class_name} key={`innkl-${k}-${l}`} onClick={slot.class_name !== "reserved" ? ()=>slotHandler(props.item,slot,availability) : null}></div>);
        });

        // hour range with its slots
        time_cell[pos]=
            <div className="time-grid" key={`conk${k}`} style={cell_style}>
                {slot_divs}
            </div>
        ;
    });

    // fills the remaining available grid space
    if(availability.length > 0) {
        let end_of_day=new Date();
        end_of_day.setDate(curr_date.getDate());
        end_of_day.setHours(23,59,59,0);
        const diff=differenceInHours(end_of_day,biggest_end);
        if (diff > 0) {
            const cell_start=biggest_end?.getHours()-day_start_time+2;
            time_cell.push(<div style={{gridColumn:cell_start+"/"+(cell_start+diff+day_start_time+1)}} />);
        }
    }

    return [time_cell,newEventModal];
}


// create a time array
const timeArray = (date, tasks) =>{    

    if (!date) date=new Date();
    if (!tasks) tasks=[];

    let timeArray = {
        header: {
            start_time: 0,
            end_time: 23
        },
        times:[],
        tasks:[]
    };

    const d = new Date(date);    

    //let dayIndex="";
    let j=0;
    for (let i=0+day_start_time;i<24+day_start_time;i++){
        if (i>23) j=i-24;
        else j=i;
        
        if (!timeArray.times[i]){
            timeArray.tasks[i]=[];
            timeArray.times[i]={
                date:new Date(d),
                hour24:new Date(d.setHours(j)).toLocaleTimeString("en-US", { hour12: false, hour: '2-digit' }),
                hour:new Date(d.setHours(j)).toLocaleTimeString("en-US", { hour12: true, hour: '2-digit' }),
            }
        }
        
        /*dayIndex=`${d.getFullYear()}/${(("0" + (d.getMonth()+1)).slice(-2))}/${("0" + d.getDate()).slice(-2)}`;

        if (tasks[dayIndex] || tasks["everyday"]){
            [...(tasks[dayIndex] || []), ...(tasks["everyday"] || [])].forEach(task => {
                let [hour]=task.time_start.split(":");
                if (hour===new Date(d.getFullYear(),d.getMonth(),d.getDate(),i,0,0).toLocaleTimeString("en-US", { hour12: false, hour: '2-digit' })){
                    timeArray.tasks[j].push(task);
                }
            });
        }*/
    }

    return timeArray;
}

// create a daily view scheduler
const Timeline = (props) => {

    const [currentDate,setCurrentDate]=useState((props.date || new Date()).toLocaleDateString("en-US",{year:"numeric", month:"2-digit", day:"2-digit"}));
    const [calendar,setCalendar]=useState();
    const locations=useSelector(state => state.map.selected_items);
    const dayNames = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

    const changeDateHandler = (event) =>{
        setCurrentDate(event.toLocaleDateString("en-US",{year:"numeric", month:"2-digit", day:"2-digit"}));
    }

	useEffect(() => {
        setCalendar(timeArray(new Date(currentDate)));
	}, [currentDate]);

    let nowClass="";
    return (
        <React.Fragment>
            <Row>
                <Col sm="12">
                    <h1>When will it happen?</h1>
                    <p>
                        Select a date, then a spot for each location you selected in the previous step. 
                        Keep in mind that red spots are already taken.
                    </p>
                </Col>
            </Row>
            <Row>
                {locations.length>0 &&
                <Col sm="12">
                    <DatePicker 
                        dateFormat="MM/dd/yyyy"
                        minDate={new Date()}
                        maxDate={new Date(new Date().getFullYear()+100,12,31)}
                        showMonthDropdown
                        showYearDropdown
                        selected={new Date(currentDate)}
                        onChange={changeDateHandler}
                        customInput={
                            <Button variant="light" className="datepicker-calendar" type="button">{currentDate}</Button>
                        }
                    />
                    <Card className="timeline-container mt-2">
                        <div className="timeline">
                            <span className="day-name">{dayNames[new Date(currentDate).getDay()]}</span>
                            {calendar && calendar.times.map((time,i) => (
                                <div className="hour" key={`spdaysi${i}`}>
                                    {time.hour}
                                </div>
                            ))}

                            {locations.map((item,i)=>(
                                <React.Fragment key={`frgi${i}`}>
                                    <div className={nowClass} key={`con-oi${i}`}>
                                        <div className="location-name">{item.name}</div>
                                    </div>
                                    { LoadEvents({ date: new Date(currentDate), item: item }) }
                                </React.Fragment>
                            ))}
                        </div>
                    </Card>
                </Col>
                }
            </Row>
        </React.Fragment>
    );
}

export default Timeline;