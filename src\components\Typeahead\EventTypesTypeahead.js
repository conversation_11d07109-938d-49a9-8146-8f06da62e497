import React, {useCallback} from 'react';
import {Typeahead} from './Typeahead';
import Events from '../../api/Events';


/**Basic async typeahead for searching and selecting event types.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected event types back
 * @param {{}} placeholder
 * @param {{}} async
*/
const EventTypesTypeahead = (props) => {
    const getEventTypes=useCallback(async()=>{
        try{
            let response = await Events.Types.get();           
            let responseObj={
                data: response.data || null,
                errors: response.errors || null
            }
            return responseObj;
        }catch(ex){console.error(ex)}
    },[])

    return(
        <Typeahead
            {...props}
            async={props.async}
            id="event-type-search"
            makeRequest={getEventTypes}
            placeholder={props.placeholder}
            paginated={false}
            initialDataIds={props.initialDataIds || null}
        />
    )
}

export default EventTypesTypeahead