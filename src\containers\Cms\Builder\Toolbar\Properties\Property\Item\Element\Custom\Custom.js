import React, { useRef, useState, useCallback, lazy } from 'react';
import { randomUUID } from '../../../../../../../../../utils/cms';

export const Custom = React.forwardRef((props, ref) => {
    const {save} = props;

    const [stuff, setStuff] = useState(props?.value || null);
        
    // this is a special case for the typeahead component
    const shadyHandler = useCallback(params => {
        let _values = params;
        if (!isNaN(params)) params = `${params}`;
        if (params.length){
            if (Array.isArray(params)) _values=params.map(a=>a?.id || a);
            else _values = params;
            if (_values){
                let valid = true;
                if (Array.isArray(_values) && stuff){
                    if (!_values.some(a => !stuff.includes(a))) valid = false;
                } else {
                    if (stuff && stuff===_values) valid = false;
                }

                if (valid){
                    setStuff(_values);
                    save({
                        preventDefault() {},
                        stopPropagation() {},
                        target: { value: _values },
                    }, _values, {id: props.id, name: props.name}, null, props.passValueToElement || null);
                }
            }
        }
    },[props.id, props.name, save, stuff, props.passValueToElement]);

    if (props?.source?.component) {
        const LazyComponent = lazy(() => import(`../../../../../../../../../${props.source.component}`));            
        let comp_props = {
            ...props,
            //onChange: e =>save(e,null,props.id),
            id: props.id,
            value: props.default_value || props.value,
            key: randomUUID(),
        }
        if (props.source?.props){
            for (let prop in props.source.props){
                // if its a method, we need to set it up
                if (typeof props.source.props[prop]==="string"){
                    let _hasspecial = false;
                    if (props.source.props[prop].indexOf("method:")>-1){ // this is sent -from- the component
                        _hasspecial = true;
                        comp_props[prop] = shadyHandler.bind(null);
                    }
                    if (props.source.props[prop].indexOf("{value}")>-1){ // this is sent -to- the component
                        _hasspecial = true;
                        comp_props[prop] = props?.value || ""; 
                    } 
                    if (!_hasspecial) comp_props[prop] = props.source.props[prop];
                } else comp_props[prop] = props.source.props[prop];
            }
        }
        return <LazyComponent {...comp_props} ref={ref} value={stuff} />;
    }

    return null;
});