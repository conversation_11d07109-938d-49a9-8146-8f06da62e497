import React from 'react';
import { Container, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';

import SubHeader from '../../../../components/common/SubHeader';

export const KillswitchPage =({path, patronLayout, registerName = "Register", page=true, ...props})=>{

    const content=<p>
            Sorry! This {" "}
            {!path ? "page" : 
                <>
                    {path?.includes('/online') || patronLayout ? "register" : ""} 
                    {path?.includes('/pos') || !patronLayout ? "POS" : ""} 
                </>
            }
            {" "}
            has been temporarily disabled! It will be disabled until turned back on by an administrator Please check back later! 
        </p>

    return(
        <>
        {page ?
            <Container fluid>
                <SubHeader items={[
                    { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                    { text: registerName }
                ]}/>
                <Card className="content-card">
                    {content}
                </Card>
            </Container>
            :
            content
        }
        </>
    )
}