@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';

.tree-view-common{
    .MuiTreeItem-content{
        align-items: baseline !important;
    }
    
    .MuiTypography-body1 {
        font-size:.85rem !important;
        font-family: inherit !important;
        line-height: 2rem !important;
    }
    
    .description {
        flex-grow: 1;
        align-self: center;
        font-size: .85rem;
        color:$card-subtitle-color;
        line-height: initial;
        padding-bottom:1rem;
    }
    
    .node-row {
        display: flex;
        align-content: center;
        flex-direction: column;
    }
    
    .node-row div {
        display:flex;
        
    }
    
    .node-row a {
       color:inherit;
       font-weight: 500;
    }
}
