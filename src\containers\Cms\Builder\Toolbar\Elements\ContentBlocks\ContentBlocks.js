import React,{ useState, useEffect } from 'react';
import { useSelector } from 'react-redux';

import Item from './Item';
import ContentBlock from '../../../../Components/Components/ContentBlock';
import FormBlock from '../../../../Components/Components/FormBlock';
import WizardBlock from '../../../../Components/Components/WizardBlock';

import { getPageById } from '../../../../../../utils/cms';

export const ContentBlocks = (props) => {
    const cmsSelector = useSelector(state => state.cms);
    const [data, setData] = useState(props.data || []);

    const clickHandler=(e, block_id)=>{
        e.preventDefault();
        e.stopPropagation();
    }

    /*
    useEffect(() => {
        if (props?.data) setData(props.data || []);
    }, [props?.data]);
    */

    useEffect(() => {
        const _getContentBlocks = async (type) => {
            if (type){
                let _blocks=[];
                try{
                    const res = await getPageById({website_id:cmsSelector.currentWebsite,page_type_id:type});
                    if (res){
                        res.forEach(block =>{
                            let _component, _component_name, _element_type, _list, _id_name, _class;
                            switch(+type){
                                case 10:
                                    _component=FormBlock;
                                    _component_name="FormBlock";
                                    _list="FormBlocks";
                                    _class="form-block";
                                    _id_name="form_id";
                                    _element_type="form";
                                    break;
                                case 12:
                                    _component=WizardBlock;
                                    _component_name="WizardBlock";
                                    _list="WizardBlocks";
                                    _class="wizard-block";
                                    _id_name="wizard_id";
                                    _element_type="wizard";
                                    break;
                                case 2:
                                default:
                                    _component=ContentBlock;
                                    _component_name="ContentBlock";
                                    _list="ContentBlocks";
                                    _class="content-block";
                                    _id_name="contentblock_id";
                                    _element_type="content-block";
                                    break;
                            }
                            _blocks.push({
                                id: `_${block?.page_type?.name || ""}-${block.id}`,
                                name: block.title,
                                component: _component,
                                component_name: _component_name,
                                element_type: _element_type || "content-block",
                                tooltip: `${block.title} ${block?.page_type?.name || "Content Block"}`,
                                apply_styles: false,
                                can_drop: false,
                                list: _list,
                                class: _class,
                                properties: [
                                    {
                                        id: 600,
                                        changed: true,
                                        name: _id_name,
                                        type: "select",
                                        options: [],
                                        description: `The ${(block?.page_type?.name || "content block").toLowerCase()} to display`,
                                        display_name: block?.page_type?.name || "Content Block",
                                        value: block.id,
                                        source: {
                                            component: "containers/Cms/Components/Properties/ContentBlockSelect",
                                            props: {
                                                page_type: type,
                                                selection: "method:selection"
                                            },
                                        },
                                        elements: ["ContentBlock"]
                                    }
                                ],
                            });
                        });
                    }
                    setData(prev=>[...prev,..._blocks]);
                } catch(e){
                    console.log(e);
                }
            }
        }
        if (props.list==="ContentBlocks" || props.list==="FormBlocks" || props.list==="WizardBlocks") _getContentBlocks(props.page_type);
    }, [props.list, props.page_type, cmsSelector.currentWebsite]);


    useEffect(() => {
        return () => {
            setData([]);
        }
    }, []);

    if (!data) return null;

    return (
        <div className="content-block-container">
            {!data?.length && props?.page_type && <span>There are no {+props.page_type===10?"Forms":+props.page_type===12?"Wizards":"Content Blocks"} for the website.</span>}
            {data?.length && data?.map((block,i) => (
                <Item {...block} key={`${+props.page_type===10?"form":+props.page_type===12?"wizard":"content"}-block-${block.id}-${i}`} click={(e)=>clickHandler(e,block.id)} />
            ))}
        </div>
    );

}