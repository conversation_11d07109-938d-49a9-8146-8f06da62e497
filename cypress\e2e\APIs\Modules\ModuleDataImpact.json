{"requiredModuleIds": [{"name": "Home", "id": 2, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Sign Out", "id": 36, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "My Profile", "id": 49, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Home Alternate URL", "id": 70, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Side Menu", "id": 190, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}], "userProfileModuleIds": [{"name": "User View Their Own Profile", "id": 74, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "View User Event Schedule", "id": 77, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Reset User Password", "id": 78, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "View User Documents", "id": 79, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Edit User Notification Settings", "id": 80, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "User Profile: View User's Groups", "id": 106, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": false}, "patron": {"access": false}}}, {"name": "View Account Settings For User", "id": 201, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": false}}}, {"name": "View User's Notes", "id": 202, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": false}, "patron": {"access": false}}}, {"name": "Print User QR Label", "id": 203, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": false}, "patron": {"access": false}}}, {"name": "Edit User Profile", "id": 204, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "View User's Family", "id": 207, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Add Members to User's Family Group", "id": 209, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "User Leave Family Group", "id": 212, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Create Family Group", "id": 213, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "View User's Wallet", "id": 216, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": false}, "patron": {"access": false}}}, {"name": "View User's Role", "id": 270, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": false}}}, {"name": "Edit a User's Role", "id": 271, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": false}, "patron": {"access": false}}}, {"name": "Deactivate User Account", "id": 272, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": false}, "patron": {"access": false}}}], "serviceModuleIds": [{"name": "Services Dashboard", "id": 52, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": false}}}, {"name": "My Services", "id": 53, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Search Bookings", "id": 57, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": false}, "patron": {"access": false}}}, {"name": "Create New Services", "id": 81, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": false}, "patron": {"access": false}}}, {"name": "Respond to Cancellation Requests", "id": 82, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": false}}}, {"name": "Edit Services", "id": 83, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": false}}}, {"name": "Delete Services", "id": 84, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": false}}}, {"name": "Book Services", "id": 85, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}], "groupModuleIds": [{"name": "Groups Dashboard", "id": 14, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": false}}}, {"name": "Create New Groups", "id": 102, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": false}}}, {"name": "Edit Groups", "id": 103, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "View Their Own Groups Page", "id": 105, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}, {"name": "Accept a Group Invitation", "id": 107, "permissions": {"sb": {"access": true}, "owner": {"access": true}, "admin": {"access": true}, "staff": {"access": true}, "non-manager": {"access": true}, "patron": {"access": true}}}]}