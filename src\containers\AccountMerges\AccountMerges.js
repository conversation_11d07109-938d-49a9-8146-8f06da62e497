import React, {useState, useEffect, useRef, useCallback} from 'react'
import { <PERSON><PERSON><PERSON><PERSON>b, Container, Col, Row, Card, Form, Button, Modal, ModalBody, Table } from 'react-bootstrap'
import { useHistory,Link } from 'react-router-dom'
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';

// import Table from '../../components/common/Table';
import ErrorCatcher from '../../components/common/ErrorCatcher';
import Toast from '../../components/Toast';
import Pagination from '../../components/common/Pagination';
import Stack from '../../components/common/Stack';
import SubHeader from '../../components/common/SubHeader';

import Users from '../../api/Users';
import { useRoleCheck } from '../../components/common/CustomHooks';
import { UndoMerge } from './UndoMerge/UndoMerge';

import "./AccountMerges.scss";
import { TableSkeleton } from '../../components/common/TableSkeleton/TableSkeleton';

export const AccountMerges = () => {

    const history = useHistory();
    const mountedRef = useRef(false);
    const currentUserRole=useRoleCheck();
    const [loading, setLoading]=useState(true)
    const [error, setError]=useState();
    const [success, setSuccess]=useState();
    const [allMerges, setAllMerges]=useState();
    const [activeMerge, setActiveMerge]=useState();
    const [modalHideShow, setModalHideShow]=useState(false);

    //for the POST call
    const [totalItems, setTotalItems]=useState(0);
    const [itemsPerPage, setItemsPerPage]=useState(25);
    const [page, setPage]=useState(1);
    const [pages, setPages]=useState();
    const [search, setSearch]=useState("");
    const [userIdSearch, setUserIdSearch]=useState();
    const [sortColumn, setSortColumn]=useState("new_user_id");
    const [sortOrder, setSortOrder]=useState("ASC");

//#region useCallback
    //Get Merges
    const getAllMerges=useCallback(async()=>{
        try{
            let response = await Users.Merge.get_merges_post({
                filters:{
                    search_words: search,
                    user_id: userIdSearch,
                },
                max_records: +itemsPerPage,
                page_no: page,
                sort_col: sortColumn,
                sort_direction: sortOrder,
            });
            if(!response.error && mountedRef.current){
                setAllMerges(response.data.user_merges);
                let numberOfPages=createPageArray(response.data.page_record_count);
                setPages(numberOfPages);
                setTotalItems(response.data.total_record_count);
            }else{
                setError("Unable to get data to display.")
            }
        }catch(ex){console.error(ex)}
        setLoading(false);
    },[search, userIdSearch, itemsPerPage, page, sortColumn, sortOrder]);

//#endregion

//#region useEffect
    //First load
    useEffect(()=>{
        mountedRef.current = true

        return()=>{
            mountedRef.current = false;
            setSearch();
            setUserIdSearch();
        }
    },[]);
    
    useEffect(()=>{
        getAllMerges();
    },[getAllMerges, currentUserRole.id]);

    useEffect(()=>{
        if(activeMerge && mountedRef.current) setModalHideShow(true)
    },[activeMerge]);
//#endregion

//#region Functions/Handlers


    const newMergeButton=()=>{
        history.push("/p/admin/merge/new");
    };

    const modalOnClose=(status, msg)=>{
        if(mountedRef.current){
            setActiveMerge();
            if(status ==="success") {
                setSuccess(<Toast>{msg}</Toast>);
                getAllMerges();
            }
            if(status === "error"){
                setError(<ErrorCatcher error={msg} />)
            }
            setModalHideShow(false);
        };
    };

    const adjustSortOrder=()=>{
        if(sortOrder === "ASC") setSortOrder("DESC")
        else setSortOrder("ASC")
    }

    const searchHandler=(e)=>{
        if(e.target.value.length >2 ) setSearch(e.target.value);
        if(e.target.value.length===0) setSearch("");
    }
    
    const adjustItemsPerPage=(e)=>{
        if(e.target.value==="") setItemsPerPage(1);
        else setItemsPerPage(e.target.value)
    }

    //the common component Pagniation uses the pages all in an array
    const createPageArray=(number)=>{
        let pages=[]
        for(let i =0; i<number;i++){
            pages.push(i)
        }
        return pages;
    }
//#endregion Functions/Handlers

  return (
    <Container fluid className="account-merges-wrapper">
        <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Account Merges" }
            ]} />
        <Card className="content-card">
            <Stack direction="horizontal" gap={2}>
                <h4 className="tm-1 section-title order-2 order-lg-1 ">Account Merges</h4>
                <div className="ms-sm-auto order-1 order-lg-2">
                    <Button variant="primary" onClick={newMergeButton}>Start New Merge</Button>
                </div>
            </Stack>
            <hr/>


            <>
                {allMerges &&
                    <div>
                        {error}
                            <Form>
                                <Row className="w-100">
                                    <Col>
                                        <Form.Group controlId="search">
                                            <Form.Label>Search Merges</Form.Label>
                                            <Form.Control type="text" placeholder="Search..." onChange={searchHandler} />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group controlId="userId">
                                            <Form.Label>User Id Lookup</Form.Label>
                                            <Form.Control type="number" placeholder="User Id..." value={userIdSearch} onChange={(e)=>setUserIdSearch(e.target.value)} />
                                        </Form.Group>
                                    </Col>
                                </Row>
                            </Form>
                            <Table className="table">
                                <thead>
                                    <tr>
                                        <th onClick={(e)=>setSortColumn("old_user_id")}>
                                            <span className="mx-1">
                                                Old User Id 
                                            </span>
                                            <i onClick={adjustSortOrder} className={`ml-1 far fa-${sortColumn==="old_user_id"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}/>
                                        </th>
                                        <th onClick={(e)=>setSortColumn("old_user_first_name")}>
                                            <span className="mx-1">
                                                Old User First Name 
                                            </span>
                                            <i onClick={adjustSortOrder} className={`ml-1 far fa-${sortColumn==="old_user_first_name"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}/>
                                        </th>
                                        <th onClick={(e)=>setSortColumn("old_user_last_name")}>
                                            <span className="mx-1">
                                                Old User Last Name 
                                            </span>
                                            <i onClick={adjustSortOrder} className={`ml-1 far fa-${sortColumn==="old_user_last_name"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}/>
                                        </th>
                                        <th onClick={(e)=>setSortColumn("new_user_id")}>
                                            <span className="mx-1">
                                                New User Id 
                                            </span>
                                            <i onClick={adjustSortOrder} className={`ml-1 far fa-${sortColumn==="new_user_id"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}/>
                                        </th>
                                        <th onClick={(e)=>setSortColumn("new_user_first_name")}>
                                            <span className="mx-1">
                                                New User First Name 
                                            </span>
                                            <i onClick={adjustSortOrder} className={`ml-1 far fa-${sortColumn==="new_user_first_name"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}/>
                                        </th>
                                        <th onClick={(e)=>setSortColumn("new_user_last_name")}>
                                            <span className="mx-1">
                                                New User Last Name 
                                            </span>
                                            <i onClick={adjustSortOrder} className={`ml-1 far fa-${sortColumn==="new_user_last_name"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}/>
                                        </th>
                                        <th className="text-center">
                                            <span className="mx-1">
                                                Undo Merge 
                                            </span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {loading ?
                                        <TableSkeleton columns={7} rows={5} />
                                    :
                                        <>
                                            {allMerges?.map((eachMerge, i)=>(
                                                <tr key={`merge-row-${i}`}>
                                                    <td>{eachMerge.old_user_id}</td>
                                                    <td>{eachMerge.old_user_first_name}</td>
                                                    <td>{eachMerge.old_user_last_name}</td>
                                                    <td>{eachMerge.new_user_id}</td>
                                                    <td>{eachMerge.new_user_first_name}</td>
                                                    <td>{eachMerge.new_user_last_name}</td>
                                                    <td className="cp text-center"><i className="far fa-undo" onClick={()=>setActiveMerge(eachMerge)} /></td>
                                                </tr>
                                            ))}
                                        </>
                                    }
                                </tbody>
                            </Table>

                            <Row className="account-merge-pag-wrapper">
                                <Col className="d-flex flex-row">
                                    <Form.Label>Records Per Page:</Form.Label>
                                    <Form.Control type="numeric" name="records-per-page-select" placeholder={`${itemsPerPage}`} onChange={adjustItemsPerPage}/>
                                </Col>
                                {totalItems > 1 &&
                                    <Col className="justify-content-end">
                                        <Pagination 
                                            itemsCount={totalItems}
                                            itemsPerPage={itemsPerPage}
                                            currentPage={page}
                                            setCurrentPage={setPage}
                                            alwaysShown={false}
                                            />
                                    </Col>
                                }
                            </Row>
                    </div>
                }
            </>
        </Card>
        <Modal show={modalHideShow} onHide={modalOnClose}>
            <ModalBody>
                <UndoMerge activeMerge={activeMerge} currentUserRole={currentUserRole} onClose={modalOnClose} />
            </ModalBody>
        </Modal>
        {success}
    </Container>
  )
}
