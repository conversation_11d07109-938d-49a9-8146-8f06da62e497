import React, { useCallback } from 'react';

import { Typeahead } from './Typeahead';

import ServicesAPI from '../../api/Services';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import './Typeahead.scss';

/**Basic async typeahead for searching services.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Services back
*/
export const ServiceTypeahead = (props) => {

    // this is so that when searching old bookings you can still filter by the service name even if the service is no longer available
    let includeDeleted = props.includeDeleted || false;

    const makeRequest = useCallback(async (query, perPage, page=1) => {
        let response = await ServicesAPI.get({
            search_words: query || null,
            max_records: perPage,
            page_no: page,
            sort_col: "name",
            sort_direction: "ASC",
            include_deleted: includeDeleted,
        });
        let responseObj = {
            data: response.data.services || null,
            errors: response.data.errors || null
        }
        return responseObj;
    },[includeDeleted]);

    // each item in responseObj.data is an option
    const formatForLabel = (option) => (
        `${option?.name}`
    );

    return (
        <Typeahead
            {...props}
            id="service-search"
            formatForLabel={formatForLabel}
            makeRequest={makeRequest}
            placeholder={props.placeholder ? props.placeholder : "Enter a service name..."}
            paginated={true}
        />
    )
}
