/*eslint-disable*/
/// <reference types="cypress" />

import React, { useState, useRef } from 'react';
import { EventFamily } from './Test';

// Test wrapper component to manage state
const EventFamilyTestWrapper = ({ 
    eventDetails, 
    user, 
    initialSelectedFamily = [],
    ...props 
}) => {
    const [selectedFamily, setSelectedFamily] = useState(initialSelectedFamily);
    const loadingStates = useRef({ family: false, eventDetails: false });
    const answersRef = useRef(null);

    const handleFamilyClick = (userId) => {
        setSelectedFamily(prev => 
            prev.includes(userId) 
                ? prev.filter(id => id !== userId)
                : [...prev, userId]
        );
    };

    return (
        <EventFamily
            eventDetails={eventDetails}
            user={user}
            loadingStates={loadingStates}
            handleFamilyClick={handleFamilyClick}
            selectedFamily={selectedFamily}
            answersRef={answersRef}
            {...props}
        />
    );
};

describe('EventFamily Component Tests', {scrollBehavior: "center", testIsolation: true}, () => {
    let familyFixtures;
    let eventFixtures;

    before(() => {
        cy.fixture('User/UserProfile/family_data.json').then((data) => {
            familyFixtures = data;
        });
        cy.fixture('Events/event_registration_conditions.json').then((data) => {
            eventFixtures = data;
        });
    });

    beforeEach(() => {
        // Mock user in localStorage for component mounting
        window.localStorage.setItem('user', JSON.stringify({
            menu: [],
            roles: [],
            profile: { id: 3075, first_name: 'Midnight', last_name: 'Boo' },
            token: "bearer test-token"
        }));
        cy.viewport(1200, 800);

        // Stub all API calls that might be made
        cy.window().then((win) => {
            cy.stub(win, 'fetch').callsFake((url) => {
                if (url.includes('/api/group/filter') || url.includes('/api/group/list')) {
                    // Check if this is a test for no family scenario
                    const currentTest = Cypress.currentTest.title;
                    if (currentTest.includes('no family exists')) {
                        return Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve({ errors: null, data: { groups: [] } })
                        });
                    }
                    // Check if this is a test for limited user access
                    if (currentTest.includes('eliminate users from families')) {
                        return Promise.resolve({
                            ok: true,
                            json: () => Promise.resolve({
                                errors: null,
                                data: [
                                    {
                                        group_id: 1002,
                                        group_type_id: 4,
                                        group_status_id: 1,
                                        name: "The Midnights",
                                        description: "A fun family full of people who know Midnight.",
                                        is_admin: 0,
                                        group_member_role_id: 12,
                                        group_member_role: "Guardian",
                                        group_member_status_id: 2,
                                        group_member_id: 2393
                                    }
                                ]
                            })
                        });
                    }
                    // Default to full family fixtures
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve(familyFixtures.userGroups)
                    });
                }
                if (url.includes('/api/group/373')) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve(familyFixtures.group373)
                    });
                }
                if (url.includes('/api/group/1002')) {
                    return Promise.resolve({
                        ok: true,
                        json: () => Promise.resolve(familyFixtures.group1002)
                    });
                }
                // Default fallback
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({ data: [] })
                });
            });
        });
    });

    it('should filter out users outside age range', () => {
        const eventWithAgeRestriction = {
            ...eventFixtures.event_with_users.publicGet.data.events[0],
            min_age: 18,
            max_age: 65,
            advancedDetails: {
                users: [],
                custom_fields: []
            }
        };

        const user = { id: 3075, first_name: 'Midnight', last_name: 'Boo' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventWithAgeRestriction}
                user={user}
            />
        );

        // Check that users outside age range are disabled
        cy.get('[data-cy="family-member-checkbox"]').each(($checkbox, index) => {
            cy.wrap($checkbox).then(($el) => {
                const isDisabled = $el.prop('disabled');
                if (isDisabled) {
                    // Verify age-related error message appears
                    cy.get('[data-cy="family-member-age-status"]').eq(index)
                        .should('contain.text', 'age');
                }
            });
        });
    });

    it('should disable users already registered for the event', () => {
        const eventWithRegisteredUsers = {
            ...eventFixtures.event_with_users.publicGet.data.events[0],
            advancedDetails: {
                users: [{ id: 3590 }, { id: 3603 }], // Laurynas and Skyler are registered
                custom_fields: []
            }
        };

        const user = { id: 3075, first_name: 'Midnight', last_name: 'Boo' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventWithRegisteredUsers}
                user={user}
            />
        );

        // Check registration status messages
        cy.get('[data-cy="family-member-registration-status"]').should('exist');
        
        // Verify some users show "Already Registered"
        cy.get('[data-cy="family-member-registration-status"]')
            .contains('Already Registered')
            .should('exist');
    });

    it('should show custom fields when user is selected', () => {
        const eventWithCustomFields = {
            ...eventFixtures.event_with_users.publicGet.data.events[0],
            advancedDetails: {
                users: [],
                custom_fields: [
                    {
                        id: 1,
                        name: 'emergency_contact',
                        placeholder_text: 'Emergency Contact',
                        custom_field_type: 'input',
                        required: true
                    },
                    {
                        id: 2,
                        name: 'shirt_size',
                        placeholder_text: 'Shirt Size',
                        custom_field_type: 'select',
                        required: false,
                        options: [
                            { value: 'S', text: 'Small' },
                            { value: 'M', text: 'Medium' },
                            { value: 'L', text: 'Large' }
                        ]
                    }
                ]
            }
        };

        const user = { id: 3075, first_name: 'Midnight', last_name: 'Boo' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventWithCustomFields}
                user={user}
            />
        );

        // Click on a family member checkbox
        cy.get('[data-cy="family-member-checkbox"]').first().click();

        // Verify custom fields appear
        cy.get('[data-cy="event-custom-fields-wrapper"]').should('exist');
        cy.get('[data-cy="event-custom-field"]').should('have.length', 2);

        // Check required field has asterisk
        cy.get('[data-cy="event-custom-field-label"]')
            .contains('Emergency Contact')
            .should('contain', '*');

        // Check field types
        cy.get('[data-cy="event-custom-field-input"]').should('exist');
        cy.get('[data-cy="event-custom-field-select"]').should('exist');
    });

    it('should show proper role labels for family members', () => {
        const user = { id: 3075, first_name: 'Midnight', last_name: 'Boo' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventFixtures.event_with_users.publicGet.data.events[0]}
                user={user}
            />
        );

        // Check that the logged-in user shows "(Me)"
        cy.get('[data-cy="family-member-name-role"]')
            .contains('Midnight Boo')
            .should('contain', '(Me)');

        // Check that other users show their roles
        cy.get('[data-cy="family-member-name-role"]')
            .contains('(Admin)')
            .should('exist');

        cy.get('[data-cy="family-member-name-role"]')
            .contains('(Guardian)')
            .should('exist');

        cy.get('[data-cy="family-member-name-role"]')
            .contains('(Child)')
            .should('exist');
    });

    it('should show all users from multiple families without duplicates', () => {
        const user = { id: 3075, first_name: 'Midnight', last_name: 'Boo' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventFixtures.event_with_users.publicGet.data.events[0]}
                user={user}
            />
        );

        // Count total family members (should deduplicate Midnight Boo who appears in both families)
        cy.get('[data-cy="family-member-checkbox"]').then($checkboxes => {
            const memberNames = [];
            $checkboxes.each((_, checkbox) => {
                const nameElement = Cypress.$(checkbox).siblings('[data-cy="family-member-name-role"]');
                const name = nameElement.text().split('(')[0].trim();
                memberNames.push(name);
            });
            
            // Check for duplicates
            const uniqueNames = [...new Set(memberNames)];
            expect(memberNames.length).to.equal(uniqueNames.length);
        });
    });

    it('should show add new family member button for admin users', () => {
        const adminUser = { id: 2, first_name: 'Joe', last_name: 'Gero II' }; // Admin in Gero Family

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventFixtures.event_with_users.publicGet.data.events[0]}
                user={adminUser}
            />
        );

        // Check that add new family member button appears for families where user is admin
        cy.get('[data-cy="add-new-family-member-btn"]').should('exist');
        cy.get('[data-cy="add-new-family-member-btn"]')
            .should('contain', 'Add New Family Member to Gero Family');
    });

    it('should show logged-in user even when no family exists', () => {
        const userWithNoFamily = { id: 9999, first_name: 'Solo', last_name: 'User' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventFixtures.event_with_users.publicGet.data.events[0]}
                user={userWithNoFamily}
            />
        );

        // The logged-in user should still appear even with no family
        cy.get('[data-cy="family-member-name-role"]')
            .contains('Solo User')
            .should('contain', '(Me)');
    });

    it('should validate required custom fields before allowing user selection', () => {
        const eventWithRequiredFields = {
            ...eventFixtures.event_with_users.publicGet.data.events[0],
            advancedDetails: {
                users: [],
                custom_fields: [
                    {
                        id: 1,
                        name: 'emergency_contact',
                        placeholder_text: 'Emergency Contact',
                        custom_field_type: 'input',
                        required: true
                    }
                ]
            }
        };

        const user = { id: 3075, first_name: 'Midnight', last_name: 'Boo' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventWithRequiredFields}
                user={user}
            />
        );

        // Select a family member
        cy.get('[data-cy="family-member-checkbox"]').first().click();

        // Verify required field appears with asterisk
        cy.get('[data-cy="event-custom-field-label"]')
            .should('contain', 'Emergency Contact')
            .should('contain', '*');

        // Verify the required attribute is set
        cy.get('[data-cy="event-custom-field-input"]')
            .should('have.attr', 'required');
    });

    it('should eliminate users from families where logged-in user has no access', () => {
        // Test with a user who only has access to one family
        const limitedUser = { id: 3604, first_name: 'Midders', last_name: 'M' }; // Only in group 1002

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventFixtures.event_with_users.publicGet.data.events[0]}
                user={limitedUser}
            />
        );

        // Should only show members from group 1002, not from group 373
        cy.get('[data-cy="family-member-name-role"]')
            .should('not.contain', 'Joe Gero II') // From group 373
            .should('not.contain', 'Victoria Gero'); // From group 373

        // Should show members from group 1002
        cy.get('[data-cy="family-member-name-role"]')
            .should('contain', 'Midders M'); // The logged-in user
    });

    it('should handle duplicate users across multiple families correctly', () => {
        // Test duplicate users across multiple families

        const user = { id: 3075, first_name: 'Midnight', last_name: 'Boo' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventFixtures.event_with_users.publicGet.data.events[0]}
                user={user}
            />
        );

        // Count occurrences of users that appear in both families
        cy.get('[data-cy="family-member-name-role"]').then($elements => {
            const names = Array.from($elements).map(el => el.textContent.split('(')[0].trim());
            const duplicateNames = names.filter((name, index) => names.indexOf(name) !== index);

            // Should not have any duplicate names
            expect(duplicateNames).to.have.length(0);
        });
    });

    it('should not show add family member button for non-admin users', () => {
        const nonAdminUser = { id: 3075, first_name: 'Midnight', last_name: 'Boo' }; // Guardian, not admin

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventFixtures.event_with_users.publicGet.data.events[0]}
                user={nonAdminUser}
            />
        );

        // Should not show add new family member button since user is not admin in any family
        cy.get('[data-cy="add-new-family-member-btn"]').should('not.exist');
    });

    it('should handle custom field interactions correctly', () => {
        const eventWithCustomFields = {
            ...eventFixtures.event_with_users.publicGet.data.events[0],
            advancedDetails: {
                users: [],
                custom_fields: [
                    {
                        id: 1,
                        name: 'emergency_contact',
                        placeholder_text: 'Emergency Contact',
                        custom_field_type: 'input',
                        required: true
                    },
                    {
                        id: 2,
                        name: 'shirt_size',
                        placeholder_text: 'Shirt Size',
                        custom_field_type: 'select',
                        required: false,
                        options: [
                            { value: 'S', text: 'Small' },
                            { value: 'M', text: 'Medium' },
                            { value: 'L', text: 'Large' }
                        ]
                    }
                ]
            }
        };

        const user = { id: 3075, first_name: 'Midnight', last_name: 'Boo' };

        cy.mount(
            <EventFamilyTestWrapper
                eventDetails={eventWithCustomFields}
                user={user}
            />
        );

        // Select a family member
        cy.get('[data-cy="family-member-checkbox"]').first().click();

        // Fill out custom fields
        cy.get('[data-cy="event-custom-field-input"]')
            .type('John Doe - 555-1234');

        cy.get('[data-cy="event-custom-field-select"]')
            .select('Medium');

        // Verify fields can be interacted with
        cy.get('[data-cy="event-custom-field-input"]')
            .should('have.value', 'John Doe - 555-1234');

        cy.get('[data-cy="event-custom-field-select"]')
            .should('have.value', 'M');

        // Unselect the user and verify fields disappear
        cy.get('[data-cy="family-member-checkbox"]').first().click();
        cy.get('[data-cy="event-custom-fields-wrapper"]').should('not.exist');
    });
});
