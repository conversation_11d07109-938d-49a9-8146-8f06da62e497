import React, {useState, useEffect, useCallback} from 'react';
import { Form, Table, OverlayTrigger, Tooltip, ToggleButtonGroup, ToggleButton } from 'react-bootstrap';
import Stack from '../../../../components/common/Stack';

const BorderRadius = React.forwardRef((props, _) => {
    const {borderRadius, selection} = props;

    const [borderUnit, setBorderUnit] = useState("px");
    const [borderRadiusTop, setBorderRadiusTop] = useState("");
    const [borderRadiusRight, setBorderRadiusRight] = useState("");
    const [borderRadiusBottom, setBorderRadiusBottom] = useState("");
    const [borderRadiusLeft, setBorderRadiusLeft] = useState("");

    const clickHandler = useCallback((borderUnit, borderRadiusTop, borderRadiusRight, borderRadiusBottom, borderRadiusLeft) => {
        selection(`${borderRadiusTop || 0}${borderUnit} ${borderRadiusRight || 0}${borderUnit} ${borderRadiusBottom || 0}${borderUnit} ${borderRadiusLeft || 0}${borderUnit}`);
    },[selection]);

    useEffect(() => {
        if (borderRadius){
            const [top, right, bottom, left] = borderRadius.split(" ");
            const unit = top.replace(/[0-9]/g, '');
            setBorderUnit(unit);
            setBorderRadiusTop(top.replace(unit, ''));
            setBorderRadiusRight(right.replace(unit, ''));
            setBorderRadiusBottom(bottom.replace(unit, ''));
            setBorderRadiusLeft(left.replace(unit, ''));
        }
    }, [borderRadius]);

    useEffect(() => {
        return () => {
            setBorderRadiusTop("");
            setBorderRadiusRight("");
            setBorderRadiusBottom("");
            setBorderRadiusLeft("");
            setBorderUnit("px");
        }
    }, []);

    return (
        <Table className="mb-0">
            <tbody>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>Sets the border radius for each side of the element</Tooltip>}><span>Radius</span></OverlayTrigger>
                    </td>
                    <td>
                        <ToggleButtonGroup type="radio" size="sm" name="borderUnit" value={borderUnit} style={{marginLeft:"auto"}} className="w-100 justify-content-end" onChange={(val)=>{
                            setBorderUnit(val);
                            clickHandler(val, borderRadiusTop, borderRadiusRight, borderRadiusBottom, borderRadiusLeft);
                        }}>
                            <ToggleButton variant="link" size="sm" value="px">px</ToggleButton>
                            <ToggleButton variant="link" size="sm" value="em">em</ToggleButton>
                            <ToggleButton variant="link" size="sm" value="rem">rem</ToggleButton>
                            <ToggleButton variant="link" size="sm" value="%">%</ToggleButton>
                        </ToggleButtonGroup>
                        <Stack direction="horizontal" gap={0}>
                            <Form.Group controlId="border-top-width" className="text-center">
                                <Form.Control type="number" placeholder="0" min="0" max="100" value={borderRadiusTop || ""} onChange={(e)=>{
                                    setBorderRadiusTop(e.target.value);
                                    clickHandler(borderUnit, e.target.value, borderRadiusRight, borderRadiusBottom, borderRadiusLeft);
                                }}/>
                                <small>TOP</small>
                            </Form.Group>
                            <Form.Group controlId="border-right-width" className="text-center">
                                <Form.Control type="number" placeholder="0" min="0" max="100" value={borderRadiusRight || ""} onChange={(e)=>{
                                    setBorderRadiusRight(e.target.value);
                                    clickHandler(borderUnit, borderRadiusTop, e.target.value, borderRadiusBottom, borderRadiusLeft);
                                }}/>
                                <small>RIGHT</small>
                            </Form.Group>
                            <Form.Group controlId="border-bottom-width" className="text-center">
                                <Form.Control type="number" placeholder="0" min="0" max="100" value={borderRadiusBottom || ""} onChange={(e)=>{
                                    setBorderRadiusBottom(e.target.value);
                                    clickHandler(borderUnit, borderRadiusTop, borderRadiusRight, e.target.value, borderRadiusLeft);
                                }}/>
                                <small>BOTTOM</small>
                            </Form.Group>
                            <Form.Group controlId="border-left-width" className="text-center">
                                <Form.Control type="number" placeholder="0" min="0" max="100" value={borderRadiusLeft || ""} onChange={(e)=>{
                                    setBorderRadiusLeft(e.target.value);
                                    clickHandler(borderUnit, borderRadiusTop, borderRadiusRight, borderRadiusBottom, e.target.value);
                                }}/>
                                <small>LEFT</small>
                            </Form.Group>
                        </Stack>
                    </td>
                </tr>
            </tbody>
        </Table>
    );
});

export default BorderRadius;