import React from 'react'

const ViewConfigTypes = ({configTypes, handleDelete=null, handleView=null, handleEdit=null, ...props}) => {

    return (
        <div className="view-config-table-wrapper">
            <table>
                <thead>
                    <tr>
                        <th>
                            Id
                        </th>
                        <th>
                            Name
                        </th>
                        <th>
                            Active
                        </th>
                        <th>
                            Sort Order
                        </th>
                        {handleView &&    
                            <th>
                                View
                            </th>
                        }
                        {handleEdit &&
                            <th>
                                Edit
                            </th>   
                        }
                        {handleDelete &&
                            <th>
                                Delete
                            </th>
                        }
                    </tr>
                </thead>
                <tbody>
                    {configTypes?.map((config)=>(
                        <tr key={config.id}>
                            <td>
                                {config.id}
                            </td>
                            <td>
                                {config.name || config.key}
                            </td>
                            <td>
                                {config.is_active===1 ? "Yes" : "No"} 
                            </td>
                            <td>
                                {config.sort_order}
                            </td>
                            {handleView &&
                                <td onClick={()=>handleView(config)}>
                                    <i className="far fa-eye" />
                                </td>
                            }
                            {handleEdit &&
                                <td onClick={()=>handleEdit(config)}>
                                    <i className="far fa-edit" />
                                </td>
                            }
                            {handleDelete &&
                                <td onClick={()=>handleDelete(config)}>
                                    <i className="far fa-trash-alt" />
                                </td>
                            }
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    )
}

export default ViewConfigTypes