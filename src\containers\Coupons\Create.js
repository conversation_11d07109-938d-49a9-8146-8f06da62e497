import React, { useState, useEffect, useCallback, useMemo, Suspense} from 'react';
import { useHistory, usePara<PERSON>, Link } from "react-router-dom";
import { useDispatch, useSelector } from 'react-redux';
import Container from 'react-bootstrap/Container';
import { Col, Row, Button, Breadcrumb, Card, Form } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { format, formatISO, isBefore } from 'date-fns'
import SubHeader from '../../components/common/SubHeader';
import * as actions from '../../store/actions';
import Coupons from '../../api/Coupons';
import ErrorCatcher from '../../components/common/ErrorCatcher';
import Toast from '../../components/Toast';
import MultiStep from '../../components/common/MultiStep';
import Name from './Name';
import Auto from './Auto';
import MaxUses from './MaxUses';
import Dates from './Dates';
import Type from './Type';
import ApplyTo from './ApplyTo';
import Combo from './Combo';
import Conditions from './Conditions';
import Summary from './Summary';
import Status from './Status';

import Products from '../../api/Products';
import Groups from '../../api/Groups';
import Events from '../../api/Events';

import './Coupon.scss';

let defaultCoupon = {
    id: null,
    name: "",
    description: "",
    auto_apply: -1,
    coupon_code: "",
    coupon_code_valid: false,
    unlimited: -1,
    max_uses: "",
    discount_type: -1,
    discount_amount: 0,
    no_end_date: 0,
    apply_to_all: -1,
    combinable: -1,
    valid_from: formatISO(new Date()),
    valid_until: formatISO(new Date()),
};

let params = {
    categories: {
        id: 'categories',
        name: 'Categories',
        method: Products.Categories.get,
        isPost: false,
        default: [],
        description: "Discount will be applied only to products in the Categories selected. If multiple Categories are selected the product need belong to only one of them to get the discount."
    },
    product_types: {
        id: 'product_types',
        name: 'Product Types',
        method: Products.Types.get,
        isPost: false,
        default: [],
        description: "Discount will be applied only to products with the selected Product Type. If multiple Product Types are selected the product need belong to only one of them to get the discount."
    },
    products: {
        id: 'products',
        name: 'Products',
        method: Products.get,
        isPost: true,
        default: [],
        description: "Discount will be applied to the selected products."
    },
    product_min_qty: {
        id: 'product_min_qty',
        name: 'Product Min Quantity',
        default: "",
        description: "Products will have the discount applied only if this quantity or more is in the cart."
    },
    product_max_qty: {
        id: 'product_max_qty',
        name: 'Product Max Quantity',
        default: "",
        description: "Products will have the discount applied only if this quantity or less is in the cart."
    },
    product_combo: {
        id: 'product_combo',
        name: 'Product Combo',
        method: Products.get,
        isPost: true,
        default: [],
        description: "Discount will be applied to the selected products only if ALL of these products are in the cart."
    },
    user_age_min: {
        id: 'user_age_min',
        name: 'User Age Min',
        default: "",
        description: "Discount will be applied if the user is this age or older."
    },
    user_age_max: {
        id: 'user_age_max',
        name: 'User Age Max',
        default: "",
        description: "Discount will be applied if the user is this age or younger."
    },
    groups: {
        id: 'groups',
        name: 'Groups',
        method: Groups.groupFilter,
        isPost: true,
        default: [],
        description: "Discount will be applied if the user belongs to this Group with status CONFIRMED."
    },
    events: {
        id: 'events',
        name: 'Events',
        method: Events.getDetail,
        isPost: true,
        default: [],
        description: "Discount will be applied if the user is registered for this Event with status ATTENDING."
    },
    active_subscriptions: {
        id: 'active_subscriptions',
        name: 'Active Subscription',
        default: 0,
        description: "Discount will be applied if the user has any active subscription."
    },
    current_subscriptions: {
        id: 'current_subscriptions',
        name: 'Current Subscription',
        method: Products.getSubscriptions,
        isPost: false,
        default: [],
        description: "Discount will be applied if the user has one of the selected subscriptions in active status."
    },
    expired_subscriptions: {
        id: 'expired_subscriptions',
        name: 'Expired Subscription',
        method: Products.getSubscriptions,
        isPost: false,
        default: [],
        description: "Discount will be applied if the user has one of the selected subscriptions in expired status."
    },
    min_cart_total: {
        id: 'min_cart_total',
        name: 'Minimum Cart Total',
        default: "0.00", // creates a float instead of an int
        description: "Discount will be applied if cart total is equal to or above this amount."
    },
}

const Create = (props) => {
    let history = useHistory();
    const dispatch = useDispatch();
    const { id } = useParams();

    const coupon = useSelector(state => state.coupon.current);
    const errors = useSelector(state => state.coupon.errors);
    const selectedConditions = useSelector(state => state.coupon.conditions);
    const paramData = useSelector(state => state.coupon.param_init_data);
    const user = useSelector(state => state.auth.user);

    const [loading,setLoading]=useState(true);
    const [pagePart, setPagePart] = useState();
    const [submitting, setSubmitting] = useState(false);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [validated, setValidated] = useState();

    function isNumeric(n, min=0) {
        return !isNaN(parseFloat(n)) && isFinite(n) && n>=min;
    }

    const showErrors = useCallback(keys => {
        return (
            <div className="err">
                {keys.forEach(key => {
                    return errors[key] ? errors[key] : "";
                })}
            </div>
        );
    }, [errors]);

    // update all inputs
    const onChangeInput = useCallback(event => {
        dispatch(actions.setErrors({[event.target.name]: false}));
        let value = event.target.value;
        if (event.target.type==="radio") {
            value = parseInt(value);
        } else if (event.target.type==="checkbox") {
            value = event.target.checked ? 1 : 0;
        } else if (event.target.type==="calendar") {
            //
        } else if (event.target.type==="text") {
            event.preventDefault(); // hitting enter key while in textbox should not reload page
        }
        // truncate field lengths
        if (event.target.name==="name" && value.length>45) {
            value = value.slice(0,45);
        }
        if (event.target.name==="coupon_code" && value.length>45) {
            value = value.slice(0,45);
        }
        // Condition params
        dispatch(actions.selectedCoupon({[event.target.name]: value}));
    }, [dispatch]);

    // update conditions inputs
    const onChangeInputCondition = useCallback((name, value) => {
        dispatch(actions.setErrors({[name]: false}));
        dispatch(actions.setParams({ [name]: value }));
    }, [dispatch]);

    const onEnterCode = useCallback(async (event) => {
        //check that the coupon code is valid
        let obj = { 'coupon_code': event.target.value };

        let response=await Coupons.checkName(obj).catch(e => console.error(e));
        try {
            if (response && !response?.errors) {
                dispatch(actions.selectedCoupon({'coupon_code_valid': true}));
            } else { // api returned errors
                dispatch(actions.selectedCoupon({'coupon_code_valid': false}));
                if(Array.isArray(response.errors)) {
                    dispatch(actions.setErrors({'coupon_code': response.errors}));
                } else {
                    setError(<ErrorCatcher error={response.errors} />);
                }
            } 
        }
        catch(e) { //no response at all
            setError(<ErrorCatcher error={e} />);
        }
    }, [dispatch]);

    // The steps are shown in order listed
    const steps = useMemo(()=>[
        {name: 'Name', component: <Name onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Auto', component: <Auto onChangeInput={onChangeInput} showErrors={showErrors} onEnterCode={onEnterCode} />},
        {name: 'Max', component: <MaxUses onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Dates', component: <Dates onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Type', component: <Type onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Apply To', component: <ApplyTo onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Combo', component: <Combo onChangeInput={onChangeInput} showErrors={showErrors} />},
        {name: 'Conditions', component: <Conditions onChangeInput={onChangeInputCondition} showErrors={showErrors} />},
    ], [onChangeInput, onChangeInputCondition, showErrors, onEnterCode]);

    const onChangeStep = useCallback(pageName => {
        // do validation stuff on the form data for the current step
        let err = {};
        let savedata = {};

        switch(pageName) {
            case "Name":
                if (!coupon.name || coupon.name==="") {
                   err = {...err, ...{name: "Name is required."}};
                }
                break;
            case "Auto":
                if (coupon.auto_apply!==1 && coupon.auto_apply!==0) {
                    err = {...err, ...{auto_apply: "Auto Apply is required, please select one."}};
                } else if (coupon.auto_apply!==1 && coupon.coupon_code==="") {
                    err = {...err, ...{coupon_code: "Coupon Code is required, please enter one or select Auto Apply."}};
                }
                break;
            case "Max":
                if (coupon.unlimited!==1 && coupon.unlimited!==0) {
                    err = {...err, ...{unlimited: "Unlimited is required, please select one."}};
                } else if (coupon.unlimited===0 && coupon.max_uses==="") {
                    err = {...err, ...{max_uses: "Max number of uses is required, please enter a number or select Unlimited."}};
                } else if (coupon.unlimited===0 && !isNumeric(coupon.max_uses,0.01)) {
                    err = {...err, ...{max_uses: "Max number of uses must be a whole number larger than 0."}};
                } else {
                    // if no errors, format the number string to whole integer, no decimals
                    dispatch(actions.selectedCoupon({max_uses: "" + Math.trunc(parseFloat(coupon.max_uses)) }));
                }
                break;
            case "Dates":
                // Dates are limited by the datepicker and validated when selected
                break;
            case "Type":
                if (coupon.discount_type!==1 && coupon.discount_type!==0) {
                    err = {...err, ...{discount_type: "Type is required, please select one."}};
                } else if (coupon.discount_amount==="") {
                    err = {...err, ...{discount_amount: "Amount is required, please enter a number."}};
                } else if (!isNumeric(coupon.discount_amount, 0.01)) {
                    err = {...err, ...{discount_amount: "Amount must be a number greater than 0."}};
                } else {
                    // if no errors, format the number string to 2 decimals
                    dispatch(actions.selectedCoupon({discount_amount: "" + Math.round(parseFloat(coupon.discount_amount) * 1e2) / 1e2}));
                }
                break;
            case "Apply To":
                if (coupon.apply_to_all!==1 && coupon.apply_to_all!==0) {
                    err = {...err, ...{apply_to_all: "Apply To is required, please select one."}};
                }
                break;
            case "Combo":
                if (coupon.combinable!==1 && coupon.combinable!==0) {
                    err = {...err, ...{combinable: "Combinable is required, please select one."}};
                }
                break;
            case "Conditions":
                if (selectedConditions.includes('product_min_qty') && !isNumeric(coupon.params.product_min_qty, 0.01)) {
                    err = {...err,
                        product_min_qty: "Product Min must be a number greater than 0."
                    };
                }
                if (selectedConditions.includes('product_max_qty') && !isNumeric(coupon.params.product_max_qty, 0.01)) {
                    err = {...err,
                        product_max_qty: "Product Max must be a number greater than 0."
                    };
                }
                if ( (selectedConditions.includes('product_min_qty')
                        && selectedConditions.includes('product_max_qty'))
                        && (parseInt(coupon.params.product_min_qty) > parseInt(coupon.params.product_max_qty)) ) {
                    err = {...err,
                        product_max_qty: "Product Max must be greater than Product Min."
                    };
                }
                if (selectedConditions.includes('min_cart_total') && !isNumeric(coupon.params.min_cart_total, 0.01)) {
                    err = {...err,
                        min_cart_total: "Min Cart Total must be a number greater than 0."
                    };
                }
                break;
            default:
                break;
        }
        if(Object.keys(err).length>0) { // fails validation
            dispatch(actions.addToErrors(err));
            return false;
        } else {
            dispatch(actions.selectedCoupon(savedata));
        }
        return true;
    }, [coupon, dispatch, selectedConditions]);

    const onEnterKey = (e) => {
        e.preventDefault();
        document.activeElement.blur();
    }

    const onSubmit = useCallback(async () => {
        // check all form validation
        let validated = true;
        steps.forEach(step => {
            if (!onChangeStep(step.name)) validated = false;
        });

        if(validated && (onChangeStep("all") || coupon.id)) {
            const formDataObj = coupon;

            if (coupon.id) formDataObj.id = parseInt(coupon.id);
            formDataObj.company_id = user.company_id;

            // convert text inputs to int
            formDataObj.discount_amount = parseFloat(formDataObj.discount_amount);
            formDataObj.max_uses = parseInt(formDataObj.max_uses) || null;

            // some variables have a set value depending on another variable
            formDataObj.coupon_code = (formDataObj.auto_apply) ? null : formDataObj.coupon_code;
            if (formDataObj.unlimited) {
                formDataObj.max_uses = 0;
            }
            if (formDataObj.no_end_date) {
                formDataObj.valid_until = null;
            }
            delete formDataObj.unlimited;
            delete formDataObj.no_end_date;

            // process each param to convert array of objects to array of ids, strings to ints, and remove empty values
            Object.keys(coupon.params).forEach(conditionName => {
                let value = coupon.params[conditionName];
                if (Array.isArray(value) && value.length>0) {
                    value = value.map(item => item.id);
                } else if('default' in paramData[conditionName] && paramData[conditionName].default === "0.00") {
                    value = Number(value).toFixed(2) || null;
                } else {
                    value = parseInt(value) || null;
                }
                if (value===null || value===[]) {
                    delete formDataObj.params[conditionName];
                } else {
                    formDataObj.params = {
                        ...formDataObj.params,
                        [conditionName]: value
                    };
                }
            });
            formDataObj.params = JSON.stringify(formDataObj.params);

            let response;
            if (coupon.id) response=await Coupons.update(formDataObj).catch(e => console.error(e));
            else response=await Coupons.create(formDataObj).catch(e => console.error(e));

            try {
                if (response && !response?.errors) {
                    setSubmitting(false);
                    setValidated(false);
                    setSuccess(<Toast>Discount created and saved successfully!</Toast>);
                    const timer = setTimeout(() => {
                        history.push(props.referer || "/p/discount/dashboard"); // pushes to dashboard to avoid resubmission
                    }, 3000);
                } else { // api returned errors
                    setSubmitting(false);
                    setError(<ErrorCatcher error={response.errors} />);
                } 
            }
            catch(e) { //no response at all
                setSubmitting(false);
                setError(<ErrorCatcher error={e} />);
            }
        }
    }, [coupon, history, onChangeStep, paramData, props.referer, steps, user.company_id]);


    const stepsCreate = useMemo(()=>[...steps,
        {name: 'Summary', component: <Summary />},
    ], [steps]);

    const stepsEdit = useMemo(()=>[...steps,
        {name: 'Status', component: <Status onChangeInput={onChangeInput} showErrors={showErrors} />},
    ], [steps, onChangeInput, showErrors]);

    //   On page load
    useEffect(() => {
        let mounted = true;
        setLoading(true);

        // set all defaults
        dispatch(actions.selectedCoupon(defaultCoupon));
        dispatch(actions.setParamInitData(params));
        dispatch(actions.setConditions( [] ));

        if(id) {
            Coupons.get({id: id})
            .then(response => {
                if(mounted && response.data[0]) {
                    let cpn = response.data[0];
                    // coupon code
                    cpn.coupon_code = cpn.coupon_code ? cpn.coupon_code : "";
                    //params
                    cpn.params = JSON.parse(cpn.params) || {};
                    //unlimited
                    cpn.unlimited = cpn.max_uses===0 ? 1 : 0;
                    cpn.max_uses = cpn.max_uses===0 ? "" : cpn.max_uses;
                    //no_end_date
                    cpn.no_end_date = cpn.valid_until===null ? 1 : 0;
                    cpn.valid_until = cpn.valid_until===null ? cpn.valid_from : cpn.valid_until;
                    dispatch(actions.selectedCoupon(cpn));
                }
                setLoading(false);
            }).catch(e => console.error(e));
        } else {

            let paramDefaults = {};
            Object.keys(paramData).forEach(conditionName => {
                paramDefaults = {
                    ...paramDefaults,
                    [conditionName]: paramData[conditionName].default
                };
            });
            dispatch(actions.selectedCoupon({ params: paramDefaults }));
        }
        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
        }        
    },[props, id, dispatch, paramData]);

    useEffect(() => {
        // check to make sure end date is after start date
        if (isBefore(new Date(coupon.valid_until), new Date(coupon.valid_from))) {
            dispatch(actions.selectedCoupon({valid_until: coupon.valid_from}));
        }

        if (id) {
            setPagePart(
                <Suspense fallback={             
                    <SkeletonTheme color="#e0e0e0">
                        <Skeleton height={30} style={{marginBottom:"1rem"}} />
                        <Skeleton height={12} count={5} />
                    </SkeletonTheme>
                }>
                    {stepsEdit.map(step => (
                        React.cloneElement(step.component, {key: `component-${step.name}`})
                    ))}
                    
                    <Row className="button-row">
                        <Button variant="secondary" onClick={() => history.push('/p/discount/dashboard')}>Cancel</Button>
                        <Button variant="primary" onClick={onSubmit}>Save Changes</Button>
                    </Row>
                </Suspense>
            );
        } else {
            setPagePart(
                <MultiStep showNavigation={true} steps={stepsCreate} onChangeStep={onChangeStep} onSubmit={onSubmit} />
            );
        }
    },[coupon, dispatch, history, id, stepsCreate, stepsEdit, onChangeStep, onSubmit]);

    return (
        <Container fluid className={`coupon-creator {id ? "edit-coupon" : "create-coupon"}`}>
            {success}
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/discount/dashboard" }, text: "Discount Dashboard" },
                { text: coupon.id ? "Update Discount" : "Create Discount" }
            ]} />
            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <h4 className="section-title">{coupon.id ? "Update a " : "Create a "}Discount</h4>
                        <hr/>
                        <Card.Body>
                            <Form onSubmit={onEnterKey}>
                                {pagePart}
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
            {error}
        </Container>
    );
}

export default Create;