@import '../../assets/css/scss/variables';
@import '../../assets/css/scss/themes.scss';



.wizard_container{
    display:flex;
    flex-direction: column;
    min-height:100%;
}

.wizard_container .accordion .card-header{
    cursor:pointer;
}

.wizard_container .accordion .card-header:hover{
    color:#3d5afe;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}


.wizard_container > .row:last-of-type{
    margin-top:auto;
}

.wizard_container > .row:last-of-type .btn{
    font-size:.8rem;
    font-weight:600;
    padding:1rem 1.5rem;
    border-radius: 4px;
}

.wizard_container > .row:last-of-type .btn.btn-light{
    background-color:transparent;
    border:0;
}

.wizard_container > .row:last-of-type .btn.btn-save{
    width:110px;
}

.wizard_container h1{
    font-size:1.4rem;
    font-weight:500;
    margin-bottom:1.2rem;
}

.wizard_container p,
.wizard_container ul > li
{
    font-size:.85rem;
    color:#616161;
}

.wizard_container ul{
    list-style: inside;
    list-style-type: circle;
}

.wizard_container b{
    font-weight: 500;
}

.wizard_container label{
    font-size:.75rem;
    color:#757575;
    display:block;
    margin-top:.5rem;
    margin-bottom:0;
}

.wizard_container .summary_item{
    margin-bottom:1.5rem;
}

.wizard_container .summary_item:not(:first-child){
    border-top:1px solid #eeeeee;
    padding-top:1.5rem;
}