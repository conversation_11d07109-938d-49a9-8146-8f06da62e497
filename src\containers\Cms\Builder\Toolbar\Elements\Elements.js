import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { Card, Accordion } from 'react-bootstrap';
import { cmsComponentList, toKebabCase } from '../../../../../utils/cms';

import ContentBlocks from './ContentBlocks';

import blocks from '../../blocks';
import {default as _cssProperties, htmlProperties as _htmlProperties, logicProperties as _logicProperties} from '../../cssProperties';

export const Elements = (props) => {
    const cmsSelector = useSelector(state => state.cms);
    const [activeKey, setActiveKey] = useState('0');

    const componentList = useMemo(() => {
        let cmp=[];
        if (props.data) cmp=[...props.data];    
        let list=[...cmsComponentList(),...blocks];
        list.forEach((component,i) => {
            if (!component?.hidden){
                let props=component?.props?.map((a,j)=>{
                    let aid = a.id || 600+i+j;
                    return {...a,...{id:aid,changed:true}}
                }) || [];
                if (component.apply_styles) props=[...props,...JSON.parse(JSON.stringify(_cssProperties))];
                props=[...props,...JSON.parse(JSON.stringify(_logicProperties))];
                cmp.push({
                    ...component,
                    id: `${component.id || component.name}`,
                    class: toKebabCase(component.component_name),
                    properties: props
                });
            }
        });
        return cmp;
    }, [props.data]);

    const isForm = useMemo(() => {
        return cmsSelector.currentPageProps?.config?.is_form===1;
    }, [cmsSelector.currentPageProps?.config?.is_form]);

    const isWizard = useMemo(() => {
        return cmsSelector.currentPageProps?.config?.is_wizard===1;
    }, [cmsSelector.currentPageProps?.config?.is_wizard]);


    useEffect(() => {
        return() => {
            setActiveKey('0');
        }
    },[]);

    return (
        <Accordion defaultActiveKey={activeKey}>
            <div>
                <Accordion.Toggle as={Card.Header} eventKey="0">Elements</Accordion.Toggle>
                <Accordion.Collapse eventKey="0">
                    <ContentBlocks list="Elements" {...{...props,...{data: componentList.filter(a=>a && a.list==="Elements")}}} />
                </Accordion.Collapse>
            </div>
            <div>
                <Accordion.Toggle as={Card.Header} eventKey="1">Content Blocks</Accordion.Toggle>
                <Accordion.Collapse eventKey="1">
                    <ContentBlocks list="ContentBlocks" {...{...props,...{data: componentList.filter(a=>a && a.list==="ContentBlocks")}}} page_type={2} />
                </Accordion.Collapse>
            </div>
            <div>
                <Accordion.Toggle as={Card.Header} eventKey="2">Forms</Accordion.Toggle>
                <Accordion.Collapse eventKey="2">
                    <>
                        {isForm && <ContentBlocks list="Forms" {...{...props,...{data: componentList.filter(a=>a && a.list==="Forms")}}} /> /* if the page is a form only show widgets that can be dropped onto a form */} 
                        {!isForm && <ContentBlocks list="FormBlocks" {...{...props,...{data: componentList.filter(a=>a && a.list==="FormBlocks")}}} page_type={10} />}
                    </>
                </Accordion.Collapse>
            </div>
            <div>
                <Accordion.Toggle as={Card.Header} eventKey="3">Wizards</Accordion.Toggle>
                <Accordion.Collapse eventKey="3">
                    <>
                        {isWizard && <ContentBlocks list="Wizards" {...{...props,...{data: componentList.filter(a=>a && a.list==="Wizards")}}} /> /* if the page is a wizard only show widgets that can be dropped onto a wizard */} 
                        {!isWizard && <ContentBlocks list="WizardBlocks" {...{...props,...{data: componentList.filter(a=>a && a.list==="WizardBlocks")}}} page_type={12} />}
                    </>
                </Accordion.Collapse>
            </div>
            <div>
                <Accordion.Toggle as={Card.Header} eventKey="4">Widgets</Accordion.Toggle>
                <Accordion.Collapse eventKey="4">
                    <ContentBlocks list="Components" {...{...props,...{data: componentList.filter(a=>a && a.list==="Components")}}} />
                </Accordion.Collapse>
            </div>
        </Accordion>
    );
}