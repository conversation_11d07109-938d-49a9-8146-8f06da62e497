import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import Coupons from '../../api/Coupons';
import * as actions from '../../store/actions';

import './Coupon.scss';

const Auto = ({ onChangeInput=()=>{}, showErrors=()=>{}, onEnterCode=()=>{} }) => {

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Will this be an automatically applied coupon or does it require a coupon code?</span>
                <Row>
                    <Col className="col-sm-auto form-row">
                        <Form.Check 
                            type="radio"
                            id="auto_apply-1"
                            label="Auto Apply"
                            name="auto_apply"
                            value={1}
                            checked={coupon.auto_apply===1}
                            onChange={onChangeInput}
                            isInvalid={!!errors.auto_apply}
                            className="form-radio"
                        />
                        <Form.Check 
                            type="radio"
                            id="auto_apply-0"
                            label="Use a Coupon Code"
                            name="auto_apply"
                            value={0}
                            checked={coupon.auto_apply===0}
                            onChange={onChangeInput}
                            isInvalid={!!errors.auto_apply}
                            className="form-radio"
                        />
                    </Col>
                    {coupon.auto_apply===0 &&
                        <>
                        <Col className="col-sm-auto">
                            <i className="far fa-arrow-right mt-4"/>
                        </Col>
                        <Col>
                            <Form.Label>Coupon Code</Form.Label>
                            <Form.Control
                                type="text"
                                id="coupon_code"
                                name="coupon_code"
                                value={coupon.coupon_code}
                                onChange={onChangeInput}
                                isInvalid={!!errors.coupon_code}
                                isValid={coupon.coupon_code_valid}
                                onBlur={onEnterCode}
                                style={{width: "250px"}}
                            />
                        </Col>
                        </>
                    }
                </Row>
                <div className={`err ${!!errors.auto_apply || !!errors.coupon_code ? "" : "hidden"}`}>
                    {errors.auto_apply}
                    {errors.coupon_code}
                </div>
            </Col>
        </Row>
    );
}

export default Auto;