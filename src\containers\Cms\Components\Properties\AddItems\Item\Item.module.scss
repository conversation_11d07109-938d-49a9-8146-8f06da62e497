@import '../../../../../../assets/css/scss/themes.scss';
$toolbar-width: 425px;

.item-group,
.item-hierarchy{
    display: flex;
    flex-direction: column;
    border: 1px dashed $disabled-color;
    padding: 0.5rem;
    margin: 0;
    margin-bottom: 0.25rem;
    border-radius: 0;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease-in-out;
}

.dropbox{
    width:100%;
    position:absolute;
    left:0;
    bottom:0;
}

.can-drop{
    height: 2rem;
    background-color: #2196f3;
}


.item-hierarchy {
    margin: 0.5rem;
    position: relative;
    padding: 0;

    &::before{
        content: '';
        position: absolute;
        top: -0.75rem;
        left: 1rem;
        width: 2px;
        height: 0.75rem;
        border-left: 1px dashed $disabled-color;
        z-index: 9998;
    }


}


.item-container{
    transition: all 0.2s ease-in-out;
    position: relative;

    div.item{
        display:flex;
        flex-direction: column;
        min-height: 1rem;
        border: 2px solid transparent;
        top: 0;
        position: relative;
        justify-content: center;
        padding: 0.5em;

        .item-properties{
            display: flex;
            flex-direction: column;
            margin-top: 0.5rem;
            padding: 0.5rem;
        
            > div {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.15rem;

                input:not([type='checkbox']), select {
                    width: calc($toolbar-width - 190px);
                }
            }
        }
    }

    &:hover{
        .item-overlay{
            display: flex;
        }
        div.item {
            background-color: #fafafa;
            border-color:#662d91;
            z-index: 9999;
        }
    }
}
