/*eslint-disable*/

/// <reference types="cypress"> 

import React from 'react';
import { EventDetails } from './Test';

describe('EventDetails Component Tests', {scrollBehavior: "center", testIsolation: true}, () => {
    let eventFixtures;

    before(() => {
        cy.fixture('Events/event_registration_conditions.json').then((data) => {
            eventFixtures = data;
        });
    });

    beforeEach(() => {
        // Mock user in localStorage for component mounting
        window.localStorage.setItem('user', JSON.stringify({
            menu: [],
            roles: [],
            profile: { id: 123, first_name: 'Test', last_name: 'User' },
            token: "bearer test-token"
        }));
        cy.viewport(1200, 800);
    });

    it('should render EventDetails with variant event data correctly', () => {
        const variantEvent = eventFixtures.event_with_variants;
        
        // Intercept API calls with fixture data
        cy.intercept('POST', '/api/public/event', variantEvent.publicGet).as('getPublicEvent');
        cy.intercept('GET', '/api/event/7107', variantEvent.getSingle).as('getSingleEvent');

        const mockLoadingStates = { current: { eventDetails: false } };

        const [event, setEvent]=useS
        let eventDetails = null
        
        cy.mount(
            <EventDetails 
            eventId={7107} 
            setEventDetails={(e)=>eventDetails(e)} 
            eventDetails={eventDetails} 
            loadingStates={mockLoadingStates} 
            />
        );
        cy.log(eventDetails)
        
        cy.wait('@getPublicEvent');
        cy.wait('@getSingleEvent');
        
        cy.log(eventDetails)
        // Test event name
        cy.get('[data-cy="register-event-name"]')
            .should('contain', 'Variant Event');

        // Test event status details
        cy.get('[data-cy="event-status-details"]')
            .should('exist');

        // Test image viewer
        cy.get('[data-cy="event-image-viewer"]')
            .should('exist');

        // Test when section
        cy.get('[data-cy="event-details-when"]')
            .should('exist')
            .within(() => {
                cy.get('label').should('contain', 'When');
                cy.get('p').should('exist');
            });

        // Test where section
        cy.get('[data-cy="event-details-where"]')
            .should('exist')
            .within(() => {
                cy.get('label').should('contain', 'Where');
                cy.get('p').should('contain', 'Arcade');
            });

        // Test age requirement section
        cy.get('[data-cy="event-details-age-requirement"]')
            .should('exist')
            .within(() => {
                cy.get('label').should('contain', 'Age Requirement');
                cy.get('[data-cy="event-details-ages"]')
                    .should('contain', 'There is no age requirement');
            });

        // Test registration requirement
        cy.get('[data-cy="event-details-registration"]')
            .should('exist')
            .within(() => {
                cy.get('label').should('contain', 'Requires Registration');
                cy.get('p').should('contain', 'Registration is required for this event');
            });

        // Test description section
        cy.get('[data-cy="event-details-description"]')
            .should('exist')
            .within(() => {
                cy.get('label').should('contain', 'What');
                cy.get('div').should('exist');
            });

        // Test price section
        cy.get('[data-cy="event-details-price"]')
            .should('exist')
            .within(() => {
                cy.get('label').should('contain', 'Price');
                cy.get('p').should('exist');
            });
    });

    it('should render EventDetails with user event data correctly', () => {
        const userEvent = eventFixtures.event_with_users;
        
        // Intercept API calls with fixture data
        cy.intercept('POST', '/api/public/event', userEvent.publicGet).as('getPublicEvent');
        cy.intercept('GET', '/api/event/7047', userEvent.getSingle).as('getSingleEvent');

        const mockLoadingStates = { current: { eventDetails: false } };
        const mockSetEventDetails = cy.stub();

        cy.mount(
            <EventDetails 
                eventId={7047} 
                setEventDetails={mockSetEventDetails} 
                eventDetails={null} 
                loadingStates={mockLoadingStates} 
            />
        );

        cy.wait('@getPublicEvent');
        cy.wait('@getSingleEvent');

        // Test event name for Sky Flyers
        cy.get('[data-cy="register-event-name"]')
            .should('contain', 'Sky Flyers');

        // Test location for Sky Flyers
        cy.get('[data-cy="event-details-where"]')
            .within(() => {
                cy.get('p').should('contain', 'Basketball Court 4');
            });

        // Test age requirements for Sky Flyers (has min and max age)
        cy.get('[data-cy="event-details-age-requirement"]')
            .within(() => {
                cy.get('[data-cy="event-details-ages"]')
                    .should('contain', 'Participant must be between 5 - 10 years old to register');
            });
    });

    it('should handle private event status correctly', () => {
        // Create a modified fixture for private event
        const privateEventData = {
            ...eventFixtures.event_with_variants.publicGet,
            data: {
                ...eventFixtures.event_with_variants.publicGet.data,
                events: [{
                    ...eventFixtures.event_with_variants.publicGet.data.events[0],
                    event_status_id: 5,
                    event_status_name: 'Private',
                    requires_registration: 1
                }]
            }
        };

        cy.intercept('POST', '/api/public/event', privateEventData).as('getPrivateEvent');
        cy.intercept('GET', '/api/event/7107', eventFixtures.event_with_variants.getSingle).as('getSingleEvent');

        const mockLoadingStates = { current: { eventDetails: false } };
        const mockSetEventDetails = cy.stub();

        cy.mount(
            <EventDetails 
                eventId={7107} 
                setEventDetails={mockSetEventDetails} 
                eventDetails={null} 
                loadingStates={mockLoadingStates} 
            />
        );

        cy.wait('@getPrivateEvent');
        cy.wait('@getSingleEvent');

        // Test private event message
        cy.get('[data-cy="event-details-private"]')
            .should('exist')
            .should('contain', 'This is a private event')
            .should('contain', 'can only be registered for via a direct invitation');
    });

    it('should handle events with no registration requirement', () => {
        const noRegEvent = {
            ...eventFixtures.event_with_variants.publicGet,
            data: {
                ...eventFixtures.event_with_variants.publicGet.data,
                events: [{
                    ...eventFixtures.event_with_variants.publicGet.data.events[0],
                    requires_registration: 0
                }]
            }
        };

        cy.intercept('POST', '/api/public/event', noRegEvent).as('getNoRegEvent');
        cy.intercept('GET', '/api/event/7107', eventFixtures.event_with_variants.getSingle).as('getSingleEvent');

        const mockLoadingStates = { current: { eventDetails: false } };
        const mockSetEventDetails = cy.stub();

        cy.mount(
            <EventDetails 
                eventId={7107} 
                setEventDetails={mockSetEventDetails} 
                eventDetails={null} 
                loadingStates={mockLoadingStates} 
            />
        );

        cy.wait('@getNoRegEvent');
        cy.wait('@getSingleEvent');

        // Test no registration required message
        cy.get('[data-cy="event-details-registration"]')
            .within(() => {
                cy.get('p').should('contain', 'Registration is not required for this event');
            });
    });

    it('should handle events with membership requirement', () => {
        const membershipEvent = {
            ...eventFixtures.event_with_variants.publicGet,
            data: {
                ...eventFixtures.event_with_variants.publicGet.data,
                events: [{
                    ...eventFixtures.event_with_variants.publicGet.data.events[0],
                    requires_membership: 1
                }]
            }
        };

        cy.intercept('POST', '/api/public/event', membershipEvent).as('getMembershipEvent');
        cy.intercept('GET', '/api/event/7107', eventFixtures.event_with_variants.getSingle).as('getSingleEvent');

        const mockLoadingStates = { current: { eventDetails: false } };
        const mockSetEventDetails = cy.stub();

        cy.mount(
            <EventDetails 
                eventId={7107} 
                setEventDetails={mockSetEventDetails} 
                eventDetails={null} 
                loadingStates={mockLoadingStates} 
            />
        );

        cy.wait('@getMembershipEvent');
        cy.wait('@getSingleEvent');

        // Test membership requirement section appears
        cy.get('[data-cy="event-details-membership"]')
            .should('exist')
            .within(() => {
                cy.get('label').should('contain', 'Requires Membership');
                cy.get('p').should('contain', 'A membership is required to register for this event');
            });
    });

    it('should display correct price ranges for events with multiple variants', () => {
        const variantEvent = eventFixtures.event_with_variants;
        
        cy.intercept('POST', '/api/public/event', variantEvent.publicGet).as('getPublicEvent');
        cy.intercept('GET', '/api/event/7107', variantEvent.getSingle).as('getSingleEvent');

        const mockLoadingStates = { current: { eventDetails: false } };
        const mockSetEventDetails = cy.stub();

        cy.mount(
            <EventDetails 
                eventId={7107} 
                setEventDetails={mockSetEventDetails} 
                eventDetails={null} 
                loadingStates={mockLoadingStates} 
            />
        );

        cy.wait('@getPublicEvent');
        cy.wait('@getSingleEvent');

        // Test price range for multiple variants (should show "From $X to $Y")
        cy.get('[data-cy="event-details-price"]')
            .within(() => {
                cy.get('p').should('exist');
                // The component should show price range since there are multiple variants
            });
    });
});
