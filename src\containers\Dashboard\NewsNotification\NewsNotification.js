import React from 'react';
// import Row from 'react-bootstrap/Row';
// import Col from 'react-bootstrap/Col';
// import { Button } from 'react-bootstrap';
// import Image from 'react-bootstrap/Image';
import Tabs from './Tabs';

//import { useHistory } from "react-router-dom";


export const NewsNotification = (props) => {
    //let history = useHistory();

    
    return (
        <React.Fragment>
            <div className="">
            <Tabs includes={[
                        {id:1,hash:"news",text:"Impact News",component:"News"},
                        {id:2,hash:"notifications",text:"Notifications",component:"Notifications"}
                    ]} />
            </div>
        </React.Fragment>
    );
}