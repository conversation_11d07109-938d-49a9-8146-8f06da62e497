import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from "react-router-dom";
import {Container,Row,Col,Form,Button} from 'react-bootstrap';
import {confirm} from '../../../components/Confirmation';
import Table from '../../../components/common/Table';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';
import {Typeahead } from 'react-bootstrap-typeahead';
import ReactTooltip from 'react-tooltip';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import '../../../components/common/Table/Table.scss';

import Companies from '../../../api/Companies';
import Users from '../../../api/Users';

export const AssignUsers = (props) => {
    let history = useHistory();

    const ref = React.createRef();    

    const [companyUsers, setCompanyUsers] = useState();
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();
    const [userSelections, setUserSelections] = useState([]);

    useEffect(() => {
        const _getUsers = async () => {
            try {
                const res = await Users.getList();
                if (res.data && mounted) {
                    setCompanyUsers(res.data);
                }
        
                /*
                const res2 = await Companies.getUsers({company_id:props.company_id});
                console.log("res2",res2);
                if (res.data && mounted) {
                    
                    setUserSelections(res2.data);
                }
                */
            } catch (e) {
                console.error(e);
            }
        }
        

        let mounted = true;
        _getUsers();

        return () => {
            mounted = false;
        }
	}, [props.company_id]);


    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            formData.delete("name"); // remove the autocomplete input from the form
            if (props.company_id) formData.append("company_id", props.company_id);

            let users=userSelections.map(user => user.id);
            formData.append("users", users);

            const formDataObj = Object.fromEntries(formData.entries());

            const response=await Companies.addUser({formDataObj})
            
            if (!response.errors) {
                setSubmitting(false);
                setValidated(false);
                setSuccess(<Toast>Users added successfully!</Toast>);
                history.push(props.referer || "/p/companies/dashboard"); // pushes to profile again to avoid resubmission
            } else { // api returned errors
                setSubmitting(false);
                setError(<ErrorCatcher error={response.errors} />);
            } 
        } else setSubmitting(false);
    };


    const confirmHandler = useCallback((props) => {
        confirm(props.text,{
            title:"Whoa!",
            okText:"Yes",
            cancelText:"No",
            cancelButtonStyle:"light"
        }).then(result =>{
            if (result===true) props.click();
        }).catch(e => console.error(e));
    },[]);

    const columns = React.useMemo(
        () => [{
            id: 'table',
            columns: [
                {
                    Header: 'Full Name',
                    id: 'first_name',
                    className: "align-middle",
                    accessor: 'first_name',
                    Cell: d => d.row.original.first_name+" "+d.row.original.last_name
                },
                {
                    id: 'last_name',
                    show: false,
                    accessor: 'last_name'
                },
                {
                    Header: 'Role',
                    id: 'role_name',
                    accessor: 'role_name',
                    className: "align-middle",
                },
                {
                    Header: '',
                    id: 'action_buttons',
                    className: "align-middle",
                    disableSortBy: true,
                    accessor: d => "",
                    Cell: props => (
                        <a href="#!" onClick={() => {
                            confirmHandler({
                                text:`Are you sure you want to remove ${props.row.original.first_name} from the group?`,
                                click:()=>props.typeAheadOnRemove(props.row.original)
                            });
                        }}>
                            <i className="far fa-times" data-tip="Remove User"></i>
                        </a> 
                    )                     
                },
            ],
        }],[confirmHandler]
    );
    

    const renderInput = ({ inputClassName, inputRef, referenceElementRef, ...props },{ onRemove, selected }) => (
        userSelections &&
        <React.Fragment>
            <input
                {...props}
                className="form-control"
                ref={input => {
                    referenceElementRef(input);
                    inputRef(input);
                }}
                type="text"
            />
            <Table columns={columns} data={userSelections || []} searchBar={false} typeAheadOnRemove={onRemove} className="mt-3" />
            <ReactTooltip globalEventOff="click" effect="solid" backgroundColor="#262D33" arrowColor="#262D33" />
            {/*
            <Table striped hover style={{ marginTop: '10px' }}>
                <thead>
                    <tr>
                        <th>Full Name</th>
                        <th>Role</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {userSelections.map((option, i) => (
                        option &&
                        <tr>
                            <td>{option.first_name+" "+option.last_name}</td>
                            <td>{option.role_name}</td>
                            <td>
                                <a href="#!" onClick={() => onRemove(option)}><i className="far fa-times"></i></a>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </Table>*/}
        </React.Fragment>
    );

    return (
        <Container fluid>
            {success}
            <Row>
                <Col sm="12">
                    <Form.Group controlId="name">
                        <Form.Label>Add User</Form.Label>
                        <Typeahead
                            id="user_autocomplete"
                            labelKey={option => `${option.first_name} ${option.last_name}`}
                            multiple
                            onChange={setUserSelections}
                            options={companyUsers || []}
                            placeholder="Enter a user name..."
                            selected={userSelections || []}
                            renderInput={renderInput}
                            ref={ref}
                        />
                    </Form.Group>
                </Col>
            </Row>
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Form.Row>
                    <Col sm="12" lg="4" className="mt-4 mb-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={`${submitting?" submitting":""}`}>Save</Button>
                    </Col>
                </Form.Row>
            </Form>
            {error}
        </Container>
    );
}