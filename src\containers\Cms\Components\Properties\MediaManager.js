import React, {useState, useEffect } from 'react';
import { Button, Modal } from 'react-bootstrap';
import Stack from '../../../../components/common/Stack';

import { MediaManager as MManager } from '../../../MediaManager/MediaManager';

const MediaManager = (props) => {
    const {selection, close} = props;
    
    const [loading, setLoading] = useState(false);
    const [mediaResult, setMediaResult] = useState();
    const [show, setShow] = useState(false);

    useEffect(() => {
        if (mediaResult){
            selection(mediaResult);
            if (!props?.mediaMultiSelect === true) setShow(false);
        }
    }, [props?.mediaMultiSelect, mediaResult, selection]);

    useEffect(() => {
        return () => {
            setLoading(false);
            setMediaResult(null);
            setShow(false);
        }
    }, []);

    return (
        <>
            <Stack direction="horizontal" gap={1} className="mb-2">
                <Button variant="light" href="#!" onClick={(e)=>setShow(true)} className="mt-1" disabled={loading}>
                    <i className="far fa-photo-video"/>
                </Button>
            </Stack>
            {!loading &&
                <Modal show={show} size="lg" centered onHide={() => {
                    setShow(false);
                    if (close && mediaResult) close(mediaResult);
                }}>
                    <Modal.Header closeButton />
                    <Modal.Body>
                        <MManager isModal multiSelect={props?.mediaMultiSelect || false} onSelection={setMediaResult} />
                    </Modal.Body>
                </Modal>
            }
        </>
    );
}

export default MediaManager;