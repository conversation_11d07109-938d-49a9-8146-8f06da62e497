import React from 'react';
import { randomUUID } from '../../../../../utils/cms';

const Hero = (props) => {
    // this should be in every component, its used to forward the click event to the builder if in preview mode
    let preview_click=null;
    if (props.is_preview && props.onClick){
        preview_click = props.onClick;
    }

    //append backgroundImage and height to props.style if props.style is defined

    let style={};
    if (props.style) style={...props.style};
    style.backgroundImage=`url(${props.image})`;
    style.backgroundRepeat="no-repeat";
    style.backgroundPosition="center";
    style.backgroundSize="cover";
    style.height=props.height;

    return (
        <div id={props.is_preview ? `preview-${randomUUID()}` : props?.id} className={`hero_${props.page_id} ${props.className || ""}`} style={style || null} onClick={preview_click}>
            {/*
            <div className={`mask mask_${props.page_id}`}>
                <div className={`d-flex h-100`}>
                    
                </div>
            </div>
            */}
            {props.children}
        </div>
    );
}

export default Hero;