import React, { useState, useEffect } from 'react';
import { EditorState, Modifier } from 'draft-js';
import styles from './CustomOptions.module.scss'

export const CustomOption = ({variables, loading=false, ...props})=>{
    const [ open, setOpen ] = useState(false);

    useEffect(()=>{
        const handleListener =(e)=>{
            if(!document.getElementById('custom-options-wrapper').contains(e.target)){
                setOpen(false)
            }
        }

        window.addEventListener('click', handleListener)

        return()=>{
            window.removeEventListener('click', handleListener);
        }
    },[]);

    const openCustomDropdown = () => {
        setOpen(!open)
    }

    const addCustom = (custom)=>{
        const { editorState, onChange } = props;
        const contentState = Modifier.replaceText(
            editorState.getCurrentContent(),
            editorState.getSelection(),
            custom,
            editorState.getCurrentInlineStyle(),
        );
        const result = EditorState.push(editorState, contentState, 'insert-characters');
        if(onChange){
            onChange(result)
        }
    }

    return(
        <>
            {!loading &&
                <div onClick={openCustomDropdown} className="rdw-block-wrapper" aria-label="rdw-block-control">
                    <div className="rdw-dropdown-wrapper rdw-block-dropdown" id="custom-options-wrapper" aria-label="rdw-dropdown">
                        <div className="rdw-dropdown-selectedtext" title="Variables">
                            <span className={`${styles["dropdown-title"]}`}>Variables</span> 
                            <div className={`rdw-dropdown-caretto${open? "close": "open"}`}></div>
                        </div>
                        <ul className={`rdw-dropdown-optionwrapper ${open? "": ""}`}>
                                {open ? 
                                    <>
                                        {variables?.map((variable)=>(
                                            <li
                                                onClick={()=>addCustom(variable?.variable)}
                                                key={variable.id}
                                                className={`rdw-dropdownoption-default ${styles["hover-item"]}`}
                                            >
                                                {variable?.description}
                                            </li>
                                        ))}
                                    </>
                                    :
                                    null
                                }
                        </ul>
                    </div>
                </div>
            }
        </>
    )
}

export default CustomOption;