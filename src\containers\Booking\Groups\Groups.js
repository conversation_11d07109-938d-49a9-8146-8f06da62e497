import React, { useState, useEffect } from 'react';
import { useSelector,useDispatch } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import { Token, Typeahead } from 'react-bootstrap-typeahead';

import * as actions from '../../../store/actions';

import 'react-bootstrap-typeahead/css/Typeahead.css';

import Users from '../../../api/Users';

export const Groups = (props) => {
    const dispatch = useDispatch();
    const [userGroups, setUserGroups] = useState();
    const [groupSelections, setGroupSelections] = useState(useSelector(state => state.map.attendees) || []);

    useEffect(() => {
        let mounted = true;
        Users.groups()
        .then(response => {
            if(mounted) setUserGroups(response.data);
        }).catch(e => console.error(e));

        // adds the groups in the event to the list
        if (props.event && props.event.groups){
            setGroupSelections(props.event.groups);
        }

        return () => {
            mounted = false;
        }
	}, [props.event]);

    const renderInput = ({ inputClassName, inputRef, referenceElementRef, ...props },{ onRemove, selected }) => (
        groupSelections &&
        <React.Fragment>
            <input
                {...props}
                className="form-control"
                ref={input => {
                    referenceElementRef(input);
                    inputRef(input);
                }}
                type="text"
            />
            <div style={{ marginTop: '10px' }}>
                {groupSelections.map((option, i) => (
                    option &&
                    <Token key={`tkn-${i}`} onRemove={() => {
                        dispatch(actions.removeAttendee(option.id));
                        return onRemove(option);
                    }}>
                        {option.name}
                    </Token>
                ))}
            </div>
        </React.Fragment>
    );

    return (
        <React.Fragment>
            <Row>
                <Col sm="12" lg="8">
                    <h1>Who's coming?</h1>
                    <p>
                        Enter the group names that will be attending the event.
                    </p>
                </Col>
            </Row>
            <Row>
                <Col sm="12" lg="4">
                    <Typeahead
                        id="group_autocomplete"
                        labelKey="name"
                        multiple
                        onChange={item=>{
                            dispatch(actions.addAttendee(item));
                            return setGroupSelections(item);
                        }}
                        options={userGroups || []}
                        placeholder="Enter a group name..."
                        selected={groupSelections}
                        renderInput={renderInput}
                    />
                </Col>
            </Row>
        </React.Fragment>
    );
}