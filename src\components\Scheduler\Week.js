import React from 'react';
import {getDayNames, getWeeks, showTasks} from './Common.js';
import './Scheduler.css';

// create a day array
const dayArray = (date, tasks) =>{    

    if (!date) date=new Date();

    const d = new Date(date);
    const weekStart = new Date(d.setDate(d.getDate() - d.getDay())) // first day of week
    const weekEnd = new Date(d.setDate(d.getDate() - d.getDay() +6)); // last day of week

    if (!tasks) tasks=[];

    let daysArray = {
        header: {
            first_day: weekStart,
            last_day: weekEnd
        },
        times:[], // will fill with sub arrays for each day
    };

    let dayIndex="";
    for (let i=0;i<24;i++){
        for (let j=0, currday=new Date(weekStart); j<7; j++, currday.setDate(currday.getDate()+1)){
            if (!daysArray.times[i]){                
                daysArray.times[i]={
                    date:new Date(currday),
                    hour24:new Date(currday.setHours(i)).toLocaleTimeString("en-US", { hour12: false, hour: '2-digit' }),
                    hour:new Date(currday.setHours(i)).toLocaleTimeString("en-US", { hour12: true, hour: '2-digit' }),
                    tasks:[]
                }
        
            }            
            daysArray.times[i].tasks[j]=[];
            
            dayIndex=`${currday.getFullYear()}/${(("0" + (currday.getMonth()+1)).slice(-2))}/${("0" + currday.getDate()).slice(-2)}`;

            if (tasks[dayIndex] || tasks["everyday"]){
                [...(tasks[dayIndex] || []), ...(tasks["everyday"] || [])].forEach(task => {
                    let [hour]=task.time_start.split(":");
                    if (hour===new Date(currday.getFullYear(),currday.getMonth(),currday.getDate(),i,0,0).toLocaleTimeString("en-US", { hour12: false, hour: '2-digit' })){
                        daysArray.times[i].tasks[j].push(task);
                    }
                });
            }
        }
    }

    return daysArray;
}

// create a daily view scheduler
const Week = (props) => {

    const date=props.date || getWeeks(props.week).date;  

    const calendar=dayArray(date, props.tasks);
    let currday=new Date(calendar.header.first_day);
    let nowClass="";

    return (
        <div className="scheduler scheduler-week-table">
            <span className="day-name"></span>
            {getDayNames().map((day,i)=>{
                currday.setDate(currday.getDate()+(i>0?1:0));
                return (
                    <div className="day-name" key={`spdays${i}`}>
                        {day.text.substr(0,3)}<br/>
                        <span className="day-date">{new Date(currday).toLocaleDateString("en-US", { month: 'short', day: 'numeric' })}</span>
                    </div>
                )
            })}
                        
            {calendar?.times.map((time,i)=>{
                nowClass="";
                if (time.date.getTime()===new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(),new Date().getHours(),0).getTime()) nowClass="day-today";
                return (
                <React.Fragment key={`frg${i}`}>
                    <div className={nowClass} key={`con${i}`}>
                        <div className="scheduler-time">{time.hour}</div>
                    </div>
                    { 
                        time.tasks.map((tasks,j) => {
                            return (
                                <div className={nowClass} key={`con${j}`}>
                                    <div>
                                        { showTasks(tasks, new Date(date.getFullYear(), date.getMonth(), j)) }
                                    </div>
                                </div>
                            )
                        })
                    }
                </React.Fragment>)
            })}
            
        </div>
    );
}

export default Week;