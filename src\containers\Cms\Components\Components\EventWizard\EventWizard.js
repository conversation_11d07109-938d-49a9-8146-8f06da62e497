import React, {useState, useEffect, useCallback} from 'react';
import { <PERSON>, Button } from 'react-bootstrap';

import { Step1, Step2, Step3, Step4, Step5, Step6, Step7 } from './Steps';

import styles from './EventWizard.module.scss';

export const EventWizard = (props) => {
    const [error, setError] = useState();
    const [submitting, setSubmitting] = useState(false);
    const [disableNext, setDisableNext] = useState(false);
    const [disableBack, setDisableBack] = useState(false);

    const [step, setStep] = useState();
    const [referrerStep, setReferrerStep] = useState();
    const [component, setComponent] = useState();
    const [stepValues, setStepValues] = useState({});

    const saveStepValues = useCallback((values) => {
        let _stepstuff = {...stepValues, ...values};
        setStepValues(_stepstuff);
    }, [stepValues]);

    const clickHandler = useCallback((e, _step=null, referrer=null) => {
        e.preventDefault();
        e.stopPropagation();

        if (_step) setStep(_step);
        else setStep(step + 1);
        
        if (referrer) setReferrerStep(referrer);
    }, [step]);

    const submitHandler = async (e) => {
        e.preventDefault();
        e.stopPropagation();

        setSubmitting(true);

        if (step === 6){
            if (stepValues?.collectJS){
                setDisableNext(true);
                setDisableBack(true);
                await stepValues.collectJS.startPaymentRequest();
                const tokenres = await stepValues.collectJS.tokenPromise;
                if (tokenres.token) saveStepValues({token: tokenres.token});
            }
        }
    }

    useEffect(() => {
        if (stepValues?.token && step===6){
            setDisableBack(false);
            setDisableNext(false);
            setStep(7);
        }
    }, [stepValues?.token, step, setDisableBack, setDisableNext]);

    useEffect(() => {
        if (step===1) setDisableNext(false);

        const _props = {
            submitting: submitting,
            click: clickHandler,
            disableNext: setDisableNext, // sets the state to disable the next button, in case we need to wait for an async call to finish, or a validation to pass
            disableBack: setDisableBack,
            referrerStep: referrerStep, // keeps track if the next or back button was pressed, in case we need to skip a step we either go to the previous or to the next of the skipped step
            currentStep: step, // keeps track of the current step
            saveStepValues: saveStepValues,
            stepValues: stepValues,
            ...props
        }

        switch(step){
            case 7:
                setComponent(<Step7 {..._props} />);
                break;
            case 6:
                setComponent(<Step6 {..._props} />);
                break;
            case 5:
                setComponent(<Step5 {..._props} />);
                break;
            case 4:
                setComponent(<Step4 {..._props} />);
                break;
            case 3:
                setComponent(<Step3 {..._props} />);
                break;
            case 2:
                setComponent(<Step2 {..._props} />);
                break;
            case "done":
            case 1:
            default:
                setComponent(<Step1 {..._props} />);
                break;
        }
    }, [step, submitting, clickHandler, props, referrerStep, saveStepValues, stepValues]);

    useEffect(() => {
        return () => {
            setStep(null);
            setComponent(null);
            setError(null);
            setSubmitting(false);
            setDisableNext(false);
            setDisableBack(false);
            setStepValues({});
        }
    }, []);    


    if (!component) return null;

    return (
        <div className={styles.wrapper}>
            <div className="modal-body">
                <Card>
                    <Card.Title>{props.name}</Card.Title>
                            {/*<Form onSubmit={submitHandler} noValidate validated={validated}>*/}
                                {component}
                            {/*</Form>*/}
                            {error && <div className="error-text-ctr">{error}</div>}
                </Card>
            </div>
            {step &&
                <div className={`${styles.toolbar} modal-footer`}>
                    {step > 1 && step < 7 && <Button variant="light" disabled={props.submitting || disableBack} onClick={e=>clickHandler(e, step-1, "back")}>Back</Button>}
                    {step < 6 && <Button variant="primary" disabled={props.submitting || disableNext} onClick={e=>clickHandler(e, step+1, "next")}>Continue</Button>}
                    {step === 6 && <Button variant="primary" disabled={props.submitting || disableNext} type="button" onClick={submitHandler} id="payButton">Register</Button>}
                    {step === 7 && props.close && <Button variant="primary" disabled={props.submitting} onClick={props.close}>Close</Button>}
                </div>
            }
        </div>
    );
}