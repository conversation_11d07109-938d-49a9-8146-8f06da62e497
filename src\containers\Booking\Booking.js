import React, { useState,useEffect,useCallback,Suspense} from 'react';
import { useSelector,useDispatch } from 'react-redux';
import { useHistory } from "react-router-dom";
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import Container from 'react-bootstrap/Container';
import Breadcrumb from 'react-bootstrap/Breadcrumb';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import ButtonGroup from 'react-bootstrap/ButtonGroup';
import Button from 'react-bootstrap/Button';
import ErrorCatcher from '../../components/common/ErrorCatcher';
import Toast from '../../components/Toast';

import EventTypes from './EventTypes';
import Map from './Map';
import Timeline from './Timeline';
import Groups from './Groups';
import AssignUsers from './AssignUsers';
import Summary from './Summary';
import Meta from './Meta';

import * as actions from '../../store/actions';

import Events from '../../api/Events';

// remove this if timeline should load like a wizard
//import Map from './Map';
//

import './Booking.scss';

export const Booking = () => {
    let history = useHistory();
    const dispatch = useDispatch();

    // show loading overlay
    //const [loading,setLoading]=useState(false);
	const [breadcrumbs,setBreadcrumbs]=useState(
		[
			{url:"/p/home",title:"Home",active:false},
			{url:"/booking",title:"Event Booking",active:true}
		]
	)    

    //const [timeline,setTimeline]=useState();
    //const selectedItems = useSelector(state => state.map.items);
    //const selectedParent = useSelector(state => state.map.selected_roots);
    const selectedItems = useSelector(state => state.map.selected_items);
    const currentStep = useSelector(state => state.map.step);
    const eventType = useSelector(state => state.map.event_type);
    const attendees = useSelector(state => state.map.attendees);
    const [nextButton,setNextButton]=useState();
    const [backButton,setBackButton]=useState();
    const [saveButton,setSaveButton]=useState();
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

    const [pagePart,setPagePart]=useState(
        <Suspense fallback={             
            <SkeletonTheme color="#e0e0e0">
                <Skeleton height={30} style={{marginBottom:"1rem"}} />
                <Skeleton height={12} count={5} />
            </SkeletonTheme>
        }>            
        </Suspense>
    );

    /* loads components based on current step */
    const loadPagePartHandler=useCallback((e) => {
        if (e?.target?.hash.substr(0,1)==="#"){
            let component;
            dispatch(actions.currentStep(e.target.hash.substr(1)));
            
            switch (e.target.hash.substr(1)){
                case "Summary":
                    component=<Summary clickHandler={loadPagePartHandler} />;
                    break;
                case "Users":
                    component=<AssignUsers clickHandler={loadPagePartHandler} />;
                    break;
                case "Groups":
                    component=<Groups clickHandler={loadPagePartHandler} />;
                    break;
                case "Timeline":
                    component=<Timeline clickHandler={loadPagePartHandler} />;
                    break;
                case "Map":
                    //clickHandler={loadPagePartHandler}
                    component=<Map />
                    break;
                case "Meta":
                    component=<Meta clickHandler={loadPagePartHandler} />;
                    break;
                case "EventTypes":
                default:
                    component=<EventTypes clickHandler={loadPagePartHandler} />
                    break;
            }
            setPagePart(
                <Suspense fallback={             
                    <SkeletonTheme color="#e0e0e0">
                        <Skeleton height={30} style={{marginBottom:"1rem"}} />
                        <Skeleton height={12} count={5} />
                    </SkeletonTheme>
                }>
                    {component}
                </Suspense>
            );
        }
    },[dispatch]);

    /* after an event is booked, refreshes the wizard after 3 seconds */
    useEffect(() => {
        if (success){
            const timer = setTimeout(() => history.go(0), 3000);
            return () => clearTimeout(timer);
        }
    }, [success,history]);

    // default page load
    useEffect(() => {
        loadPagePartHandler({target:{hash:"#!"}});
    }, [loadPagePartHandler]);

    /* handles next and back buttons */
    useEffect(() => {

        /* saves booking info */
        const submitHandler = async () => {

            setError(null);
            setSuccess(null);

            let book_data=[];
            selectedItems.forEach(item=>{
                let data = {
                    location_id: item.id,
                    event_type_id: eventType.id,
                    start_datetime: item.booking.start_datetime,
                    end_datetime: item.booking.end_datetime,
                    name: item.booking.event_name,
                    description: item.booking.event_description,
                    event_status_id: 1,
                    requires_registration: item.booking.requires_registration,
                    attendees: eventType.individual_invites ? attendees.map(i=>i.id) : [],
                    groups: eventType.group_invites ? attendees.map(i=>i.id) : [],
                };
                if (item.booking.parent_id && item.booking.parent_id !== "0") data.parent_id = item.booking.parent_id;
                book_data.push(data);
            });

            if (book_data){
                for(let i = 0; i < book_data.length; i+=1) {
                    await Events.create(book_data[i])
                    .then( response => {
                        if (!response.errors) {
                            setSubmitting(false);
                            setSuccess(<Toast>Event booked successfully!</Toast>);
                        } else {
                            setSubmitting(false);
                            setError(<ErrorCatcher error={response.errors} />);
                        }
                    }).catch(e => {
                        setError(<ErrorCatcher error={e} />);
                        console.error(e);
                    });
                }
            }
    }

        setBackButton(null);
        setNextButton(null);
        setSaveButton(null);
        let nextA=null;
        let backA=null;
        let saveA=null;
        switch(currentStep){
            case "Summary":
                if (eventType.group_invites) backA="#Groups";
                if (eventType.individual_invites) backA="#Users";
                saveA="#!";
                break;
            case "Users":
            case "Groups":
                nextA="#Summary";
                if (eventType.id === 5) backA="#Meta";
                else backA="#Timeline";
                break;
            case "Timeline":
                if (eventType.group_invites) nextA="#Groups";
                if (eventType.individual_invites) nextA="#Users";
                if (eventType.id === 5) backA="#EventTypes";
                else backA="#Map";
                break;
            case "Map":
                backA="#EventTypes";
                if (selectedItems.length>0) nextA="#Timeline";
                break;
            case "Meta":
                backA="#EventTypes";
                nextA="#Users";
                break;
            case "EventTypes":
            default:
                if (eventType) {
                    if (eventType.id === 5) nextA="#Meta"; //meta events don't have locations 
                    else nextA="#Map";
                }
                break;
        }
        if (backA)
            setBackButton(
                <Button variant="secondary" className={submitting?"submitting":""} href={backA} onClick={loadPagePartHandler}>Back</Button>
            );

        if (nextA)
            setNextButton(
                <Button variant="primary" className={"mr-l10 " + (submitting?"submitting":"")} href={nextA} onClick={loadPagePartHandler}>Continue</Button>
            );

        if (saveA)
            setSaveButton(
                <Button variant="primary" className={`btn-save${submitting?" submitting":""}`} href={saveA} onClick={submitHandler}>Save</Button>
            );
    }, [currentStep, loadPagePartHandler, submitting, selectedItems, eventType, attendees, dispatch]);

    /* handles breadcrumbs depending on current step */
    useEffect(() => {
        let baseCrumbs=[
			{url:"/p/home",title:"Home",active:false},
			{url:"/booking",title:"Event Booking",active:false}
		];

        switch(currentStep){
            case "Summary":
                baseCrumbs.push(
                    {
                        url:"#Map",
                        active:false,
                        title:"Location"
                    },
                    {
                        url:"#Timeline",
                        active:false,
                        title:"Time"
                    },
                    {
                        url:"#Groups",
                        active:false,
                        title:"Assign "+(eventType.group_invites?"Groups":"Users")
                    },
                    {
                        url:"#"+currentStep,
                        active:true,
                        title:"Summary"
                    },
                );
                break;
            case "Users":
            case "Groups":
                baseCrumbs.push(
                    {
                        url:"#Map",
                        active:false,
                        title:"Location"
                    },
                    {
                        url:"#Timeline",
                        active:false,
                        title:"Time"
                    },
                    {
                        url:"#"+currentStep,
                        active:true,
                        title:"Assign "+currentStep
                    }
                );
                break;
            case "Timeline":
                baseCrumbs.push(
                    {
                        url:"#Map",
                        active:false,
                        title:"Location"
                    },
                    {
                        url:"#"+currentStep,
                        active:true,
                        title:"Time"
                    }
                );
                break;
            case "Map":
                baseCrumbs.push(
                    {
                        url:"#"+currentStep,
                        active:true,
                        title:"Location"
                    }
                );
                break;
            default:
                baseCrumbs[1].active=true;
                break;
        }
        setBreadcrumbs(baseCrumbs);
    }, [currentStep,eventType]);


    return (
        <Container fluid className="wizard_container">
            <Row className="header">
                <Col>
                    <Breadcrumb>
                        {breadcrumbs.map((item,i)=>(
                            <Breadcrumb.Item key={`breadcrmb-${i}`} href={item.url} active={item.active} onClick={loadPagePartHandler}>{item.title}</Breadcrumb.Item>
                        ))}
                    </Breadcrumb>
                </Col>
            </Row>
            <Row className="body">
                <Col sm="12">
                    {success}
                    {error}
                    {pagePart}
                </Col>
            </Row>
            <Row className="body pt-0 pb-0 mt-auto">
                <ButtonGroup className="ml-auto">
                    {backButton}
                    {nextButton}
                    {saveButton}
                </ButtonGroup>
            </Row>
        </Container>
    );
}
