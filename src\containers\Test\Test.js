import React, {useState, useEffect, useCallback, useRef} from 'react';
import { useSelector } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Image from 'react-bootstrap/Image';
import Button from 'react-bootstrap/Button';
import Container from 'react-bootstrap/Container';
import Card from 'react-bootstrap/Card';
import { Form, Modal } from 'react-bootstrap';

import { format, differenceInYears } from 'date-fns';

import Events from '../../api/Events';
import User from '../../api/Users';
import Groups from '../../api/Groups';
import ImageViewer from '../../components/common/ImageViewer';
import { UserCreateEdit } from '../../components/UserCreateEdit/UserCreateEdit';

import {
    getFamily,
    checkFamilyStatuses
} from './EventHelperFunctions';

import styles from "./Test.module.scss"

// export const Gladius = () => (
//     <Container fluid>
//         <Row className="mb-4">
//             <Col className="text-right">
//                 <Button variant="light" size="sm"><i className="far fa-arrow-to-bottom"></i>Export</Button>
//                 <Button variant="light" size="sm"><i className="far fa-share-alt"></i>Share</Button>
//             </Col>
//         </Row>
//         <Row>
//             <Col>
//                 <div className="card">
//                     <h1>Scientia Et Gladius</h1>
//                     <div>
//                         I'm baby blog kogi prism bicycle rights subway tile helvetica selvage. Thundercats cray la croix, plaid seitan food truck chartreuse street art. Jean shorts brooklyn bicycle rights disrupt stumptown farm-to-table enamel pin gastropub unicorn prism. Artisan ennui cray photo booth. Pinterest fixie taxidermy lumbersexual ennui, vexillologist beard listicle. Blog ramps offal, twee jean shorts narwhal PBR&B neutra affogato XOXO synth normcore.                
//                     </div>
//                     <div>
//                         Migas biodiesel pickled listicle wayfarers. Offal mixtape coloring book, church-key art party plaid lomo wolf shaman cred master cleanse taxidermy irony whatever hoodie. Marfa twee neutra taxidermy 90's sustainable, celiac vegan vape next level tumblr selvage. Yr leggings vegan, franzen kogi raw denim hot chicken shoreditch 8-bit gochujang selfies affogato biodiesel ennui. Venmo narwhal cloud bread sartorial craft beer. IPhone butcher hoodie tote bag poke fashion axe wayfarers adaptogen vice kickstarter activated charcoal coloring book sartorial thundercats succulents.
//                     </div>
//                 </div>

//             </Col>
//         </Row>
//     </Container>
// );

// export const WizPig = () => (
//     <Container fluid>
//         <Row>
//             <Col className="text-left">
//                 <h1 className="pb-0">Test it!</h1>
//                 {/* <Image src={require("peep.png").default} fluid /> */}
//             </Col>
//         </Row>
//     </Container>
// );


// const data=[
// 			{
// 				"Test":{
// 					"props":{
// 						"text1": "This is not a",
// 						"text2": "This is a"
// 					}
// 				}
// 			},
//             {
//                 "div":{
//                     "props":{
//                         "className":"card"
//                     },
//                     "content":[
//                         {
//                             "span":{
//                                 "innerText":"This is a span"
//                             }
//                         },
//                         {
//                             "contentBlock":{
//                                 "id":2
//                             }
//                         }            
//                     ]
//                 }
//             }
// 		]

// const Test = (props) => {
//     return (
//         <Container fluid>
//             PAGE ID: {props.page_id}
//             <Row className="body">
//                 <Col id="content-goes-here">
//                 </Col>
//             </Row>
//         </Container>
//     );
// };


const ParentComponent = (props)=>{

    // const eventId = 7107; //variat event
    // const eventId = 7047 //event with users 
    const eventId = 7113 //event with questions
    // const eventId = 7103 //event with age range
    const user = useSelector(state => state?.auth?.user?.profile);
    const loadingStates = useRef({
        eventDetails: false,
        family: false,
        user: false
    })
    const answersRef=useRef([])
    const [eventDetails, setEventDetails]=useState(null);
    const [selectedFamily, setSelectedFamily]=useState([]);

    const handleFamilyClick=(clickedFamilyId)=>{
        if(selectedFamily?.length && selectedFamily.includes(clickedFamilyId)){
            setSelectedFamily(selectedFamily.filter((family)=>family !== clickedFamilyId))
        }else{
            setSelectedFamily([...selectedFamily, clickedFamilyId])
        }
    }

    const handlePayNow=()=>{
        console.log(selectedFamily)
    }

    const handlePayLater=()=>{

    }

    return(
        <>
            {/* invite link should be handled at the parent level */}
            {/* we need event details to be consistent */}
                <EventDetails 
                    eventId={eventId} 
                    setEventDetails={setEventDetails} 
                    eventDetails={eventDetails}
                    loadingStates={loadingStates}
                />
            {/* We need to get family details */}
            {/* We need the checks for being already registered and the proper age to be consistent */}
                <EventFamily 
                    user={user}
                    eventId={eventId}
                    eventDetails={eventDetails}
                    loadingStates={loadingStates}
                    handleFamilyClick={handleFamilyClick}
                    selectedFamily={selectedFamily}
                    answersRef={answersRef}
                />

            {/* we need to allow the ability to create new users here */}
            {/* We also need to make sure there's a family because...the backened doesn't handle it */}


            {/* 
                we need to allow the user to be selected from among those who are valid.  
                Those who are not valid need to say why 
                Select multi can apply here (checkmark like CMS)
                Questions need to be answered for each 
                Registration endpoints and such need to happen for each.  
            */}

            {/* we need to see/answer any required questions for the event */}
                
            {/* we need to be confirmed if there's no price assiated with a click */}

            {/* If there is a price association, we need to offer "pay now" or "pay later" */}

            {/* If pay later, registration for the event needs to occur*/}

            {/* when an event is paid for, the registration needs to switch to confirmed        */}
        
            {/* If any of the loading states has an error, need to show said error */}
        
            <Button onClick={handlePayNow}>
                Register Users and Pay Now
            </Button>
            <Button onClick={handlePayLater}>
                Pay Later (Add to Cart)
            </Button>
        </>

    )
}

export const EventDetails = ({eventId, eventDetails, setEventDetails, loadingStates, ...props})=>{

    const adjustTimeString=useCallback((event)=>{
        let startFormatted = format(new Date(event?.start_datetime), "ccc MM/dd/yyyy");
        let endFormatted = format(new Date(event?.end_datetime), "ccc MM/dd/yyyy");
        if(startFormatted === endFormatted) {
            startFormatted = format(new Date(event?.start_datetime), "ccc MM/dd/yyyy hh:mm aa");
            endFormatted = format(new Date(event?.end_datetime), "hh:mm aa");
        }
        return (startFormatted + " - " + endFormatted);
    },[]);

    const variantPriceArray = useCallback((event)=>{
        if(event?.variants?.length > 0){
            let priceArray = []
            for(let i = 0; i < event.variants.length; i++){
                priceArray.push(event?.variants[i]?.price)
            }
            priceArray.sort((a,b)=>a - b)
            return priceArray
        }else{
            return [event?.default_variant_price]
        }
    },[])

    useEffect(()=>{
        //use the public api here so details can be SEEN in any case
        const getEventBasics=async()=>{
            loadingStates.current.eventDetails = true;
            try{
                let response = await Events.publicGet({id: eventId});
                if(response?.data?.events) {
                    let event = response?.data?.events?.filter((event)=>event.id === eventId)[0]
                    if(event.images.length > 0) event.images.forEach((image)=>image.url = image.preview_url)
                    event.timeString = adjustTimeString(event);
                    event.eventPricesOrder = variantPriceArray(event);
                    //This needs to get both the custom fields and the users registered for the event.
                    event.advancedDetails= await getAdvancedEventDetails();
                    setEventDetails(event);
                    loadingStates.current.eventDetails = false;
                }
            }catch(ex){
                console.error(ex)
                loadingStates.current.eventDetails = "ERROR";
            }
        }

        const getAdvancedEventDetails = async()=>{
            try{
                let response = await Events.getSingle({id: eventId});
                if(response?.data) {
                    let neededDetails = {
                        users: response.data[0].users,
                        custom_fields: response.data[0].custom_fields
                    }
                    return neededDetails;
                }
            }catch(ex){
                console.error(ex);
                loadingStates.current.eventDetails = "ERROR";
            }
        }

        if(loadingStates.current.eventDetails === false) getEventBasics();
        //we don't want to trigger with "loadingStates"
        /*eslint-disable-next-line react-hooks/exhaustive-deps*/ 
    },[eventId, adjustTimeString, variantPriceArray, setEventDetails]);


    return(
        <div className={styles["event-details-wrapper"]}>
            {eventDetails &&
                <div>
                    <div className={styles["detail-pair"]} data-cy="event-status-details">
                        {(eventDetails?.event_status_id === 1 || eventDetails?.event_status_id === 3 || eventDetails?.event_status_id === 4 || eventDetails?.event_status_id === 6 || eventDetails?.event_status_id===8) && 
                            <h6>At this time, registration for this event is not available due to its {eventDetails?.event_status_name?.toLowerCase()} status.  If you have any questions, don't hesitate to reach out.</h6>
                        }
                        <h2 data-cy="register-event-name">{eventDetails?.name}</h2>
                        {eventDetails?.event_status_id === 5 && 
                            <h6 data-cy="event-details-private">This is a private event 
                                {eventDetails.requires_registration ===1 &&
                                    <span>
                                        and can only be registered for via a direct invitation.                                
                                    </span>
                                }
                            </h6>
                        }
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-image-viewer">
                        <ImageViewer 
                            images={eventDetails?.images}
                            largeImgMax={"200px"}
                            thumbMax={"50px"}
                            thumbLimit={4}
                        />
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-when">
                        <label>
                            When
                        </label>
                        {eventDetails.start_datetime && 
                            <p>
                                {eventDetails.timeString}
                            </p>
                        }
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-where">
                        <label>
                            Where
                        </label>
                        <p>
                            {eventDetails.location_name}
                        </p>
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-age-requirement">
                        <label>
                            Age Requirement
                        </label>
                        <p data-cy="event-details-ages">
                            {eventDetails.min_age > 0 && eventDetails.max_age > 0 && 
                                `Participant must be between ${eventDetails?.min_age} - ${eventDetails?.max_age} years old to register`
                            }{eventDetails.min_age > 0  && !eventDetails.max_age &&
                                `Participant must be at least ${eventDetails?.min_age} years old to register`
                            }{!eventDetails.min_age && eventDetails.max_age > 0 &&
                                `Participant must be no more than ${eventDetails?.max_age} years old to register`
                            }{!eventDetails.min_age && !eventDetails.max_age &&
                                `There is no age requirement.`
                            }
                        </p>
                    </div>
                    {eventDetails.requires_membership === 1 && 
                        <div className={styles["detail-pair"]} data-cy="event-details-membership">
                            <label>
                                Requires Membership
                            </label>
                            <p>
                                Requires membership to attend
                            </p>
                        </div>
                    }
                    <div className={styles["detail-pair"]} data-cy="event-details-registration">
                        <label>
                            Requires Registration
                        </label>
                        {eventDetails.requires_registration ===1 ?
                            <p>
                                Registration is required for this event
                            </p>
                            :
                            <p>
                                No registration required
                            </p> 
                        }
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-description">
                        <label>
                            What
                        </label>
                        {eventDetails?.description ? 
                            <div dangerouslySetInnerHTML={{__html: eventDetails?.description}} />
                        :
                            <div>
                                No description provided
                            </div>
                        }
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-price">
                        <label>
                            Price
                        </label>
                        <p>
                            {eventDetails?.eventPricesOrder?.length > 1 ?
                                <span>
                                    From ${eventDetails?.eventPricesOrder[0]} to ${eventDetails?.eventPricesOrder[eventDetails?.eventPricesOrder?.length-1]}
                                </span>
                                :
                                <span>
                                    ${eventDetails?.eventPricesOrder[0]}
                                </span>
                            }
                        </p>
                    </div>
                </div>
            }
        </div>
    )
}

export const EventFamily=({
    eventDetails, 
    user, 
    loadingStates, 
    handleFamilyClick, 
    selectedFamily, 
    answersRef,
    ...props
})=>{

    const [family, setFamily]=useState(null);
    const [allFamilyMembers, setAllFamilyMembers]=useState([]);
    const [showNewUser, setShowNewUser]=useState(null);

    //this will be the first load - family will only be null on first load, otherwise it should be an array
    useEffect(()=>{
        const handleFirstLoad = async()=>{
            let family = await getFamily(user, loadingStates);
            setFamily(family || [])
        }

        if(loadingStates.current.family === false && !family){
            handleFirstLoad();
        }
    },[loadingStates, family, user]);

    useEffect(()=>{
        const handleFamilyMembers=async()=>{
            let members =await checkFamilyStatuses(loadingStates, user, family, eventDetails);
            setAllFamilyMembers(members)
        }

        if(family && eventDetails && loadingStates.current.family === false){
            handleFamilyMembers();
        }
    },[family, eventDetails, loadingStates, user]);

    const toggleNewUser=(groupId)=>{
        setShowNewUser(groupId);
    }

    const handleNewFamily=()=>{
        setShowNewUser(null);
        let family = getFamily(user, loadingStates);
        setFamily(family || [])
    }

    return(
        <>
            {family && family.length > 0 &&
                <>
                    {family?.map(group=>(
                        <Button 
                            data-cy="add-new-family-member-btn"
                            key={`new-member-btn-${group.id}`} 
                            onClick={()=>toggleNewUser(group.id)}
                        >
                            Add New Family Member to {group.name} ({group.id})
                        </Button>
                    ))}
                </>
            }
            {
                allFamilyMembers?.length > 0 &&
                <>
                    {allFamilyMembers.map((member, i) => (
                        <p key={`family-member-${i}`}>
                            <input
                                data-cy="family-member-checkbox"
                                type="checkbox"
                                id={`family-member-${i}`}
                                checked={selectedFamily?.includes(member.id)}
                                onChange={(e)=>handleFamilyClick(member.id)}
                                disabled={member.registered || !member?.ageAllowed?.isAllowed}
                            />
                            {" "}
                            <span data-cy="family-member-name-role">
                                {member.name} {" "}
                                {member.isSelf ? 
                                    "(Me)" 
                                : 
                                    <span>({member.roleName})</span>
                                }
                            </span>
                            <br />
                            <span data-cy="family-member-registration-status">
                               {member.registered ? "Already Registered" : "Not Registered"}
                            </span>
                            <br />
                            <span data-cy="family-member-age-status">
                                {member?.ageAllowed?.isAllowed ? "" : member?.ageAllowed?.notAllowedReason}
                            </span>
                            {selectedFamily?.length > 0 && selectedFamily?.includes(member.id) ?
                                <>
                                    <EventCustomFields 
                                        currentUser = {member}
                                        eventDetails={eventDetails}
                                        loadingState={loadingStates}
                                        answersRef={answersRef}
                                    />
                                </>
                                :
                                <>
                                </>
                            }
                        </p>
                    ))}
                </>
            }
            <Modal show={showNewUser ? true : false} onHide={()=>setShowNewUser(null)}>
                <UserCreateEdit 
                    afterSubmit={handleNewFamily}
                    groupId={showNewUser}
                />
            </Modal>
        </>
    )
}

const EventCustomFields=({eventDetails, loadingStates, currentUser, answersRef, ...props})=>{

    if(loadingStates?.current?.eventDetails === false) console.log(eventDetails?.advancedDetails?.custom_fields)

    const handleChange=(field, answer)=>{
        let currentA = answersRef.current
        let userExists = currentA?.filter((user)=>user.user === currentUser.id);

        //first time 
        if(!currentA){
            currentA = [
                {
                    user: currentUser.id,
                    answers: {
                        [field]: answer
                    }
                }
            ]
        //new user
        }else if(!userExists?.length > 0){
            currentA=[
                ...currentA,
                {
                    user: currentUser.id,
                    answers: {
                        [field]: answer
                    }
                }
            ]
        //user exists & need to change/add/update answer
        }else if(answer && userExists?.length > 0){
            userExists[0].answers[field] = answer; //they're objects so it will mutate
        //user exists & need to remove answer    
        }else if(!answer && userExists?.length > 0 && userExists[0]?.answers?.hasOwnProperty(field)){
            delete userExists[0]?.answers[`${field}`];
        }
        answersRef.current = currentA;
    }
    
    return(
        <div data-cy="event-custom-fields-wrapper">
            {eventDetails && eventDetails?.advancedDetails?.custom_fields &&
                <>
                    {eventDetails?.advancedDetails?.custom_fields?.map((field, i) =>(
                        <Form.Group 
                            data-cy="event-custom-field"
                            controlId={`${field.name}_${currentUser?.id}`} 
                            key={`custom-field-group--${currentUser?.id}-${field.id}-${i}`}
                        >
                            <Form.Label data-cy="event-custom-field-label">
                                {field.placeholder_text} 
                                {field.required ? 
                                    <span className="required-star"> * </span>
                                    : 
                                    <></>
                                }
                            </Form.Label>
                            {field.custom_field_type === "select" &&
                                <Form.Control 
                                    data-cy="event-custom-field-select"
                                    as="select" 
                                    name={`custom_${field.name}`} 
                                    placeholder={field.placeholder_text} 
                                    required={field.required? true : undefined} 
                                    onChange={(e)=>handleChange(field.name, e.target.value)}
                                >
                                    <option key={`custom-select-${field.id}-${currentUser?.id}-x`} value=""></option>
                                    {field.options.map((option, i) => (
                                        <option key={`custom-select-${field.id}-${currentUser?.id}-${i}`} value={option.value}>{option.text}</option>
                                    ))}
                                </Form.Control>
                            }
                            {field.custom_field_type === "input" &&
                                <Form.Control 
                                    data-cy="event-custom-field-input"
                                    type="text" 
                                    required={field.required? true : undefined} 
                                    name={`custom_${field.name}`} 
                                    placeholder={field.placeholder_text} 
                                    onChange={(e)=>handleChange(field.name, e.target.value)} 
                                />
                            }
                        </Form.Group>
                    ))}
                </>  
            }    
        </div>
    )
}

const Test = (props) => {
    return (
        <Card className="content-card">
            <ParentComponent />
        </Card>
    );
};

export default Test;