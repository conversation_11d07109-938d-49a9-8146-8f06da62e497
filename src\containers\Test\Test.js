import React, {useState, useEffect} from 'react';
import { useSelector } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Image from 'react-bootstrap/Image';
import Button from 'react-bootstrap/Button';
import Container from 'react-bootstrap/Container';
import { Card } from 'react-bootstrap';
import Timeslots from '../Service/Booking/Timeslots';
import Selection from '../Service/Booking/Selection';
import TimeslotGrid from '../Service/Booking/TimeslotGrid';
import { BookingDescription } from '../Service/Components/Booking/BookingDescription';
import ProductCard from '../../containers/POS/Items/Products'
import { format } from 'date-fns';
import Events from '../../api/Events';
import Services from '../../api/Services'


// export const Gladius = () => (
//     <Container fluid>
//         <Row className="mb-4">
//             <Col className="text-right">
//                 <Button variant="light" size="sm"><i className="far fa-arrow-to-bottom"></i>Export</Button>
//                 <Button variant="light" size="sm"><i className="far fa-share-alt"></i>Share</Button>
//             </Col>
//         </Row>
//         <Row>
//             <Col>
//                 <div className="card">
//                     <h1>Scientia Et Gladius</h1>
//                     <p>
//                         I'm baby blog kogi prism bicycle rights subway tile helvetica selvage. Thundercats cray la croix, plaid seitan food truck chartreuse street art. Jean shorts brooklyn bicycle rights disrupt stumptown farm-to-table enamel pin gastropub unicorn prism. Artisan ennui cray photo booth. Pinterest fixie taxidermy lumbersexual ennui, vexillologist beard listicle. Blog ramps offal, twee jean shorts narwhal PBR&B neutra affogato XOXO synth normcore.                
//                     </p>
//                     <p>
//                         Migas biodiesel pickled listicle wayfarers. Offal mixtape coloring book, church-key art party plaid lomo wolf shaman cred master cleanse taxidermy irony whatever hoodie. Marfa twee neutra taxidermy 90's sustainable, celiac vegan vape next level tumblr selvage. Yr leggings vegan, franzen kogi raw denim hot chicken shoreditch 8-bit gochujang selfies affogato biodiesel ennui. Venmo narwhal cloud bread sartorial craft beer. IPhone butcher hoodie tote bag poke fashion axe wayfarers adaptogen vice kickstarter activated charcoal coloring book sartorial thundercats succulents.
//                     </p>
//                 </div>

//             </Col>
//         </Row>
//     </Container>
// );

// export const WizPig = () => (
//     <Container fluid>
//         <Row>
//             <Col className="text-left">
//                 <h1 className="pb-0">Test it!</h1>
//                 {/* <Image src={require("peep.png").default} fluid /> */}
//             </Col>
//         </Row>
//     </Container>
// );


// const data=[
// 			{
// 				"Test":{
// 					"props":{
// 						"text1": "This is not a",
// 						"text2": "This is a"
// 					}
// 				}
// 			},
//             {
//                 "div":{
//                     "props":{
//                         "className":"card"
//                     },
//                     "content":[
//                         {
//                             "span":{
//                                 "innerText":"This is a span"
//                             }
//                         },
//                         {
//                             "contentBlock":{
//                                 "id":2
//                             }
//                         }            
//                     ]
//                 }
//             }
// 		]

    const fakeService={
        "id": 3,
        "name": "Athletic Performance Training",
        "description": "Semi-private \nAges 12+\n\nOur process starts with a 30-minute complementary consultation with our Certified Strength and Conditioning Specialist discussing the athlete’s training goals and their prior training experience. In order to create the most sport-specific type of adaptation throughout their time training at Impact, the thoughtful consideration to the demands of the sport and the athlete’s goals will remain at the forefront of the programming. Using our state-of-the-art facility, we are able to help athletes push themselves to heights they never knew existed. The combination of strategic exercise progressions, thorough coaching, and scientifically proven progressive overload will lead to a healthier, stronger, faster, and more confident athlete.\n\nEvery athlete can expect attention to all areas of athletic performance including:\n- Strength\n- Power\n- Speed\n- Conditioning\n- Core Strength\n- Mobility/Flexibility\n- Proprioception and Body Awareness & Nutrition\n- Mental Wellness",
        "managers": [
            {
            "id": 8538,
            "first_name": "Elijah ",
            "last_name": "Washington"
            }
        ],
        "location_ids": [
            7
        ],
        "start_date": "1000-01-01",
        "end_date": "3000-12-31",
        "block_minutes": 30,
        "timeslots_for_token": 2,
        "min_timeslots": 2,
        "max_timeslots": 2,
        "min_participants": 1,
        "max_participants": 10,
        "min_booking_notice": 1080,
        "max_booking_notice": 525600,
        "membership_required": 0,
        "has_fee": 1,
        "products": [
            {
            "id": 493,
            "name": "Athletic Performance Training Session Pack Token",
            "product_status_id": 1,
            "variants": [
                {
                "id": 923,
                "name": "Default",
                "price": "40.00",
                "product_status_id": 1
                }
            ]
            },
            {
            "id": 731,
            "name": "Athletic Performance Training Subscription Token",
            "product_status_id": 1,
            "variants": [
                {
                "id": 1254,
                "name": "default",
                "price": "40.00",
                "product_status_id": 1
                }
            ]
            }
        ],
        "default_product_variant_id": 923,
        "default_price": "40.00",
        "cancel_threshold_minutes": 720,
        "cancel_fee_variant_id": null,
        "blocks": [
            {
            "day_of_week": 1,
            "start_time": "06:30:00",
            "duration": 270,
            "end_time": "11:00",
            "timeslots": [
                {
                "start_time": "6:30",
                "end_time": "7:00"
                },
                {
                "start_time": "7:00",
                "end_time": "7:30"
                },
                {
                "start_time": "7:30",
                "end_time": "8:00"
                },
                {
                "start_time": "8:00",
                "end_time": "8:30"
                },
                {
                "start_time": "8:30",
                "end_time": "9:00"
                },
                {
                "start_time": "9:00",
                "end_time": "9:30"
                },
                {
                "start_time": "9:30",
                "end_time": "10:00"
                },
                {
                "start_time": "10:00",
                "end_time": "10:30"
                },
                {
                "start_time": "10:30",
                "end_time": "11:00"
                }
            ]
            },
            {
            "day_of_week": 1,
            "start_time": "14:00:00",
            "duration": 240,
            "end_time": "18:00",
            "timeslots": [
                {
                "start_time": "14:00",
                "end_time": "14:30"
                },
                {
                "start_time": "14:30",
                "end_time": "15:00"
                },
                {
                "start_time": "15:00",
                "end_time": "15:30"
                },
                {
                "start_time": "15:30",
                "end_time": "16:00"
                },
                {
                "start_time": "16:00",
                "end_time": "16:30"
                },
                {
                "start_time": "16:30",
                "end_time": "17:00"
                },
                {
                "start_time": "17:00",
                "end_time": "17:30"
                },
                {
                "start_time": "17:30",
                "end_time": "18:00"
                }
            ]
            },
            {
            "day_of_week": 2,
            "start_time": "06:30:00",
            "duration": 150,
            "end_time": "9:00",
            "timeslots": [
                {
                "start_time": "6:30",
                "end_time": "7:00"
                },
                {
                "start_time": "7:00",
                "end_time": "7:30"
                },
                {
                "start_time": "7:30",
                "end_time": "8:00"
                },
                {
                "start_time": "8:00",
                "end_time": "8:30"
                },
                {
                "start_time": "8:30",
                "end_time": "9:00"
                }
            ]
            },
            {
            "day_of_week": 2,
            "start_time": "10:00:00",
            "duration": 120,
            "end_time": "12:00",
            "timeslots": [
                {
                "start_time": "10:00",
                "end_time": "10:30"
                },
                {
                "start_time": "10:30",
                "end_time": "11:00"
                },
                {
                "start_time": "11:00",
                "end_time": "11:30"
                },
                {
                "start_time": "11:30",
                "end_time": "12:00"
                }
            ]
            },
            {
            "day_of_week": 2,
            "start_time": "15:00:00",
            "duration": 300,
            "end_time": "20:00",
            "timeslots": [
                {
                "start_time": "15:00",
                "end_time": "15:30"
                },
                {
                "start_time": "15:30",
                "end_time": "16:00"
                },
                {
                "start_time": "16:00",
                "end_time": "16:30"
                },
                {
                "start_time": "16:30",
                "end_time": "17:00"
                },
                {
                "start_time": "17:00",
                "end_time": "17:30"
                },
                {
                "start_time": "17:30",
                "end_time": "18:00"
                },
                {
                "start_time": "18:00",
                "end_time": "18:30"
                },
                {
                "start_time": "18:30",
                "end_time": "19:00"
                },
                {
                "start_time": "19:00",
                "end_time": "19:30"
                },
                {
                "start_time": "19:30",
                "end_time": "20:00"
                }
            ]
            },
            {
            "day_of_week": 3,
            "start_time": "06:30:00",
            "duration": 270,
            "end_time": "11:00",
            "timeslots": [
                {
                "start_time": "6:30",
                "end_time": "7:00"
                },
                {
                "start_time": "7:00",
                "end_time": "7:30"
                },
                {
                "start_time": "7:30",
                "end_time": "8:00"
                },
                {
                "start_time": "8:00",
                "end_time": "8:30"
                },
                {
                "start_time": "8:30",
                "end_time": "9:00"
                },
                {
                "start_time": "9:00",
                "end_time": "9:30"
                },
                {
                "start_time": "9:30",
                "end_time": "10:00"
                },
                {
                "start_time": "10:00",
                "end_time": "10:30"
                },
                {
                "start_time": "10:30",
                "end_time": "11:00"
                }
            ]
            },
            {
            "day_of_week": 3,
            "start_time": "14:00:00",
            "duration": 240,
            "end_time": "18:00",
            "timeslots": [
                {
                "start_time": "14:00",
                "end_time": "14:30"
                },
                {
                "start_time": "14:30",
                "end_time": "15:00"
                },
                {
                "start_time": "15:00",
                "end_time": "15:30"
                },
                {
                "start_time": "15:30",
                "end_time": "16:00"
                },
                {
                "start_time": "16:00",
                "end_time": "16:30"
                },
                {
                "start_time": "16:30",
                "end_time": "17:00"
                },
                {
                "start_time": "17:00",
                "end_time": "17:30"
                },
                {
                "start_time": "17:30",
                "end_time": "18:00"
                }
            ]
            },
            {
            "day_of_week": 4,
            "start_time": "06:30:00",
            "duration": 150,
            "end_time": "9:00",
            "timeslots": [
                {
                "start_time": "6:30",
                "end_time": "7:00"
                },
                {
                "start_time": "7:00",
                "end_time": "7:30"
                },
                {
                "start_time": "7:30",
                "end_time": "8:00"
                },
                {
                "start_time": "8:00",
                "end_time": "8:30"
                },
                {
                "start_time": "8:30",
                "end_time": "9:00"
                }
            ]
            },
            {
            "day_of_week": 4,
            "start_time": "15:00:00",
            "duration": 150,
            "end_time": "17:30",
            "timeslots": [
                {
                "start_time": "15:00",
                "end_time": "15:30"
                },
                {
                "start_time": "15:30",
                "end_time": "16:00"
                },
                {
                "start_time": "16:00",
                "end_time": "16:30"
                },
                {
                "start_time": "16:30",
                "end_time": "17:00"
                },
                {
                "start_time": "17:00",
                "end_time": "17:30"
                }
            ]
            },
            {
            "day_of_week": 5,
            "start_time": "06:30:00",
            "duration": 270,
            "end_time": "11:00",
            "timeslots": [
                {
                "start_time": "6:30",
                "end_time": "7:00"
                },
                {
                "start_time": "7:00",
                "end_time": "7:30"
                },
                {
                "start_time": "7:30",
                "end_time": "8:00"
                },
                {
                "start_time": "8:00",
                "end_time": "8:30"
                },
                {
                "start_time": "8:30",
                "end_time": "9:00"
                },
                {
                "start_time": "9:00",
                "end_time": "9:30"
                },
                {
                "start_time": "9:30",
                "end_time": "10:00"
                },
                {
                "start_time": "10:00",
                "end_time": "10:30"
                },
                {
                "start_time": "10:30",
                "end_time": "11:00"
                }
            ]
            },
            {
            "day_of_week": 5,
            "start_time": "14:00:00",
            "duration": 240,
            "end_time": "18:00",
            "timeslots": [
                {
                "start_time": "14:00",
                "end_time": "14:30"
                },
                {
                "start_time": "14:30",
                "end_time": "15:00"
                },
                {
                "start_time": "15:00",
                "end_time": "15:30"
                },
                {
                "start_time": "15:30",
                "end_time": "16:00"
                },
                {
                "start_time": "16:00",
                "end_time": "16:30"
                },
                {
                "start_time": "16:30",
                "end_time": "17:00"
                },
                {
                "start_time": "17:00",
                "end_time": "17:30"
                },
                {
                "start_time": "17:30",
                "end_time": "18:00"
                }
            ]
            },
            {
            "day_of_week": 6,
            "start_time": "08:00:00",
            "duration": 180,
            "end_time": "11:00",
            "timeslots": [
                {
                "start_time": "8:00",
                "end_time": "8:30"
                },
                {
                "start_time": "8:30",
                "end_time": "9:00"
                },
                {
                "start_time": "9:00",
                "end_time": "9:30"
                },
                {
                "start_time": "9:30",
                "end_time": "10:00"
                },
                {
                "start_time": "10:00",
                "end_time": "10:30"
                },
                {
                "start_time": "10:30",
                "end_time": "11:00"
                }
            ]
            }
        ],
        "selected_location": 7
    }

const Test = (props) => {

    const [services, setServices] = useState([]);
    const [activeService, setActiveService]=useState(null)

    useEffect(() => {
        const getServices = async ()=>{
            try{
                let response = await Services.get({
                    include_deleted: false,
                    search_words: null
                })
                if(response.data) setServices(response.data.services);
                console.log(response.data)
            }catch(ex){
                console.error(ex)
            }
        }

        getServices();

    },[]);

    return (
        // <Container fluid>
        //     PAGE ID: {props.page_id}
        //     <Row className="body">
        //         <Col id="content-goes-here">
        //         </Col>
        //     </Row>
        // </Container>
        <Card className="content-card">
            <div className="d-flex flex-wrap">
                {services?.length > 0 &&
                    services?.map((service)=>{
                        let price;
                        if (service?.products?.length === 1) price = service?.products[0]?.variants[0]?.price;
                        else if (service?.products?.length > 1) price = service?.products.map(product => product.variants[0].price);
                        return(
                            <ProductCard 
                                key={`service-${service.id}`}   
                                item={service}
                                type={0}
                                name={service?.name}
                                price={price}
                                click={()=>setActiveService(service)}
                            />
                        )
                    })
                }
            </div>

            <BookingDescription 
                service={activeService}
                onClickBack={()=>console.log("stuff")}
                tokens={[]}
                linkToBundles={<button>Hi</button>}
            />
            <TimeslotGrid 
                location={7}
                startDate={new Date("2025-09-16")}
                endDate={new Date("2025-09-019")}
                minHour={8}
                maxHour={11}
                conflictEvents={[]}              
                service={fakeService}
                showConflictInfo={false}
            />
        </Card>
    );
};

export default Test;