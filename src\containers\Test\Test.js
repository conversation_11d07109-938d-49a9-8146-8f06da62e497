import React, {useState, useEffect} from 'react';
import { useSelector } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Image from 'react-bootstrap/Image';
import Button from 'react-bootstrap/Button';
import Container from 'react-bootstrap/Container';
import { Card } from 'react-bootstrap';
import Timeslots from '../Service/Booking/Timeslots';
import Selection from '../Service/Booking/Selection';
import TimeslotGrid from '../Service/Booking/TimeslotGrid';
import { BookingDescription } from '../Service/Components/Booking/BookingDescription';
import ProductCard from '../../containers/POS/Items/Products'
import WeeklyDateScroller from '../../components/common/WeeklyDateScroller';
import { format, endOfWeek, startOfWeek, subWeeks, addWeeks, differenceInMinutes, parseISO, formatISO } from 'date-fns';
import NewUserTypeahead from '../../components/Typeahead/NewUserTypeahead';
import Tooltip from '../../components/common/Tooltip';
import Events from '../../api/Events';
import Services from '../../api/Services'
import Users from '../../api/Users'
import POS from '../../api/Pos'
import Locations from '../../api/Locations';
import { generateServiceBlockTimeslots } from '../../utils/dates';


// export const Gladius = () => (
//     <Container fluid>
//         <Row className="mb-4">
//             <Col className="text-right">
//                 <Button variant="light" size="sm"><i className="far fa-arrow-to-bottom"></i>Export</Button>
//                 <Button variant="light" size="sm"><i className="far fa-share-alt"></i>Share</Button>
//             </Col>
//         </Row>
//         <Row>
//             <Col>
//                 <div className="card">
//                     <h1>Scientia Et Gladius</h1>
//                     <p>
//                         I'm baby blog kogi prism bicycle rights subway tile helvetica selvage. Thundercats cray la croix, plaid seitan food truck chartreuse street art. Jean shorts brooklyn bicycle rights disrupt stumptown farm-to-table enamel pin gastropub unicorn prism. Artisan ennui cray photo booth. Pinterest fixie taxidermy lumbersexual ennui, vexillologist beard listicle. Blog ramps offal, twee jean shorts narwhal PBR&B neutra affogato XOXO synth normcore.                
//                     </p>
//                     <p>
//                         Migas biodiesel pickled listicle wayfarers. Offal mixtape coloring book, church-key art party plaid lomo wolf shaman cred master cleanse taxidermy irony whatever hoodie. Marfa twee neutra taxidermy 90's sustainable, celiac vegan vape next level tumblr selvage. Yr leggings vegan, franzen kogi raw denim hot chicken shoreditch 8-bit gochujang selfies affogato biodiesel ennui. Venmo narwhal cloud bread sartorial craft beer. IPhone butcher hoodie tote bag poke fashion axe wayfarers adaptogen vice kickstarter activated charcoal coloring book sartorial thundercats succulents.
//                     </p>
//                 </div>

//             </Col>
//         </Row>
//     </Container>
// );

// export const WizPig = () => (
//     <Container fluid>
//         <Row>
//             <Col className="text-left">
//                 <h1 className="pb-0">Test it!</h1>
//                 {/* <Image src={require("peep.png").default} fluid /> */}
//             </Col>
//         </Row>
//     </Container>
// );


// const data=[
// 			{
// 				"Test":{
// 					"props":{
// 						"text1": "This is not a",
// 						"text2": "This is a"
// 					}
// 				}
// 			},
//             {
//                 "div":{
//                     "props":{
//                         "className":"card"
//                     },
//                     "content":[
//                         {
//                             "span":{
//                                 "innerText":"This is a span"
//                             }
//                         },
//                         {
//                             "contentBlock":{
//                                 "id":2
//                             }
//                         }            
//                     ]
//                 }
//             }
// 		]


const Test = (props) => {

    const [services, setServices] = useState([]);
    const [activeService, setActiveService]=useState(null);
    const [dateWeek, setDateWeek]=useState(new Date());
    const [selectedUser, setSelectedUser]=useState(null);
    const [userTokens, setUserTokens]=useState([]);
    const [selectedBookings, setSelectedBookings]=useState([]);
    const [selectedLocation, setSelectedLocation]=useState(null);
    const [matchedTokens, setMatchedTokens]=useState({text: <></>, matchedTokens: []});

    useEffect(() => {
        const getServices = async ()=>{
            try{
                let response = await Services.get({
                    start_date: formatISO(new Date()),
                    max_record: 99,
                    include_deleted: false,
                    search_words: null
                })
                if(response.data && response.status === 200) setServices(response.data.services);
                else if (response.errors) handleError(response.errors)
                else handleError("Unknown error getting services")
            }catch(ex){
                console.error(ex)
            }
        }

        getServices();

    },[]);

    const handleError=(error)=>{

    }

    const handleSelectedUser=(selection)=>{
        setSelectedUser(selection[0]);
        getUserTokens(selection[0]?.id);
    }

    const addUserTokenToCart=()=>{
        //need a register id
        //need to add it to the cart
    }

    const getUserTokens=async (id)=>{   
        //need to get user tokens and see if the user has the token
        //if so, can use the tokens for the booking
        try{
            let response = await Users.tokens({
                user_id: id
            })
            if(response.status === 200 && response.data) {
                setUserTokens(sortTokens(response.data))
                checkServiceAndUserTokens(response.data, activeService.products, selectedBookings)
            }
            else if (response.errors) handleError(response.errors)
            else handleError("Unknown error getting user tokens")
        }catch(ex){
            console.error(ex)
            handleError(ex)
        }
    }

    const sortTokens = (tokens) => {
        if (!tokens || !Array.isArray(tokens)) return [];

        const grouped = tokens.reduce((acc, token) => {
            const key = token.product_name;

            if (!acc[key]) {
                acc[key] = {
                    id: token.product_id,
                    product_name: token.product_name,
                    number_of_tokens: 0,
                    exp_dates: [],
                    product_variant_id: token.product_variant_id
                };
            }

            acc[key].number_of_tokens++;
            if (token.exp_date) {
                acc[key].exp_dates.push(token.exp_date);
            }

            return acc;
        }, {});

        return Object.values(grouped).map(group => {
            let soonest_exp = null;

            if (group.exp_dates.length > 0) {
                // Find the earliest date
                soonest_exp = group.exp_dates.reduce((earliest, current) => {
                    const currentDate = new Date(current);
                    const earliestDate = new Date(earliest);
                    return currentDate < earliestDate ? current : earliest;
                });
            }

            return {
                id: group.id,
                product_name: group.product_name,
                number_of_tokens: group.number_of_tokens,
                soonest_exp: soonest_exp,
                variant_id: group.product_variant_id
            };
        });
    }

    const handleSlotChange=(slots)=>{
        console.log(slots)
        setSelectedBookings(slots);
        checkServiceAndUserTokens(userTokens, activeService.products, slots)
    }

    useEffect(()=>{
        console.log("selectedbookings userEffect", selectedBookings, selectedBookings.length)
    })

    const handleBooking=async(token)=>{
        console.log("user tokens", userTokens)
        let adaptedTimeSlot = adaptTimeSlots();
        debugger
        const bookingObject ={
            service_id: activeService.id,
            location_id:activeService.location_ids[0], 
            user_id: selectedUser.id,
            start_datetime: formatISO(adaptedTimeSlot?.start),
            end_datetime: formatISO(adaptedTimeSlot?.end),
            token_quantity: selectedBookings?.length,
            product_variant_id: token.variant_id
        }
        console.log("booking object",bookingObject)
        try{
            let response = await POS.payment.service_tokens(bookingObject);
            console.log(response)
        }catch(ex){
            console.error(ex)
            handleError(ex)
        }
    }

    const adaptTimeSlots=()=>{
        let obj = { start: null, end: null, duration: 0 };
        selectedBookings.forEach(slot=>{
            let start = parseISO(slot.start);
            let end = parseISO(slot.end);
            if (obj.start===null || start < obj.start) {
                obj.start = start;
            }
            if (obj.end===null || end > obj.end) {
                obj.end = end;
            }
            obj.duration = differenceInMinutes(obj.end, obj.start);
            return slot;
        });
        return obj;
    }

    //if a service takes more than one token, notify staff if there is a cheaper option
    const checkForCheapestToken=(service)=>{
        if(service?.products?.length > 1){
            let token = service?.products.reduce((acc, product) => {
                const currentPrice = product.variants[0].price; //tokesn should only ever have one variant
                const accPrice = acc.variants[0].price;
                return currentPrice < accPrice ? product : acc;
            })?.id;
            return token;
        }
    }

    //get the locations for things like name after selecting the active service
    const getLocations=async(service)=>{
        service.locations = [];
        try{
            let response = await Locations.get() //endpoint can only do one at a time, doesn't work with an array of ids so we get them all and filter
            if(response.data && response.status === 200) {
                let locations = response.data.filter(location => service.location_ids.includes(location.id));
                service.location_info = locations;
                setSelectedLocation(locations[0].id); //If there are multiple, we'll just start with the first one because it can be changed anyway
            }
            else if (response.errors) handleError(response.errors)
            else handleError("Unknown error getting locations")
            setActiveService(service);
            checkServiceAndUserTokens(userTokens, service.products, selectedBookings)
        }catch(ex){
            handleError(ex)
        }
    }

    const setBlocks = (service)=>{
        let newBlocks = generateServiceBlockTimeslots(service);
        service.blocks = newBlocks;
    }

    const handleServiceSelect=(service)=>{
        setBlocks(service);
        getLocations(service);
    }

    const checkServiceAndUserTokens=(userTokens, tokensNeeded, slots)=>{
        // const tokensNeeded = activeService?.products;
        let returned ={
            text: <></>,
            matched: []
        }

        tokensNeeded.forEach(token =>{
            userTokens.forEach((userToken)=>{
                if(token?.id === userToken?.id) returned.matched.push(userToken);
            })
        })

        if(returned?.matched?.length > 0) {
            returned.text = (
                <div>
                    <p>
                        <span>The user needs a total of {slots?.length} tokens.</span>
                        <span>
                            The user has {returned?.matched?.length} type of token{returned?.matched?.length > 1 ? "s" : ""} 
                            that match{returned?.matched?.length > 1 ? "" : "es"} the service:
                        </span>
                    </p>

                    {returned?.matched.map((token, index) => (
                        <p key={index}>
                            The user has {token?.product_name} that can be used for this service. They have {token?.number_of_tokens}/{slots?.length} tokens/required.
                            {token?.number_of_tokens >= slots?.length ?
                            <p>
                                <span> They have enough tokens to cover the booking.</span>
                                <Button onClick={()=>handleBooking(token)}>Use These Tokens to Book</Button>
                            </p>
                            :
                                <span> They do not have enough tokens to cover the booking.</span>
                            }
                        </p>
                    ))}
                </div>
            )
        }else if(returned?.matched?.length === 0) {
            returned.text = (
                <span>
                    The user has no matching tokens that can be used for this service.
                </span>
            )
        }
        setMatchedTokens(returned);
    }

    //what tokens doe the services require (and how many do we need)
    //does the user have tokens at all
    //what tokens does the user have
    //return if we have enough or not


    //do the booking first, hold the slot
    //fix the strange gap on the button/date
    //make the timeslots look better
    //see if any of the rest of the html elements are better served as components
    //do the add to cart process
    //elegantly error handle
    //override or hast tokens, the booking happens then and there

    return (
        <Card className="content-card">
            <NewUserTypeahead 
                passSelection={(selection)=>handleSelectedUser(selection)} 
                multiple={false}
            />
            <div className="d-flex flex-wrap">
                {services?.length > 0 &&
                    services?.map((service)=>{
                        let price;
                        if (service?.products?.length === 1) price = service?.products[0]?.variants[0]?.price;
                        else if (service?.products?.length > 1) price = service?.products.map(product => product.variants[0].price);
                        return(
                            <ProductCard 
                                key={`service-${service.id}`}   
                                item={service}
                                type={0}
                                name={service?.name}
                                price={price}
                                click={()=>handleServiceSelect(service)}
                            />
                        )
                    })
                }
            </div>
            {userTokens?.length > 0&& selectedUser && 
                <div>
                    {selectedUser?.first_name} currently has the following tokens:
                    <ul>
                        {userTokens?.map((tokens)=>(
                            <li key={`each-token-${tokens.id}`}>
                                {tokens?.number_of_tokens} {tokens.product_name} token{tokens?.number_of_tokens > 1 ? "s " : " "} 
                                that {tokens?.soonest_exp ? `expire starting on ${format(new Date(tokens?.soonest_exp), "MM/dd/yyyy")}` : "do not expire"}
                            </li>
                        ))}
                    </ul>
                    {/* <p>
                        <label htmlFor="override-tokens-true">Override Tokens</label>
                        <Tooltip 
                            text="If you select this option, the user will just be added to the booking and not need to purchase a token."
                        >
                            <i className="far fa-question-circle ms-1"/>
                        </Tooltip>
                        <input 
                            type="radio" 
                            id="override-tokens-true" 
                            name="override-tokens" 
                            value={1}
                            onChange={(e)=>console.log(e.target.value)}
                            label="Override"
                        />
                        <input 
                            type="radio" 
                            id="override-tokens-false" 
                            name="override-tokens" 
                            value={0}
                            onChange={(e)=>console.log(e.target.value)}
                        />
                    </p> */}
                </div>
            }
            {selectedBookings && selectedBookings?.length > 0 &&
                <>
                    {matchedTokens.text}
                </>
            }
            {activeService && activeService?.products?.map((token)=>{
                let price;
                let cheapestId = checkForCheapestToken(activeService);
                if(token.id === cheapestId) price = `$${token.variants[0].price} (This is the cheapest option)`;
                else price = token.variants[0].price;
                return(
                    <div>
                        <ProductCard
                            key={`token-${token.id}`}
                            item={token}
                            type={0}
                            name={token?.name}
                            price={price}
                            click={()=>console.log("stuff")}
                        />
                    </div>
                )
            })}
            {activeService &&
                <BookingDescription 
                    service={activeService}
                    onClickBack={()=>console.log("stuff")}
                    tokens={[]}
                    linkToBundles={null}
                />
            }
            <WeeklyDateScroller 
                backDisabled={false}
                smallScreen={false}
                selectedDate={null}
                onDateBack={()=>setDateWeek(subWeeks(dateWeek, 1))}
                onDateNext={()=>setDateWeek(addWeeks(dateWeek, 1))}
                onChangeDate={()=>console.log("change")}
                rangeStartDate={startOfWeek(dateWeek)}
                rangeEndDate={endOfWeek(dateWeek)}
            />
            
            {activeService &&
                activeService.location_info?.map((info)=>(
                    <Button key={`location-${info.id}`} onClick={()=>setSelectedLocation(info.id)}>
                        {info?.name}
                    </Button>
                ))
            } 

            <button onClick={()=>{handleBooking()}}>
                Book Slot
            </button>
            {activeService && selectedUser &&           
                <TimeslotGrid 
                    location={selectedLocation}
                    startDate={startOfWeek(dateWeek)}
                    endDate={endOfWeek(dateWeek)}
                    minHour={6}
                    maxHour={18}
                    conflictEvents={[]}              
                    service={activeService}
                    showConflictInfo={true}
                    onSlotChange={(change)=>handleSlotChange(change.selected_slots)}
                />
            }
        </Card>
    );
};

export default Test;