import React, {useState, useEffect, useCallback, useRef} from 'react';
import { useSelector } from 'react-redux';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Image from 'react-bootstrap/Image';
import Button from 'react-bootstrap/Button';
import Container from 'react-bootstrap/Container';
import Card from 'react-bootstrap/Card';
import { Form, Modal } from 'react-bootstrap';

import { format, differenceInYears } from 'date-fns';

import Events from '../../api/Events';
import User from '../../api/Users';
import Groups from '../../api/Groups';
import ImageViewer from '../../components/common/ImageViewer';
import { UserCreateEdit } from '../../components/UserCreateEdit/UserCreateEdit';

import styles from "./Test.module.scss"

// export const Gladius = () => (
//     <Container fluid>
//         <Row className="mb-4">
//             <Col className="text-right">
//                 <Button variant="light" size="sm"><i className="far fa-arrow-to-bottom"></i>Export</Button>
//                 <Button variant="light" size="sm"><i className="far fa-share-alt"></i>Share</Button>
//             </Col>
//         </Row>
//         <Row>
//             <Col>
//                 <div className="card">
//                     <h1>Scientia Et Gladius</h1>
//                     <div>
//                         I'm baby blog kogi prism bicycle rights subway tile helvetica selvage. Thundercats cray la croix, plaid seitan food truck chartreuse street art. Jean shorts brooklyn bicycle rights disrupt stumptown farm-to-table enamel pin gastropub unicorn prism. Artisan ennui cray photo booth. Pinterest fixie taxidermy lumbersexual ennui, vexillologist beard listicle. Blog ramps offal, twee jean shorts narwhal PBR&B neutra affogato XOXO synth normcore.                
//                     </div>
//                     <div>
//                         Migas biodiesel pickled listicle wayfarers. Offal mixtape coloring book, church-key art party plaid lomo wolf shaman cred master cleanse taxidermy irony whatever hoodie. Marfa twee neutra taxidermy 90's sustainable, celiac vegan vape next level tumblr selvage. Yr leggings vegan, franzen kogi raw denim hot chicken shoreditch 8-bit gochujang selfies affogato biodiesel ennui. Venmo narwhal cloud bread sartorial craft beer. IPhone butcher hoodie tote bag poke fashion axe wayfarers adaptogen vice kickstarter activated charcoal coloring book sartorial thundercats succulents.
//                     </div>
//                 </div>

//             </Col>
//         </Row>
//     </Container>
// );

// export const WizPig = () => (
//     <Container fluid>
//         <Row>
//             <Col className="text-left">
//                 <h1 className="pb-0">Test it!</h1>
//                 {/* <Image src={require("peep.png").default} fluid /> */}
//             </Col>
//         </Row>
//     </Container>
// );


// const data=[
// 			{
// 				"Test":{
// 					"props":{
// 						"text1": "This is not a",
// 						"text2": "This is a"
// 					}
// 				}
// 			},
//             {
//                 "div":{
//                     "props":{
//                         "className":"card"
//                     },
//                     "content":[
//                         {
//                             "span":{
//                                 "innerText":"This is a span"
//                             }
//                         },
//                         {
//                             "contentBlock":{
//                                 "id":2
//                             }
//                         }            
//                     ]
//                 }
//             }
// 		]

// const Test = (props) => {
//     return (
//         <Container fluid>
//             PAGE ID: {props.page_id}
//             <Row className="body">
//                 <Col id="content-goes-here">
//                 </Col>
//             </Row>
//         </Container>
//     );
// };

const ParentComponent = (props)=>{

    // const eventId = 7107; //variat event
    // const eventId = 7047 //event with users 
    const eventId = 7113 //event with questions
    // const eventId = 7103 //event with age range
    const user = useSelector(state => state?.auth?.user?.profile);
    const loadingStates = useRef({
        eventDetails: false,
        family: false,
        user: false
    })
    const [eventDetails, setEventDetails]=useState(null);
    const [selectedFamily, setSelectedFamily]=useState([]);

    const handleFamilyClick=(clickedFamilyId)=>{
        if(selectedFamily?.length && selectedFamily.includes(clickedFamilyId)){
            setSelectedFamily(selectedFamily.filter((family)=>family !== clickedFamilyId))
        }else{
            setSelectedFamily([...selectedFamily, clickedFamilyId])
        }
    }

    return(
        <>
            {/* invite link should be handled at the parent level */}
            {/* we need event details to be consistent */}
                <EventDetails 
                    eventId={eventId} 
                    setEventDetails={setEventDetails} 
                    eventDetails={eventDetails}
                    loadingStates={loadingStates}
                />
            {/* We need to get family details */}
            {/* We need the checks for being already registered and the proper age to be consistent */}
                <EventFamily 
                    user={user}
                    eventId={eventId}
                    eventDetails={eventDetails}
                    loadingStates={loadingStates}
                    handleFamilyClick={handleFamilyClick}
                    selectedFamily={selectedFamily}
                />

            {/* we need to allow the ability to create new users here */}
            {/* We also need to make sure there's a family because...the backened doesn't handle it */}


            {/* 
                we need to allow the user to be selected from among those who are valid.  
                Those who are not valid need to say why 
                Select multi can apply here (checkmark like CMS)
                Questions need to be answered for each 
                Registration endpoints and such need to happen for each.  
            */}

            {/* we need to see/answer any required questions for the event */}
                
            {/* we need to be confirmed if there's no price assiated with a click */}

            {/* If there is a price association, we need to offer "pay now" or "pay later" */}

            {/* If pay later, registration for the event needs to occur*/}

            {/* when an event is paid for, the registration needs to switch to confirmed        */}
        </>

    )
}

const EventDetails = ({eventId, eventDetails, setEventDetails, loadingStates, ...props})=>{

    const adjustTimeString=useCallback((event)=>{
        let startFormatted = format(new Date(event?.start_datetime), "ccc MM/dd/yyyy");
        let endFormatted = format(new Date(event?.end_datetime), "ccc MM/dd/yyyy");
        if(startFormatted === endFormatted) {
            startFormatted = format(new Date(event?.start_datetime), "ccc MM/dd/yyyy hh:mm aa");
            endFormatted = format(new Date(event?.end_datetime), "hh:mm aa");
        }
        return (startFormatted + " - " + endFormatted);
    },[]);

    const variantPriceArray = useCallback((event)=>{
        if(event?.variants?.length > 0){
            let priceArray = []
            for(let i = 0; i < event.variants.length; i++){
                priceArray.push(event?.variants[i]?.price)
            }
            priceArray.sort((a,b)=>a - b)
            return priceArray
        }else{
            return [event?.default_variant_price]
        }
    },[])

    useEffect(()=>{
        //use the public api here so details can be SEEN in any case
        const getEventBasics=async()=>{
            loadingStates.current.eventDetails = true;
            try{
                let response = await Events.publicGet({id: eventId});
                if(response?.data?.events) {
                    let event = response?.data?.events?.filter((event)=>event.id === eventId)[0]
                    if(event.images.length > 0) event.images.forEach((image)=>image.url = image.preview_url)
                    event.timeString = adjustTimeString(event);
                    event.eventPricesOrder = variantPriceArray(event);
                    //This needs to get both the custom fields and the users registered for the event.
                    event.advancedDetails= await getAdvancedEventDetails();
                    setEventDetails(event);
                    loadingStates.current.eventDetails = false;
                }
            }catch(ex){
                console.error(ex)
                loadingStates.current.eventDetails = "ERROR";
            }
        }

        const getAdvancedEventDetails = async()=>{
            try{
                let response = await Events.getSingle({id: eventId});
                if(response?.data) {
                    let neededDetails = {
                        users: response.data[0].users,
                        custom_fields: response.data[0].custom_fields
                    }
                    return neededDetails;
                }
            }catch(ex){
                console.error(ex);
                loadingStates.current.eventDetails = "ERROR";
            }
        }

        if(loadingStates.current.eventDetails === false) getEventBasics();
        //we don't want to trigger with "loadingStates"
        /*eslint-disable-next-line react-hooks/exhaustive-deps*/ 
    },[eventId, adjustTimeString, variantPriceArray, setEventDetails]);


    return(
        <div className={styles["event-details-wrapper"]}>
            {eventDetails &&
                <div>
                    <div className={styles["detail-pair"]} data-cy="event-status-details">
                        {(eventDetails?.event_status_id === 1 || eventDetails?.event_status_id === 3 || eventDetails?.event_status_id === 4 || eventDetails?.event_status_id === 6 || eventDetails?.event_status_id===8) && 
                            <h6>At this time, registration for this event is not available due to its {eventDetails?.event_status_name?.toLowerCase()} status.  If you have any questions, don't hesitate to reach out.</h6>
                        }
                        <h2 data-cy="register-event-name">{eventDetails?.name}</h2>
                        {eventDetails?.event_status_id === 5 && 
                            <h6>This is a private event 
                                {eventDetails.requires_registration ===1 &&
                                    <span>
                                        and can only be registered for via a direct invitation.                                
                                    </span>
                                }
                            </h6>
                        }
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-image-viewer">
                        <ImageViewer 
                            images={eventDetails?.images}
                            largeImgMax={"200px"}
                            thumbMax={"50px"}
                            thumbLimit={4}
                        />
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-when">
                        <label>
                            When
                        </label>
                        {eventDetails.start_datetime && 
                            <p>
                                {eventDetails.timeString}
                            </p>
                        }
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-where">
                        <label>
                            Where
                        </label>
                        <p>
                            {eventDetails.location_name}
                        </p>
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-age-requirement">
                        <label>
                            Age Requirement
                        </label>
                        <p data-cy="event-details-ages">
                            {eventDetails.min_age > 0 && eventDetails.max_age > 0 && 
                                `Participant must be between ${eventDetails?.min_age} - ${eventDetails?.max_age} years old to register`
                            }{eventDetails.min_age > 0  && !eventDetails.max_age &&
                                `Participant must be at least ${eventDetails?.min_age} years old to register`
                            }{!eventDetails.min_age && eventDetails.max_age > 0 &&
                                `Participant must be no more than ${eventDetails?.max_age} years old to register`
                            }{!eventDetails.min_age && !eventDetails.max_age &&
                                `There is no age requirement.`
                            }
                        </p>
                    </div>
                    {eventDetails.requires_membership === 1 && 
                        <div className={styles["detail-pair"]} data-cy="event-details-membership">
                            <label>
                                Requires Membership
                            </label>
                            <p>
                                Requires membership to attend
                            </p>
                        </div>
                    }
                    <div className={styles["detail-pair"]} data-cy="event-details-registration">
                        <label>
                            Requires Registration
                        </label>
                        {eventDetails.requires_registration ===1 ?
                            <p>
                                Registration is required for this event
                            </p>
                            :
                            <p>
                                No registration required
                            </p> 
                        }
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-description">
                        <label>
                            What
                        </label>
                        <div dangerouslySetInnerHTML={{__html: eventDetails?.description}} />
                    </div>
                    <div className={styles["detail-pair"]} data-cy="event-details-price">
                        <label>
                            Price
                        </label>
                        <p>
                            {eventDetails?.eventPricesOrder?.length > 1 ?
                                <span>
                                    From ${eventDetails?.eventPricesOrder[0]} to ${eventDetails?.eventPricesOrder[eventDetails?.eventPricesOrder?.length-1]}
                                </span>
                                :
                                <span>
                                    ${eventDetails?.eventPricesOrder[0]}
                                </span>
                            }
                        </p>
                    </div>
                </div>
            }
        </div>
    )
}

const EventFamily=({eventDetails, user, loadingStates, handleFamilyClick, selectedFamily, ...props})=>{

    const [family, setFamily]=useState(null);
    const [allFamilyMembers, setAllFamilyMembers]=useState([]);
    const [showNewUser, setShowNewUser]=useState(null);
  
    /** We need this because the other calls for group don't include the group member roles to know when the user is an admin or not becuase it's different for each group type and we don't wnat to hardcode */
    const getIndividualFamily=async(groupId)=>{
        try{
            let response = await Groups.get({id: groupId})
            if(response.data[0]) return response.data[0]
        }catch(ex){
            console.error(ex)
        }
    }

    /**If need be, we make a new family */
    const createNewFamilyGroup=async()=>{
        try{
            //this should, in theory, add the auth user because the group_member_role_id is included (according to the docs)
            let response = await Groups.create({
                name: user?.last_name + " Family",
                description: "Family Group",
                group_type_id: 4,
                group_status_id: 1,
                group_member_role_id: 10
            })
            if(response.status === 200) getFamily();
        }
        catch(ex){
            console.error(ex)
        }
    }

    const checkFamilyAllowedByRole=async(family)=>{
        //the status of the user logged in for the family being checked
        let currentUserStatusId = family.group_members?.filter((member)=>member.user_id === user.id)[0]?.group_member_role_id; 
        //if the role for that group type is an admin;
        if(family?.group_member_roles?.filter((role)=>role.id === currentUserStatusId)[0]?.is_admin === 1) return true;
        else return false;  
    }

    const getFamily=useCallback(async()=>{
        loadingStates.current.family = true;
        let familyGroups = [];
        try{
            let response = await Groups.groupFilter({filters:{user_id: user.id}})
            //get rid of the groups that are not family and the user doesn't have active status
            familyGroups = response.data.groups?.filter((group)=>group.group_type_name === "Family" && group?.group_status_id === 1)
            //if we don't have a family, we make one
            if(familyGroups.length === 0) await createNewFamilyGroup();
            else{
                let finalGroups = []
                for(let i = 0; i < familyGroups.length; i++){
                    console.log(familyGroups[i])
                    let extraDetails = await getIndividualFamily(familyGroups[i].id)
                    familyGroups[i].tags = extraDetails?.tags || [];
                    familyGroups[i].group_member_roles = extraDetails.group_member_roles || [];
                    //check permission to manage the family (is_admin) first
                    if(await checkFamilyAllowedByRole(familyGroups[i])){
                        finalGroups.push(familyGroups[i])
                    }
                }
                setFamily(finalGroups);
            }
            loadingStates.current.family = false;
        }catch(ex){
            console.error(ex)
            loadingStates.current.family = "ERROR";
        }
    },[user.id, loadingStates]);

    //check if the user is allowed by age
    const checkAllowedAge = useCallback((user, min, max)=>{
        const age = differenceInYears(new Date(), new Date(user.dob));
        let allowed={
            isAllowed: false,
            notAllowedReason: ""
        };
         if(min || max){
            if(min && max){
                allowed.isAllowed = age >= min && age <= max ? true : false;
                allowed.notAllowedReason = "Participant is not within the age range";
            }else if(min){
                allowed.isAllowed = age >= min ? true : false;
                allowed.notAllowedReason = "Participant is below the age limit";
            }else if(max){
                allowed.isAllowed = age <= max ? true : false;
                allowed.notAllowedReason = "Participant is above the age limit";
            }else allowed = true;
        }
        return allowed;
    },[])

    //check if the user is allowed if they're registered or not
    const checkIsRegistered = useCallback((user)=> {
        let isRegistered = eventDetails?.advancedDetails?.users?.filter((member)=> member.id === user.user_id)
        if(isRegistered.length > 0) return true;
        else return false;
    },[eventDetails]);

    const eachFamilyMemberCallback = useCallback(async(checkedFamily, i)=>{
        let memberDetails;
        let memberArray = []
        try{
            checkedFamily?.group_members.forEach((member)=>{
                memberDetails = {
                    key: i + 2,
                    id: member.user_id,
                    firstName: member.first_name,
                    lastName: member.last_name,
                    name: member.first_name + " " + member.last_name,
                    isSelf: false,
                    roleName: member.group_member_role_name,
                    ageAllowed: checkAllowedAge(member, eventDetails.min_age, eventDetails.max_age),
                    registered: checkIsRegistered(member)
                }
                memberArray.push(memberDetails)
            })
            
            return memberArray;
        }catch(ex){
            console.error(ex)
        }
    },[checkAllowedAge, checkIsRegistered, eventDetails])

    const checkFamilyStatuses=useCallback(async()=>{
        loadingStates.current.family = true;
        let familyMembers = [];
        //at the very least, self will be added to the family array
        familyMembers.push({
            key: 1,
            id: user.id,
            firstName: user.first_name,
            lastName: user.last_name,
            name: user.first_name + " " + user.last_name,
            isSelf: true,
            ageAllowed: checkAllowedAge(user, eventDetails.min_age, eventDetails.max_age),
            registered: checkIsRegistered(user),
        });

        //we need to know if this family member has the credentials to register their family for the event.
        for(let i = 0; i < family.length; i++){
            //need to get the family
            let familyMember = await eachFamilyMemberCallback(family[i], i)
            if(familyMember?.length) familyMembers=([...familyMembers, ...familyMember])
        }
        //filter by unique
        familyMembers = familyMembers.filter((object, index, self) =>
            index === self.findIndex((member) => member.id === object.id)
        );
        setAllFamilyMembers(familyMembers)
        loadingStates.current.family = false;
    },[eventDetails, family, user, checkAllowedAge, checkIsRegistered, eachFamilyMemberCallback, loadingStates]);

    //this will be the first load - family will only be null on first load, otherwise it should be an array
    useEffect(()=>{
        if(loadingStates.current.family === false && !family){
            getFamily();
        }
    },[getFamily, loadingStates, family]);

    useEffect(()=>{
        if(family && eventDetails && loadingStates.current.family === false){
            checkFamilyStatuses();
        }
    },[family, eventDetails, loadingStates, checkFamilyStatuses]);

    const toggleNewUser=(groupId)=>{
        setShowNewUser(groupId);
    }

    const handleNewFamily=()=>{
        setShowNewUser(null);
        getFamily();
    }

    return(
        <>
            {family && family.length > 0 &&
                <>
                    {family?.map(group=>(
                        <Button onClick={()=>toggleNewUser(group.id)}>
                            Add New Family Member to {group.name} ({group.id})
                        </Button>
                    ))}
                </>
            }
            {
                allFamilyMembers?.length > 0 &&
                <>
                    {allFamilyMembers.map((member, i) => (
                        <p key={`family-member-${i}`}>
                            <input
                                type="checkbox"
                                id={`family-member-${i}`}
                                checked={selectedFamily?.includes(member.id)}
                                onChange={(e)=>handleFamilyClick(member.id)}
                            />
                            {" "}
                            <span>
                                {member.name} {" "}
                                {member.isSelf ? 
                                    "(Me)" 
                                : 
                                    <span>({member.roleName})</span>
                                }
                            </span>
                            <br />
                            {member.registered ? "Registered" : "Not Registered"}
                            <br />
                            {member?.ageAllowed?.isAllowed ? "" : member?.ageAllowed?.notAllowedReason}
                            {selectedFamily?.length > 0 && selectedFamily?.includes(member.id) ?
                                <>
                                    <EventCustomFields 
                                        currentUser = {member}
                                        eventDetails={eventDetails}
                                        loadingState={loadingStates}
                                    />
                                    {member.id}
                                </>
                                :
                                <>
                                </>
                            }
                        </p>
                    ))}
                </>
            }
            <Modal show={showNewUser ? true : false} onHide={()=>setShowNewUser(null)}>
                <UserCreateEdit 
                    afterSubmit={handleNewFamily}
                    groupId={showNewUser}
                />
            </Modal>
        </>
    )
}

const EventCustomFields=({eventDetails, loadingStates, currentUser, ...props})=>{

    if(loadingStates?.current?.eventDetails === false) console.log(eventDetails?.advancedDetails?.custom_fields)
    
    console.log(currentUser)

    const answersRef=useRef({user_id: currentUser.id})

    const handleChange=(field, answer)=>{
        answersRef.current.answers[field] = answer;
    }
    
    return(
        <div>
            {eventDetails && eventDetails?.advancedDetails?.custom_fields &&
                <>
                    {eventDetails?.advancedDetails?.custom_fields?.map((field, i) =>(
                        <Form.Group 
                            controlId={`${field.name}_${currentUser?.id}`} 
                            key={`custom-field-group--${currentUser?.id}-${field.id}-${i}`}
                        >
                            <Form.Label>
                                {field.placeholder_text} 
                                {field.required ? 
                                    <span className="required-star"> * </span>
                                    : 
                                    <></>
                                }
                            </Form.Label>
                            {field.custom_field_type === "select" &&
                                <Form.Control 
                                    as="select" 
                                    custom name={`custom_${field.name}_${currentUser?.id}`} 
                                    placeholder={field.placeholder_text} 
                                    required={field.required? true : undefined} 
                                    onBlur={handleChange}
                                >
                                    <option key={`custom-select-${field.id}-${currentUser?.id}-x`} value=""></option>
                                    {field.options.map((option, i) => (
                                        <option key={`custom-select-${field.id}-${currentUser?.id}-${i}`} value={option.value}>{option.text}</option>
                                    ))}
                                </Form.Control>
                            }
                            {field.custom_field_type === "input" &&
                                <Form.Control 
                                    type="text" 
                                    required={field.required? true : undefined} 
                                    name={`custom_${field.name}_${currentUser?.id}`} 
                                    placeholder={field.placeholder_text} 
                                    onChange={handleChange} 
                                    onBlur={handleChange} 
                                />
                            }
                        </Form.Group>
                    ))}
                </>  
            }    
        </div>
    )
}

const Test = (props) => {
    return (
        <Card className="content-card">
            <ParentComponent />
        </Card>
    );
};

export default Test;