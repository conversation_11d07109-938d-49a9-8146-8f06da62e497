import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form } from 'react-bootstrap';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Name = ({ onChangeInput=()=>{} }) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);

    return (
        <div className="wizard">
            <span className="title">Coupon Information</span>
            <Row>
                <Col>
                    <Form.Label>Coupon Name</Form.Label>
                    <Form.Control
                        type="text"
                        id="name"
                        name="name"
                        value={coupon.name}
                        onChange={onChangeInput}
                        isInvalid={!!errors.name}
                    />
                    <div className={`err ${!!errors.name ? "" : "hidden"}`}>
                        {errors.name}
                    </div>
                </Col>
                <Col>
                    <Form.Label>Brief description (optional)</Form.Label>
                    <Form.Control
                        type="text"
                        id="description"
                        name="description"
                        value={coupon.description}
                        onChange={onChangeInput}
                        isInvalid={!!errors.description}
                    />
                    <div className={`err ${!!errors.description ? "" : "hidden"}`}>
                        {errors.description}
                    </div>
                </Col>
            </Row>
        </div>
    );
}

export default Name;