
import React, {useState, useEffect} from "react";
import { uniq } from "lodash";
import Tree from "../Tree";
import { filterTree, expandFilteredNodes, getIDsExpandFilter } from "./util/FilterTreeUtil";
import Card from "react-bootstrap/Card";
import Form from "react-bootstrap/Form";
import Nav from "react-bootstrap/Nav";
import Button from "react-bootstrap/Button";
import InputGroup from "react-bootstrap/InputGroup";

import './FilteredTree.css';

const FilteredTree = (props) => {
    const data = props.data;
    const click = props.linkOnClick;
    const referer = props.referer;
    const newButtonClickHandler = props.newButton;
    const [expanded, setExpanded] = useState(["root"]);
    const [selected, setSelected] = useState([]);
    const [subjectData, setSubjectData] = useState();
    //const [selectedSingleItem, setSelectedSingleItem] = React.useState("");

    useEffect(() => {
        setSubjectData(data);
    }, [data]);

    const onFilterMouseUp = (e) => {
        const value = e.target.value;
        const filter = value.trim();
        let expandedTemp = expanded;
        if (!filter) {
            setSubjectData(data);
            setExpanded(['root']);
            return;
        }

        let filtered = filterTree(data, filter);
        filtered = expandFilteredNodes(filtered, filter);
        if (filtered && filtered.children) {
            expandedTemp = [];
            expandedTemp.push(...getIDsExpandFilter(filtered));
        }
        setExpanded(uniq(expandedTemp));
        setSubjectData(filtered);
    };

    const handleSelect=(e)=>{
        e.preventDefault();
        if(!props.linkOnClick && props.setSelectedForEdit && e.target.dataset.id){
            const category={
                id: e.target.dataset.id,
                name: e.target.dataset.name,
                description: e.target.dataset.description,
                parent_id: e.target.dataset.parent,
                add_on_only: e.target.dataset.add_on_only ? 1 : 0,
                company_id: e.target.dataset.company
            }
            props.setSelectedForEdit(category)
        }
    }

    return (
        <div>
            <Card>
                <Form inline className="d-flex mb-2" onSubmit={e=>e.preventDefault()}>
                    <Form.Group className="flex-fill">
                        <Form.Label className="my-1 mr-2" htmlFor="search_input">Search</Form.Label>
                        <InputGroup>
                            <Form.Control
                                id="search_input"
                                placeholder={`Search`}
                                aria-label={`Search`}
                                onKeyUp={onFilterMouseUp}
                            />
                            {/*<InputGroup.Text><i className="far fa-search"/></InputGroup.Text>*/}
                        </InputGroup>
                    </Form.Group>
                    {/* {newButtonClickHandler && <Button variant="primary" onClick={newButtonClickHandler}>Add New</Button>} */}
                </Form>
                <Tree
                    data={subjectData}
                    expanded={expanded}
                    selected={selected}
                    handleSelect={handleSelect}
                    handleIconSelect={()=>{props.setSelectedForEdit(null)}}
                    linkOnClick={click}
                    referer={referer}
                    setExpandedNodeHasChanged={props.setExpandedNodeHasChanged}
                    expandedNodeHasChanged={props.expandedNodeHasChanged}
                />
            </Card>
        </div>
    );
};

export default FilteredTree;
