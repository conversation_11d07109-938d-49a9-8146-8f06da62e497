import React, { useState, useCallback } from 'react'
import Permissions from '../../api/Permissions';
import { Typeahead } from './Typeahead'

const ModuleTypeahead = ({
    async=false, 
    paginated=false, 
    multiple=false, 
    initialDataIds=null,
    ...props}) => {

    const [ loading, setLoading ]=useState(true);
    
    const getModules=useCallback(async()=>{
        let responseObj;
        try{
            let response = await Permissions.Modules.get();
            responseObj={
                data: response.data || null,
                errors: response.errors || null
            }
        }catch(ex){
            console.error(ex)
            responseObj = {
                data: null,
                errors: ex
            }
        }
        setLoading(false);
        return responseObj;
    },[])
    
    return (
        <Typeahead
            {...props}
            id="module-typeahead"
            makeRequest={getModules}
            async={async}
            multiple={multiple}
            paginated={paginated}
            initialDataIds={initialDataIds}
            placeholder={loading ? "Loading Modules..." : props.placeholder || "Search Modules"}
        />
    )
}

export default ModuleTypeahead