@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';

.droppable {
    margin: $main-padding 0;
}

.droppable .draggable {
    padding: 6px 12px;
    margin: 0 0 6px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* change cursor when dragging is enabled */
.droppable.enabled, .droppable.enabled .draggable {
    cursor: pointer;
}

.droppable.enabled.draggingOver {
    cursor: move;
}

/* each item drag box */
.droppable.enabled .draggable {
    background-color: $neutral-background-color !important;
    color: $neutral-color !important;
    /* prevent the text from being selected instead of dragging the box */
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    -khtml-user-select: none; /* Konqueror HTML */
    -moz-user-select: none; /* Old versions of Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently supported by Chrome, Edge, Opera and Firefox */
}
.droppable.enabled .draggable:hover {
    background-color: $primary-color !important;
}

.droppable.enabled .draggable.dragging {
    /* change background colour if dragging */
    background-color: $primary-color !important;
    color: $button-hover-color !important;
}

.droppable a {
    font-weight: $bold-font-weight;
    text-decoration: none !important;
    color: inherit;
    width: 100%;
}

.droppable a>div:hover {
    color: $button-hover-color !important;    
    background-color: $primary-color !important;
    text-decoration: none !important;

    i{
        color:$primary-inverse-color !important;
    }
}
.droppable.enabled a>div:hover {
    color: $button-hover-color !important;    
    background-color: $primary-color !important;
    text-decoration: none;
}


.droppable i {
    display: none;
}
.droppable.enabled i {
    display: block;
    color: $primary-font-color;
}

.btn.reorder-button {
    margin: 0 0 1.5rem 0;
}