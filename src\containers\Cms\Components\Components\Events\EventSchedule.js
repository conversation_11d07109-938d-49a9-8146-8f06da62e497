import React, {useState, useEffect, useMemo} from 'react';
import {<PERSON>, Col, Card, Button, Table} from 'react-bootstrap';
import {format} from 'date-fns';

import APIEvents from '../../../../../api/Events';

const EventSchedule = (props) => {
    let dummyData = useMemo(() => [], []);
    const {event_types} = props;

    // this should be in every component, its used to forward the click event to the builder if in preview mode
    let preview_click=null;
    if (props.is_preview && props.onClick){
        preview_click = props.onClick;
    }

    const [data, setData] = useState([]);

    useEffect(() => {
        if (props.is_preview && !dummyData.length){
            for (let i=0;i<5;i++){
                dummyData.push({
                    id: i+1,
                    name: `Event ${i+1}`,
                    start_datetime: new Date(new Date().getTime() + 1 * 24 * 60 * 60 * 1000),
                    end_datetime: new Date(new Date().getTime() + 2 * 24 * 60 * 60 * 1000),
                    description: "Placeholder description",
                    location_name: "Location 1",
                    image: null
                });            
            }
        }
    }, [props.is_preview, dummyData]);

    useEffect(() => {
        return () => {
            setData([]);
        }
    }, []);    

    useEffect(() => {        
        const _loadEvents = async () => {
            const response = await APIEvents.publicGet({
                start_datetime:"now",
                max_records:props?.limit || 10,
                sort_col:"start_datetime",
                sort_direction:"asc",
                event_types:event_types || null
            });

            if (response?.data?.events){
                setData(response.data.events);
            } 
        }

        if (props?.data) setData(props?.data);
        else {
            if (props.is_preview) setData(dummyData);
            else _loadEvents();
        }        
    }, [props?.data, props?.limit,event_types, props.is_preview, dummyData]);


    return (
        <div className={`event-schedule_${props.page_id} ${props.className || ""}`} style={props?.style || null} onClick={preview_click}>
            <Row className={`row_${props.page_id}`}>
                <Col className={`col_${props.page_id}`}>
                    <Card className={`card_${props.page_id}`}>
                        <Table>
                            <tbody>
                                {(!data || data.length<=0) && <tr><td colSpan={3}>No events scheduled</td></tr>}
                                {data && data.map((item, i) => (
                                    <tr key={`event-schedule-table-row-${item.id}-${i}`}>
                                        <td>{format(new Date(item.start_datetime), "MMM dd h:mm a")}</td>
                                        <td>{item.location_name}</td>
                                        <td>{item.name}</td>
                                    </tr>                            
                                ))}
                            </tbody>
                        </Table>
                    </Card>
                </Col>
            </Row>
            {data && data.length>0 &&
                <Row className={`row_${props.page_id}`}>
                    <Col className={`col_${props.page_id}`}>
                        <Button href="/p/schedule" className={`btn_${props.page_id}`}>Full Schedule</Button>
                    </Col>
                </Row>
            }
        </div>
    );
}

export default EventSchedule;