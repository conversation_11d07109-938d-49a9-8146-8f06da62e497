import React, { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Card, Nav, Tab, Row, Col } from 'react-bootstrap';

import BasicInfo from './BasicInfo';
import Elements from './Elements';
import Properties from './Properties';

import {default as _cssProperties} from '../cssProperties';

//import Editor from './Properties/Editor';
//import Tree from './Tree';

//import styles from './Toolbar.module.scss';

import * as actions from '../../../../store/actions';

export const Toolbar = (props) => {

    const dispatch = useDispatch();
    const cmsSelector = useSelector(state => state.cms);
    const cmsSelectorElements = useSelector(state => state.cmsElements.present);

    const [currentElement, setCurrentElement] = useState();
    const [activeKey, setActiveKey] = useState('0');
    const [selectedMenuItem, setSelectedMenuItem] = useState();


    useEffect(() => {
        if (cmsSelector.currentElement){
            if (cmsSelectorElements.elements && currentElement?.id!==cmsSelector.currentElement){
                let _currentElement = cmsSelectorElements.elements.filter(el => el && el?.id === cmsSelector.currentElement);
                if (_currentElement.length > 0) setCurrentElement(_currentElement[0]);
            }
        } else setCurrentElement(null);
    }, [cmsSelectorElements.elements, cmsSelector.currentElement, currentElement]);

    useEffect(() => {            
        if (cmsSelector.currentElement && currentElement) setActiveKey('2');
    }, [cmsSelector.currentElement, currentElement]);


    /*
    useEffect(() => {
        if (currentElement){
            const _treeData = flatten([currentElement]);
            setTreeData(_treeData?.[0] || null);
        } else {
            setTreeData(null);
        }
    }, [currentElement]);
    */

    useEffect(() => {
        return() => {
            setCurrentElement(null);
            setActiveKey('0');
            //setTreeData(null);
        }
    },[]);

    const saveValueHandler = useCallback((e, value="", property, type=null, element = null, selected_menu_item = null) => {
        e.preventDefault();
        e.stopPropagation();

        if (selected_menu_item) setSelectedMenuItem(selected_menu_item);

        if (!value && e?.target?.value) value=e.target.value;
        if (type==="json" && value) value=JSON.parse(value);
        if (type==="boolean") value=value?0:1;

        let id, name;
        if (typeof property === "string" || typeof property === "number") id=property;
        else {
            id=property.id;
            name=property.name;
        }

        setCurrentElement(prev => {
            let __elem = [prev];
            if (element && element.length>0){
                /*if (Array.isArray(element)) _elem = element[0]; // for now just take the first element
                else _elem = element;*/
                __elem = element;
            }

            if (!__elem) return;

            let _currentElement;
            __elem.forEach(_elem => {
                _currentElement = JSON.parse(JSON.stringify(_elem));

                // if the property doesn't exist, create it
                if (_currentElement?.properties?.filter(a=>a.id === id)?.length===0){
                    const _cssprops = JSON.parse(JSON.stringify(_cssProperties));
                    let _prop = _cssprops.filter(a=>a.id === id);
                    if (_prop.length===0) _prop = _cssprops.filter(a=>a.name === name);
                    if (_prop.length===0) _prop = [{
                        id: id,
                        name: name,
                    }];
                    _currentElement.properties.push(_prop[0]);
                }


                _currentElement?.properties?.filter(a=>a.id === id)?.forEach(prop => {
                    prop.value = value;
                    prop.changed = true; // this flag is needed in utils/cms.js to update only values that have changed

                    name = prop.name;
                    
                    /*
                    // updates other properties with the same name, in case they're under advaced settings or something
                    _currentElement?.properties?.filter(b=>b.name === prop.name)?.forEach(_prop => {
                        _prop.value = value;
                        _prop.changed = true;
                    });
                    */
    
                    // updates the wtf object, because some stuff is still using this old object
                    Object.keys(_currentElement?.[_currentElement?.element_id]?.props)?.forEach(key => {
                        if (key === prop.name) _currentElement[_currentElement.element_id].props[key] = value;
                    });
                });

                if (name){
                    //console.log(name,_currentElement?.properties?.filter(b=>b.name === name))
                    // updates other properties with the same name, in case they're under advaced settings or something
                    _currentElement?.properties?.filter(b=>b.name === name)?.forEach(_prop => {
                        _prop.value = value;
                        _prop.changed = true;
                    });
                }

                // updates the new values
                dispatch(actions.CMSAddElement({...JSON.parse(JSON.stringify(_currentElement))}));
            });


            if (!element) dispatch(actions.CMSSetCurrentElement({..._currentElement}.id));
            return _currentElement;
        });

        return value;

    }, [dispatch]);

    return (
        <Card className="cms-builder-toolbar">
            <Tab.Container id="property-tabs" activeKey={activeKey} onSelect={k=>setActiveKey(k)}>
                <Row>
                    {(!cmsSelector.currentPageProps?.config?.is_code || cmsSelector.currentPageProps?.config?.is_code===0) &&
                        <Col sm={12} className="p-0">
                            <Nav variant="pills" className="flex-row">
                                <Nav.Item>
                                    <Nav.Link eventKey="0">Info</Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link eventKey="1">Elements</Nav.Link>
                                </Nav.Item>
                                <Nav.Item>
                                    <Nav.Link eventKey="2">Properties</Nav.Link>
                                </Nav.Item>
                                {/*
                                <Nav.Item>
                                    <Nav.Link eventKey="3">Element Tree</Nav.Link>
                                </Nav.Item>
                                */}
                            </Nav>
                        </Col>
                    }
                    <Col sm={12} className="p-0">
                        <Tab.Content>
                            <Tab.Pane eventKey="0">
                                <div>
                                    <BasicInfo page_type={props.page_type} pageFactor={props.pageFactor} save={saveValueHandler} setLoading={props.setLoading} />
                                </div>
                            </Tab.Pane>
                            {(!cmsSelector.currentPageProps?.config?.is_code || cmsSelector.currentPageProps?.config?.is_code===0) &&
                                <>
                                    <Tab.Pane eventKey="1">
                                        <Elements {...props} />
                                    </Tab.Pane>
                                    
                                    <Tab.Pane eventKey="2">
                                        <>
                                            {currentElement && 
                                                <div className="title">
                                                    <span>
                                                        {currentElement?.name || currentElement?.element_id?.toUpperCase() || ""}
                                                    </span>
                                                    <a href="#!" className="btn" onClick={props.remove}>Remove</a>
                                                </div>
                                            }                                            
                                            <Properties {...props} currentElement={currentElement} save={saveValueHandler} selectedItem={selectedMenuItem} />
                                        </>
                                    </Tab.Pane>

                                    {/*
                                    <Tab.Pane eventKey="3">
                                        <Tree data={ treeData } select={element_id=>{
                                            dispatch(actions.CMSSetCurrentElement(element_id));
                                            setActiveKey('2');
                                        }} />
                                    </Tab.Pane>
                                    */}
                                </>
                            }   
                        </Tab.Content>
                    </Col>
                </Row>
            </Tab.Container>
        </Card>
    );
}