import React, { useState, useCallback } from 'react'
import { Typeahead } from './Typeahead'

import Permissions from '../../api/Permissions'

const ModuleTypeTypeahead = ({...props}) => {
    
    const [ loading, setLoading ]=useState("Loading Module Types...");
    
    const getModuleTypes = useCallback(async()=>{
        let responseObject={
            data: null,
            errors: null
        };
        try{
            let response = await Permissions.ModuleTypes.get();
            if(response.data){
                setLoading(null);
                responseObject.data = response.data;
            }
            else if(response.errors){
                setLoading("Could not load module types.")
                responseObject.errors = response.errors;
            }
        }catch(ex){
            console.error(ex);
            setLoading("Could not load module types.")
        }
        return responseObject;
    },[]);

    return (
        <div>
            <Typeahead
                {...props}
                id={"module-type-typeahead"}
                makeRequest={getModuleTypes}
                async={false}
                placeholder={loading ? loading : props.placeholder || "Search Module Types"}
            />
        </div>
    )
}

export default ModuleTypeTypeahead