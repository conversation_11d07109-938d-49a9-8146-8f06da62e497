import React, { useEffect } from 'react';
import { Container, Row, Col, Image } from 'react-bootstrap';
import { randomUUID } from '../../../../utils/cms';

const ImagePreview = (props) => {
    const {selection} = props;

    const images = props.images ? (!Array.isArray(props.images) ? [{url:props.images}] : props.images) : [];
    const randomness = randomUUID();

    useEffect(()=>{
        if (props.images && selection){
            selection(props.images);
        }
    },[selection, props.images]);

    if (!props.images) return null;
    
    return (
        <Container>
            <Row>
                {images?.map((image, i) => (
                    <Col sm="auto" key={`preview-image-${i}-${randomness}`}>
                        <Image thumbnail src={image.url} width="50px" height="50px" style={{objectFit: "cover"}} />
                    </Col>
                ))}
            </Row>
        </Container>
    );
}

export default ImagePreview;