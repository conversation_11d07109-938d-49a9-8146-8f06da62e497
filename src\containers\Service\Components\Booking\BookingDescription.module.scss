@import '../../../../assets/css/scss/themes.scss';

/* Service Selection Rows */
.serviceSelection {
    margin-top: 0;
    margin-bottom: 0;
}

/* Form Labels */
.description {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    color: $primary-font-color;
    font-weight: bold;
    padding-right: 15px;
}

.question {
    margin-top: 0 !important;
    font-size: $secondary-font-size;
    color: $primary-font-color;
}

/* Service Description List */
.serviceDescriptionList {
    list-style: outside none;
    margin-left: 1.3em;

    li {
        margin-top: 0.5rem;
    }

    i {
        color: $primary-color;
        font-size: 1.2rem;
    }
}

.faLi {
    left: 3rem;
    width: 1rem;
}

/* Tokens Available */
.tokensAvail {
    color: $primary-color;
    font-size: 0.9rem;
    border: 1px solid $primary-color;
    padding: 0.3rem 1rem;
    border-radius: 4px;

    &.none {
        color: rgb(158, 158, 158);
        border: 1px solid rgb(158, 158, 158);
    }
}

/* Bold Text */
.boldText {
    font-weight: bold;
}

/* Info Text */
.info {
    color: $primary-font-color;
}