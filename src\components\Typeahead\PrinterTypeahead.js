import React, { useState, useCallback } from 'react'
import LocationsAPI from '../../api/Locations';
import { Typeahead } from './Typeahead'

const PrinterTypeahead = ({
    async=false, 
    paginated=false, 
    multiple=false, 
    initialDataIds=null,
    ...props}) => {

    const [ loading, setLoading ]=useState(true);
    
    const getPrinters=useCallback(async()=>{
        let responseObj;
        try{
            let response = await LocationsAPI.get({
                location_type_ids: [11] //printers
            });
            responseObj={
                data: response.data || null,
                errors: response.errors || null
            }
        }catch(ex){
            console.error(ex)
            responseObj = {
                data: null,
                errors: ex
            }
        }
        setLoading(false);
        return responseObj;
    },[])
    
    return (
        <Typeahead
            {...props}
            id="module-typeahead"
            makeRequest={getPrinters}
            async={async}
            multiple={multiple}
            paginated={paginated}
            initialDataIds={initialDataIds}
            placeholder={loading ? "Loading Printers..." : props.placeholder || "Search Printers"}
        />
    )
}

export default PrinterTypeahead