import { React, useEffect, useRef, useCallback, useMemo } from "react";
import { But<PERSON>, Row, Col } from "react-bootstrap";
import { useState } from "react";
import { useLocation, useHistory } from "react-router-dom";
import { useSelector } from "react-redux";
import { format } from 'date-fns';

import { roundTimes } from "../../../utils/dates";
import BasicInfo from "../BasicInfo/BasicInfo";
import Events from "../../../api/Events";
import Locations from '../../../api/Locations'
import DatePicker from "react-datepicker";

import "./EventModal.scss";

const MANAGE_EVENT_MODULE_ID = 118 // to see the link to manage events from the calendar

const EventModal = (props) => {
	const mountedRef = useRef(false);
	const history = useHistory();
	const [eventInfo, setEventInfo] = useState();
	const [locationName, setLocationName]=useState();
	const [loading, setLoading] = useState(true);
	const [times, setTimes]=useState({});

	const eventUpdate = useSelector((state) => {
		return state.calendar.updateNeeded;
	});

	const getEvent=useCallback(async()=>{
		if ((props.data.id && props.data.is_event) || eventUpdate === true) {
			try{
				let response = await Events.getSingle({ id: parseInt(props.data.id)});
				if(mountedRef.current && !response.errors){
					setEventInfo(response.data[0])
				}
			}catch(ex){console.error(ex)}
		}
	},[props.data, mountedRef, eventUpdate]);

	const getLocationName=useCallback(async()=>{
		console.log(eventInfo.location_id,eventInfo)
		try{
			let response = await Locations.get({id:eventInfo.location_id});
			if(!response.errors) setLocationName(response.data?.[0]?.name)
		}catch(ex){console.error(ex)}
	},[eventInfo]);

	const convertTimes=useCallback(()=>{
		if(eventInfo){
			let sDate = format(new Date(eventInfo.start_datetime), "MMMM do, yyyy" );
			let eDate = format(new Date(eventInfo.end_datetime), 'MMMM do, yyyy');
			let sTime = format(new Date(eventInfo.start_datetime), 'hh:mm')
			let eTime = format(roundTimes(new Date(eventInfo.end_datetime)), "hh:mm");
			let same;
			if(sDate === eDate) same=true
			else same=false
			setTimes({
				startDate: sDate,
				endDate: eDate,
				startTime: sTime,
				endTime: eTime,
				sameDay: same,
			});
		}
	},[eventInfo])

	useEffect(()=>{
		mountedRef.current = true

		return()=>{
			mountedRef.current = false;
			setLoading(true);
			setLocationName();
			setEventInfo();
			setTimes();
		}
	},[])

	useEffect(() => {

		getEvent();

		setLoading(true);

	}, [props.data, eventUpdate, getEvent]);

	useEffect(()=>{
		if(eventInfo) {
			getLocationName();
			convertTimes();
		}
	},[eventInfo, getLocationName, convertTimes]);

	useEffect(()=>{
		if(eventInfo && locationName && times) setLoading(false);
	},[eventInfo, locationName, times])

    const statusClass = useMemo(() => {
        if (!eventInfo) return '';
        if (eventInfo?.event_status_id===1) return "text-green";   // Pending
        if (eventInfo?.event_status_id===3) return "text-orange";   // Postponed
        if (eventInfo?.event_status_id===4) return "error-text";    // Cancelled
        return '';
      }, [eventInfo]);

	return (
		<>
				{props.datePickerClick === 1 ? (
					<DatePicker
						className="m-auto"
						selected={props.newDate}
						showYearDropdown
						showMonthDropdown
						onChange={(date) => {
							props.sendNewDate(date);
						}}
						inline
					/>
				) : null}
				
				
				{props.datePickerClick === 0 && !loading && 
					<>
						<div className="calendar-event-modal-wrapper">
						<div style={{marginBottom: '0.5rem'}} className="bold">Status:{' '}
							<span className={statusClass}>{eventInfo.event_status_name}</span>
						</div>
						{eventInfo?.requires_registration>0 &&
							<Row>
								<Col>
									<span className="bold">This event requires registration to attend.</span>
								</Col>
							</Row>
						}
						<Row>
							<Col>
								<h4><i className="far fa-location" /> What</h4>
								<hr/>
								<p>
									<label className="form-label">{eventInfo?.event_type_name}:</label><br/>
									{eventInfo?.name}
								</p>
								{eventInfo?.description &&  <div dangerouslySetInnerHTML={{ __html: eventInfo?.description }} />}
							</Col>
						</Row>
						<Row>
							<Col>
								<h4><i className="far fa-calendar-alt" /> When</h4>
								<hr/>
								{times.sameDay ? 
									<>
										<p><label className="form-label">Date:</label><br/>{times?.startDate}</p>
										<p>{times?.startTime} to {times?.endTime}</p>
									</>
								:
									<>
										<p><label className="form-label">Start Date:</label> {times?.startDate}</p>
										<p><label className="form-label">End Date:</label> {times?.endDate}</p>
										<p>{times?.startTime} to {times?.endTime}</p>
									</>
								}
							</Col>
							<Col>
								<h4><i className="far fa-building" /> Where</h4>
								<hr/>
									<p>
										<label className="form-label">Location:</label><br/>
										{locationName}
									</p>

									{props.userHasModulePermission && props.userHasModulePermission[MANAGE_EVENT_MODULE_ID] && 
										<p>
											<br />
											<Button variant="primary" onClick={()=>history.push(`/p/events/${eventInfo.id}`)}>Manage Event <i className="far fa-external-link" /></Button>
										</p>
									}
							</Col>
						</Row>
						</div>
					</>   
				}
		</>
	);
};

export default EventModal;
