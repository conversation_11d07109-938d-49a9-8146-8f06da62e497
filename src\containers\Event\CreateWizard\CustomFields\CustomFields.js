import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Row, Col, Button } from 'react-bootstrap';
import ReactTooltip from 'react-tooltip';

import Fields from './Fields';

import * as actions from '../../../../store/actions';
import { selectCurrentEvent } from '../../../../store/selectors';
import { randomUUID } from '../../../../utils/cms';

const types = [ "input", "select" ];

const CustomFields = (props) => {
    const dispatch = useDispatch();

    const currentEvent = useSelector(selectCurrentEvent);
    const [customFields, setCustomFields] = useState(currentEvent?.custom_fields || []);

    const newFieldHandler = useCallback(() => {
        setCustomFields( prevState => [...prevState, {
            "custom_field_type": "input",
            "custom_field_type_id": 1,
            "name": "cf_"+randomUUID(),
            "options": [],
            "required": 0,
            "placeholder_text": ""
        }]);
    },[]);

    const changeFieldHandler = (i, key, value) => {
        let newFields = [...customFields];

        if (key === "text") newFields[i]["name"] = value.toLowerCase().replace(/\s/, "_").replace(/[^a-zA-Z\d_]/, "");
        if (key === "custom_field_type") newFields[i]["custom_field_type_id"] = value === "input" ? 1 : 2;
        newFields[i][key] = value;

        setCustomFields(newFields);
    }

    const removeFieldHandler = i => {
        let newFields = [...customFields];
        newFields.splice(i, 1);
        setCustomFields(newFields);
    }

    const newOptionHandler = i => {
        let newFields = [...customFields];
        newFields[i].options.push({"value": "", "text": ""});
        
        setCustomFields(newFields);
    }

    const changeOptionHandler = (i, j, key, value) => {
        let newFields = [...customFields];

        if (key === "text") newFields[i].options[j]["value"] = value.toLowerCase().replace(/\s/, "_").replace(/[^a-zA-Z\d_]/, "");
        newFields[i].options[j][key] = value;

        setCustomFields(newFields);
    }

    const removeOptionHandler = (i, j) => {
        let newFields = [...customFields];
        newFields[i].options.splice(j, 1);
        setCustomFields(newFields);
    }

    useEffect(() => {
        dispatch(actions.setCurrentEventWizard({ custom_fields: customFields }));
    }, [dispatch, customFields]);
    
    useEffect(() => {
        if (customFields.length === 0) newFieldHandler();
    },[customFields, newFieldHandler]);
    
    return (
        <Row>
            <Col className="wizard">
                <span className="title">Custom Fields</span>
            </Col>
            <Col sm={12}>
                <p className="text-center small">
                    Editing or deleting existing questions on public registration forms that have already been submitted may cause user responses to become unclear or lost.
                </p>
                {customFields.map((field, i) => {
                    return (
                        <Fields 
                            {...field}
                            id={i} 
                            key={`field-${field}-${i}`} 
                            changeFieldHandler={changeFieldHandler}
                            removeFieldHandler={removeFieldHandler}
                            newOptionHandler={newOptionHandler}
                            changeOptionHandler={changeOptionHandler}
                            removeOptionHandler={removeOptionHandler}
                        />
                    );
                })}
                <Row>
                    <Col className="mt-4">
                        <Button variant="secondary" onClick={newFieldHandler}>Add Field</Button>
                    </Col>
                </Row>
                <ReactTooltip globalEventOff="click" effect="solid" backgroundColor="#262D33" arrowColor="#262D33" />
            </Col>
        </Row>
    );
}

export default CustomFields;