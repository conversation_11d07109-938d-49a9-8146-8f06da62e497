@import '../../../assets/css/scss/themes.scss';

.wrapper{
    font-family: 'Roboto', sans-serif !important;
    font-size: 0.75rem !important;
}

.toolbar{
    font-family: '<PERSON>o', sans-serif !important;
    font-size: 0.75rem;

    a{
        color: inherit;

        &:hover{
            color: inherit;
            box-shadow: unset;
        }
    }

    input{
        font-family: "Roboto", sans-serif !important;
        font-size: 0.75rem !important;
        font-weight: 400 !important;
        line-height: 1rem !important;
        color: #000 !important;
        background-color: #fff !important;
        border: 1px solid #eee !important;
        border-radius: 0 !important;
        padding: 0.5rem 0.75rem !important;        
    }

    button{
        font-family: 'Outfit', sans-serif;
        font-size: 0.8rem;
        position: relative;
        font-weight: 500;
        line-height: 1rem;
        border-radius: 0;
        padding: .5rem 1.5rem !important;
        margin: 0.25rem 0.5rem 0.25rem 0;
        border: 0;
        color: #fff;
        background-color: #662d91;
        text-transform: uppercase;
        text-shadow: 0 0 2px #9e9e9e;
        box-shadow: rgba(#000, .2) 0px 2px 1px -1px, rgba(#000, .14) 0px 1px 1px 0px, rgba(#000, .12) 0px 1px 3px 0px;
        text-decoration: none;
        align-items: center;
        width: fit-content;
        &:hover {
            background-color: #512da8;
            color: #fff;
            text-decoration: none;
        }

        &:first-child{
            margin-left: 0;
        }

        &:disabled,
        .disabled,
        &:disabled:hover {
            color: #000;
            background-color: #bdbdbd;
        }

        &:nth-of-type(2){
            background-color: #e0e0e0;
            color: #000;
            &:hover {
                background-color: #bdbdbd;
                color: #000;
            }
        }
    }


}

.editor{
    border:1px solid #eee;
    background: #fff;
    padding: 0.5rem 1rem;
    border-top:0;
}

.modal{
    
    &.modal-right{
        right: 5px;
        left: unset !important;
    }
}
