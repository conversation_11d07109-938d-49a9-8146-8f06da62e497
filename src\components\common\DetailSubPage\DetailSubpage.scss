@import '../../../assets//css/scss/mixins';
@import '../../../assets//css/scss/variables';


.detail-sub-page-wrapper{
    container: detail-subpage / inline-size;
    .sub-page{
        display: flex;
    }
    .subpage-subnav{
        min-width: 110px;
        display: flex;
        a.subnav-item{
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            .li-text {
                display: inline-block;
            }
            .li-icon {
                width: 2rem;
                display: flex;
                flex-direction: column;
                align-items: center;
                align-content: center;
            }
            &:hover{
                color:$secondary-color;
            }
        }
    }
    .sub-detail-component{
        min-width:85%;
        max-width:95%;
        padding-right: 10px;
        margin-top: 1rem;
        h4.section-title{
            font-family: $secondary-font-family;
            @media (max-width: 420px){
                text-align: center;
                margin-top: .5rem;
            }
        }
    }
}

@container detail-subpage (max-width: 750px){
    .li-text{
        display: none !important;
    }
    .stubborn-tooltip{
        left: inherit !important;
    }
}
@container detail-subpage (max-width: 350px){

    .subpage-subnav{
        a.subnav-item{
            max-width: 40px;
        }
    }
}
@container detail-subpage (min-width: 751px){
    .stubborn-tooltip, .stubborn-tooltip-manageUsers{
        display: none !important;
    }
}
@container detail-subpage (max-width: 1000px){
    .sub-page{
        flex-direction: column-reverse;
    }
    .subpage-subnav{
        flex-direction: row;
        a.subnav-item{
            min-width: 30px;
            // max-width: 40px;
            border-left: none !important;
            border-bottom: 4px solid $divider-color !important;
            &.active-hash-item{
                border-left: none !important;
                border-bottom: 4px solid $primary-color !important;
            }
        }
    }
}
@container detail-subpage (min-width: 1001px){
    .sub-page{
        flex-direction: row;
    }
    .subpage-subnav{
        flex-direction: column;
        a.subnav-item{
            border-left: 4px solid $divider-color !important;
            &.active-hash-item{
                border-left: 4px solid $primary-color !important;
            }
        }
    }
}