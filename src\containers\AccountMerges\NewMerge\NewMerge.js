import React, {useState, useEffect, useCallback, useRef} from 'react'
import { <PERSON><PERSON><PERSON><PERSON>b, Container, Col, Row, Card, Button } from 'react-bootstrap'
import { useHistory,Link } from 'react-router-dom'

import ErrorCatcher from '../../../components/common/ErrorCatcher'
import Toast from '../../../components/Toast'

import { useRoleCheck } from '../../../components/common/CustomHooks'
import NewUserTypeahead from '../../../components/Typeahead/NewUserTypeahead'

import Users from '../../../api/Users'
import './NewMerge.scss'

import SubHeader from '../../../components/common/SubHeader';

//Called in the component "src\containers\AccountMerges\AccountMerges.js"

/**Roles above patron are protected from being merged by anyone lower than role 1 by only returning patron from the typeahead if any other role is using the page. */
export const NewMerge = () => {

    const mountedRef = useRef(false);
    const currentUserRole=useRoleCheck();
    const history = useHistory();
    const [success, setSuccess]=useState();
    const [error, setError]=useState();
    const [primaryUser, setPrimaryUser]=useState();
    const [mergeUser, setMergeUser]=useState();
    const [roleSearch, setRoleSearch]=useState(null);
    const [saved, setSaved]=useState(false);
    const [verified, setVerified]=useState(false);

//#region useCallback
    //if an account is anything other than 1, they can only merge patron accounts.  Determining if they are or not so as to apply a role to the typeaheads appropriately.
    const determineRole=useCallback(()=>{
        // staff can only search for patrons
        let rolesAllowedToSearchFor = [7];
        // company admins can search for all company accounts
        if(currentUserRole.id === 4 && mountedRef.current) rolesAllowedToSearchFor = [4,5,6,7]; // all roles in that company
        // SB admins can search for all accounts
        if(currentUserRole.id < 4 && mountedRef.current) rolesAllowedToSearchFor = null; // all roles
        setRoleSearch({user_roles: rolesAllowedToSearchFor});
    },[currentUserRole]);

    const initiateMerge=useCallback(async()=>{
        try{
            let response = await Users.Merge.merge({
                primary_user_id : primaryUser.id,
                merge_user_id : mergeUser.id
            })
            if(!response.errors && mountedRef.current){
                setSuccess(<Toast>Users Merged Successfully</Toast>);
                history.push("/p/admin/merge");
            }else{
                setError(<ErrorCatcher error={response.errors} />)
            }
        }catch(ex){console.error(ex)}
    },[primaryUser, mergeUser, history]);

    const saveUsers=useCallback(()=>{
        if(!primaryUser || !mergeUser) setError("Please ensure two users are selected.")
        else {
            setSaved(true);
            setError(null);
        }
    },[primaryUser, mergeUser]);
//#endregion useCallback

//#region useEffect
    //First load
    useEffect(()=>{
        mountedRef.current = true

        return()=>{
            mountedRef.current = false;
            setSaved(false);
            setVerified(false);
        }
    },[]);

    useEffect(()=>{
        determineRole();
    },[determineRole]);

    useEffect(()=>{
        saveUsers();
    },[saveUsers])

    useEffect(()=>{
        if(verified && mountedRef.current) initiateMerge();
    }, [verified, initiateMerge]);
//#endregion useEffect

    const verifyUsers=()=>{
        setVerified(true);
    }

  return (
    <Container fluid>
        <SubHeader items={[
            { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
            { linkAs: Link, linkProps: { to: "/p/admin/merge" }, text: "Account Merges" },
            { text: "New Merge" }
        ]} />
        <Card className="account-new-merge-wrapper content-card">
            <h4 className="section-title">Merge Users</h4>
            <hr/>
            <div className="new-merge-select-users">
                <div>
                    <p>
                        Please select the user you want to use as the primary account.
                        <NewUserTypeahead passSelection={(selection)=>setPrimaryUser(selection[0])} postFilters={roleSearch} multiple={false}/>
                    </p>
                </div>
                
                <div>
                    <p>
                        Please select the user you want to merge into that primary account.
                        <NewUserTypeahead passSelection={(selection)=>setMergeUser(selection[0])} postFilters={roleSearch} multiple={false}/>
                    </p>
                </div>

                <span>{error}</span>
            </div>

            {saved && 
                <div className="selected-users">
                    <p>Please verify that the two users you want to merge are correct.  </p>
                    {primaryUser && 
                        <fieldset>
                            <legend>Primary User</legend>
                            <p>
                                <i className="far fa-id-card-alt"></i>
                                {" "}
                                <span>User Id : {primaryUser.id}</span>
                            </p>
                            <p>
                                <i className="far fa-user-shield"></i>
                                {" "}
                                <span>Username: {primaryUser.username}</span>
                            </p>
                            <p>
                                <i className="far fa-user"></i>
                                {" "}
                                <span>User's Name: {primaryUser.first_name} {primaryUser.middle_name} {primaryUser.last_name}</span>
                            </p>
                            <p>
                                <i className="far fa-envelope"></i>
                                {" "}
                                <span>Email: {primaryUser.email || "Has None on File"}</span>
                            </p>
                            <p>
                                <i className="far fa-phone-alt"></i>
                                {" "}
                                <span>Phone: {primaryUser.mobile_phone || "Has None on File"}</span>
                            </p>
                            <p>
                                <i className="far fa-phone-alt"></i>
                                {" "}
                                <span>Alt Phone: {primaryUser.home_phone || "Has None on File"}</span>
                            </p>
                        </fieldset>
                    }
                    {mergeUser && 
                        <fieldset>
                            <legend>Secondary User</legend>
                            <p>
                                <i className="far fa-id-card-alt"></i>
                                {" "}
                                <span>User Id : {mergeUser.id}</span>
                            </p>
                            <p>
                                <i className="far fa-user-shield"></i>
                                {" "}
                                <span>Username: {mergeUser.username}</span>
                            </p>
                            <p>
                                <i className="far fa-user"></i>
                                {" "}
                                <span>User's Name: {mergeUser.first_name} {mergeUser.middle_name} {mergeUser.last_name}</span>
                            </p>
                            <p>
                                <i className="far fa-envelope"></i>
                                {" "}
                                <span>Email: {mergeUser.email || "Has None on File"}</span>
                            </p>
                            <p>
                                <i className="far fa-phone-alt"></i>
                                {" "}
                                <span>Phone: {mergeUser.mobile_phone || "Has None on File"}</span>
                            </p>
                            <p>
                                <i className="far fa-phone-alt"></i>
                                {" "}
                                <span>Alt Phone: {mergeUser.home_phone || "Has None on File"}</span>
                            </p>
                        </fieldset>
                    }
                    <div className="text-center">
                        <p className="you-sure-merge">If you're sure these are the users you want to merge, click to confirm.  Please beware, though this action is reversible, some data may be lost or altered in the process.</p>
                        <Button type="button" onClick={verifyUsers} disabled={error ? true : false}>These Are The Users</Button>
                    </div>
                </div>
            }

        </Card>
        {success}
    </Container>
  )
}
