import React, { useCallback } from 'react'

import { Typeahead } from './Typeahead'
import Permissions from '../../api/Permissions'

const EndPointsTypeahead = (props) => {
    
    const getEndpoints = useCallback(async()=>{
        let responseObj;
        try{
            let response = await Permissions.Endpoints.getAll()
            responseObj = {
                data: response.data || null,
                errors: response.errors || null
            }
        }catch(ex){
            console.error(ex)
            responseObj = {
                data: null,
                errors: ex
            }
        }
        return responseObj
    },[])

    return (
        <Typeahead 
            {...props}
            id="endpoint-typeahead"
            makeRequest={getEndpoints}
            placeholder={props.placeholder || "Search Endpoints"}
            paginated={false}
            async={false}
            multiple={props.multiple}
            formatForLabel = {(option) => (`${option?.slug} --- ${option.method}`)}
        />
    )
}

export default EndPointsTypeahead