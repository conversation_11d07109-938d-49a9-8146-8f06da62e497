import React, { useState, useEffect, useCallback } from 'react';
import { Con<PERSON>er, Card, Modal, Button, Table } from 'react-bootstrap';
import { Link } from 'react-router-dom'

import Toast from '../../../components/Toast';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Registers from '../../../api/Registers';
import SubHeader from '../../../components/common/SubHeader';
import { returnKillswitchStatus } from '../../../utils/pos';

export const RegisterKillSwitch = () => {

    const [ registers, setRegisters ] = useState([]);
    const [ error, setError ] = useState();
    const [ success, setSuccess ] = useState();
    const [ activeRegister, setActiveRegister ]=useState(null);

    const getRegisters = useCallback(async()=>{
        let test = await returnKillswitchStatus(1);
        console.log(test)
        try{
            let response = await Registers.get();
            if(response.status === 200){
                setRegisters(response.data);
            }
            else setError(<ErrorCatcher error={response.errors} />)
        }catch(ex){console.error(ex)}
    },[])

    useEffect(()=>{

        getRegisters();

    },[getRegisters]);

    const handleActiveRegister=(register)=>{
        if(!activeRegister) setActiveRegister(register);
        if(activeRegister && activeRegister.id === register.id) setActiveRegister(null);
    }

    const updateKillswitch = async()=>{
        setSuccess(null);
        setError(null);

        let regDetails = {...activeRegister}
        let defCopy = {...activeRegister.register_definition};
        
        defCopy.change_id = ++defCopy.change_id;
        
        if(!defCopy.hasOwnProperty("killswitch")) defCopy.killswitch = true;
        else defCopy.killswitch = !activeRegister.register_definition.killswitch;
        
        //this gets added in with reg definitions when they're new,  but the call to edit will fail if the field is null
        if(regDetails.hasOwnProperty("register_group_id") && !regDetails.register_group_id) delete regDetails.register_group_id;
        if(regDetails.hasOwnProperty("register_group_name") && !regDetails.register_name) delete regDetails.register_group_name;
        
        try{
            let response = await Registers.edit({...regDetails, register_definition: defCopy});
            if(response.status === 200) {
                setSuccess(<Toast>Register Updated Successfully</Toast>);
                getRegisters();
                setActiveRegister(null);
            }else if(response.errors) setError(<ErrorCatcher error={response.errors} />)
        }catch(ex){
            console.error(ex);
            setError(<ErrorCatcher error={"An error has occured when making the request"} />)
            setActiveRegister(null);
        }
    }

    return(
        <Container fluid>
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Registers Killswitches" }
            ]}/>
            <Card className="content-card">
                <h4 className="section-title">
                    Disable Registers
                </h4>
                {error}
                {success}
                <div>
                    <div>
                        Please make sure you're selecting the proper register! 
                        Activating the kill switch will prevent anyone from shopping on this register so long as the killswitch is enabled.  
                        Any user accessing the register will be notified that it is temporarily unavailable.  
                        For a more long term solution, please consider using register hours to control availablity.  
                    </div>
                    <Table>
                        <thead>
                            <tr>
                                <th>
                                    Id
                                </th>
                                <th>
                                    Name
                                </th>
                                <th>
                                    Description
                                </th>
                                <th>
                                    Killswitch?
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {registers?.map((register)=>(
                                <tr key={`register-info-${register.id}`} onClick={()=>handleActiveRegister(register)}>
                                    <td>
                                        {register.id}
                                    </td>
                                    <td>
                                        {register.name}
                                    </td>
                                    <td>
                                        {register.description}
                                    </td>
                                    <td>
                                        {register.register_definition?.killswitch ? "Yes" : "No"}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </Table>
                </div>

                <Modal show={activeRegister ? true : false} onHide={()=>setActiveRegister(null)}>
                    <Modal.Body>
                        <Modal.Header></Modal.Header>
                        <p>
                            Make sure you've verified that {activeRegister?.name} (register id: {activeRegister?.id}) is the proper register.
                        </p>
                        <p>
                            Are you sure you want to 
                            <strong>
                                {" "}{activeRegister?.register_definition?.killswitch ? "enable" : "disable" } {" "}
                            </strong>
                            this register!? 
                        </p>
                        <p>
                            <Button onClick={updateKillswitch}>
                                Yes
                            </Button>
                            <Button variant="danger" onClick={()=>setActiveRegister(null)} >
                                Cancel     
                            </Button>
                        </p>
                    </Modal.Body>
                </Modal>
            </Card>
        </Container>
    )
}