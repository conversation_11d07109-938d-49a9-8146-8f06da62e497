@import './variables';
@import './mixins';
@import './themes';
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

@import './wizard';


// weird ones
.react-datepicker--time-only{
    min-width: unset !important;
}

.text-bc-blue {
    //color: $primary-color;
}

.text-company-primary{
    color: $primary-color;
}
.text-company-secondary{
    color: $secondary-color;
}

.bordered-neutral-btn.btn.btn-primary{
    border-radius: $button-border-radius;
    border: 2px solid $primary-color;
    background-color: $primary-light-color;
    color: $neutral-color;
    box-shadow: $button-shadow;
    &:hover{
        background-color: $secondary-color;
        color: $primary-inverse-color;
    }
    &:disabled{
        &:hover{
            background-color: $button-disabled-background-color;
            color: $button-disabled-color;
        }
    }
}

//To keep this properly organized, put a comment for the name of the page, sort of like tags
//To find the relative SCSS that you need and it's nested tree (or trees), CTRL/CMD+F
//Collapse what you don't need (and get rid of visual clutter) alt-click on the region you're working on to collapse all others
//Else, you can shift-click a region to expand the whole thing.

// setting the rem for the entire page
html,body {
    font-family: $primary-font-family;
    font-size: $primary-font-size;
    color: $primary-color;
    background-color: $background-color;
    overflow: hidden;
    overflow-y: auto;
}

//General class names, helpful anywhere
//#region
//so the mobile hide something size stays consistent
.mobile-hidden{
    @media (max-width: 525px){
        display: none;
    }
}

button.outline-primary.btn.btn-primary{
    background-color: transparent;
    border: 2px solid $primary-color;
    color: $primary-color;
}

button.outline-secondary.btn.btn-primary{
    background-color: transparent;
    border: 2px solid $secondary-color;
    color: $secondary-color;
}

button.outline-tertiary.btn.btn-primary{
    background-color: transparent;
    border: 2px solid $tertiary-color;
    color: $tertiary-color;
}

.success-text{
    color: $success-color;
}

.fail-text{
    color: $error-color;
}
.txt-al-l {
    text-align: flex-start;
}

.txt-al-r {
    text-align: end;
}

.txt-al-ctr {
    text-align: center;
}

.error-text, .required-star {
    color: $error-color;
    word-break: break-all;
    /*font-size: $primary-font-size;*/
}

.error-text-ctr {
    color: $error-color;
    font-size: $primary-font-size;
    text-align: center;
}

.site-row {
    display: flex;
    flex-direction: row;
}

.site-col {
    display: flex;
    flex-direction: column;
}

.hide-element {
    display: none;
}

.text-link a {
    text-decoration: $link-text-decoration;
    font-size: $link-font-size;
    font-weight: $link-font-weight;
    color: $primary-color;
    cursor: pointer;
    transition: all .15s ease-in-out;

    &:hover {
        text-decoration: $link-hover-text-decoration !important;
        color: $link-hover-color !important;
    }
}

.flex-center {
    display: flex;
    direction: row;
    justify-content: center;
    align-items: center;   
}

.hide-slaask .slaask-button,
.hide-slaask .slaask-iframe,
.hide-slaask .slaask-iframe-container {
    display: none;
}

#slaask-button{
    height: 0.85em !important;
    width: 0.85em !important;
}
#slaask-button[style],
#slaask-button #slaask-button-cross[style]{
    background-color: $primary-color !important;

    #slaask-button-image[style]{
        background-color: $primary-color !important;
        background-image: url("data:image/svg+xml,%3Csvg width='1000' height='1000' xmlns='http://www.w3.org/2000/svg' version='1.1'%3E%3Cg%3E%3Cg%3E%3Cpath fill='%23ffffff' d='m427.92457,467.4928c3.5195,-2.4922 7.7266,-3.832 12.039,-3.832c4.3164,0 8.5234,1.3398 12.043,3.832c3.4648,2.6133 6.5078,5.75 9.0156,9.2969c2.6719,0.36719 5.3672,0.5 8.0625,0.39062c2.3203,0.18359 4.6523,-0.04297 6.8906,-0.67188c3.1055,-8.6523 1.832,-18.273 -3.418,-25.816c-7.3047,-11.074 -19.688,-17.742 -32.957,-17.742c-13.266,0 -25.648,6.668 -32.953,17.742c-5.2734,7.5547 -6.5859,17.184 -3.5312,25.871c2.668,0.68359 5.4336,0.91016 8.1758,0.67188c2.3984,0.08984 4.7969,-0.04297 7.168,-0.39062c2.6406,-3.5977 5.8359,-6.7539 9.4648,-9.3516l-0.0001,-0.0003z' id='svg_2'/%3E%3Cpath fill='%23ffffff' d='m536.40457,476.7328c2.5664,-3.5352 5.6641,-6.6523 9.1836,-9.2383c3.5195,-2.4922 7.7266,-3.832 12.039,-3.832c4.3164,0 8.5234,1.3398 12.043,3.832c3.5625,2.5938 6.6992,5.7305 9.293,9.2969c2.6719,0.36719 5.3711,0.5 8.0664,0.39062c2.3164,0.18359 4.6484,-0.04297 6.8867,-0.67188c3.1094,-8.6523 1.8359,-18.273 -3.4141,-25.816c-7.3086,-11.074 -19.688,-17.742 -32.957,-17.742c-13.27,0 -25.648,6.668 -32.957,17.742c-5.2695,7.5547 -6.582,17.184 -3.5273,25.871c2.668,0.68359 5.4336,0.91016 8.1758,0.67188c2.3984,0.05469 4.8008,-0.11719 7.168,-0.50391l-0.0001,-0.00031z' id='svg_3'/%3E%3Cpath fill='%23ffffff' d='m248.72857,587.7828c5.9844,15.832 18.977,27.996 35.168,32.926c4.5352,1.2891 7.168,1.7344 10.527,2.5742l9.7422,2.2383l9.8008,2.2383l1.2305,0l1.8477,0.33594l0.00391,0.00391c1.3086,0.33594 2.6367,0.57813 3.9766,0.73047c9.5,0.54688 18.852,-2.5547 26.141,-8.6758c7.2891,-6.1211 11.961,-14.801 13.059,-24.254l0,-11.871l0.44922,-42.84l0.67188,-88.762c0.3125,-13.695 2.4414,-27.293 6.3281,-40.43c4.3555,-13.148 10.516,-25.625 18.312,-37.074c16.094,-23.086 38.883,-40.68 65.293,-50.398c6.5586,-2.3516 13.277,-4.2227 20.105,-5.6016l10.359,-1.5664c3.457,-0.45312 6.9336,-0.71484 10.418,-0.78516l5.207,-0.28125l5.207,0c3.4727,0 7,0 10.414,0.61719l10.305,1.5664c3.4727,0.44922 6.7773,1.625 10.137,2.4062l0,0.0039c26.766,7.1914 50.734,22.301 68.77,43.344c8.6836,10.34 15.918,21.812 21.504,34.105c5.3359,12.145 9.0117,24.953 10.918,38.078c0.55859,3.2461 0.44922,6.4961 0.72656,9.7422c0.28125,3.2461 0.44922,6.4414 0.39062,10.137l0,21.281l0.44922,41.551l1.1211,78.398c0,6.1602 0.28125,12.656 0,18.312l0,8.1211c0,2.6875 -0.67188,5.3203 -1.0078,7.9531c-1.625,10.23 -4.7773,20.16 -9.3516,29.457c-4.4961,8.7266 -10.148,16.805 -16.797,24.023c-5.9883,6.7344 -12.82,12.664 -20.328,17.641c-12.43,8.043 -26.453,13.297 -41.105,15.398c-2.2188,-12.703 -9.2891,-24.047 -19.711,-31.637c-6.0352,-4.3789 -12.988,-7.3281 -20.328,-8.625c-6.875,-1.1836 -13.926,-0.85547 -20.664,0.95312c-11.812,3.043 -21.902,10.711 -28,21.277c-4.2578,7.5273 -5.8711,16.258 -4.5938,24.809c0,0.95312 3.7539,1.6797 8.0078,1.5664c2.3828,0.22656 4.7812,-0.12109 7,-1.0078c1.5625,-4.8516 4.2266,-9.2812 7.7852,-12.934c4.1484,-4.2383 9.6211,-6.9258 15.512,-7.6172c6.2383,-0.73047 12.504,1.1562 17.305,5.207c4.9219,4.0742 7.7695,10.133 7.7695,16.52c0,6.3906 -2.8477,12.445 -7.7695,16.523c-4.793,4.0234 -11.035,5.8867 -17.25,5.1523c-5.875,-0.75 -11.312,-3.4961 -15.398,-7.7852c-3.5742,-3.6641 -6.2383,-8.1133 -7.7852,-12.992c-2.6445,-0.98438 -5.4805,-1.3477 -8.2852,-1.0664c-3.9219,0 -6.9453,0.72656 -7.0547,1.625l-0.00391,0c-1.1914,8.6836 0.56641,17.52 4.9844,25.09c6.1055,10.555 16.195,18.223 28,21.277c14.148,3.7617 29.234,0.91797 41.047,-7.7266c10.84,-7.9141 18.027,-19.867 19.938,-33.152c17.691,-0.86328 34.938,-5.8086 50.398,-14.449c9.5547,-5.2422 18.352,-11.762 26.152,-19.375c8.4102,-8.2969 15.629,-17.719 21.449,-28c6.1133,-11.156 10.477,-23.188 12.938,-35.672c0.55859,-3.2461 1.1758,-6.4961 1.5664,-9.8008l0,-2.3516l1.1211,0.55859l0,0.00391c3.8359,1.5859 7.9414,2.4414 12.094,2.5195c2.1758,0.05469 4.3477,-0.07422 6.4961,-0.39453l4.4805,-0.61719l18.762,-3.0781l4.9844,-0.83984c2.2109,-0.41016 4.3984,-0.95312 6.5508,-1.625c4.4258,-1.3086 8.6602,-3.1914 12.602,-5.5977c8.2578,-5.1406 15.016,-12.363 19.598,-20.945c2.4062,-4.3945 4.1758,-9.1094 5.2656,-14c0.53516,-2.4375 0.91016,-4.9062 1.1172,-7.3906l0,-62.828l0,-5.6016l0,-0.00391c-0.0625,-2.4961 -0.30859,-4.9844 -0.72656,-7.4453c-3.1484,-18.914 -15.859,-34.844 -33.602,-42.113l-0.67188,0l0,-25.426c-0.8125,-11.949 -2.5547,-23.82 -5.207,-35.504c-6.4844,-28.363 -18.637,-55.129 -35.727,-78.68c-19.91,-27.441 -45.898,-49.906 -75.938,-65.633c-32.262,-16.863 -68.09,-25.773 -104.5,-25.98c-36.453,0.25 -72.32,9.2148 -104.61,26.148c-29.961,15.828 -55.852,38.367 -75.656,65.859c-17.102,23.453 -29.293,50.121 -35.84,78.398c-2.6445,11.68 -4.3672,23.551 -5.1523,35.504c0,10.414 0,20.105 0.61719,28l-0.0039,0c-8.8828,3.8242 -16.512,10.074 -22.008,18.031c-5.6797,8.2656 -8.8867,17.977 -9.2383,28l0,8.457l-0.95312,35.055l-0.33594,18.09c-0.14844,3.7383 0.02343,7.4883 0.50391,11.199c0.55078,3.9102 1.5469,7.7422 2.9688,11.426l0.0064,0.00255zm476,-87.191l0,-0.00391c0.08594,1.1758 0.08594,2.3555 0,3.5312l0,61.262l0,2.6328l0,1.793c-0.07422,1.1836 -0.22656,2.3594 -0.44922,3.5273c-0.40625,2.332 -1.1406,4.5938 -2.1836,6.7188c-2.0039,4.4414 -5.1289,8.2812 -9.0703,11.145c-1.8438,1.3906 -3.8594,2.5391 -5.9922,3.418c-1.0625,0.44531 -2.1445,0.83984 -3.25,1.1758l-4.7617,1.2891l-18.367,5.0391l-4.3125,1.2305l0.00391,0c-0.71484,0.23438 -1.4453,0.42188 -2.1836,0.55859c-1.4648,0.27344 -2.9648,0.27344 -4.4258,0c-2.7188,-0.47656 -5.2422,-1.7188 -7.2812,-3.582l0,-2.6875l1.0078,-78.398l0.5625,-41.609l0,-13.215c4.0898,1.3984 8.5117,2.8008 13.215,4.3125l16.801,4.9297l9.0156,2.5195l0,-0.0039c2.2383,0.60547 4.3984,1.4688 6.4414,2.5742c8.2695,4.4023 13.887,12.547 15.062,21.84l0.16791,0.00222zm-421.12,-100.8c7.7227,-24.594 19.887,-47.559 35.898,-67.762c18.32,-23.453 41.59,-42.574 68.152,-56c28.219,-14.016 59.266,-21.406 90.773,-21.617c31.508,0.21094 62.555,7.6016 90.777,21.617c26.523,13.309 49.793,32.293 68.152,55.609c16.098,20.27 28.324,43.336 36.062,68.039c3.2188,10.395 5.7812,20.98 7.6719,31.695l1.625,14.336l0.50391,4.1992l-4.0312,-0.61719l-17.137,-2.2969c-6.5508,-0.78516 -12.543,-1.3984 -18.031,-1.8477c0,-3.9219 0,-7.8945 -0.72656,-11.816l-0.00391,0.00391c-1.7812,-16.023 -5.7773,-31.723 -11.871,-46.648c-6.5586,-15.07 -15.191,-29.152 -25.648,-41.832c-21.754,-25.945 -50.898,-44.641 -83.551,-53.594c-4.1445,-0.95312 -8.1758,-2.3516 -12.434,-2.9688l-12.766,-1.9023c-4.2578,-0.50391 -8.5117,-0.55859 -12.77,-0.83984l-6.3828,0l-6.3828,0.28125c-4.2695,0.09375 -8.5312,0.41406 -12.77,0.95312l-12.711,1.9609l0,-0.00391c-8.3984,1.7539 -16.652,4.1328 -24.695,7.1133c-32.316,11.816 -60.188,33.332 -79.801,61.602c-9.4805,14.027 -16.91,29.34 -22.066,45.469c-4.1992,14.266 -6.625,28.992 -7.2227,43.848l-19.32,3.3594l-13.16,2.3516l-6.7188,1.1797c0.67188,-6.9453 1.7344,-14.168 2.4648,-22.398l0,-0.00391c1.9844,-10.645 4.6406,-21.156 7.9492,-31.469l0.17096,-0.00083zm-35.109,98.445c1.0273,-5.7852 3.625,-11.176 7.5039,-15.59c3.8789,-4.4141 8.8984,-7.6797 14.504,-9.4414l41.719,-10.582l0.50391,78.398l0.33594,43.008l0,7.8945c-0.02734,0.375 -0.02734,0.75 0,1.1211c-0.12109,1.4609 -0.5625,2.875 -1.2891,4.1445c-1.875,3.3047 -5.4375,5.293 -9.2383,5.1523c-0.37109,0.03516 -0.75,0.03516 -1.1211,0l-1.9023,0l-9.6875,-2.5195l-9.8008,-1.9609c-3.0781,-0.61719 -7,-1.3438 -8.7344,-1.8477l-0.00391,0c-8.8203,-2.4023 -16.035,-8.75 -19.543,-17.191c-0.86328,-2.0312 -1.4844,-4.1523 -1.8477,-6.3281c-0.38672,-2.4453 -0.55469,-4.918 -0.50391,-7.3945l-0.50391,-18.031l-0.72656,-34.609l0,-8.1758l0,0.00391c-0.08203,-2.0234 0.03125,-4.0508 0.33594,-6.0508l-0.0002,-0.00061z' id='svg_4'/%3E%3Cpath fill='%23ffffff' d='m444.22457,573.3928c15.293,12.988 34.703,20.117 54.77,20.117c20.062,0 39.473,-7.1289 54.766,-20.117c11.098,-9.1133 18.953,-21.566 22.402,-35.504c0,-0.67188 -2.9688,-2.7422 -6.8867,-4.5938l-0.00391,0.00391c-2.0977,-1.2539 -4.4531,-2.0156 -6.8867,-2.2422c-7.2617,7.9648 -15.547,14.934 -24.641,20.719c-11.656,7.1094 -25.043,10.871 -38.695,10.871c-13.652,0 -27.039,-3.7617 -38.695,-10.871c-9.1289,-5.7852 -17.453,-12.75 -24.754,-20.719c-2.8828,0.23047 -5.6797,1.1094 -8.1758,2.5781c-3.4141,1.6797 -5.6016,3.3594 -5.6016,3.9766l0.00391,-0.00391c3.3711,14.051 11.234,26.613 22.398,35.785l-0.0002,0.0003z' id='svg_5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
    }
}
.text-blue {
    color: $blue-dark;
}
.text-green {
    color: $green-dark;
}
.text-purple {
    color: $purple-dark;
}
.text-orange {
    color: $orange-dark;
}



//replaced what came up in the search with "company-primary" instead.  Keeping it for now in case all results didn't show up


.cp {
    cursor: pointer;
}

.no-select-text {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}


.highlight {
    font-weight: $highlight-font-weight;
    color: $highlight-color;
    background: $highlight-background-color;
}

.strong {
    font-weight: $bold-font-weight;
}

.focus-glow:focus{
    color: $primary-color;
    border-color: $primary-color;
    outline: 0;
    box-shadow: 0 0 4px 4px $primary-color;
}

fieldset:disabled {
    input{
        background-color: $disabled-color;
        opacity: .75;
    }
    button{
        opacity: .75;
    }
}

.custom-box-shadow{
    box-shadow: $company-neutral-light 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
}

fieldset:disabled{
    input{
        background-color: $disabled-color;
        opacity: .75;
    }
    button{
        opacity: .75;
    }
}

.flex-collapse{
    visibility: collapse;
}
.custom-box-shadow{
    box-shadow: $company-neutral-light 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;
}

.sub-active{
    color: $active-sub;
}
.sub-suspended{
    color: $suspended-sub;
}
.sub-cancelled{
    color: $cancelled-sub;
}
.sub-expired{
    color: $expired-sub;
}

//#endregion

//#region bootstrap overrides / theming
.btn.btn-primary:disabled {
    background-color: $button-disabled-background-color;
    color: $button-disabled-color;
}

div.form-row, div.form-group{
    margin-bottom: 0.5rem;
}

.form-control:disabled{
    background-color: $disabled-color;
    opacity: 0.75;
}

.btn-group>.btn-group:not(:last-child)>.btn, .btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
    margin-right: 0;
}
//#endregion



//START paste of app.css
//#region
body>iframe[style*="2147483647"] {
    display: none;
}

*,
:after,
:before {
    box-sizing: border-box;
}

body {
    font-family: $primary-font-family !important;
    font-size: $primary-font-size !important;
    background-color: $background-color !important;
    color: $primary-font-color !important;
    scrollbar-width: thin !important;
    scrollbar-color: $scrollbar-color $scrollbar-background-color !important;
}

::-webkit-scrollbar {
    width: $scrollbar-width;
}

::-webkit-scrollbar-track {
    background: $scrollbar-background-color;
}

::-webkit-scrollbar-thumb {
    background: $scrollbar-color;
    border-radius: $scrollbar-border-radius;
}

::placeholder{
    color: $disabled-color;
    font-style: italic;
    opacity: 0.75 !important;
}

#root {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
}

.main {
    display: flex;
    flex: 1 0 auto;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.modal-content{
    border-radius: $modal-border-radius !important;
    background-color: $modal-background-color !important;
    border: $modal-border !important;
    max-height: calc(100vh - 0px) !important;

    .modal-header{
        font-family: $modal-header-font-family;
        font-size: $modal-header-font-size;
        border: $modal-header-border !important;
        background-color: $modal-header-background-color;
        padding: $modal-header-padding;
        margin: $modal-header-margin;
        max-height: 72px !important;
        
        @if $modal-header-content == var(--modal-header-content, "") {
            padding: 0;
            margin: 0;

            div,h1,h2,h3,h4,h5,h6{
                display: none;
            }
        }

        button{
            border: $modal-close-button-border;
            border-radius: $modal-close-button-border-radius;
            background-color: $modal-close-button-background-color;
            color: $modal-close-button-color;
            padding: $modal-close-button-padding;
            margin: $modal-close-button-margin;
            opacity: $modal-close-button-opacity;
            width: $modal-close-button-size;
            height: $modal-close-button-size;
            box-shadow: $modal-close-button-shadow;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover{
                background-color: $modal-close-button-hover-background-color;
                color: $modal-close-button-hover-color;
                opacity: $modal-close-button-hover-opacity !important;
            }

            &>span{
                font-size: $modal-close-button-font-size;
            }
        }

        .input-group-append button,
        .input-group-prepend button {
            margin-bottom: 0;
        }
    }

    .modal-body{
        background-color: $modal-body-background-color;
        border-radius: $modal-body-border-radius;
        font-family: $modal-body-font-family;
        font-size: $modal-body-font-size;
        border: $modal-body-border;
        padding: $modal-body-padding;
        line-height: $modal-body-line-height;
        box-shadow: $modal-body-shadow;
        overflow-y: auto !important;
        overflow-x: hidden !important;
    
        .modal-header{
            padding: $modal-header-padding;
            margin: $modal-header-margin;
    
            * {
                display:block;
            }
            .modal-title{
                font-family: $modal-header-font-family;
                font-size: $modal-header-font-size;
            }
        }
    }
}

select{
    border-radius: $form-control-border-radius !important;
}
.form-label{
    font-family: $form-control-label-font-family;
    font-size: $form-control-label-font-size;
    font-weight: $form-control-label-font-weight;
    line-height: $form-control-label-line-height;
    margin: $form-control-label-margin;
    color: $form-control-label-color;
}
.form-control,
.custom-select {
    font-family: $form-control-font-family !important;
    font-size: $form-control-font-size !important;
    font-weight: $form-control-font-weight !important;
    line-height: $form-control-line-height !important;
    color: $form-control-color !important;
    background-color: $form-control-background-color !important;
    border: $form-control-border !important;
    border-radius: $form-control-border-radius !important;
    padding: $form-control-padding !important;
    width: 100%;

    &:focus{
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25) !important;
    }
}

.custom-select{
    appearance: auto !important;
}

span.input-group-text{
    height:100%;
    border-radius: $form-control-border-radius;
    border: $form-control-border;
    font-family: $secondary-font-family;
    font-size: $form-control-font-size;
    font-weight: $form-control-font-weight;
    background-color: $modal-background-color;
    color: $form-control-label-color;
    /*line-height: $form-control-line-height;*/
}


.tabs{
    font-family: $tabs-font-family;
    font-size: $tabs-font-size;
    line-height: $tabs-line-height;
    font-weight: $tabs-font-weight !important;
    background-color: $tabs-background-color;
    color: $tabs-color;
    border-top: $tabs-border-top;
    border-bottom: $tabs-border-bottom;
    border-right: $tabs-border-right;
    border-left: $tabs-border-left;    
    margin: $tabs-margin !important;
    padding: $tabs-padding;
    transition: all 0.125ms ease-in-out;

    a.nav-link {
        font-family: inherit;
        font-weight: inherit;
        font-size: inherit;
        line-height: inherit;
        background-color: inherit;
        color: inherit;
        text-decoration: none;

        background-color: $tab-background-color;
        border-color: $tab-border-color;
        border-top: $tab-border-top;
        border-bottom: $tab-border-bottom;
        border-right: $tab-border-right;
        border-left: $tab-border-left;
        padding: $tab-padding;
        margin: $tab-margin;

        &:hover{
            color: $tab-hover-color;
            background-color: $tab-hover-background-color;
            border-color: $tab-hover-border-color;
            border-top: $tab-hover-border-top;
            border-bottom: $tab-hover-border-bottom;
            border-right: $tab-hover-border-right;
            border-left: $tab-hover-border-left;
            font-weight: $tab-hover-font-weight;
        }

        &.active{
            font-weight: $tab-active-font-weight;
            color: $tab-active-color;
            background-color: $tab-active-background-color;
            border-color: $tab-active-border-color !important;
            border-top: $tab-active-border-top !important;
            border-bottom: $tab-active-border-bottom !important;
            border-right: $tab-active-border-right !important;
            border-left: $tab-active-border-left !important;
        }
    }
}

#site-header{
    background-color: $header-background-color;
    color: $header-font-color;
    font-size: $header-font-size;
    font-weight: $header-font-weight;
    font-family: $header-font-family;
    padding: $header-padding;
    height: $header-height;
    min-height: $header-height;
    max-height: $header-height;
    align-items: center;

    &span,
    &a{
        color: $header-font-color;
        text-decoration: $header-text-decoration;
    }

    .navbar-brand{
        height:$logo-height;
        width: $logo-width;
    }
}

.container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.component-information-icon{
    padding-right: 2rem;
    padding-top: 1rem;
    font-size: $big-font-size;
    color: $secondary-color;
    cursor: pointer;
}

.main-content {
    flex: 1 1 100%;
    background-color: $background-color;
    padding: $content-padding;
    font-size: $content-font-size;
    transition: all .25s ease-in-out;

    .header {
        display:flex;
        justify-content: space-between;
        // breadcrumbs
        .breadcrumb {
            background-color: $breadcrumb-background-color;
            margin: $breadcrumb-margin;
            padding: $breadcrumb-padding;
            border-radius: $breadcrumb-border-radius;

            .breadcrumb-item {
                font-family: $primary-font-family;
                font-size: $breadcrumb-font-size;
                color: $breadcrumb-default-color;
            }

            a,
            .breadcrumb-item a {
                color: $breadcrumb-color;
                font-size: $breadcrumb-font-size;
            }
            .breadcrumb-item a:hover {
                color: $breadcrumb-hover-color;
                background: $breadcrumb-background-color;
            }

        }

        .col,
        .col-sm-auto {
            display: flex;
            align-content: center;
            align-items: center;
            padding-left: 0;
            padding-right: 0;
        }

        h1 {
            font-family: $secondary-font-family;
            font-size: $secondary-font-size;
            font-weight: $secondary-font-weight;
        }
    }
}

.section-btn-header{
    @include flex-row-space-between;
    button{
        @include basic-button;
    }
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: $secondary-font-family;
    font-weight: $secondary-font-weight;
}
h1 {
    font-size: $secondary-font-size;
}

hr {
    border-top-color: $divider-color !important;
    opacity: 1 !important;
}

ul {
    list-style: outside none none;
    margin: 0;
    padding: 0;
}

div.table a,
table a,
li a,
div a,
.links,
a {
    text-decoration: $link-text-decoration;
    font-size: $link-font-size;
    font-weight: $link-font-weight;
    color: $link-color;
    padding: $link-padding;
    margin: $link-margin;
    cursor: pointer;
    transition: all .15s ease-in-out;

    &:hover {
        text-decoration: $link-hover-text-decoration;
        color: $link-hover-color;
    }

    &:active,
    &:focus,
    &:visited {
        text-decoration: inherit;
        color: inherit;
    }

    &:active,
    &:focus{
        color: $link-active-color;
    }    
}

.user-buttons {
    background-color: transparent;
    color:$primary-font-color;
    transition-duration: 0s;
    cursor:pointer;

    &:hover, &.active:hover {
        color: $primary-color;
        background-color: transparent !important;
    }
    
    &.active {
        color: $primary-font-color !important;
        background-color: transparent !important;
    }
}

.show {
    display: block !important;
}

.hide {
    display: none !important;
}

.visible {
    visibility: visible;
    opacity: 1;
}

.uppercase{
    text-transform: uppercase;
}

.lowecase{
    text-transform: lowercase;
}

.capitalize{
    text-transform: capitalize;
}

.secondary-font{
    font-family: $secondary-font-family;
}

.primary-font{
    font-family: $primary-font-family;
}

.bold {
    font-weight: 500;
}

.bolder{
    font-weight: 600;
    font-size: 1.2rem;
}

.text-center {
    text-align: center !important;
}

.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

.vert-mid td {
    vertical-align: middle;
}

.input-group-append .btn {
    padding: revert;
}

.button-row {
    /* Back / Next button row */
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 1rem 0;
    column-gap: 12px;

    .btn {
        margin: 0;
    }
}
.button-row.left {
    justify-content: flex-start;
}
.button-row.right {
    justify-content: flex-end;
}
.button-row.center {
    justify-content: center;
}

div.dropdown-menu{
    font-family: $dropdown-font-family;
    background-color: $dropdown-background-color;
    border: $dropdown-border;
    border-radius: $dropdown-border-radius;
    padding: $dropdown-padding;
    margin: $dropdown-margin;
    font-size: $dropdown-font-size;
    font-weight: $dropdown-font-weight;
    line-height: $dropdown-line-height;
    color: $dropdown-color;
    box-shadow: $dropdown-shadow;

    .dropdown-item{
        font-family: $dropdown-item-font-family;
        background-color: $dropdown-item-background-color;
        border: $dropdown-item-border;
        border-radius: $dropdown-item-border-radius;
        padding: $dropdown-item-padding;
        margin: $dropdown-item-margin;
        font-size: $dropdown-item-font-size;
        font-weight: $dropdown-item-font-weight;
        line-height: $dropdown-item-line-height;
        color: $dropdown-item-color;

        &:hover{
            background-color: $dropdown-item-hover-background-color !important;
            color: $dropdown-item-hover-color !important;
            text-decoration: $dropdown-item-hover-text-decoration !important;
            font-weight: $dropdown-item-hover-font-weight !important;
        }

        &.active{
            background-color: $dropdown-item-active-background-color;
            color: $dropdown-item-active-color;
            font-weight: $dropdown-item-active-font-weight;
        }

        &.disabled{
            background-color: $dropdown-item-disabled-background-color;
            color: $dropdown-item-disabled-color;
            font-weight: $dropdown-item-disabled-font-weight;
        }
        
    }
}

.progress{
    height: 5px !important;
    border-radius: $form-control-border-radius !important;
    background-color: $disabled-color !important;
    
    .progress-bar{
        background-color: $primary-color !important;
    }
}

.profileMenu{
    .list-group-item{
        font-size: $link-font-size;
        font-weight: $link-font-weight;
        text-decoration: $link-text-decoration;
        background-color: inherit;

        &:hover{
            color: $link-hover-color;
        }

        i:not(:first-child){
           font-size:75%;
        }
    }
}

div.btn-group-toggle{
    label.btn{
        input[type="radio"]{
            display:none;
        }

        &.active{
            background-color: $primary-color;
        }
    }
}

legend{
    font-family: $secondary-font-family;
    font-size: $primary-font-size !important;
}

.card.standout {
    border: $card-standout-border;
}

.btn {
    // styles applied directly to .btn aren't loading for some reason, applying to specific button types instead
    &.btn-primary,
    &.btn-secondary,
    &.btn-light,
    &.btn-danger,
    &.btn-outline-primary,
    &.btn-outline-danger,
    &.btn-outline-secondary,
    &.btn-outline-light,
    &.btn-dark,
    &.btn-sm,
    &.btn-lg{
        // display:flex;
        position: relative;
        font-family: $button-font-family;
        font-size:$button-font-size;
        font-weight: $button-font-weight;
        line-height: $button-line-height;
        border-radius: $button-border-radius;
        padding: $button-padding;
        margin: $button-margin;
        border: $button-border;
        color: $button-color;
        background-color: $button-background-color;
        text-transform: $button-text-transform;
        text-shadow: $button-text-shadow;
        box-shadow: $button-shadow;
        text-decoration: none;
        align-items: center;
        width: fit-content;

        &:hover {
            background-color: $button-hover-background-color;
            color: $button-hover-color;
            text-decoration: none;
        }

        &:first-child{
            margin-left: 0;
        }

        /*
        &:last-child{
            margin-right: 0;
        }*/

        &:disabled,
        .disabled,
        &:disabled:hover {
            color: $button-disabled-color;
            background-color: $button-disabled-background-color;
        }
    }

    &.btn-outline-primary,
    &.btn-outline-danger,
    &.btn-outline-secondary,
    &.btn-outline-light{
        box-shadow: $shadow-elevation-1;
        color: $primary-font-color;
    }


    &.btn-secondary,
    &.btn-outline-secondary{
        background-color: $secondary-color;
        color: $button-color;
        font-size: $button-font-size;
        font-weight: $button-font-weight;
        text-transform: $button-text-transform;
        &:hover {
            background-color: $secondary-hover-color;
            color: $button-hover-color;
        }
    }

    &.btn-outline-primary {
        background:transparent;
        border: $button-border-width solid $button-background-color;
    }

    &.btn-outline-light {
        background: transparent;
        color: $neutral-color;
    }
    
    &.btn-danger {
        background-color: $error-color;
    }

    &.active{
        background-color: $button-active-background-color !important;
        color: $button-active-color !important;
        filter: $button-active-filter;
    }

    &.submitting:after,
    &.loading:after {
        font-family: 'Font Awesome 5 Pro';
        font-weight: 300;
        content: '\f1ce';
        position: absolute;
        right: .75rem;
        animation: fa-spin 2s linear infinite;
    }

    &.w-100{
        text-align: center;
        justify-content: center;
    }

    i.fab,
    i.fal,
    i.far,
    i.fas,
    i.fad {
        margin-right: $button-icon-margin;
    }

    &.icon-on-right {
        i.fab,
        i.fal,
        i.far,
        i.fas,
        i.fad {
            margin-left: $button-icon-margin;
            margin-right: 0;
        }
    }

    .button-icon-margin {
        margin-left: $button-icon-margin;
        margin-right: $button-icon-margin;
    }
    
    .button-icon-margin-right {
        margin-left: 0;
        margin-right: $button-icon-margin;
    }
    
    .button-icon-margin-left {
        margin-left: $button-icon-margin;
        margin-right: 0 !important;
    }

    &.btn-sm {
        font-size: $button-small-font-size;
        font-weight: $button-font-weight;
        padding: $button-small-padding !important;
    }

    &.btn-lg {
        font-size: $button-large-font-size !important;
        font-weight: $button-font-weight !important;
        padding: $button-large-padding !important;
    }

    &.btn-light,
    &.btn-transparent {
        background-color: $neutral-background-color;
        color: $neutral-color;
        font-size: $button-font-size;
        font-weight: $button-font-weight;
        text-transform: $button-text-transform;
        box-shadow: $button-shadow;
        border: $neutral-border;

        i.fal,
        i.fab,
        i.far,
        i.fas,
        i.fad {
            color: $neutral-color !important;
        }

        &:hover,
        &:focus{
            color:$neutral-hover-color;
            background-color: $neutral-hover-background-color;
            border: $neutral-hover-border;
        }
    }


    &.btn-transparent {
        background:  $button-transparent;
    }

    &.btn-outline-light, &.btn-light {
        i.fal,
        i.far,
        i.fab,
        i.fas,
        i.fad {
            color: $primary-font-color;
            opacity: .75;
            
        }

        &:hover{
            background-color: $neutral-hover-background-color;
            color: $neutral-hover-color;
            box-shadow: $button-shadow;

            i.fal,
            i.far,
            i.fab,
            i.fas,
            i.fad {
                color: $neutral-hover-color;
                opacity: 1;
            }
        }
    }



    &.btn-primary:active,
    &.btn-secondary:active {
        filter: $button-active-filter;
    }

    &.btn-primary:active,
    &.btn-primary:focus,
    &.btn-secondary:active,
    &.btn-secondary:focus {
        box-shadow: none !important;
    }
    &.rounded{
        border-radius: 50% !important;
        width: 2rem !important;
        height: 2rem !important;
        padding: 0.5rem !important;
    }    
}

.input-group-append .btn,
.input-group-prepend .btn {
    margin-bottom: 0;
}

button.dropdown-toggle.btn{
    position:relative;
    padding-right: 3rem !important;

    &:after{
        font-family: 'Font Awesome 5 Pro';
        content: "\f078" !important;
        font-weight: 600;
        border: 0 !important;
        margin-left:0.5rem;
        font-size: 0.65rem;
        position:absolute;
        right:1rem;
    }
}

button[data-cy="done-btn"].btn{
    text-indent: -9999999px;
    position: relative;
    width: 2.5rem !important;
    height: 2.5rem !important;
    padding: 0 !important;

    &:after {
        position: absolute;
        top: 50%;
        left: 50%;
        font-family: 'Font Awesome 5 Pro';
        font-weight: 400;
        content: "\f00d";
        text-indent: 0;
        transform: translate(-50%, -50%);
        font-size: $modal-close-button-font-size;
    }
}

.vertical-align {
    display: flex;
    align-items: center;
}

.horizontal-align-center {
    display: flex;
    justify-content: center;
}

div.popover {
    background-color: $modal-body-background-color;
    border-radius: $modal-body-border-radius;
    border: $modal-border;
    color: $primary-font-color !important;
    box-shadow: $shadow-elevation-2;
    max-width: unset;
    position: relative;
    animation: popoverFadeIn 0.7s;

    .popover-body {
        color: $primary-font-color !important;
    }
}

/* w3-animate-bottom{position:relative;animation:animatebottom 0.4s} */
@keyframes popoverFadeIn {
    from {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.fa-d {
    display: inherit !important;
}

div.rbt-token,
.badge-chip {
    font-family: $chip-font-family;
    padding: $chip-padding;
    font-weight:$chip-font-weight;
    font-size: $chip-font-size;
    background-color: $chip-background-color;
    box-shadow: $shadow-elevation-1;
    display: inline-flex;
    margin: $chip-margin;
    transition: all .15s ease-out;
    white-space: normal;
    text-align: left;
    line-height: $chip-line-height;
    border-radius: $chip-border-radius;
    color: $chip-color;
    border: $chip-border;
    max-width: 250px;
    width: fit-content;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;

    &:hover {
        background-color: $chip-hover-background-color;
        color: $chip-hover-color;
    }

    &:last-child{
        margin-right: 0;
    }
}

div.rbt-aux{
    width: fit-content;
    height: fit-content;
    margin: 1px;
}
button.rbt-close{
    border: $button-border;
    margin: 0 !important;
}

div.rbt-token.rbt-token-removeable{
    padding-right: 1.5rem !important;

    .rbt-token-remove-button{
        font-size: inherit !important;
        background-color: $chip-background-color;
        border: 0;
    }
}

.tooltip-error>.tooltip-inner {
    background-color: $error-color;
    color: $error-text-color;
}

.tooltip-error>.arrow:before {
    border-bottom-color: $error-color;
}

.profile-card {
    background-color: $profile-card-background-color;
    padding: $profile-card-padding;
    margin: $profile-card-margin;
    border-radius: $card-border-radius;
    border:$card-border;
    box-shadow: $shadow-elevation-1;
    &img {
        width: $profile-card-image-width;
        max-height: $profile-card-image-height;
        height: auto;
        border-radius: $profile-card-image-border-radius;
        box-shadow: $shadow-elevation-1;
        border: $profile-card-image-border;
        margin: $profile-card-image-margin;
        object-fit: cover;
        object-position: center;
    }    
    @media (max-width: 420px){
        margin: .5rem .5rem .5rem .5rem;
    }
}

.profile-name {
    color: $primary-color;
    font-weight: $bold-font-weight;
    font-size: $big-font-size;
    line-height: $big-font-line-height;
}

a.card,
div.card,
.prof-bg {
    font-size: $card-font-size;
    background: $card-background-color;
    color: $card-color;
    padding: $card-padding;
    border-radius: $card-border-radius;
    border: $card-border;
    box-shadow: $shadow-elevation-1;

    div.card-footer{
        background-color: $card-footer-background-color;
        border-top: $card-footer-border;
        border-radius: $card-footer-border-radius;
        padding: $card-footer-padding;
        margin: $card-footer-margin;
        
    }
}

.titles, h4.prof-he {
    padding: $card-title-padding;
    margin-bottom: $card-title-margin;
    font-family: $card-title-font-family;
    font-weight: $card-title-font-weight;
    font-size: $card-title-font-size;
    line-height: $card-title-line-height;
}

.prof-he-line {
    margin: $divider-margin;
    border-top: $divider-border;
}

.label-desc {
    overflow: hidden;
}

.prof-amount {
    font-weight: $bold-font-weight;
}

.pay-am {
    font-weight: $bold-font-weight;
    text-align: right;
}

.pay-he strong {
    font-weight: $bold-font-weight;
}

.bor-t {
    border-top: $divider-border;
}

.btn-credit-wh:hover {
    background: rgb(216, 216, 216);
    border: 0.5px solid #6A7178;
    color: #000;
}

.btn-credit-or {
    /* border: 2px solid #000; */
    background: #f67730;
    color: #fff;
    text-shadow: 0 0 black;
    border: 0.5px solid #f67730;
    border-radius: 100px;
    font-size: 18px;
    padding: 10px 35px;
    margin: 20px;
}

.btn-credit-or:hover {
    background: #f67730;
    border: 0.5px solid #f67730;
    color: #fff;
    opacity: 0.8;
}

.profile-card-info {
    font-size: $primary-font-size;
    color: $primary-color;
}

.profile-card-name {
    font-family: $card-title-font-family;
    font-size: $card-title-font-size;
    font-weight: $card-title-font-weight;
    line-height: $card-title-line-height;
    margin: $card-title-margin;
    padding: $card-title-padding;
    color: $card-title-color;
}

.br-ri {
    border-right: $divider-border;
}

.ali-ri {
    text-align: right;
}

.subs-active {
    color: $tertiary-color;
}

.org-desc {
    padding: .3rem .5rem;
    border-left: 4px solid;
    border-color: $secondary-color;
    background: $secondary-light-color;
    margin-bottom: 2px;
}

.blue-desc {
    padding: .3rem .5rem;
    border-left: 4px solid;
    border-color: $primary-color;
    background: $neutral-background-color;
    margin-bottom: 2px;
}

.pd-10 {
    padding: .3rem 1rem;
}

.highlight {
    color: $highlight-color;
}

.card,
.card.card-panel {
    border: $card-border;
    border-radius: $card-border-radius;
    background-color: $card-background-color;
    color: $card-color;
    padding: $card-padding;
    box-shadow: $shadow-elevation-1;
    transition: all 0.3s cubic-bezier(.25, .8, .25, 1);
}

div.content-card {
    margin: $card-margin;
    @media (max-width: 420px){
        margin: .5rem .5rem .5rem .5rem;
    }
}

div.modal-card {
    margin: .5rem;
    padding: 16px;
}

.card.card-panel,
.card.card-panel .card-body {
    padding: $card-padding;
    width: 100%;
}

.profile-picture {
    width: $profile-image-big-size;
    height: $profile-image-big-size;
    margin-bottom: $profile-image-margin;
}

.card .card-title {
    position: relative;
    font-size: $card-title-font-size;
    font-family: $card-title-font-family;
    font-weight: $card-title-font-weight;
    line-height: $card-title-line-height;
    margin: $card-title-margin;
    padding: $card-title-padding;
    color: $card-title-color;
}

.card .card-subtitle {
    font-family: $card-subtitle-font-family;
    font-size: $card-subtitle-font-size;
    font-weight: $card-subtitle-font-weight;
    line-height: $card-subtitle-line-height;
    margin: $card-subtitle-margin;
    padding: $card-subtitle-padding;
    color: $card-subtitle-color;
}

.card .card-body .card-header-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.card .card-body .nav a {
    color: inherit;
    display: flex;
    align-items: center;
}

.card .card-body .nav a .badge {
    background-color: $badge-background-color;
    color: $badge-color;
    padding: $badge-padding;
    margin: $badge-margin;
    font-size: $badge-font-size;
    font-weight: $badge-font-weight;
    line-height: $badge-line-height;
    border-radius: $badge-border-radius;
    border: $badge-border;
}

.user-container .card {
    padding: 0.2rem 0.5rem;
    margin: 0.2rem;
}

@media (max-width: 991px) {
    .hid-id {
        display: none !important;
    }
}


@media (min-width: 576px) {
    .card-columns {
        column-count: 1;
    }
}

@media (min-width: 992px) {
    .card-columns {
        column-count: 2;
    }
}

@media (min-width: 1200px) {
    .card-columns {
        column-count: 3;
    }
}

.rounded-circle {
    border-radius: 50%;
}

.tile {
    background-color: $card-background-color;
}

.tile .card-title {
    font-family: $card-title-font-family;
    font-size: $card-title-font-size;
    font-weight: $card-title-font-weight;
    line-height: $card-title-line-height;
    margin: $card-title-margin;
    padding: $card-title-padding;
    color: $card-title-color;
}

.tile .card-text {
    padding: $card-padding;
}

.tile ul {
    list-style-type: circle;
}

.tile img {
    padding: 0;
}

div.table,
table{
    margin: $table-margin;
}

div.table-header,
table thead {
    font-family: $table-header-font-family;
    font-size: $table-header-font-size !important;
    font-weight: $table-header-font-weight !important;
    line-height: $table-header-line-height !important;
    color: $table-header-color;
    padding: $table-header-padding !important;
    margin: $table-header-margin;
    text-transform: $table-header-text-transform;
    border: $table-header-border !important;
    border-radius: $table-header-border-radius;
    background-color: $table-header-background-color !important;
    text-align: $table-header-text-align;
    box-shadow: $table-header-shadow;

    i.fad{
        margin-left:0.5rem;
    }
}

div.table-header{
    display: flex;
    flex-direction: row;
    align-items: center;
}

table thead th{
    padding: $table-header-padding !important;
    border: 0 !important;
    background-color: transparent !important;
    font-family: $table-header-font-family !important;
    font-size: $table-header-font-size !important;
    font-weight: $table-header-font-weight !important;
}

div.table-row,
table tbody tr{
    font-family: $table-row-font-family;
    font-size: $table-row-font-size;
    font-weight: $table-row-font-weight;
    line-height: $table-row-line-height;
    color: $table-row-color;
    padding: $table-row-padding;
    margin: $table-row-margin;
    text-transform: $table-row-text-transform;
    border: $table-row-border;
    border-bottom: $table-row-border-bottom;
    border-radius: $table-row-border-radius;
    background-color: $table-row-background-color;
    text-align: $table-row-text-align;
    box-shadow: $table-row-shadow;
    transition: all 0.125s ease-in-out;
    
    &:nth-child(odd) {
        background-color: $table-row-background-color-odd;
    }
    &:hover,
    &.active {
        background-color: $table-row-hover-background-color;
        &.primary{
            background-color: $primary-light-color;
        }
    }
}
div.table-row{
    display:flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: center;
    height: fit-content;
}

div.table-row div,
table tbody tr td{
    border-bottom:0 !important;
    box-shadow: none;
}

div.table-row div{
    height:100%;
    width:100%;
    justify-content: center;
}

.small,
small {
    font-size: $small-font-size;
    line-height: $small-font-line-height;
}

.smaller{
    font-size: $small-font-size;
}

@media (min-width: 768px) {
    .ms-sm-auto {
        margin-left: auto !important;
    }
}

@media (max-width: 786px) {
    .br-ri {
        border-right: 0;
    }

    .btn-credit-wh {
        margin: 10px;
    }
}

.react-datepicker-wrapper {
    display: unset;
}

.react-datepicker-wrapper + .react-datepicker__tab-loop{
    z-index:9999;
}

.react-datepicker {
    font-family: $secondary-font-family;
    font-size: $form-control-font-size;
    padding: $form-control-padding;
    width:100%;
    min-width:350px;
    background-color: $card-background-color;
    color: $primary-font-color;
    border: 1px solid $secondary-font-color;
    border-radius: $card-border-radius;
    box-shadow: $shadow-elevation-1;
}
.react-datepicker__triangle{
    border: 1px solid $secondary-font-color;
}

div.modal-body .react-datepicker{
    border:0;
    box-shadow: none;
    background-color: $card-background-color;
    padding:0;
}

.react-datepicker *:focus {
    outline: 0;
}

.react-datepicker__month-container{
    width:100%;
}

.react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle,
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
    margin-bottom: -7px;
}

.react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
    margin-top: -7px;
}

.react-datepicker__navigation {
    border: 0;
    margin: 1.55rem .55rem 0 .55rem;
}

.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown,
.react-datepicker__month-year-dropdown {
    background-color: $form-control-background-color;
    text-align: left;
}

.react-datepicker__year-dropdown-container--select,
.react-datepicker__month-dropdown-container--select,
.react-datepicker__month-year-dropdown-container--select,
.react-datepicker__year-dropdown-container--scroll,
.react-datepicker__month-dropdown-container--scroll,
.react-datepicker__month-year-dropdown-container--scroll {
    margin-top: .35rem;
    margin-right: .75rem;
}

.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown,
.react-datepicker__month-year-dropdown {
    top: unset;
    left: unset;
    font-size: .85rem;
    font-weight: 500;
    border: $form-control-border;
    border-radius: $form-control-border-radius;
}

.react-datepicker__year-option--selected,
.react-datepicker__month-option--selected,
.react-datepicker__month-year-option--selected {
    right: 1rem;
    display: none;
}

.react-datepicker__month-option--selected_month,
.react-datepicker__year-option--selected_year {
    background-color: $primary-color;
    color: $primary-font-color;
}

.react-datepicker__year-option,
.react-datepicker__month-option,
.react-datepicker__month-year-option {
    line-height: 1.5rem;
    padding: .3rem .85rem;
}

.react-datepicker__year-option:hover,
.react-datepicker__month-option:hover,
.react-datepicker__month-year-option:hover {
    background-color: $form-control-background-color;
    color: inherit;
}

.react-datepicker__navigation--years-previous,
.react-datepicker__navigation--years-upcoming,
.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow,
.react-datepicker__navigation--previous,
.react-datepicker__navigation--next {

    border: none;
    width:1rem;
    height:1rem;
    text-indent: unset !important;
    color:transparent;
    line-height: inherit;

    &:after{
        // content: '\f053';
        font-family: 'Font Awesome 5 Pro';
        color: $primary-font-color !important;
        font-size: 1rem;
        line-height: 1rem;
        position: absolute;
        top:0;
        left:0;
        width:1rem;
        height:1rem;
        text-indent: unset !important;

    }
}

.react-datepicker__navigation--next {
    &:after{
        // content: '\f054';
    }
}

.react-datepicker__navigation--years-previous{
    &:after{
        // content: '\f078';
    }
}

.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
    &:after{
        // content: '\f078';
        font-size:.75rem;
    }
}

.react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow {
    top: 2px;
    margin-left: .5rem;
}

.react-datepicker__month-read-view--down-arrow{
    margin-left:.5rem;
}

.react-datepicker__navigation--years-upcoming {
    &:after{
        content: '\f077';
    }
}

.react-datepicker__navigation--years-previous,
.react-datepicker__navigation--years-upcoming {
    margin: 0 auto;
}


.react-datepicker__year-read-view--down-arrow::before,
.react-datepicker__month-read-view--down-arrow::before,
.react-datepicker__month-year-read-view--down-arrow::before {
    content: unset;
}

.react-datepicker__header {
    background-color: transparent;
    border-bottom: 0;
}

.react-datepicker__header__dropdown {
    font-size: $form-control-font-size;
    font-weight: $bold-font-weight;
}

.react-datepicker__day-names,
.react-datepicker__month > .react-datepicker__week{
    display: flex;
    justify-content: space-between;
    margin-bottom: 0;
    margin-top: 0;
}

.react-datepicker__header .react-datepicker__day-names{
    margin-top:$main-padding;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
    font-weight: $bold-font-weight;
    font-size: $form-control-font-size;
}

.react-datepicker__current-month {
    display: none;
}


.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name{
    color: $primary-font-color;
}

.react-datepicker__day-name {
    margin-top: 1rem;
}

.react-datepicker__day--outside-month {
    color: $disabled-color;
    user-select: none;
}


.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
    width: 100%;
    line-height: 3rem;
    margin:0;
}

.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__month-text--keyboard-selected:hover,
.react-datepicker__quarter-text--keyboard-selected:hover,
.react-datepicker__year-text--keyboard-selected:hover,
.react-datepicker__day--selected:hover,
.react-datepicker__day--in-selecting-range:hover,
.react-datepicker__day--in-range:hover,
.react-datepicker__month-text--selected:hover,
.react-datepicker__month-text--in-selecting-range:hover,
.react-datepicker__month-text--in-range:hover,
.react-datepicker__quarter-text--selected:hover,
.react-datepicker__quarter-text--in-selecting-range:hover,
.react-datepicker__quarter-text--in-range:hover,
.react-datepicker__year-text--selected:hover,
.react-datepicker__year-text--in-selecting-range:hover,
.react-datepicker__year-text--in-range:hover {
    background-color: $primary-hover-color;
    color: $button-hover-color;
}

.react-datepicker__day--today,
.react-datepicker__day:hover,
.react-datepicker__day:focus,
.react-datepicker__month-text:hover,
.react-datepicker__quarter-text:hover,
.react-datepicker__year-text:hover {
    border-radius: $date-picker-day-border-radius;
    background-color: $primary-hover-color;
    color: $button-hover-color;
    outline: 0;
}

.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--keyboard-selected,
.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range,
.react-datepicker__month-text--selected,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--selected,
.react-datepicker__year-text--in-selecting-range,
.react-datepicker__year-text--in-range {
    background-color: $primary-color;
    color: $button-hover-color;
    border-radius: $date-picker-day-border-radius;
}

.react-datepicker__day--today,
.react-datepicker__month-text--today,
.react-datepicker__quarter-text--today,
.react-datepicker__year-text--today {
    font-weight: $bold-font-weight;
}


.react-datepicker__day--selected.react-datepicker__day--outside-month {
    background-color: transparent;
}

.event-types {
    min-width: 201px;
}

div.form-check {
    display: flex;
    align-items: center;
    margin-left: 0;

    .form-check-input{
        margin-top: 0;
        margin-left: inherit !important;
    }

    .form-check-label{
        font-family: $form-control-label-font-family;
        font-size: $form-control-label-font-size;
        line-height: $form-control-label-line-height;
        color: $form-control-label-color;
    }
}
.form-check-input{
    position: relative !important; //this prevents the checkbox from stacking ontop of the label b/c of bootstrap default absolute
}

input.form-range {
    &:focus {
      &::-webkit-slider-thumb { box-shadow: 0 0 0 1px $background-color, 0 0 0 0.25rem $primary-color !important; }
      &::-moz-range-thumb     { box-shadow: 0 0 0 1px $background-color, 0 0 0 0.25rem $primary-color !important; }
    }
  
    &::-webkit-slider-thumb {
        background-color: $primary-color !important;

      &:active {
        background-color: $primary-light-color !important;
      }
    }
  
    &::-webkit-slider-runnable-track {
      background-color: $divider-color !important;
    }
  
    &::-moz-range-thumb {
        background-color: $primary-color !important;
  
      &:active {
        background-color: $primary-light-color !important;
      }
    }
  
    &::-moz-range-track {
      background-color: $primary-color !important;
    }
}

input.form-check-input {
    margin-right: 0.5rem;

    &:checked {
        background-color: $primary-color !important;
        border-color: $primary-color !important;
    }
    &:focus{
        border-color: $primary-color !important;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25) !important;        
    }
}

.form-switch{
    padding-left:0 !important;

    input.form-check-input{
        border-radius: $form-control-switch-border-radius;
    }
    input.form-check-input:not(:checked):focus{
        background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%27-4 -4 8 8%27%3e%3ccircle r=%273%27 fill=%27rgba%280, 0, 0, 0.25%29%27/%3e%3c/svg%3e") !important;
    }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type=number] {
  -moz-appearance: textfield;
}

.text-muted {
    margin-bottom: .25rem;
}

.mr-l10 {
    margin-left: 10px !important;
}

.mr-r10 {
    margin-right: 10px !important;
}

.mr-b10 {
    margin-bottom: 10px !important;
}

.mr-b20 {
    margin-bottom: 20px !important;
}

.mr-b30 {
    margin-bottom: 30px !important;
}

.mr-t10 {
    margin-top: 10px !important;
}

.mr-t20 {
    margin-top: 20px !important;
}

.__react_component_tooltip {
    padding: .25rem .4rem .25rem .4rem !important;
    opacity: 1 !important;
}

.__react_component_tooltip::before {
    opacity: 1 !important;
}

.box-shadow-1{
    box-shadow: $shadow-elevation-1;
}

.box-shadow-2{
    box-shadow: $shadow-elevation-2;
}

.box-shadow-3{
    box-shadow: $shadow-elevation-3;
}

@media(max-width: 576px) {
    .btn-group {
        margin-right: 1rem;
    }
}

@media (max-width: 786px) {
    .tab-scrol {
        overflow-x: auto;
        width: 600px;
    }

    .tab-scrol div {
        padding: 5px;
    }

    .ov-x-sc {
        overflow-x: scroll;
    }
}

@media (max-width: 630px) {
    .tab-scrol {
        overflow-x: auto;
        width: 380px;
    }

    .tab-scrol div {
        padding: 5px;
    }

    .ov-x-sc {
        overflow-x: scroll;
    }
}

@media (max-width: 375px) {
    .tab-scrol {
        overflow-x: auto;
        width: 340px;
    }

    .tab-scrol div {
        padding: 5px;
    }

    .ov-x-sc {
        overflow-x: scroll;
    }
}

.col-parent-child {
    min-width: 173px;
}


.preload-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    z-index: 9999999;
    pointer-events: none;
}

.preload-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding-top: 0px;
    padding-left: 0px;
    padding-right: 0px;
    background-color: $primary-color;
    color: $primary-font-color;
    width: 0%;
    height: 100%;
    overflow: hidden;
}

.Headd {
    opacity: 0;
    pointer-events: none;
}

.error-boundary-wrapper {
    position: absolute;
    width: 100vw;
    height: 100vh;
    padding-right:0;
    display: flex;
    align-self: center;
    justify-content: center;
    top: 50%;
    transform: translateY(-50%);
    
    &:after{
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: $error-color;
        background: linear-gradient(to right, $background-color, $error-color);
        opacity: 0.8;
        z-index: -1;
        mask-image: url("data:image/svg+xml,%3Csvg version=%271.1%27 xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 500 500%27 width=%27100%25%27%3E%3Cpath fill=%27%23000%27%3E%3Canimate attributeName=%27d%27 dur=%2710700ms%27 repeatCount=%27indefinite%27 values=%27M440.5,320.5Q418,391,355.5,442.5Q293,494,226,450.5Q159,407,99,367Q39,327,31.5,247.5Q24,168,89,125.5Q154,83,219.5,68Q285,53,335.5,94.5Q386,136,424.5,193Q463,250,440.5,320.5Z;M453.78747,319.98894Q416.97789,389.97789,353.96683,436.87838Q290.95577,483.77887,223.95577,447.43366Q156.95577,411.08845,105.64373,365.97789Q54.33169,320.86732,62.67444,252.61056Q71.01719,184.3538,113.01965,135.21007Q155.02211,86.06634,220.52211,66.46683Q286.02211,46.86732,335.5,91.94472Q384.97789,137.02211,437.78747,193.51106Q490.59704,250,453.78747,319.98894Z;M411.39826,313.90633Q402.59677,377.81265,342.92059,407.63957Q283.24442,437.46649,215.13648,432.5428Q147.02853,427.61911,82.23325,380.9572Q17.43796,334.29529,20.45223,250.83809Q23.46649,167.38089,82.5856,115.05707Q141.70471,62.73325,212.19045,63.73015Q282.67618,64.72705,352.67308,84.79839Q422.66998,104.86972,421.43486,177.43486Q420.19974,250,411.39826,313.90633Z;M440.5,320.5Q418,391,355.5,442.5Q293,494,226,450.5Q159,407,99,367Q39,327,31.5,247.5Q24,168,89,125.5Q154,83,219.5,68Q285,53,335.5,94.5Q386,136,424.5,193Q463,250,440.5,320.5Z;%27%3E%3C/animate%3E%3C/path%3E%3C/svg%3E");
        mask-repeat: no-repeat;
        mask-position: top right;
        mask-size: contain;
    }
}

.error-boundary-fallback {
    position:relative;
    display: flex;
    flex-direction: column;
    align-self: center;
    background-color:transparent !important;
    font-family: $secondary-font-family;
    color: $primary-font-color;
    box-shadow: $shadow-elevation-0 !important;
    padding: $card-padding;
    border-radius: $card-border-radius;
    max-width: 500px;

    h1 {
        font-family: $card-title-font-family;
        font-size: $card-title-font-size;
        font-weight: $card-title-font-weight;
        line-height: $card-title-line-height;
        margin: $card-title-margin;
        padding: $card-title-padding;
    }

    .try-home-btns{
        display: flex;
        flex-direction: row;
        
        button,
        a{
            align-self: center;
            width: fit-content;
        }
    }
}

.hide-space{
    visibility: hidden;
}

.hidden {
    display: none !important;
}

/* switch coloring */
.custom-control-input:checked~.custom-control-label::before {
    color: $primary-inverse-color;
    border-color: $primary-color;
    background-color: $primary-color;
}


/* Summary Grids */
.summary-header {
    font-weight: $bold-font-weight;
}

.summary-grid {
    display: grid;
    overflow-x: auto;
    grid-template-columns: auto 1fr;
    grid-template-rows: minmax(.75rem, auto);
    column-gap: 1.5rem;
    row-gap: 0.7rem;
}

.confirmation-display {
    text-align: center;
}

.confirmation-display.reg {
    font-weight: 400;
}
//#endregion
//END paste of app.css


//#region checkin
.checkin {
    padding: $main-padding;

    .user-pic {
        width: $profile-image-huge-size;
        height: $profile-image-huge-size;
        background-size: contain;
        // border-radius: $profile-image-border-radius;
    }

    h4 {
        margin-top: 1rem;
    }

    .sign-waiver-btn {
        background-color: $secondary-color;
        color: $secondary-inverse-color;
    }

    .sign-waiver-btn.signed {
        background-color: $success-color;
    }

    .checkin-cards {
        &.card {
            display: flex;
            justify-content: center;
            border: 0;
            box-shadow: none;
        }
        .family-member{
            margin: 3px;
        }
        .card-subtitle{
            font-size: 1.25rem;
        }
    }

    .checkin-grid {
        display: grid;
        grid-template-columns: auto;
        column-gap: $main-padding;
        row-gap: 5px;
    }

    h2.error-text {
        font-size: $primary-font-size;
        background-color: $error-color;
        color: $error-text-color;
    }
    .middle-column{
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .click-card.card{
        text-align:center;
        padding: $card-padding;
        margin: $card-margin;
        box-shadow: $shadow-elevation-1;
    }
}

//#endregion


//--Lists
//#region

.list-group-item {
    padding: .5rem !important;
    border: 0 !important;
    transition: all .12s linear;
}

.list-group-item-action:hover {
    color: $link-hover-color !important;
    background-color: inherit !important;
}

.list-group-item.active,
.list-group-item-action:focus{
    color: $link-active-color !important;
    background-color: inherit !important;
}


//#endregion


.general-typeahead {

    // Align the X close all button to the center of the input at the top
    .rbt-aux {
        align-items: flex-start;
    }
    .rbt-aux .rbt-close {
        margin-top: 9px;
    }

}


.icon-right {
    i.fal, i.far, i.fas, i.fad, i.fab {
        margin-left: 0.55rem;
        margin-right: 0;
    }
}



//--Responsive design
//#region
    
@media (max-width: 600px) {
    .main-content {
        padding: 0;
    }

    a.card, div.card, .prof-bg{
        margin-right: 0 !important;
    }

    .waiver-home-card.card{
        margin-bottom:0;
    }
}

@media screen and (max-width: 991px) {

    div.ms-sm-auto.d-flex{
        flex-direction: column;
        width:100%;

        &>*{
            padding-bottom: 0.25rem;
        }

        &>.dropdown{
            width:100%;

            .btn{
                width:100%;
            }
        }
    }

    div.card:not(.waiver-home-card){
        max-width: calc(100vw - 1.5rem);
    }

    .hstack{
        flex-direction: column !important;
        align-items:flex-start !important;
    }

    div.profile-card {
        img {
            width: inherit;
        }
    }

    .profileMenu {
        border-left: none;
        display: flex;
        flex-direction: row !important;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding: 0 auto;
        padding-bottom: $main-padding;

        a{
            width: 42px;
            height: 42px;
            color: transparent;
        }

        a:only-child{
            display: none;
        }

        i{
            color: $primary-font-color;
            font-size: 1.1rem;
            padding: 10px;
            width: 42px;
            height: 42px;
            text-align: center;
        }
        @media (max-width: 420px){
            padding-bottom: 0;
        }
    }
}

//#endregion

div.loading-screen {
    width:100vw;
    height:100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-family: 'Roboto', sans-serif;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;    
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' style='margin: auto; display: block;' width='200px' height='200px' viewBox='0 0 100 100' preserveAspectRatio='xMidYMid'%3E%3Cdefs%3E%3Cfilter id='ldio-tys7mf2vwb-filter' x='-100%25' y='-100%25' width='300%25' height='300%25' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur in='SourceGraphic' stdDeviation='2.4000000000000004'%3E%3C/feGaussianBlur%3E%3CfeComponentTransfer result='cutoff'%3E%3CfeFuncA type='table' tableValues='0 0 0 0 0 0 1 1 1 1 1'%3E%3C/feFuncA%3E%3C/feComponentTransfer%3E%3C/filter%3E%3C/defs%3E%3Cg filter='url(%23ldio-tys7mf2vwb-filter)'%3E%3Cg transform='translate(50 50)'%3E%3Cg%3E%3Ccircle cx='17' cy='0' r='5' fill='%233be8b0'%3E%3Canimate attributeName='r' keyTimes='0;0.5;1' values='3.5999999999999996;8.399999999999999;3.5999999999999996' dur='4s' repeatCount='indefinite' begin='-0.25s'%3E%3C/animate%3E%3C/circle%3E%3CanimateTransform attributeName='transform' type='rotate' keyTimes='0;1' values='0;360' dur='4s' repeatCount='indefinite' begin='0s'%3E%3C/animateTransform%3E%3C/g%3E%3C/g%3E%3Cg transform='translate(50 50)'%3E%3Cg%3E%3Ccircle cx='17' cy='0' r='5' fill='%231aafd0'%3E%3Canimate attributeName='r' keyTimes='0;0.5;1' values='3.5999999999999996;8.399999999999999;3.5999999999999996' dur='2s' repeatCount='indefinite' begin='-0.2s'%3E%3C/animate%3E%3C/circle%3E%3CanimateTransform attributeName='transform' type='rotate' keyTimes='0;1' values='0;360' dur='2s' repeatCount='indefinite' begin='-0.05s'%3E%3C/animateTransform%3E%3C/g%3E%3C/g%3E%3Cg transform='translate(50 50)'%3E%3Cg%3E%3Ccircle cx='17' cy='0' r='5' fill='%236a67ce'%3E%3Canimate attributeName='r' keyTimes='0;0.5;1' values='3.5999999999999996;8.399999999999999;3.5999999999999996' dur='1.3333333333333333s' repeatCount='indefinite' begin='-0.15s'%3E%3C/animate%3E%3C/circle%3E%3CanimateTransform attributeName='transform' type='rotate' keyTimes='0;1' values='0;360' dur='1.3333333333333333s' repeatCount='indefinite' begin='-0.1s'%3E%3C/animateTransform%3E%3C/g%3E%3C/g%3E%3Cg transform='translate(50 50)'%3E%3Cg%3E%3Ccircle cx='17' cy='0' r='5' fill='%23ffb900'%3E%3Canimate attributeName='r' keyTimes='0;0.5;1' values='3.5999999999999996;8.399999999999999;3.5999999999999996' dur='1s' repeatCount='indefinite' begin='-0.1s'%3E%3C/animate%3E%3C/circle%3E%3CanimateTransform attributeName='transform' type='rotate' keyTimes='0;1' values='0;360' dur='1s' repeatCount='indefinite' begin='-0.15s'%3E%3C/animateTransform%3E%3C/g%3E%3C/g%3E%3Cg transform='translate(50 50)'%3E%3Cg%3E%3Ccircle cx='17' cy='0' r='5' fill='%23fc636b'%3E%3Canimate attributeName='r' keyTimes='0;0.5;1' values='3.5999999999999996;8.399999999999999;3.5999999999999996' dur='0.8s' repeatCount='indefinite' begin='-0.05s'%3E%3C/animate%3E%3C/circle%3E%3CanimateTransform attributeName='transform' type='rotate' keyTimes='0;1' values='0;360' dur='0.8s' repeatCount='indefinite' begin='-0.2s'%3E%3C/animateTransform%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 200px;
}

.css-13goscp-SkeletonTheme .react-loading-skeleton { 
    filter: saturate(50%);
    opacity: .5;
    background-color: $primary-color !important;
    background-image: linear-gradient(90deg, $primary-color, $tertiary-color, $primary-color ) !important;
}

.overflow-x-visible{
    overflow-x: visible;
}

input[type="checkbox"] {
    accent-color: $primary-color;
}


// add styles for print
@media print {
    html, body, .main-content {
        background-color: #fff !important;
        color: #000 !important;
    }
}