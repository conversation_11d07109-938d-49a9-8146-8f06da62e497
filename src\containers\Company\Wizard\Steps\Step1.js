import React, { useEffect, useState, createRef } from 'react';
import {Container, Col, Row, Form, InputGroup, Card} from 'react-bootstrap';

import Selector from '../../../../components/common/Selector';
import SingleUpload from '../../../../components/Uploader';

import APICms from '../../../../api/Cms';

import styles from '../Wizard.module.scss';

const themes=[
    {id:1,name:"Light Theme"},
    {id:2,name:"Dark Theme"},
];

const states=[
    {id:"AL",name:"Alabama"},
    {id:"AK",name:"Alaska"},
    {id:"AZ",name:"Arizona"},
    {id:"AR",name:"Arkansas"},
    {id:"CA",name:"California"},
    {id:"CO",name:"Colorado"},
    {id:"CT",name:"Connecticut"},
    {id:"DE",name:"Delaware"},
    {id:"DC",name:"District Of Columbia"},
    {id:"FL",name:"Florida"},
    {id:"GA",name:"Georgia"},
    {id:"HI",name:"Hawaii"},
    {id:"ID",name:"Idaho"},
    {id:"IL",name:"Illinois"},
    {id:"IN",name:"Indiana"},
    {id:"IA",name:"Iowa"},
    {id:"KS",name:"Kansas"},
    {id:"KY",name:"Kentucky"},
    {id:"LA",name:"Louisiana"},
    {id:"ME",name:"Maine"},
    {id:"MD",name:"Maryland"},
    {id:"MA",name:"Massachusetts"},
    {id:"MI",name:"Michigan"},
    {id:"MN",name:"Minnesota"},
    {id:"MS",name:"Mississippi"},
    {id:"MO",name:"Missouri"},
    {id:"MT",name:"Montana"},
    {id:"NE",name:"Nebraska"},
    {id:"NV",name:"Nevada"},
    {id:"NH",name:"New Hampshire"},
    {id:"NJ",name:"New Jersey"},
    {id:"NM",name:"New Mexico"},
    {id:"NY",name:"New York"},
    {id:"NC",name:"North Carolina"},
    {id:"ND",name:"North Dakota"},
    {id:"OH",name:"Ohio"},
    {id:"OK",name:"Oklahoma"},
    {id:"OR",name:"Oregon"},
    {id:"PA",name:"Pennsylvania"},
    {id:"RI",name:"Rhode Island"},
    {id:"SC",name:"South Carolina"},
    {id:"SD",name:"South Dakota"},
    {id:"TN",name:"Tennessee"},
    {id:"TX",name:"Texas"},
    {id:"UT",name:"Utah"},
    {id:"VT",name:"Vermont"},
    {id:"VA",name:"Virginia"},
    {id:"WA",name:"Washington"},
    {id:"WV",name:"West Virginia"},
    {id:"WI",name:"Wisconsin"},
    {id:"WY",name:"Wyoming"}
];

const Step1 = (props) => {
    const {change, values, errors, setErrors} = props;
    const [previewImage, setPreviewImage] = useState();

    const uploadLogoHandler = (data) =>{
        const file = data.get('file');
        if (file) {
            const fileUrl = URL.createObjectURL(file);
            if (previewImage !== fileUrl){
                setPreviewImage(fileUrl);
                change({target: { name: "file", value: file}});
            }
        }
    }

    const subdomainChangeHandler = (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (e.target.value){
            e.target.value.replace(/[^a-z0-9]/gi,'');
            APICms.urls.validate({subdomain: e.target.value, domain: "siteboss.net"}).then(res => {
                if (res.data && res.data.length > 0){
                    setErrors({subdomain: "Subdomain already exists."});
                    return false;
                } else {
                    setErrors({subdomain: null});
                    change(e);
                }
            });
        }
    }

    useEffect(() => {
        if (values?.file && !previewImage) {            
            const fileUrl = URL.createObjectURL(values.file);
            setPreviewImage(fileUrl);
        }
    }, [values, previewImage]);

    return (
        <Container fluid>
            <Row>
                <Col sm="12" lg="8">
                    <Row>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="name">
                                <Form.Label>Company Name</Form.Label>
                                <Form.Control required type="text" name="name" defaultValue={values?.name || ""} onChange={change} />
                                {errors?.name && <Form.Text bsPrefix="error-text">{errors.name}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="email">
                                <Form.Label>Email</Form.Label>
                                <Form.Control required type="email" name="email" defaultValue={values?.email || ""} onChange={change} />
                                {errors?.email && <Form.Text bsPrefix="error-text">{errors.email}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="primary_number">
                                <Form.Label>Phone</Form.Label>
                                <Form.Control type="tel" name="primary_number" defaultValue={values?.primary_number || ""} onChange={change} />
                                {errors?.primary_number && <Form.Text bsPrefix="error-text">{errors.primary_number}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="fax_number">
                                <Form.Label>Fax</Form.Label>
                                <Form.Control type="tel" name="fax_number" defaultValue={values?.fax_number || ""} onChange={change} />
                                {errors?.fax_number && <Form.Text bsPrefix="error-text">{errors.fax_number}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="address_street">
                                <Form.Label>Address Line 1</Form.Label>
                                <Form.Control type="text" name="address_street" defaultValue={values?.address_street || ""} onChange={change} />
                                {errors?.address_street1 && <Form.Text bsPrefix="error-text">{errors.address_street1}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="address_street2">
                                <Form.Label>Address Line 2</Form.Label>
                                <Form.Control type="text" name="address_street2" defaultValue={values?.address_street2 || ""} onChange={change} />
                                {errors?.address_street2 && <Form.Text bsPrefix="error-text">{errors.address_street2}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="address_city">
                                <Form.Label>City</Form.Label>
                                <Form.Control type="text" name="address_city" defaultValue={values?.address_city || ""} onChange={change} />
                                {errors?.address_city && <Form.Text bsPrefix="error-text">{errors.address_city}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="address_state">
                                <Form.Label>State</Form.Label>
                                <Selector required custom as="select" name="address_state" options={states} value={values.address_state || ""} onChange={change} />
                                {errors?.address_state && <Form.Text bsPrefix="error-text">{errors.address_state}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="3">
                            <Form.Group controlId="address_postcode">
                                <Form.Label>Zip Code</Form.Label>
                                <Form.Control type="text" name="address_postcode" defaultValue={values?.address_postcode || ""} onChange={change} />
                                {errors?.address_postcode && <Form.Text bsPrefix="error-text">{errors.address_postcode}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="subdomain">
                                <Form.Label>Subdomain</Form.Label>
                                <InputGroup>
                                    <Form.Control type="text" max="155" name="subdomain" defaultValue={values.subdomain || ""} style={{textAlign:"right"}} onChange={subdomainChangeHandler} />
                                    <InputGroup.Text>.siteboss.net</InputGroup.Text>
                                </InputGroup>
                                {errors?.subdomain && <Form.Text bsPrefix="error-text">{errors.subdomain}</Form.Text>}
                            </Form.Group>
                        </Col>
                        <Col sm="12" lg="6">
                            <Form.Group controlId="website_theme_id">
                                <Form.Label>Base Theme</Form.Label>
                                <Selector required custom as="select" name="website_theme_id" options={themes} value={values.website_theme_id || ""} onChange={change} />
                                {errors?.website_theme_id && <Form.Text bsPrefix="error-text">{errors.website_theme_id}</Form.Text>}
                            </Form.Group>
                        </Col>
                    </Row>
                </Col>

                <Col sm="12" lg="4" className={styles.stripes}>
                    <SingleUpload 
                        previewSrc={previewImage} 
                        DZRef={createRef()} 
                        type="image/*" 
                        maxHeight="600px"
                        minHeight="300px" 
                        minWidth="100%"
                        backgroundSize="contain"
                        onSend={uploadLogoHandler}
                        disableCrop
                    >
                        {!previewImage &&
                            <Card className={`standout position-absolute h-100 w-100`} style={{background:"transparent"}}>
                                <Card.Body className="d-flex justify-content-center align-items-center">
                                    <small>Drop a logo or click</small>
                                </Card.Body>
                            </Card>
                        }
                    </SingleUpload>
                </Col>
            </Row>
        </Container>
    );
}

export default Step1;