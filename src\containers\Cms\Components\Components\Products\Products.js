import React,{ useState, useEffect } from 'react';
import { Row, Col, Card} from 'react-bootstrap';
import Stack from '../../../../../components/common/Stack';

import APIProducts from '../../../../../api/Products';
//import FormGroupButton from '../../FormGroupButton';
import Variants from './Variants';

import styles from './Products.module.scss';

const Products =  props => {
    const {callback} = props;
    const [options, setOptions] = useState([]);
    const [selectedPlan, setSelectedPlan] = useState();
    const [total, setTotal] = useState(0);

    useEffect(() => {
        const _getProductsByCategory = async () => {
            const res=await APIProducts.get({category_id:props.product_category_id});
            if (res.data.products) {
                setOptions(res.data.products);
            }
        }

        const _getProductsByIds = async () => {
            const res=await APIProducts.get({ids:props.product_ids});
            if (res.data?.products) {
                setOptions(res.data.products);
            }
        }

        if (props.product_ids) _getProductsByIds();
        else if (props.product_category_id) _getProductsByCategory();
        
    },[props.product_category_id, props.product_ids]);

    useEffect(() => {
        return () => {
            setOptions([]);
            setSelectedPlan(null);
            setTotal(0);
        }
    }, []);


    const planClickHandler = (variant) => {
        setSelectedPlan(variant);
        let _total = +variant?.price || 0;
        if (variant?.addons) {
            variant.addons.forEach(addon => {
                _total+=+addon.price;
            });
        }
        setTotal(_total.toFixed(2));
        if (callback) {
            //console.log("callback")
            //callback(variant);
        }
    }

    return (        
        <Row>
            {options.filter(a=>a?.product_variants?.length>0)?.map((option, i) => {
                return (
                    <Col key={`product-step3-${i}`} sm={12} className={`${styles.plan} ${selectedPlan?.product_id===option.id?styles.active:""}`}>
                        <Stack gap={1} direction="horizontal">
                            <h4>{option.name}</h4>
                            {selectedPlan && selectedPlan?.product_id === option.id && total>0 &&
                                <p className={styles.total}>Due today<br/><span>${total}</span></p>
                            }
                        </Stack>
                        <p>{option.description}</p>
                        <Variants {...option} selected={selectedPlan?.id || null} change={planClickHandler} />
                    </Col>
                )
            })}
        </Row>
    );
}

export default Products;