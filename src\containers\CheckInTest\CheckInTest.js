import React, { useEffect, useState, useRef } from 'react';
import { Button, Form } from 'react-bootstrap';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
// import { BarcodeScanner } from 'react-barcode-qrcode-scanner';
import useSound from 'use-sound';
import { format } from 'date-fns';

import beepSound from '../../assets/sounds/beep2.mp3';
// import { UserSidebar } from '../CheckIn/UserSidebar';
import { parseErrorToString } from '../../utils/validation';

import UsersAPI from '../../api/Users';

const NUM_SECONDS_TO_SHOW_USER = 20;
const NUM_SECONDS_TO_SHOW_ERROR = 5;

const style = {
    position: 'absolute',
    top: '0',
    left: '0',
    height: '100vh',
    width: '100vw',
    zIndex: '1040',
    background: '#FFFFFF',
    overflow: 'hidden'
};

/*
 *  This component uses the webcam to scan QR codes from membership cards and checks in the user appropriately
 */

export const CheckInTest = (props) => {
    // const mountedRef = useRef(false);

    // const timeoutUserRef = useRef();
    // const timeoutErrorRef = useRef();

    // const [error, setError] = useState("");
    // const [scannedData, setScannedData] = useState("");
    // const [userId, setUserId] = useState();
    // const [playBeep] = useSound(beepSound);
    // const [checkinTime, setCheckinTime] = useState("");
    // const [runtimeSettings,setRuntimeSettings] = React.useState("{\"ImageParameter\":{\"BarcodeFormatIds\":[\"BF_QR_CODE\"],\"Description\":\"\",\"Name\":\"Settings\"},\"Version\":\"3.0\"}");
    // const [testContent, setTestContent] = useState(null);

    // useEffect(() => {
    //     mountedRef.current = true;
    //     const timerReload = setTimeout(() => window.location.reload(true), 60*60*1000);
    //     return () => {
    //         clearTimeout(timerReload);
    //         clearTimeout(timeoutUserRef.current);
    //         clearTimeout(timeoutErrorRef.current);
    //         mountedRef.current = false;
    //     }
    // },[]);

    // useEffect(() => {
    //     if (scannedData!=="") {
    //         playBeep();
    //         let splitData = scannedData.split('/');
    //         let baseUrl = splitData[2];
    //         let isLocalhost = baseUrl.split(':')[0]==='localhost';
    //         let isCurrentSite = getBaseCompanyOfUrl(scannedData)===getBaseCompanyOfUrl(window.location.href);
    //         let isImpactSite = getBaseCompanyOfUrl(scannedData)==="impactathleticsny";
    //         let numPartOfUrl = parseInt(splitData[4]);
    //         if (splitData.length>=5 && (isCurrentSite || isLocalhost || isImpactSite) && numPartOfUrl!==NaN) {
    //             checkinUser(numPartOfUrl);
    //         } else {
    //             displayError("QR code scanned doesn't contain the expected URL");
    //             console.log("QR code scanned doesn't contain the expected URL");
    //         }
    //         // reset the scanned info so that it will beep again if the same qr code is scanned
    //         const timerToResetBeepSound = setTimeout(() => setScannedData(""), 2000);
    //         return () => clearTimeout(timerToResetBeepSound);
    //     }
    // },[scannedData]);

    // const getBaseCompanyOfUrl = (url) => {
    //     let domain = url.split('/')[2];
    //     let company = domain.split('.')[1];
    //     return company;
    // }

    // const onScan = (results) => {
    //     if (!!results) {
    //         console.log(results);
    //         if (results[0]?.barcodeText) {
    //             setTestContent(results[0].barcodeText);
    //         }
    //         // setScannedData(result?.text);
    //     }
    //     if (!!error) {
    //         console.info(error);
    //     }
    // }

    // const checkinUser = async (user_id) => {
    //     // send the user_id to check in
    //     await UsersAPI.Checkin.create({
    //         user_id: user_id,
    //     }).then(async response => {
    //         if (!response.errors && mountedRef.current) {
    //             setUserId(user_id);
    //             if (response?.data?.checkin_at) setCheckinTime(new Date(response.data.checkin_at));
    //             setTimeoutClearUser();
    //         } else {
    //             displayError(parseErrorToString(response.errors));
    //             console.error(response.errors);
    //         }
    //     }).catch(e => console.error(e));
    // }

    // const displayError = (error) => {
    //     setError(error);
    //     timeoutErrorRef.current = setInterval(() => {
    //         setError();
    //     }, NUM_SECONDS_TO_SHOW_ERROR * 1000);
    // }

    // const setTimeoutClearUser = () => {
    //     timeoutUserRef.current = setInterval(() => {
    //         setUserId();
    //         setCheckinTime();
    //     }, NUM_SECONDS_TO_SHOW_USER * 1000);
    // }

    return (
        <Row style={style} className="checkin">
            <Col sm="12" md="4">
                {/* <h4>Check In Scanner</h4>
                <div style={{width: '400px', height: '400px'}}>
                    <BarcodeScanner
                        onScanned={onScan}
                        isActive={true}
                        drawOverlay={true}
                        desiredResolution={{width: 400, height: 400}} 
                    />
                </div>
                {error &&
                    <h2 className="error-text mt-4">{error}</h2>
                }
            </Col>
            <Col sm="12" md="8">
                <h4 className="text-center">Most Recently Checked In User
                    {checkinTime &&
                        <span className="checkin-time">
                            {format(checkinTime, " @ h:mm b")}
                        </span>
                    }
                </h4>

                {testContent} */}
                
                {/* <UserSidebar userID={userId} /> */}
                
            </Col>
        </Row>
    );
}