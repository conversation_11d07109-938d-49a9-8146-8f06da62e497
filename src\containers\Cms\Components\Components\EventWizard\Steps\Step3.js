import React, {useState, useEffect, useCallback} from 'react';
import { useSelector } from 'react-redux';
import { Form, Button } from 'react-bootstrap';

import SignUpFamily from '../../SignUpFamily';

import APIUser from '../../../../../../api/Users';
import APIGroups from '../../../../../../api/Groups';
import APIEvents from '../../../../../../api/Events';

export const Step3 = (props) => {
    const {disableNext, saveStepValues} = props;
    const user = useSelector(state => state.auth.user.profile);

    const [loading, setLoading] = useState(false);
    const [familyMembers, setFamilyMembers] = useState([]);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [showAddFamily, setShowAddFamily] = useState(props?.showForm ? true : false);
    const [familyGroupId, setFamilyGroupId] = useState();

    useEffect(() => {
        if (props.stepValues?.selectedUsers) setSelectedUsers(props.stepValues.selectedUsers.map(a=>+a.id));
        //else setSelectedUsers([user.id]);
    }, [props.stepValues]);

    useEffect(() => {
        const _loadFamilyMembers = async (user_id) => {
            setLoading(true);
            let members = [];
            if (user_id){
                const res = await APIUser.get({id: user_id});
                if (res?.data){
                    let _families = res.data[0].family_groups;
                    let _groupId = res.data[0]?.family_groups?.[0]?.id || null;
                    if (!_groupId){
                        const res2 = await APIGroups.create({name: `${res.data[0]?.last_name} Family`, group_type_id: 4, group_status_id: 1, group_member_role_id: 1});
                        if (res2?.data?.[0]?.id) {
                            _groupId = res2.data[0].id;
                            _families = [{id: _groupId}];
                        }
                    }
                    setFamilyGroupId(_groupId);
                    for (const family of _families){
                        const res2 = await APIGroups.get({id:family.id});
                        if (res2?.data?.length>0){
                            for (const member of res2.data[0].group_members.filter(a=>+a.group_member_status_id===2 && a.first_name && a.last_name)){
                                let disabled = false;
                                if (!props?.users){
                                    const res3=await APIEvents.publicGet({id: props.id, user_id: member.user_id});
                                    if (res3.data.events.length>0) disabled=true;
                                } else if (props.users.findIndex(a=>+a.id===+member.user_id)>-1) disabled=true;                                
                                members.push({
                                    id: member.user_id,
                                    first_name: member.first_name,
                                    last_name: member.last_name,
                                    group_member_role: member.group_member_role_name,
                                    disabled: disabled
                                });
                            }
                        }
                    }
                } else {
                    members.push({
                        id: user_id,
                        first_name: user.first_name,
                        last_name: user.last_name,
                        group_member_role: "Me",
                        disabled: false
                    });
                }
            }
            setFamilyMembers(members);
            setLoading(false);
        }

        if (user.id){
            //setSelectedUsers(prev=>[...prev,user.id]);
            _loadFamilyMembers(user.id);
        }
    }, [user, props.id, props?.users, showAddFamily]);

    useEffect(() => {
        if (familyMembers.filter(a=>!a.disabled).length>0 || selectedUsers.length>0){
            disableNext(false);
        } else disableNext(true);
    }, [familyMembers, selectedUsers, disableNext]);

    useEffect(() => {
        if (familyMembers.length>0){
            setSelectedUsers(prev=>{
                let newSelectedUsers = [...prev];
                if (prev.length>0){
                    for (const member of familyMembers){
                        if (member.disabled && newSelectedUsers.includes(member.id)){
                            newSelectedUsers = newSelectedUsers.filter(a=>+a!==+member.id);
                        }
                    }
                }
                return newSelectedUsers;
            })
        }
    }, [familyMembers]);

    useEffect(() => {
        if (selectedUsers.length<=0) disableNext(true);
        else disableNext(false);
    }, [selectedUsers, disableNext]);

    useEffect(() => {
        return () => {
            setFamilyMembers([]);
            setFamilyGroupId(null);
            setSelectedUsers([]);
            setLoading(false);
            setShowAddFamily(false);
        }
    }, []);


    const clickHandler = useCallback(e => {
        e.preventDefault();
        e.stopPropagation();
        let newSelectedUsers = [...selectedUsers];

        if (e.target.checked){
            if (!newSelectedUsers.includes(+e.target.value)) newSelectedUsers.push(+e.target.value);
        } 
        else newSelectedUsers = newSelectedUsers.filter(a => +a !== +e.target.value);

        const _selected = [];
        newSelectedUsers.forEach((user, i) => {
            const newSelectdUserData = familyMembers.filter(a => +a.id === +user);
            if (newSelectdUserData.length > 0) _selected.push({id: user, ...newSelectdUserData[0]});
        });
        setSelectedUsers(newSelectedUsers);
        saveStepValues({selectedUsers: _selected});

        if (newSelectedUsers.length > 0) disableNext(false);
        else disableNext(true);

    }, [selectedUsers, disableNext, saveStepValues, familyMembers]);
    
    const toggleFamilyHandler = e => {
        const _showAddFamily = !showAddFamily;
        if (_showAddFamily) disableNext(true);
        setShowAddFamily(_showAddFamily);
    }

    return (
        <>
            {showAddFamily && familyGroupId &&
                <div>
                    <SignUpFamily {...props} forceMobile group_id={familyGroupId} success_title="Welcome to the family!" />
                    <a href="#!" onClick={toggleFamilyHandler} style={{display:"block",textAlign:"end"}}>Close</a>
                </div>
            }
            {!showAddFamily &&
                <div>
                    <p>
                        <label className="form-label">Who is attending the event?</label>
                    </p>
                    <p className="bold">
                        Note: Please select the person actually participating in the event. If not listed, please add the account now.
                    </p>
                    <Form.Check 
                        type="checkbox"
                        id={`default-user`}
                        label={`${user.first_name} ${user.last_name} (Me) ${familyMembers.filter(a=>+a.id===+user.id)?.[0]?.disabled ? '-- Registered' : ''}`}
                        checked={selectedUsers.includes(+user.id) && (familyMembers.length>0?!familyMembers.filter(a=>+a.id===+user.id)?.[0]?.disabled:true)}
                        value={user.id}
                        onChange={clickHandler}
                        disabled = {familyMembers.filter(a=>+a.id===+user.id)?.[0]?.disabled || undefined}
                    />
                    {loading && <p>Loading ...</p>}
                    {familyMembers && familyMembers.filter(a=>+a.id!==+user.id).map((member, i) => (
                        <Form.Check 
                            key={`family-member-${i}`}
                            type="checkbox"
                            id={`family-member-${i}`}
                            label={`${member.first_name} ${member.last_name} (${member.group_member_role}) ${member.disabled ? '-- Registered' : ''}`}
                            value={member.id}
                            checked={selectedUsers.includes(+member.id)}
                            onChange={clickHandler}
                            disabled={member.disabled || undefined}
                        />
                    ))}
                    <br/> 
                    <Button variant="light" onClick={toggleFamilyHandler} disabled={loading || showAddFamily}>Add Family Member</Button>
                </div>
            }
        </>
    );
}