@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';
@import '../../../assets/css/scss/mixins';

.accordion-wrapper{
    .overall-item{
        padding: 0;
        &.neutral{
            border-width: 1px 1px 1px 5px;
            border-color: $company-neutral-light;
            border-style: solid;
        }
        &.primary{
            border-left: 15px;
            border-color: $primary-color;
            border-style: solid;
            border-radius: 4px 4px 0 0;
            margin-top: 2px;
        }
    }
    .card-toggle{
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: $card-border-radius;
        &.neutral{
            color: $primary-color;
        }
    }
    .collapse-portion{
        &.primary{
            padding: 10px;
            border-left: 15px;
            border-right: 0;
            border-top: 0;
            border-bottom:0;
            border-color: $primary-color;
            border-style: solid;
            border-radius: 0 0 4px 4px;
        }
    }
}