// main theme

@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@400;500;600&family=Roboto:wght@400;500;700&display=swap');

$fonts: (
    'primary': 'Outfit:300,500,600',
    'secondary': 'Roboto:400,500,700'
);
$company-name: "SiteBoss";
$background-image: "https://siteboss.s3.amazonaws.com/themes/1/siteboss-1.mp4";
$background-image-filter: none;
$themeS3Bucket: "https://siteboss.s3.amazonaws.com/themes/1/";

$logoNoText: $themeS3Bucket + 'logo-short.svg';
$logoHeader: $themeS3Bucket + 'logo.svg';
$logo: $themeS3Bucket + 'logo.svg';
$backgroundEvents: $themeS3Bucket + 'events.png';

:export {
    backgroundImage: $background-image;
    companyName: $company-name;
    logo: $logo;
    logoHeader: $logoHeader;
    logoNoText: $logoNoText;
    backgroundEvents: $backgroundEvents;
}

$primary-font-family: 'Roboto', sans-serif;
$primary-font-size: 1rem;
$primary-line-height: 1.25rem;
$primary-font-color: #eee;
$primary-inverse-color: #000;
$primary-font-weight: 400;
$secondary-font-family: 'Outfit', sans-serif;
$secondary-font-size: 1rem;
$secondary-line-height: 1.25rem;
$secondary-font-color: #eee;
$secondary-inverse-color: #000;
$secondary-font-weight: 600;

$bold-font-weight: 600;
$light-font-weight: 300;
$regular-font-weight: 400;
$big-font-size: 1.25rem;
$big-font-line-height: 1.5rem;
$small-font-size: 0.7rem;
$small-font-line-height: 0.8rem;

$primary-color: #662d91;
$primary-hover-color: #512da8;
$primary-light-color: #b39ddb;
$secondary-color: #f7931e;
$secondary-hover-color: #f57f17;
$secondary-light-color: #fff8e1;
$tertiary-color: #f44336;
$tertiary-hover-color: #d32f2f;
$tertiary-light-color: #ef9a9a;
$neutral-background-color: #212121;
$neutral-color: $primary-font-color;
$neutral-hover-background-color: #424242;
$neutral-hover-color: $primary-font-color;

$shadow-color:#424242;
$shadow-elevation-0: none;
$shadow-elevation-1: rgba($shadow-color, .2) 0px 2px 1px -1px, rgba($shadow-color, .14) 0px 1px 1px 0px, rgba($shadow-color, .12) 0px 1px 3px 0px;
$shadow-elevation-2: rgba($shadow-color, .2) 0px 3px 1px -2px, rgba($shadow-color, .14) 0px 2px 2px 0px, rgba($shadow-color, .12) 0px 1px 5px 0px;
$shadow-elevation-3: rgba($shadow-color, .2) 0px 3px 3px -2px, rgba($shadow-color, .14) 0px 3px 4px 0px, rgba($shadow-color, .12) 0px 1px 8px 0px;

$error-color: #f44336;
$error-text-color: #fff;
$success-color: #4caf50;
$warning-color: #ffe082;
$disabled-color: #757575;

$background-color: #000;

$divider-color: #212121;
$divider-border: 1px solid $divider-color;
$divider-margin: 0.5rem 0 1rem 0;

$scrollbar-color: #9e9e9e;
$scrollbar-background-color: #e5e5e5;
$scrollbar-width: 10px;
$scrollbar-border-radius: 0px;

$main-padding: 2rem;
$content-font-size: 1rem;
$content-padding: 0 $main-padding $main-padding $main-padding;


$small-main-padding: 1rem;
$small-content-padding: 0 $small-main-padding $small-main-padding $small-main-padding;

$header-background-color:#000;
$header-font-color: #eee;
$header-hover-color: #fff;
$header-font-size: .85rem;
$header-font-weight: 400;
$header-font-family: $primary-font-family;
$header-height: 3rem;
$header-padding: 0 1rem;
$header-text-decoration: none;

$logo-height: 50px;
$logo-width: 85px;
$logo-url: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 222.44 90.3'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23f7931e;%7D.cls-2%7Bfill:%23662d91;%7D%3C/style%3E%3C/defs%3E%3Cg id='Layer_2' data-name='Layer 2'%3E%3Cg id='Layer_1-2' data-name='Layer 1'%3E%3Cpath class='cls-1' d='M.19,68.51A136.17,136.17,0,0,0,5.63,54,91.54,91.54,0,0,1,9,44.78,13.51,13.51,0,0,1,13,39.11a10.28,10.28,0,0,1,2.74-.42,3.48,3.48,0,0,1,2.56,1,4.87,4.87,0,0,1,1,3.49,17.22,17.22,0,0,1-.2,2.43q0,.58,0,1.2a33.61,33.61,0,0,0,1.21,8.62q1.21,4.58,3,9.43a12.17,12.17,0,0,1,.23,2.32A11,11,0,0,1,21.87,73a12.88,12.88,0,0,1-4.09,4.21,9.26,9.26,0,0,1-5,1.56A7.29,7.29,0,0,1,9.37,78a7.94,7.94,0,0,1-1.91-2.37,5.48,5.48,0,0,1-.59-2.45,7.61,7.61,0,0,1,1-3.59,23.07,23.07,0,0,1,2.45-3.65q1.49-1.83,3.14-3.68c.26-.26.51-.53.74-.81L11.3,50.76q-1.92,5.57-4,10.52T3.93,68.51a5.85,5.85,0,0,1-1.38,1.91A2.17,2.17,0,0,1,1.23,71C.51,71,.1,70.53,0,69.55A3.24,3.24,0,0,1,.19,68.51Z'/%3E%3Cpath class='cls-1' d='M62.42,0A3.3,3.3,0,0,1,65,1.72a7.51,7.51,0,0,1,1,4A14.08,14.08,0,0,1,65,10.45c.44-.07.9-.16,1.39-.27Q70.68,9.3,77,8.25a73.89,73.89,0,0,1,11.8-1,26.48,26.48,0,0,1,4.86.41A8.14,8.14,0,0,1,97.1,9a3,3,0,0,1,1.23,2.51v.07q-.19,2.36-3.45,3.69a32.5,32.5,0,0,1-8.18,1.93c-3.28.39-6.62.64-10,.73s-6.41.13-9,.13c-2.09,0-3.61,0-4.56.08a102.32,102.32,0,0,0-4,22.07Q58,53.47,57.56,65.77q-.39,9-.85,14.72t-1.47,6.07H55q-1.77,0-3-3.09a37.59,37.59,0,0,1-2-8.06c-.52-3.32-.89-6.93-1.12-10.86s-.35-7.74-.35-11.47a131.29,131.29,0,0,1-3.82,12.65,39.76,39.76,0,0,1-4,8.43q-2,3-4.16,3h-.47a9.42,9.42,0,0,1-5.8-1.86,15.06,15.06,0,0,1-4.17-4.86,24.94,24.94,0,0,1-2.52-6.69,32.18,32.18,0,0,1-.85-7.31,25.31,25.31,0,0,1,1.19-8A10.83,10.83,0,0,1,27.59,43a15.15,15.15,0,0,1,3.55-.62h.5a5,5,0,0,1,2.89.72,3.67,3.67,0,0,1,1.45,2,9.59,9.59,0,0,1,.4,2.9v.54c-.13,1.39-.27,2.8-.42,4.24q-.32,3-.56,6c-.17,2-.25,3.86-.25,5.61a27.61,27.61,0,0,0,.29,4.26,9,9,0,0,0,1,3.18.54.54,0,0,0,.46.31q1.08,0,3.36-4.84A96.94,96.94,0,0,0,45.2,52.79a241.66,241.66,0,0,0,5-24.28q.47-3.71,1.16-8.07L37.15,23.3a12,12,0,0,1-3,.42,7.21,7.21,0,0,1-4.74-1.44,4.07,4.07,0,0,1-1.74-3.07,2.9,2.9,0,0,1,.85-2,5.32,5.32,0,0,1,2.66-1.33l22.07-3.2a39,39,0,0,1,3-7.42,13.74,13.74,0,0,1,3.1-4A4.73,4.73,0,0,1,62.19,0ZM31.71,27.7a8.66,8.66,0,0,1,3.65.75,3.28,3.28,0,0,1,2,2.18,6,6,0,0,1,.07,1,7.9,7.9,0,0,1-.65,3.05,7.05,7.05,0,0,1-1.85,2.58A4.26,4.26,0,0,1,32,38.27h-.31a9.6,9.6,0,0,1-4-1,4.53,4.53,0,0,1-1.91-1.87,5,5,0,0,1-.52-2.2,6.66,6.66,0,0,1,.31-2.08,4.11,4.11,0,0,1,2.28-2.6A8.61,8.61,0,0,1,31.71,27.7Z'/%3E%3Cpath class='cls-1' d='M74.65,41h.93a17.72,17.72,0,0,1,2,.12,9.42,9.42,0,0,1,2.12.5,8.1,8.1,0,0,1,4.38,3.22,9,9,0,0,1,1.33,4.84,16.52,16.52,0,0,1-2.07,7.64,23.82,23.82,0,0,1-5.24,6.83,13.56,13.56,0,0,1-6.77,3.47,6.53,6.53,0,0,0,2.77,3,8.43,8.43,0,0,0,4.17,1,11.91,11.91,0,0,0,5.13-1.24,14.33,14.33,0,0,0,4.8-3.78,18.47,18.47,0,0,0,3.46-6.48c.3-.61.68-.92,1.11-.92a1.41,1.41,0,0,1,1,.46A1.56,1.56,0,0,1,94.25,61v.19a18.56,18.56,0,0,1-3.28,8A20.33,20.33,0,0,1,84.57,75a15.83,15.83,0,0,1-8.06,2.22,11.8,11.8,0,0,1-1.66-.12A14.91,14.91,0,0,1,67,73.68a16.43,16.43,0,0,1-4.61-6.62A22.4,22.4,0,0,1,60.88,59a22.79,22.79,0,0,1,1.59-8.43,16.28,16.28,0,0,1,4.64-6.63A12.46,12.46,0,0,1,74.65,41Zm3.94,5h-.08a4.1,4.1,0,0,0-3.12,1.77,17.61,17.61,0,0,0-2.59,4.44A34.28,34.28,0,0,0,71,57.8a23.07,23.07,0,0,0-.66,5.08,9.65,9.65,0,0,0,.08,1.23,17.65,17.65,0,0,0,3.9-4,29.67,29.67,0,0,0,3.26-5.82,15.45,15.45,0,0,0,1.33-5.91A7.16,7.16,0,0,0,78.59,46.06Z'/%3E%3Cpath class='cls-2' d='M142.07,45.21a3.14,3.14,0,0,1,1.47.62,1.16,1.16,0,0,1,.5.84,1,1,0,0,1-.64.87,4.31,4.31,0,0,1-2,.41,19.94,19.94,0,0,0-6.52,1.95,43.33,43.33,0,0,0-6,3.56,27.93,27.93,0,0,1,.23,3.67A32.19,32.19,0,0,1,128,65.67a30.75,30.75,0,0,1-3.21,7.68A19.05,19.05,0,0,1,120,78.87,9.11,9.11,0,0,1,110.23,80a14.5,14.5,0,0,1-4.11-3,12.6,12.6,0,0,1-.11-1.62,14.33,14.33,0,0,1,1.46-6.27,30.1,30.1,0,0,1,3.86-5.92,73.19,73.19,0,0,1,5.29-5.71c0-.64.07-1.31.07-2a25.9,25.9,0,0,0-.77-6.37,8.11,8.11,0,0,0-2.24-4.32,28.53,28.53,0,0,0-5.7,7.45,29.88,29.88,0,0,0-2.88,8.16,45,45,0,0,0-.83,8.66,76.89,76.89,0,0,0,.46,8.25q.47,4.17,1,8.33c0,.18,0,.35.07.5a3.08,3.08,0,0,1,0,.51,3.75,3.75,0,0,1-.91,2.6,2.62,2.62,0,0,1-2,1,2.36,2.36,0,0,1-1.22-.36,3.25,3.25,0,0,1-1.06-1.18q-2.89-5.45-4.22-15.62a172.23,172.23,0,0,1-1.33-22.3A243.09,243.09,0,0,1,98.1,13a5.24,5.24,0,0,1,2.12-3.82A6.76,6.76,0,0,1,104.16,8a7.4,7.4,0,0,1,2.89.59,5.37,5.37,0,0,1,2.26,1.78,4.8,4.8,0,0,1,.86,2.91,7.22,7.22,0,0,1-.07,1c-.52,2.81-1.12,5.82-1.82,9q-1.35,5.9-2.52,12.61a98,98,0,0,0-1.41,14,19.23,19.23,0,0,1,5.8-7.55A11.4,11.4,0,0,1,116.73,40a10.37,10.37,0,0,1,6.91,2.64,13.63,13.63,0,0,1,4.24,7.2l.73-.43c1.13-.66,2.33-1.33,3.59-2a25.25,25.25,0,0,1,3.93-1.68,13.4,13.4,0,0,1,4.21-.68A10.41,10.41,0,0,1,142.07,45.21Zm-25.45,17a36,36,0,0,0-4.48,6.21A18.21,18.21,0,0,0,109.9,76q3.24-.78,4.88-4.84A26.48,26.48,0,0,0,116.62,62.22Z'/%3E%3Cpath class='cls-2' d='M167.42,37.15a3.28,3.28,0,0,1,2.53,1,5.18,5.18,0,0,1,1,3.53,17.05,17.05,0,0,1-.2,2.39c0,.39,0,.79,0,1.2a33.64,33.64,0,0,0,1.22,8.62q1.22,4.58,3,9.43a11.89,11.89,0,0,1,.23,2.27,11.17,11.17,0,0,1-1.62,5.91,12.68,12.68,0,0,1-4.11,4.22,9.4,9.4,0,0,1-5,1.56,7.17,7.17,0,0,1-3.4-.85,8.39,8.39,0,0,1-1.91-2.39,5.31,5.31,0,0,1-.59-2.43,7.64,7.64,0,0,1,1-3.59,22.92,22.92,0,0,1,2.43-3.62q1.47-1.81,3.12-3.67l.78-.84-2.86-10.65q-4.17,3.43-7.75,3.82a25.18,25.18,0,0,1,.19,3.16,31.81,31.81,0,0,1-1.29,8.89,25.11,25.11,0,0,1-3.57,7.66,10.08,10.08,0,0,1-5.28,4.09,14,14,0,0,1-2.44.23,9.78,9.78,0,0,1-5.57-1.52,11.84,11.84,0,0,1-3.66-4,18.92,18.92,0,0,1-2-5.45,28.89,28.89,0,0,1-.63-6,26.46,26.46,0,0,1,.38-4.63,22.78,22.78,0,0,1,2.66-7.78,11.25,11.25,0,0,1,10-5.76,16.74,16.74,0,0,1,4,.5,9.17,9.17,0,0,1,6.25,6.29,10,10,0,0,0,2.65-2.24q1.22-1.43,2.37-3.09c.74-1,1.52-2.06,2.31-3a11.53,11.53,0,0,1,2.64-2.38A6,6,0,0,1,167.42,37.15ZM146.75,47.6c-1.16.41-2.19,1.53-3.11,3.36a27.71,27.71,0,0,0-2.18,6.38,31,31,0,0,0-.81,6.69,11.61,11.61,0,0,0,.5,3.67q.51,1.45,1.59,1.62h.07q1.27,0,2.82-3.09a38.27,38.27,0,0,0,2.64-7.13,30.76,30.76,0,0,0,1.18-7.37A7,7,0,0,1,146.75,47.6Z'/%3E%3Cpath class='cls-2' d='M186,37.88a3.36,3.36,0,0,1,2.53,1,5,5,0,0,1,1,3.46,17.86,17.86,0,0,1-.2,2.46c0,.34,0,.66,0,1a28.71,28.71,0,0,0,.5,5.28q.5,2.74,1.31,5.64,3.36-1.81,7.06-3.4a57.92,57.92,0,0,1,7.38-2.58,26.45,26.45,0,0,1,6.93-1,14.63,14.63,0,0,1,3.86.5,9.33,9.33,0,0,1,3.2,1.59,8.41,8.41,0,0,1,2.27,2.62,5.63,5.63,0,0,1,.7,2.62,5.79,5.79,0,0,1-2.05,4.42A6.43,6.43,0,0,1,216,63.3a4.86,4.86,0,0,1-3.32-1.39,8.57,8.57,0,0,0-3.82-2.06,20.3,20.3,0,0,0-5.44-.68,33.25,33.25,0,0,0-10.91,2,18.43,18.43,0,0,1,1,5.67,13.31,13.31,0,0,1-2.6,8.31,7.9,7.9,0,0,1-6.5,3.42,10.13,10.13,0,0,1-4.79-1.39,7.72,7.72,0,0,1-1.89-2.38,5.7,5.7,0,0,1-.58-2.44,7.58,7.58,0,0,1,1-3.57,22,22,0,0,1,2.41-3.61q1.47-1.81,3.09-3.63c.28-.3.55-.61.81-.92L181.47,50c-1.31,4.38-2.73,8.48-4.26,12.33s-2.87,6.43-4,7.77a2.9,2.9,0,0,1-1.89.93,1.33,1.33,0,0,1-.93-.37,1.52,1.52,0,0,1-.38-1.14,5.15,5.15,0,0,1,.42-1.77,133.88,133.88,0,0,0,5.36-14.43q1.67-5.33,3.34-9.28a13.21,13.21,0,0,1,4.07-5.69A11.45,11.45,0,0,1,186,37.88Z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
$logo-background-position: center center;
$logo-background-repeat: no-repeat;
$logo-background-size: contain;
$logo-filter: brightness(0) invert(1);

$link-color: $primary-color;
$link-visited-color: $primary-color;
$link-hover-color: $primary-light-color;
$link-active-color: $primary-light-color;
$link-font-size: 0.85rem;
$link-font-weight: 500;
$link-text-decoration: none;
$link-hover-text-decoration: underline;
$link-padding:0;
$link-margin:0;

$button-font-family: $secondary-font-family;
$button-font-size: 0.8rem;
$button-line-height:1rem;
$button-font-weight: 500;
$button-border-radius: 0px;
$button-border: 0px;
$button-border-color: unset;
$button-padding: .5rem 1.5rem;
$button-margin: 0 0.5rem 0.5rem 0;
$button-background-color: $primary-color;
$button-color: #eee;
$button-shadow: $shadow-elevation-1;
$button-hover-background-color: $primary-hover-color;
$button-hover-color: #fff;
$button-active-background-color: $primary-hover-color;
$button-active-color: #fff;
$button-active-filter: brightness(0.9);
$button-disabled-background-color: $disabled-color;
$button-disabled-color: $primary-font-color;
$button-border-width: 2px;
$button-text-transform: uppercase;
$button-text-shadow: 0 0 2px #9e9e9e;
$button-transparent: rgba($background-color, 0.9);
$button-small-font-size: 0.75rem;
$button-small-padding: .25rem .75rem;
$button-round-border-radius: 50%;
$button-round-padding: 0.5rem;
$button-transparent-background-color: rgba($header-background-color, 0.9);
$button-transparent-color: $header-font-color;
$button-transparent-hover-color: $header-hover-color;

$highlight-color: $primary-color;
$highlight-hover-color: $primary-hover-color;
$highlight-background-color: transparent;
$highlight-background-hover-color: transparent;
$highlight-font-weight: 500;

$breadcrumb-background-color: transparent;
$breadcrumb-color: $primary-light-color;
$breadcrumb-default-color: #eee;
$breadcrumb-font-size: .85rem;
$breadcrumb-text-decoration: none;
$breadcrumb-hover-color: #fff;
$breadcrumb-hover-background-color: transparent;
$breadcrumb-padding: 1rem 0;
$breadcrumb-margin: 0;
$breadcrumb-border-radius: 0;

$chip-font-family: $secondary-font-family;
$chip-background-color: #121212;
$chip-padding:0.3rem 0.5rem;
$chip-margin: 0 .5rem .5rem 0;
$chip-font-size: 0.8rem;
$chip-font-weight: 400;
$chip-line-height: 1rem;
$chip-border-radius: 1rem;
$chip-border: 0;
$chip-color: #eee;
$chip-hover-background-color: #212121;
$chip-hover-color: #eee;

$badge-font-family: $primary-font-family;
$badge-background-color: $primary-color;
$badge-color: $primary-inverse-color;
$badge-padding: .2rem .35rem;
$badge-margin: 0 .3rem 0 0;
$badge-font-size: 0.7rem;
$badge-font-weight: 400;
$badge-line-height: 1rem;
$badge-border-radius: 50%;
$badge-border: 0;
$badge-hover-background-color: $primary-hover-color;
$badge-hover-color: $primary-inverse-color;
$badge-size: 1rem;

$modal-border: 0;
$modal-border-radius: 0;
$modal-header-font-family: $secondary-font-family;
$modal-header-font-size: 1rem;
$modal-header-border: 0;
$modal-header-background-color: transparent;
$modal-header-padding: 0;
$modal-header-margin: 0;
$modal-header-content: "";
$modal-body-font-family: $primary-font-family;
$modal-body-font-size: 0.85rem;
$modal-body-line-height: 1rem;
$modal-body-shadow: $shadow-elevation-1;
$modal-body-border: 0;
$modal-body-padding: $main-padding;
$modal-body-background-color: #121212;
$modal-body-border-radius: 0;
$modal-background-color: transparent;
$modal-close-button-border: 1px solid #757575;
$modal-close-button-border-radius: $button-round-border-radius;
$modal-close-button-background-color: $header-background-color;
$modal-close-button-color: $button-transparent-color;
$modal-close-button-padding: $button-round-padding;
$modal-close-button-margin: 1rem 0 1rem auto;
$modal-close-button-opacity: 1;
$modal-close-button-size: 2.5rem;
$modal-close-button-shadow: $shadow-elevation-1;
$modal-close-button-hover-background-color: $primary-hover-color;
$modal-close-button-hover-color: $button-transparent-hover-color;
$modal-close-button-hover-opacity: 1;

$profile-image-border-radius:50%;
$profile-image-small-size: 24px;
$profile-image-size: 80px;
$profile-image-big-size: 120px;
$profile-image-huge-size: 325px;
$profile-image-padding: 0.5rem;
$profile-image-margin: 0 1rem 1rem 0;

$card-background-color: rgba(255,255,255,.05);
$card-color: $primary-font-color;
$card-border-radius: 0;
$card-border: 0;
$card-padding: 2rem;
$card-margin: 0 1rem 1rem 0;
$card-font-size: 0.85rem;

$card-title-font-family: $secondary-font-family;
$card-title-font-size: 1.25rem;
$card-title-font-weight: 700;
$card-title-line-height: 1.5rem;
$card-title-margin: 0;
$card-title-padding: 0.5rem 0;
$card-title-color: #eee;

$card-subtitle-font-family: $secondary-font-family;
$card-subtitle-font-size: 0.85rem;
$card-subtitle-font-weight: 700;
$card-subtitle-line-height: 1rem;
$card-subtitle-margin: 0;
$card-subtitle-padding: 0 0 0.5rem 0;
$card-subtitle-color: #616161;

$card-footer-background-color: transparent;
$card-footer-border: 1px solid #424242;
$card-footer-border-radius: 0;
$card-footer-padding: default;
$card-footer-margin: default;

$profile-card-background-color: #121212;
$profile-card-padding: 1.25rem;
$profile-card-margin: 1rem 0;

$profile-card-image-width: 100%;
$profile-card-image-height: 250px;
$profile-card-image-border-radius: 0.625rem;
$profile-card-image-border: 1px solid #000;
$profile-card-image-margin: 0 0 0.625rem;

$form-control-label-font-family: $secondary-font-family;
$form-control-label-font-size: 0.85rem;
$form-control-label-font-weight: 700;
$form-control-label-line-height: 1rem;
$form-control-label-margin: 0.5rem 0;
$form-control-label-color: #eee;
$form-control-font-family: $primary-font-family;
$form-control-font-size: 0.85rem;
$form-control-font-weight: 400;
$form-control-line-height: 1rem;
$form-control-color: #fff;
$form-control-background-color: #212121;
$form-control-border-color: $divider-color;
$form-control-border: 1px solid $form-control-border-color;
$form-control-border-radius: 0;
$form-control-padding: 0.5rem 0.75rem;
$form-control-margin: 0 0 1rem 0;
$form-control-placeholder-color: #eee;
$form-control-placeholder-font-weight: 400;
$form-control-placeholder-font-size: 1rem;
$form-control-placeholder-line-height: 1.5rem;
$form-control-switch-border-radius: 1rem;
$form-control-switch-padding: 0 0 0 1rem;

$dropdown-font-family: $secondary-font-family;
$dropdown-background-color: #121212;
$dropdown-border: 0;
$dropdown-border-radius: 0;
$dropdown-padding:0;
$dropdown-margin: 0;
$dropdown-font-size: 0.85rem;
$dropdown-font-weight: 400;
$dropdown-line-height: 1rem;
$dropdown-color: $primary-font-color;
$dropdown-shadow: $shadow-elevation-1;
$dropdown-item-font-family: $dropdown-font-family;
$dropdown-item-background-color: $dropdown-background-color;
$dropdown-item-border: 0;
$dropdown-item-border-radius: $dropdown-border-radius;
$dropdown-item-padding: 0.75rem 1rem;
$dropdown-item-margin: 0;
$dropdown-item-font-size: $dropdown-font-size;
$dropdown-item-font-weight: $dropdown-font-weight;
$dropdown-item-line-height: $dropdown-line-height;
$dropdown-item-color: $dropdown-color;
$dropdown-item-hover-background-color: $primary-color;
$dropdown-item-hover-color: $primary-font-color;
$dropdown-item-hover-font-weight: $dropdown-font-weight;
$dropdown-item-hover-text-decoration: none;
$dropdown-item-active-background-color: $primary-color;
$dropdown-item-active-color: $primary-inverse-color;
$dropdown-item-active-font-weight: $dropdown-font-weight;
$dropdown-item-disabled-background-color: $neutral-background-color;
$dropdown-item-disabled-color: $primary-inverse-color;
$dropdown-item-disabled-font-weight: $dropdown-font-weight;

$table-margin: $main-padding 0;
$table-header-font-family: $secondary-font-family;
$table-header-font-size: 0.8rem;
$table-header-font-weight: 700;
$table-header-line-height: 1rem;
$table-header-color: $primary-font-color;
$table-header-padding: 0.5rem;
$table-header-margin: 0;
$table-header-border: 1px solid $divider-color;
$table-header-border-radius: 0;
$table-header-background-color: #121212;
$table-header-text-align: left;
$table-header-shadow: $shadow-elevation-0;
$table-header-text-transform: uppercase;
$table-row-font-family: $primary-font-family;
$table-row-font-size: 0.85rem;
$table-row-font-weight: 400;
$table-row-line-height: 1.25rem;
$table-row-color: $primary-font-color;
$table-row-padding: 0.5rem;
$table-row-margin: 0;
$table-row-border: 0;
$table-row-border-bottom: 1px solid #121212;
$table-row-border-radius: 0;
$table-row-background-color: transparent;
$table-row-text-align: left;
$table-row-shadow: $shadow-elevation-0;
$table-row-text-transform: default;
$table-row-background-color-odd: transparent;
$table-row-hover-background-color: #121212;
$table-row-hover-color: $primary-font-color;

$pagination-container-margin: 0 0 0 auto;
$pagination-container-justify: flex-end;
$pagination-height: 2.25rem;
$pagination-font-family: $secondary-font-family;
$pagination-font-size: 0.7rem;
$pagination-font-weight: 700;
$pagination-line-height: 1rem;
$pagination-color: $primary-font-color;
$pagination-padding: 0.5rem 0.75rem;
$pagination-margin: 0 0 1rem 0;
$pagination-border: 2px solid transparent;
$pagination-border-radius: 0;
$pagination-background-color: transparent;
$pagination-text-align: center;
$pagination-shadow: $shadow-elevation-0;
$pagination-text-transform: uppercase;
$pagination-hover-background-color: #121212;
$pagination-hover-color: $primary-font-color;
$pagination-hover-text-decoration: none;
$pagination-active-background-color: $pagination-background-color;
$pagination-active-color: $primary-font-color;
$pagination-active-border: 2px solid $primary-color;
$pagination-active-shadow: $shadow-elevation-1;
$pagination-disabled-background-color: $background-color;
$pagination-disabled-color: $divider-color;
$pagination-disabled-border: 2px solid transparent;


$tabs-font-family: $secondary-font-family;
$tabs-color: $primary-font-color;
$tabs-font-size: 0.85rem;
$tabs-font-weight: 500;
$tabs-line-height: 2rem;
$tabs-background-color: transparent;
$tabs-border-top: 1px solid transparent;
$tabs-border-bottom: 1px solid $divider-color;
$tabs-border-left: 0;
$tabs-border-right: 0;
$tabs-padding:0;
$tabs-margin: 0 0 1rem 0;

$tab-background-color: transparent;
$tab-border-top:0;
$tab-border-right:0;
$tab-border-bottom: 2px solid transparent;
$tab-border-left:0;
$tab-padding: 0;
$tab-margin: 0 1rem 0 0;
$tab-border-color: $divider-color;
$tab-active-color: $primary-font-color;
$tab-active-background-color: transparent;
$tab-active-border-color: $primary-color;
$tab-active-border-top:0;
$tab-active-border-right:0;
$tab-active-border-bottom:2px solid $primary-color;
$tab-active-border-left:0;
$tab-active-font-weight: 500;
$tab-hover-color: $primary-font-color;
$tab-hover-background-color: transparent;
$tab-hover-border-color: $divider-color;
$tab-hover-border-top:0;
$tab-hover-border-right:0;
$tab-hover-border-bottom:2px solid $divider-color;
$tab-hover-border-left:0;
$tab-hover-font-weight: 500;

$menu-font-family: $secondary-font-family;
$menu-background-color: #000;
$menu-width: 250px;
$menu-box-shadow: $shadow-elevation-3;
$menu-hover-background-color: $primary-color;
$menu-hover-color: $primary-font-color;
$menu-active-background-color: $primary-color;
$menu-active-color: $primary-font-color;
$menu-active-hover-background-color: $primary-hover-color;
$menu-item-background-color: transparent;
$menu-item-color: #eee;
$menu-item-border-radius: 0;
$menu-divider-color: #000;
$menu-scrollbar-color: #212121;
$menu-scrollbar-background-color: #000;
$menu-scrollbar-width: 5px;
$menu-scrollbar-border-radius: 0px;

$date-picker-day-border-radius: 0;

$alert-background-color: $tertiary-light-color;
$alert-font-color: $primary-inverse-color;

$pos-menu-width: 300px;
$pos-secondary-color: #757575;
$pos-column-color:rgba(255,255,255,0.05);

$cms-drop-ready-background-color: inherit;
$cms-drop-background-color: inherit; 

$water-mark-filter: brightness(0) invert(1) grayscale(100%);
