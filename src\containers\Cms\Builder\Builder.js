import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { ActionCreators as UndoActionCreators } from 'redux-undo';
import { useParams } from "react-router-dom";
import CodeEditor from '@uiw/react-textarea-code-editor';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Container, Row, Col, ProgressBar } from 'react-bootstrap';

import { Dustbin, Frame } from './Layers';
import DisplayBar from './DisplayBar';
import Toolbar from './Toolbar';

import APIThemes from '../../../api/Themes';
import APICms from '../../../api/Cms';
import {groupElementsbyParentId, formatJsonFromCMS, formatCMSFromJson, getPageById, injectCss, injectSASSVars, emptyPage, setHeader} from '../../../utils/cms';
import * as actions from '../../../store/actions';

import './Builder.scss';

export const Builder = (props) => {
    let { id, website } = useParams();

    const dispatch = useDispatch();
    const cmsSelector = useSelector(state => state.cms);
    const cmsSelectorElements = useSelector(state => state.cmsElements.present);

    const query = new URLSearchParams(window.location.search);
    
    const [data, setData] = useState();
    const [loading, setLoading] = useState(true);
    const [type, setType] = useState();
    const [bgColors, setBgColors] = useState({background: "#fff", color: "#000"});
    const [containerSize, setContainerSize] = useState({width: "100%", height: "100vh"});
    const [currentElement, setCurrentElement] = useState(null);

    if (!id && query.get('id')) id=query.get('id');
    const pageFactor = id;

    const undoRedo = useCallback((type) => {
        let _currentElement = cmsSelector.currentElement;
        if (type==='undo') dispatch(UndoActionCreators.undo());
        else dispatch(UndoActionCreators.redo());
        dispatch(actions.CMSSetCurrentElement(null));        
        if (_currentElement) setCurrentElement(_currentElement);
        else setCurrentElement(null);
    }, [dispatch, cmsSelector.currentElement]);

    const undoHandler = useCallback(() => undoRedo('undo'), [undoRedo]);
    const redoHandler = useCallback(() => undoRedo('redo'), [undoRedo]);

    const removeItem = useCallback((e, element = null) => {
        e.preventDefault();
        e.stopPropagation();

        if (!element) element = cmsSelector.currentElement;
        if (element){
            const _currentElement = cmsSelectorElements.elements.filter(el => el && el?.id === element);
            if (_currentElement.length > 0){
                dispatch(actions.CMSRemoveElement({...JSON.parse(JSON.stringify(_currentElement[0]))}));
                dispatch(actions.CMSSetCurrentElement(null));
            }
        }
    }, [cmsSelector.currentElement, cmsSelectorElements.elements, dispatch]);

    const saveAndPublish = useCallback(async (action) => {
        const cms_data=JSON.parse(localStorage.getItem(`cms_${pageFactor}`));

        let content;
        if (cmsSelector.currentPageProps?.config?.is_code===1){
            content=cms_data?.elements?.[0] || "";
        } else {
            //if (+type===2) content=cms_data?.elements || {};
            //else {
                content={...cms_data};
                content.content = cms_data?.elements || [];
                if (content.elements) delete content.elements;
                if (cms_data?.css?.id) content.css = cms_data.css;
                if (cms_data?.js?.id) content.js = cms_data.js;
            //}
            content=JSON.stringify(content);
        }

        // this is what we send to the API
        let page_data = {
            slug: cms_data?.slug || "",
            title: cms_data?.title || "",
            page_type_id: type || 2, // defaults to content block just in case
            website_id: +cmsSelector?.currentWebsite || null,
            keywords: cms_data?.keywords || "",
            description: cms_data?.description || "",
            content: content,
            restricted_access: cms_data?.restricted_access || 0,
            redirect_to: cms_data?.redirect_to || "",
            restricted_roles: cms_data?.restricted_roles || [],
            restricted_groups: cms_data?.restricted_groups || [],
            themes: cms_data?.themes || [],
        };

        let res, action_text, button_enabled;
        switch(action){
            case "publish":
                action_text = "Published";
                if (cms_data?.page_id || cmsSelector.currentPage) page_data.id = +cms_data?.page_id || +cmsSelector.currentPage;
                if (cmsSelector.currentPageProps?.config?.is_code===1 && cms_data?.theme) page_data.themes = cms_data?.theme;
                res = APICms.pages.create(page_data);
                button_enabled = cmsSelector.currentPageProps?.config?.is_code===1 ? 1 : 0; // if its not a script or style, disable all buttons, it will reenable when there's a change to the elements, but css and js types dont track elements
                break;
            case "save":
            default:
                action_text = "Saved";
                if (cms_data?.page_id || cmsSelector.currentPage) page_data.page_id = +cms_data?.page_id || +cmsSelector.currentPage;
                res = APICms.pages.history.create(page_data);

                // saves title if its a new page, if its a css it saves the theme associations too
                const res2 = await getPageById({id: cmsSelector.currentPage});
                if (res2.length) {
                    if (!res2[0].content){
                        let fields={};
                        let css_theme=[];
                        if (cms_data?.title) fields.title = cms_data.title;
                        if (cmsSelector.currentPageProps?.config?.is_code===1 && cms_data?.theme) css_theme = cms_data?.theme;
                        res = APICms.pages.create({id: cmsSelector.currentPage, website_id: cmsSelector.currentWebsite, ...fields, themes: css_theme});
                    }
                }
                button_enabled = 2;
                break;
        }
        return {result: res, action_text: action_text, button_enabled: button_enabled};
    }, [cmsSelector.currentPage, cmsSelector.currentPageProps, cmsSelector.currentWebsite, pageFactor, type]);

    const _getPageById = useCallback(async (id) => {
        let page = null;
        if (id) page = await getPageById({id: id});
        return page;
    },[]);

    const _saveToLocalStorage = useCallback(content => {
        if (!loading){
            const _head = setHeader(cmsSelector.currentPageProps, {
                elements: content 
            });
            let _head2 = JSON.parse(JSON.stringify({..._head}));
            if (_head2.config) delete _head2.config;

            localStorage.setItem(`cms_config_${pageFactor}`,JSON.stringify(_head?.config || {}));        
            localStorage.setItem(`cms_${pageFactor}`,JSON.stringify(_head2 || {}));
            window.dispatchEvent(new Event("storage"));
        }
    },[cmsSelector.currentPageProps, loading, pageFactor]);

    useEffect(() => { // this one is used to refresh the sidebar when an undo/redo action is performed
        console.log("useEffect - currentElement")
        if (currentElement){
            dispatch(actions.CMSSetCurrentElement(currentElement));
            setCurrentElement(null);
        }
    }, [currentElement, dispatch]);

    // if an id is set, save that as the current page and attempt to load from local storage
    useEffect(() => {
        console.log("useEffect - id")
        if (id) {
            setLoading(true);
            _getPageById(id).then(page => {
                if (page && page.length) {
                    const cms_data=JSON.parse(localStorage.getItem(`cms_${pageFactor}`));
                    const cms_config=JSON.parse(localStorage.getItem(`cms_config_${pageFactor}`));

                    let data, _props;
                    if (page[0]?.content?.content) data=page[0]?.content?.content;
                    else if (page[0]?.content) data=[page[0]?.content];
                    else data=[emptyPage(page[0]?.page_type?.config || cms_config || {})];

                    if (cms_data && cms_data?.page_id===id) {
                        _props = {
                            page_id: id,
                            title: cms_data.title,
                            slug: cms_data?.slug || "",
                            description: cms_data?.description || "",
                            keywords: cms_data?.keywords || "",
                            page_type: page?.[0]?.page_type_id,
                            page_type_name: cms_data?.page_type_name || "",
                            themes: cms_data?.themes || [],
                            config: page[0]?.page_type?.config || cms_config || {},
                            css: cms_data?.css?.id || 0,
                            js: cms_data?.js?.id || 0,
                            restricted_access: cms_data?.restricted_access || 0,
                            redirect_to: cms_data?.redirect_to || "",
                            restricted_roles: cms_data?.restricted_roles || [],
                            restricted_groups: cms_data?.restricted_groups || [],                
                        };

                        if (cms_data?.elements) data = cms_data?.elements || [];
                    } else {
                        _props = {
                            page_id: id,
                            title: page?.[0]?.title,
                            slug: page?.[0]?.slug || "",
                            description: page?.[0]?.description || "",
                            keywords: page?.[0]?.keywords || "",
                            page_type: page?.[0]?.page_type_id,
                            page_type_name: page?.[0]?.page_type?.name || "",
                            themes: page?.[0]?.theme_ids || [],
                            config: page?.[0].page_type?.config || {},
                            css: page?.[0]?.content?.css?.id || 0,
                            js: page?.[0]?.content?.js?.id || 0,
                            restricted_access: page?.[0]?.restricted_access || 0,
                            redirect_to: page?.[0]?.redirect_to || "",
                            restricted_roles: page?.[0]?.page_access_roles?.map(a=>a.role_id) || [],
                            restricted_groups: page?.[0]?.page_access_groups?.map(a=>a.group_id) || [],
                        };
                    }

                    formatCMSFromJson(data, _props).then(res => {
                        if (!res.length) res=[emptyPage(page[0]?.page_type?.config || cms_config || {})];
                        localStorage.setItem(`cms_${pageFactor}`,JSON.stringify({..._props, elements: res}));
                        localStorage.setItem(`cms_config_${pageFactor}`,JSON.stringify(page[0]?.page_type?.config || {}));
                        setData(res);
                        setType(page[0].page_type_id);
                        dispatch(actions.CMSSetCurrentWebsite(website));
                        dispatch(actions.CMSSetCurrentPage(id));
                        dispatch(actions.CMSSetCurrentPageProps(_props));
                        dispatch(actions.CMSReset(res));
                        dispatch(UndoActionCreators.clearHistory());
                    });
                }
            }).finally(() => {
                setLoading(false);
            });
        } 
    },[id, website, dispatch, _getPageById, pageFactor]);


    // keypress handlers
    useEffect(() => {
        console.log("useEffect - keypress handlers")
        const keyDownHandler = (e => {
            switch(e.keyCode){
                case 46: // delete
                    if (e.target.tagName !== "INPUT" && e.target.tagName !== "TEXTAREA" && e.target.tagName !== "SELECT" && e.target.role !== "textbox") removeItem(e);
                    break;                
                case 90: // ctrl + z
                    if (e.ctrlKey && e.target.tagName !== "INPUT" && e.target.tagName !== "TEXTAREA" && e.target.tagName !== "SELECT" && e.target.role !== "textbox") {
                        e.preventDefault();
                        undoHandler();
                    }
                    break;
                case 89: // ctrl + y
                    if (e.ctrlKey && e.target.tagName !== "INPUT" && e.target.tagName !== "TEXTAREA" && e.target.tagName !== "SELECT" && e.target.role !== "textbox") {
                        e.preventDefault();
                        redoHandler();
                    }
                    break;
                case 83: // ctrl + s
                    if (e.ctrlKey) {
                        e.preventDefault();
                        saveAndPublish("save");
                    }
                    break;
                default:
                    break;
            }
        });
    
        document.addEventListener('keydown', keyDownHandler);
        return () => {
            document.removeEventListener('keydown', keyDownHandler);
        }
    }, [removeItem, undoHandler, redoHandler, saveAndPublish]);


    // format cms (drag & drop) elements to json when elements change
    useEffect (() => {
        console.log("useEffect - cmsSelectorElements.elements, cmsSelector.currentPageProps")
        if (cmsSelectorElements.elements.length>0 && !loading){
            let cms_elements;
            if (cmsSelector.currentPageProps?.config?.is_code===1) cms_elements = JSON.parse(JSON.stringify(cmsSelectorElements.elements));
            else  {
                const elements = groupElementsbyParentId(JSON.parse(JSON.stringify(cmsSelectorElements.elements)),"parent_id");
                cms_elements = formatJsonFromCMS(elements); // we store this in the database, remember to add slug, and css, js, etc
            }
            // save to local storage
            _saveToLocalStorage(cms_elements);
            setData(cmsSelectorElements.elements);
        }
    }, [cmsSelectorElements.elements, cmsSelector.currentPageProps, _saveToLocalStorage, loading]);


    // when the preview theme changes, redraw the page and inject the new theme
    useEffect(() => {
        console.log("useEffect - cmsSelector.currentWebsiteTheme")
        if (cmsSelector.currentWebsiteTheme){
            injectSASSVars(cmsSelector.currentWebsiteTheme);
            const bg = cmsSelector.currentWebsiteTheme.filter(a=>a.name === "$background-color" || a.name === "$primary-font-color");
            if (bg.length>0){
                let bgprops = {backgroundColor: "#fff", color: "#000"};
                bg.forEach(b=>{
                    if (b.name === "$background-color") bgprops.backgroundColor = b.compiledValue;
                    if (b.name === "$primary-font-color") bgprops.color = b.compiledValue;
                });
                setBgColors(bgprops);
            }
        }
    }, [cmsSelector.currentWebsiteTheme]);

        
    // when the current css changes, inject css attached to the theme
    useEffect(() => {
        console.log("useEffect - cmsSelector.currentWebsiteCss")
        const _loadCss = async (id) => {
            let css="";
            if (Array.isArray(id)){
                for (const i of id){
                    const res = await _getPageById(i);
                    if (res) css += res[0].content;
                }
            } else {
                const res = await _getPageById(id);
                if (res) css += res[0].content;
            }
            injectCss(css,"preview");
        }
        if (cmsSelector.currentWebsiteCss) _loadCss(cmsSelector.currentWebsiteCss);
    },[cmsSelector.currentWebsiteCss, _getPageById]);


    // when the current website changes, get the default theme
    useEffect(() => {
        console.log("useEffect - cmsSelector.currentWebsite")
        const _getDefaultWebsite = async () => {
            const res=await APIThemes.get({my:1});
            if (res.data){
                dispatch(actions.CMSSetCurrentWebsiteTheme(res.data[0]?.content?.variables || null));
                dispatch(actions.CMSSetCurrentWebsiteCss(res.data[0]?.css_ids || []));
                dispatch(actions.CMSSetCurrentWebsite(res.data[0].website_id));
            }
        }
        if (!cmsSelector.currentWebsite) _getDefaultWebsite();
    }, [dispatch, cmsSelector.currentWebsite]);

    
    // when the display mode changes, update the container size to mimic the device
    useEffect(() => {
        console.log("useEffect - cmsSelector.displayMode")
        if (cmsSelector.displayMode.indexOf('mobile') > -1){
            let landscape=false;
            if (cmsSelector.displayMode.indexOf('-landscape') > -1) landscape=true;
            const mode=cmsSelector.displayMode.replace("mobile-","").replace("-landscape","");
            let [width, height] = mode.split("x");
            width=width+"px";
            height=height+"px";
            if (landscape) setContainerSize({width: height, height: width});
            else setContainerSize({width: width, height: height});
        } else setContainerSize({width: "100%", height: "100vh"});
    },[cmsSelector.displayMode]);


    useEffect(() => {
        console.log("useEffect - componentDidMount")
        return () => {
            setData(null);
            setLoading(false);
            setType(null);
            setBgColors({background: "#fff", color: "#000"});
            setContainerSize({width: "100%", height: "100vh"});
            setCurrentElement(null);
        }
    }, []);


    return (
        <Container fluid className="cms-builder-container">
            <Row>
                <DndProvider backend={HTML5Backend}>
                    <Col className="designer-area" data-color-mode="light">
                        {loading && 
                            <div className="cms-builder-loading">
                                <ProgressBar className="indeterminate" now={85} />
                            </div>
                        }
                        {cmsSelector && <DisplayBar page_type={type} save={saveAndPublish} undo={undoHandler} redo={redoHandler} pageFactor={pageFactor} />}
                        {!cmsSelector.currentPageProps?.config?.is_code &&
                            <>
                                {/*<Frame title={randomUUID()} {...containerSize}>*/}
                                    <Dustbin className={`designer`} style={{...bgColors,...containerSize}} {...props} page_type={type} data={data} remove={removeItem} setLoading={setLoading} />
                                {/*</Frame>*/}
                            </>
                        }
                        {(cmsSelector.currentPageProps?.config?.is_code===1) && 
                            <div className="cover-area editor">
                                <CodeEditor
                                    value={data?.[0] || data || ""}
                                    language={+type===7?"css":"js"}
                                    placeholder="Please enter JS code."
                                    padding={15}
                                    className="cover-area"
                                    style={{
                                        fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace',
                                        backgroundColor: "#fff",
                                    }}
                                    onBlur={e=>_saveToLocalStorage([e.currentTarget.value])}
                                />
                            </div>
                        }
                    </Col>
                    {cmsSelector.displayMode!=="expand" &&
                        <Col sm="12" lg="auto" className="toolbar-area" style={{position:"relative"}}>
                            <Toolbar page_type={type} /*save={saveAndPublish} remove={removeItem}*/ pageFactor={pageFactor} setLoading={setLoading} />
                        </Col>
                    }
                </DndProvider>
            </Row>
        </Container>
    );
}