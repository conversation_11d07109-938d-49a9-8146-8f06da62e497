import React from 'react'
import { format } from 'date-fns'

const DateHeader = ({styles, rangeStart, rangeEnd, dateRange, ...props}) => {
    return (
        <div>
            <div className={styles.date}>
                    <i className="far fa-calendar-week" /> 
                    {dateRange === "day" &&
                        <span>
                            {format(rangeStart, 'MMM dd')}, {format(rangeStart, "yyyy")}
                        </span>
                    }
                    {dateRange === "week" &&
                        <span>
                            Week of {format(rangeStart, 'MMM dd')} - {format(rangeEnd, 'MMM dd')}, {format(rangeStart, "yyyy")}
                        </span>
                    }
                    {dateRange === "month" &&
                        <span>
                            Month of {format(rangeStart, 'MMMM')}, {format(rangeStart, "yyyy")}
                        </span>
                    }
                    {dateRange === "custom" &&
                        <span>
                            {rangeStart && 
                                <span>
                                    {format(rangeStart, 'MMM dd')}
                                </span>
                            } 
                            {rangeEnd &&  
                                <span>
                                    {" "}- {format(rangeEnd, 'MMM dd')}
                                </span>
                            }
                            , {format(rangeStart, "yyyy")}
                        </span>
                    }
            </div>
        </div>  
    )
}

export default DateHeader