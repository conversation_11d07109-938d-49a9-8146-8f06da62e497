import React, { useState, useEffect, useRef } from "react";
import { EditorState, ContentState, convertToRaw, convertFromHTML } from 'draft-js';
import { Editor } from "react-draft-wysiwyg";
import draftToHtml from 'draftjs-to-html';
import htmlToDraft from 'html-to-draftjs';
//import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
//import { toolbar } from "./toolbar";

import "./styles.scss";
import styles from "./Wysiwyg.module.scss";

export const Wysiwyg = (props) => {
    const [editorState, setEditorState] =useState(EditorState.createEmpty());
    
    useEffect(() => {
        const contentBlock = props.defaultValue ? htmlToDraft(props.defaultValue) : convertFromHTML("<p>Add something <b>COOL</b> here...</p>");
        if (contentBlock) {
            setEditorState(EditorState.createWithContent(ContentState.createFromBlockArray(contentBlock.contentBlocks)));
        }
    }, [props.defaultValue]);

    useEffect(() => {

        return () => {
            setEditorState(null);
        }
    }, []);

    const changeHandler = (editorState) => {
        setEditorState(editorState);
    };

    const blurHandler = (e, editorState) => {
        if (props.onBlur){
            const _value = draftToHtml(convertToRaw(editorState.getCurrentContent()));        
            props.onBlur(e, _value);

            /*props.onBlur({
                preventDefault() {},
                stopPropagation() {},
                target: { value: _value },
            });*/
        }
    };

    return (
        <Editor
            editorState={editorState}
            onEditorStateChange={changeHandler}
            onBlur={(e, editorState) => blurHandler(e, editorState)}
            wrapperClassName={styles.wrapper}
            editorClassName={styles.editor}
            toolbarClassName={styles.toolbar}
            toolbar={{
                options: ['inline', 'blockType', 'fontSize', 'fontFamily', 'list', 'textAlign', 'colorPicker', 'link', 'embedded', 'emoji', 'history'],
                link: { popupClassName: `${styles.modal} ${styles["modal-right"]}` },
                embedded: { popupClassName: `${styles.modal} ${styles["modal-right"]}` },
                emoji: { popupClassName: `${styles.modal} ${styles["modal-right"]}` },
                colorPicker: { popupClassName: `${styles.modal}` },
            }}  
            toolbarCustomButtons={props.customToolbarButtons ? props.customToolbarButtons : null}          
        />
    );
}