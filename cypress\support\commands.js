/*eslint-disable*/
// ***********************************************
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

let baseQaApi = Cypress.env('qa_api_url');
let baseDevApi = Cypress.env('dev_api_url');

Cypress.Commands.add('timeFormatCheck', (timeValue)=>{
    expect(timeValue).to.match(/^[0-9]{4}-[0-9]{2}-[0-9]{2}[T][0-9]{2}:[0-9]{2}:[0-9]{2}[+][0-9]{2}:[0-9]{2}/)
})
//confirmed ATOM  "2021-10-04T00:00:00+00:00"
//from Notes 2022-04-04T14:44:02.000000Z

Cypress.Commands.add('restoreLocalUser', (user)=>{
    localStorage.setItem('user', JSON.stringify(user));
})

Cypress.Commands.add('continueAfterFail',()=>{
    cy.on('uncaught:exception', ()=>{
        return
    })
    cy.on('fail', ()=>{
        return
    })
});

/**Used for when you want to designate the url (such as local or dev) */
Cypress.Commands.add('loginLocal', (localURL, username, password)=>{
    cy.session([username, password], ()=>{
        cy.intercept('GET', "/api/user/user/**").as('getUserUser');
        cy.intercept('POST', "/api/user/login").as('loginCall');
        cy.visit(localURL);
        cy.wait(1000)//added a wait in case we need to redirect from a different url
        cy.get('[data-cy="login-username"]').type(username);
        cy.get('[data-cy="login-password"]').type(password);
        cy.get('[data-cy="login-submit"]').click();
        cy.wait('@loginCall');
        cy.wait('@getUserUser');
    })
});



Cypress.Commands.add('basicProductTest',()=>{
    cy.get('[data-cy="variant-type-single-variant"]')
        .should('exist');
    cy.get('[data-cy="add-variant-btn"]')
        .click();
    cy.wait(500);
    cy.get('[data-cy="variant-type-each-basic"]')
        .should('exist');
    cy.get('[data-cy="variant-type-single-variant"]')
        .should('not.exist');
})

Cypress.Commands.add('spyLog', ()=>{
    cy.window()
        .its('console').then((console)=>{
            cy.spy(console, 'log').as('log')
        })
})

Cypress.Commands.add('stubPrint',()=>{
    cy.window().then((win)=>{
        cy.stub(win, 'print')
    })
})

/** Follow with a .then((response)=>{}) to use the response and get the token 
 * 
 * Example: 
 * .then((response)=>{
 *           header = {
 *              Authorization: response.body.data.token,
 *              "Content-Type": "application/json"
 *           }
 *       })
*/
Cypress.Commands.add('loginApi',(un, pw)=>{
    cy.request('POST', `${baseQaApi}/user/login`, {
        username: un,
        password: pw
    })
})