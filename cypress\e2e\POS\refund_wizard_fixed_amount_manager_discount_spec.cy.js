/*eslint-disable*/

let baseUrl = "https://portal-qa.impactathleticsny.com/p/";
let adminUserName= Cypress.env("impact_admin_user");
let password = Cypress.env("login_password");

describe("it will make a transaction and refund it by a fixed amount", {testIsolation: false, scrollBehavior: "center"}, ()=>{
    let orderNumber;
    let transactionNumber;
    let local;
    let maxRefund;

    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, adminUserName, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach(()=>{
        cy.viewport(1920, 1080);
    });

    context("it will make a payment to refund", ()=>{
        it("will visit the POS",()=>{
            cy.visit(`${baseUrl}pos/5`);
            cy.wait(500);
        })

        it("will add multiple items to the order",()=>{
            cy.get('[data-cy="product-card"]')
                .contains('Cheese Stick')
                .click({force:true});
            cy.wait(1500);
            cy.get('[data-cy="product-card"]')
                .contains('Cheese Pizza Slice')
                .click({force:true});
            cy.get('[data-cy="order-number"]')
                .invoke('text').as('orderNumber')
            cy.get('@orderNumber').then((text)=>{
                orderNumber = text
            });
            cy.get('.item-name')
                .children()
                .should('have.length', 2)
        });

        it("will add a user to the order",()=>{
            cy.get('.current-customer')
                .should('not.exist')
            cy.get('[data-cy="search-users"]')
                .click()
                .type("midnight")
            cy.wait(500)
            cy.get('#user_autocomplete-item-0')
                .invoke('text')
                .should("include", "<MidnightP>")
            cy.get('#user_autocomplete-item-0')
                .click();
            cy.get('.current-customer')
                .should('exist')
            cy.get('.current-customer')
                .invoke('text')
                .should('include', 'Mid Night')
        })

        it("will utilize a manager discount",()=>{
            cy.get('[data-cy="pos-button-checkout"]')
                .click();
            cy.get('[data-cy="payment-type-5"]')
                .click();
            cy.get('#username')
                .click()
                .type(adminUserName);
            cy.get('#password')
                .click()
                .type(password);
            cy.get('#amount')
                .click()
                .type('{selectall}{backspace}3.12');
            cy.get('[data-cy="details-add-to-cart"]')
                .click();
        });
    });

    context("it will refund from the order page by a fixed amount of 1.50",()=>{
        it("will visit the order page and check order data",()=>{
            cy.visit(`${baseUrl}order/${orderNumber}`);
            cy.wait(1000)
            cy.get('.transaction-pair-selectable')
                .children()
                .eq(1)
                .invoke('text')
                .should('include', '3.12')
            cy.get(':nth-child(1) > .collapse > .selected-transaction')
                .should('not.be.visible')
            cy.get('[data-cy="transaction-histories"]')
                .children()
                .should('have.length', 2) //the collapse of the accordion also counts as a child
            cy.get('.transaction-pair-selectable')
                .click()
            cy.get(':nth-child(1) > .collapse > .selected-transaction')
                .should('be.visible')
                
            cy.get('[data-cy="transaction-payment-type"]')
                .invoke('text')
                .should('include', "Manager Discount")
            cy.get('[data-cy="transaction-status"]')
                .invoke('text')
                .should('include', 'Complete')
        });

        it("will start the advanced refund and check data",()=>{
            cy.get('[data-cy="advanced-refund-btn"]')
                .click()
            cy.get('[data-cy="refund-sum-subtotal"]')
                .invoke('text')
                .should('include', '2.92')
            cy.get('[data-cy="refund-sum-payments"]')
                .invoke('text')
                .should('include', '3.12')
            cy.get('[data-cy="refund-sum-total"]')
                .invoke('text')
                .should('include', '3.12')
            cy.get('[data-cy="refund-sum-type"]')
                .invoke('text')
                .should('include', "By Amount")
        });

        it("will make sure you cannot refund more than max",()=>{
            cy.get('[data-cy="adv-refund-fixed-amount-btn"]')
                .click();
            cy.get('[data-cy="refund-next"]')
                .click();
            cy.get('#refund-amount')
                .click()
                .type('{selectall}{backspace}5')
            cy.get('[data-cy="refund-sum-max"]')
                .invoke('text').as('maxRefund')
            cy.get('@maxRefund').then((text)=>{
                    //setting both variables here seems redundant, but d/t Cypress's timing and async nature, using one local to this function is good for here, but we also need it later
                    let max = text.split(" ")[3].split("$")[1]
                    maxRefund = max;
                    cy.get('#refund-amount')
                        .invoke('val')
                        .should('equal', max) //going over the amount should result in the max amount
                })
            cy.get('#refund-amount')
                .click()
                .type('{selectall}{backspace}1.5')
            cy.get('[data-cy="refund-next"]').click()
        })

        it("will select internal register",()=>{
            cy.get('[data-cy="adv-refund-register-select"]')
                .within(()=>{
                    cy.get('button')
                        .contains('Internal Testing Only')
                        .click()
                });
            cy.get('[data-cy="refund-next"]')
                .click();
        });

        it("will add a memo!",()=>{
            cy.get('#refund-memo')
                .click()
                .type("This refund was done by Cypress! Because it was told to!")

            cy.get('[data-cy="refund-next"]')
                .click();
        });

        it("will double check summary data",()=>{
            cy.get('[data-cy="adv-refund-summary-amount"]')
                .invoke('text')
                .should('include', "1.5")
            cy.get('[data-cy="adv-refund-summary-customer"]')
                .invoke('text')
                .should('include', 'Mid Night');
            });
            
        it("will finish the refund and close the modal",()=>{
            cy.intercept('POST', '/api/payment/process/refund').as("finishRefund")
            cy.intercept('GET', '/api/order/order/*').as('refreshOrder')
            cy.get('.modal-body')
                .should('exist')
            cy.get('[data-cy="refund-submit"]')
                .invoke('text')
                .should('include', 'Process Refund')
            cy.get('[data-cy="refund-submit"]')
                .click()
            cy.wait('@finishRefund')
            cy.get('[data-cy="adv-refund-success-title"]')
                .invoke('text')
                .should('include', 'Refund Successful')
            cy.get('[data-cy="refund-cancel-button"]')
                .click()
            cy.wait(250)
            cy.get('.modal-body')
                .should('not.exist')
            cy.wait('@refreshOrder')
            cy.get('[data-cy="transaction-histories"]')
                .children()
                .its('length')
                .should('equal', 4) //double the number of transactions because of accordion drop down
            cy.get('[data-cy="transaction-histories"]')
                .children()
                .eq(2)
                .invoke('text')
                .should('include', "A. Auth")
            cy.get('[data-cy="transaction-authorized-by"]')
                .invoke('text')
                .should('include', "Skyler")
        });
    })

    it('will clear all sessions',()=>{
        Cypress.session.clearAllSavedSessions()
    })
})