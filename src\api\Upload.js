import Request from './Api';

const send = (props) => {
    return Request({
        url: '/upload',
        method:'POST',
        headers: {'Content-Type': 'multipart/form-data'},
        data: props.data,
        test: {id:1,file_url:"/static/media/sports-pattern.8e9c523e.jpg"} // simulate api call, sends the expected return
    })
}

const Upload = {
    send //, create, update, delete, etc. ...
}
  
export default Upload;