import React, {useState, useEffect, useCallback} from 'react';
import { Form, Table, OverlayTrigger, Tooltip } from 'react-bootstrap';
import ColorPicker from '../../../../components/common/ColorPicker';
//import Stack from '../../../../components/common/Stack';

const Shadow = React.forwardRef((props, _) => {
    const {shadow, selection} = props;

    const [color, setColor] = useState();
    const [horizontal, setHorizontal] = useState();
    const [vertical, setVertical] = useState();
    const [blur, setBlur] = useState();
    const [spread, setSpread] = useState();
    const [position, setPosition] = useState("");

    const clickHandler = useCallback((color, horizontal, vertical, blur, spread, position) => {
        selection(`${position || ""} ${horizontal || 0}px ${vertical || 0}px ${blur || 0}px ${spread || 0}px ${color || ""}`.trim());
    },[selection]);

    useEffect(() => {
        if (shadow){
            const ashadow = shadow.split(" ");
            if (ashadow.includes("inset")){
                setPosition("inset");
                ashadow.splice(ashadow.indexOf("inset"), 1);
            }
            setColor(ashadow?.[4] || null);
            setHorizontal(ashadow?.[0]?.replace("px", ''));
            setVertical(ashadow?.[1]?.replace("px", ''));
            setBlur(ashadow?.[2]?.replace("px", ''));
            setSpread(ashadow?.[3]?.replace("px", ''));
        }
    }, [shadow]);

    useEffect(() => {
        return () => {
            setColor(null);
            setHorizontal(null);
            setVertical(null);
            setBlur(null);
            setSpread(null);
            setPosition("");
        }
    }, []);

    return (
        <Table className="mb-0">
            <tbody>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>Color for the shadow</Tooltip>}><span>Color</span></OverlayTrigger>
                    </td>
                    <td>
                        <ColorPicker type="text" placeholder={`Shadow Color`} value={color || ""} onChange={(e,c)=>{
                            
                            //setColor(c);
                            clickHandler(c, horizontal, vertical, blur, spread, position);
                        }}/>
                    </td>
                </tr>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>Sets the horizontal distance of the shadow</Tooltip>}><span>Horizontal</span></OverlayTrigger>
                    </td>
                    <td>
                        <Form.Control 
                            type="range"
                            className="form-range"
                            step={1}
                            min={-100}
                            max={100}
                            value={horizontal || 0}
                            onChange={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setHorizontal(e.target.value);
                                clickHandler(color, e.target.value, vertical, blur, spread, position);
                            }
                        }/>
                    </td>
                </tr>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>Sets the vertical distance of the shadow</Tooltip>}><span>Vertical</span></OverlayTrigger>
                    </td>
                    <td>
                        <Form.Control 
                            type="range"
                            className="form-range"
                            step={1}
                            min={-100}
                            max={100}
                            value={vertical || 0}
                            onChange={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setVertical(e.target.value);
                                clickHandler(color, horizontal, e.target.value, blur, spread, position);
                            }
                        }/>
                    </td>
                </tr>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>Sets amount of blur</Tooltip>}><span>Blur</span></OverlayTrigger>
                    </td>
                    <td>
                        <Form.Control 
                            type="range"
                            className="form-range"
                            step={1}
                            min={0}
                            max={100}
                            value={blur || 0}
                            onChange={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setBlur(e.target.value);
                                clickHandler(color, horizontal, vertical, e.target.value, spread, position);
                            }
                        }/>
                    </td>
                </tr>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>Sets how much the shadow will spread from the element</Tooltip>}><span>Spread</span></OverlayTrigger>
                    </td>
                    <td>
                        <Form.Control 
                            type="range"
                            className="form-range"
                            step={1}
                            min={-100}
                            max={100}
                            value={spread || 0}
                            onChange={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setSpread(e.target.value);
                                clickHandler(color, horizontal, vertical, blur, e.target.value, position);
                            }
                        }/>
                    </td>
                </tr>
                <tr>
                    <td>
                        <OverlayTrigger placement="bottom" overlay={<Tooltip>Set the position of the shadow relative to the element</Tooltip>}><span>Position</span></OverlayTrigger>
                    </td>
                    <td>
                        <Form.Control as="select" custom value={position || ""} onChange={(e)=>{
                            setPosition(e.target.value);
                            clickHandler(color, horizontal, vertical, blur, spread, e.target.value);
                        }}>
                            <option value="">Outside</option>
                            <option value="inset">Inside</option>
                        </Form.Control>
                    </td>
                </tr>
            </tbody>
        </Table>
    );
});

export default Shadow;