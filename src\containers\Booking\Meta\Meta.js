import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Container, Col, Form, Button } from 'react-bootstrap';
import DatePicker from 'react-datepicker';

import Toast from '../../../components/Toast';
import * as actions from '../../../store/actions';
import Events from '../../../api/Events';
import Locations from '../../../api/Locations';
import { format } from 'date-fns';

import "./Meta.css";

export const Meta = (props) => {
    const dispatch = useDispatch();

    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [success, setSuccess] = useState();

    const [location, setLocation] = useState();
    const [startDate, setStartDate] = useState(new Date());
    const [endDate, setEndDate] = useState(new Date());
    const [parentId, setParentId] = useState(null);
    const [parentEvents, setParentEvents] = useState([]);
    const [requiresRegistration, setRequiresRegistration] = useState(1);

    useEffect(() => {
        let mounted = true;

        Locations.get({id: 1})
        .then( response => {
            if (mounted) setLocation(response.data[0]);
        }).catch(e => console.error(e));

        let now = new Date();
        setStartDate(new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0))
        setEndDate(new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59))

        return ()=> mounted = false;
    },[]);

    useEffect(() => {
        let mounted = true;

        Events.getSimple()
        .then( response => {
            if(mounted) {
                let events = [];
                response.data?.events.map( event => //add to list of possible parents if occurring at same time
                    (new Date(event.start_datetime) <= startDate && new Date(event.end_datetime) >= endDate) ? events = [...events, {"id": event.id, "name": event.name}] : null
                );
                setParentEvents(events);
            }
        }).catch(e => console.error(e));

        return ()=> mounted = false;
    },[endDate, startDate]);

    const startDateHandler = date => { //make sure to update end date if start date passes it
        let newDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
        setStartDate(newDate);
        if(newDate > endDate) setEndDate(new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59));
    }

    const endDateHandler = date => { //make sure end date can't be set to before start date
        let newDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
        if(newDate < startDate) setEndDate(new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 23, 59, 59));
        else setEndDate(newDate);
    }

    const parentHandler = (e) => {
        setParentId(e.currentTarget.value);
    }

    const toggleRequiresRegistration = () => {
        requiresRegistration === 1 ? setRequiresRegistration(0) : setRequiresRegistration(1);
    }

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            formData.append("location_id", location.id);
            formData.append("start_datetime", format(startDate, "yyyy-MM-dd HH:mm:ss"));
            formData.append("end_datetime", format(endDate, "yyyy-MM-dd HH:mm:ss"));
            formData.append("requires_registration", requiresRegistration.toString());
            
            let json_data = {};
            formData.forEach((value, key) => {
                    json_data[key] = value;
            });

            dispatch(actions.selectedItems(
                {...location,
                    ...{booking: 
                        {
                            event_name: formData.name,
                            event_description: formData.description,
                            start_datetime: formData.start_datetime,
                            end_datetime: formData.end_datetime,
                            requires_registration: formData.requires_registration
                        }
                    }
                }
            ));

            dispatch(actions.saveSpot(json_data));

            setSuccess(<Toast>Event details set successfully!</Toast>);
            setSubmitting(false);
        } else setSubmitting(false);
    };

    return (
        <Container fluid>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Form.Row>
                    <Col sm="12">
                        <Form.Group className="mt-3">
                            <h5 className="title">{"New Meta Event at " + location?.name}</h5>
                        </Form.Group>
                    </Col>
                </Form.Row>
                <Form.Row>
                    <Form.Group>
                        <Form.Label>Start</Form.Label>
                        <DatePicker 
                            dateFormat="MM/dd/yyyy"
                            minDate={new Date()}
                            maxDate={new Date(new Date().getFullYear()+100,12,31)}
                            showMonthDropdown
                            showYearDropdown
                            selected={startDate}
                            onChange={startDateHandler}
                            customInput={
                                <Button variant="light" className="datepicker-calendar" type="button">
                                    {format(startDate, "eee, MM/dd/yyyy")}
                                </Button>
                            }
                        />
                    </Form.Group>
                    <Form.Group className="endtime">
                        <Form.Label>End</Form.Label>
                        <DatePicker 
                            dateFormat="MM/dd/yyyy"
                            minDate={new Date()}
                            maxDate={new Date(new Date().getFullYear()+100,12,31)}
                            showMonthDropdown
                            showYearDropdown
                            selected={endDate}
                            onChange={endDateHandler}
                            customInput={
                                <Button variant="light" className="datepicker-calendar" type="button">
                                    {format(endDate, "eee, MM/dd/yyyy")}
                                </Button>
                            }
                        />
                    </Form.Group>
                </Form.Row>
                <Form.Row>
                    <Col sm="12">
                        <Form.Group controlId="event_name">
                            <Form.Label>Event Name</Form.Label>
                            <Form.Control required type="text" name="event_name" autocomplete="off" />
                        </Form.Group>
                    </Col>
                    <Col sm="12">
                        <Form.Group controlId="event_description">
                            <Form.Label>Description</Form.Label>
                            <Form.Control required as="textarea" name="event_description" />
                        </Form.Group>
                    </Col>
                </Form.Row>
                <Form.Row>
                    <Form.Group controlId="event_parent">
                        <Form.Label>Parent Event</Form.Label>
                        <Form.Text className="text-muted">An event must occur within the duration of its parent. Only valid parents are shown.</Form.Text>
                        <Form.Control required custom as="select" name="parent_id" value={parentId} onChange={parentHandler}>
                            <option key="default_option" value={"0"}>None</option>
                            {parentEvents.map( (parent, i) => 
                                <option key={`option_${i}`} value={parent.id}>{parent.name}</option>
                            )}
                        </Form.Control>
                    </Form.Group>
                </Form.Row>
                <Form.Row>
                    <Form.Group controlId="event_requires_registration">
                        <Form.Label>Requires Registration</Form.Label>
                        <Form.Switch name="requires_registration" checked={requiresRegistration === 1} onChange={toggleRequiresRegistration} />
                    </Form.Group>
                </Form.Row>
                <Form.Row>
                    <Col sm="12" lg={props.modal?"8":"4"} className="mt-4 mb-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={submitting?"submitting":""}>Save</Button>
                    </Col>
                </Form.Row>
            </Form>
        </Container>
    );
}