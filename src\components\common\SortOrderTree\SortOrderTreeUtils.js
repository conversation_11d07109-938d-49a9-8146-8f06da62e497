

/**Takes an array of items, sorts them according to their sort order and parent_id.
 * Also adds a name field if there isn't one and an index field as required by tree.
  */
export const adjustMenuItems = (items, applyChildren=true) =>{

    let index = 1

    //sort into order by sort_order
    let sorted = items.sort((a,b)=>(a.sort_order < b.sort_order ? -1 : a.sort_order > b.sort_order))
    //add the name field/index field that is required by the tree
    for(let i = 0; i < sorted.length; i++){
        sorted[i].index = index;
        index+=1;
        if(sorted[i].text && !sorted[i].name) sorted[i].name = sorted[i].text;
    }
    if(applyChildren) giveChildrenParents(sorted);
    return sorted;
}

export const getOnlyDefaultItems=(items)=>{
    let defaultOnly = items.filter((item)=>!item.company_id && item.is_disabled === 0)
    return defaultOnly;
}

export const disabledDefaultItems=(items)=>{
    let disabledDefaultOnly = items.filter((item)=>!item.company_id && item.is_disabled === 1)
    return disabledDefaultOnly;
}

//want enabled = true means it only returns items without the is_disabled.  If it's false, we get is_disabled items
export const onlyCompanyItems=(items, companyId, wantEnabled=true)=>{
    
    if(companyId){
        //remove all other company items (backend should be doing this, but just in case)
        let enabled = items.filter((item) => item.company_id === companyId || !item.company_id);
        
        let matches = [];
        let companyOnly = enabled.filter((item) => item.company_id === companyId);
        let defaultOnly = enabled.filter((item) => !item.company_id);
        
        for(let i = 0; i < companyOnly.length; i++){
            //there should only be one module per menu item
            let match = defaultOnly.find((item)=>item.module_id === companyOnly[i].module_id);
            if(match) matches.push(match);
        }
        for(let i = 0; i < matches.length; i++){
            let index = defaultOnly.findIndex((item) =>item.module_id ===  matches[i].module_id)
            if(index) defaultOnly.splice(index, 1)
        }

        let final;
        if(wantEnabled) final = [...companyOnly, ...defaultOnly].filter((item)=>item.is_disabled === 0);
        else if (!wantEnabled) final = [...companyOnly, ...defaultOnly].filter((item)=>item.is_disabled === 1);
        return final;
    }
    else return items;
}

export const giveChildrenParents =(sorted)=>{
    //filter down to the children so that we can assign them to their parents
    let onlyChildren = sorted.filter((item)=>item.parent_id)
    for(let i = 0; i < onlyChildren.length; i++){
        let parent = sorted.find((item)=>item.id === onlyChildren[i].parent_id)
        if(!parent) onlyChildren[i].parent_id = null; //if parent was deleted or otherwise removed
        else{
            if(!parent.children) parent.children = [onlyChildren[i]]
            else if(parent.children) parent.children.push(onlyChildren[i])
        }
    }
}

export const assignParentIds=(items, allItems)=>{
    let children=[];
    for(let i = 0; i < items.length; i++){
        if(items[i].children.length > 0){
           let tempChildren = childLogicForRecursion(items[i], allItems)
           children = [...children, ...tempChildren]
        }
    }
    return children;
}

const childLogicForRecursion=(parent, allItems)=>{
    let children = []
    for(let j = 0; j < parent.children.length; j++){
        let match = allItems.find((item)=>item.index === parent.children[j]);
        if(match){
            let menuItem={
                parent_id : parent.id,
                sort_order : j+1,
                id: match.id,
                wholeItem: match
            }
            children.push(menuItem)
        } 
        if(match.children?.length > 0){
            let moreChildren = childLogicForRecursion(match, allItems)
            children = [...children, ...moreChildren]
        }
    }
    return children;
}

export const getHighestSortOrder=(items)=>{
    items.filter(item=>item.parent_id === null);
    let sorted = items.sort((a,b)=>(a.sort_order < b.sort_order ? -1 : a.sort_order > b.sort_order));
    let lastItem = sorted[sorted.length -1];
    return lastItem.sort_order;
}