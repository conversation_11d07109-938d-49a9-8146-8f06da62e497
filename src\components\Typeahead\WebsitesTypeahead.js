import React, { useCallback } from 'react';

import { Typeahead } from './Typeahead';
import CMS from '../../api/Cms';

/**requires props.companyId */
export const WebsitesTypeahead=(props)=>{

    const getWebsites = useCallback(async()=>{
        let responseObj = {
            data: null,
            errors: null
        }
        
        try{
            let response = await CMS.websites.get({id: props?.companyId});
            if(response.status === 200 && response.data) responseObj.data = response.data;
            else if (response.errors) responseObj.errors = response.errors;
        }catch(ex){
            console.error(ex)
            responseObj.errors = ex;
        }

        return responseObj;
    },[props.companyId])

    return(
        <Typeahead
            {...props}
            id="theme-search"
            makeRequest={getWebsites}
            placeholder = {props.placeholder || "Search Websites"}
            paginated = {false}
        />
    )
}