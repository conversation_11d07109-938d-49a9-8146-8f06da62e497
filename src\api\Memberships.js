import Request from './Api';

// get memberships
const get = async (props) => {

    // use test data on localhost only
    let membership={};
    //if (["localhost", "127.0.0.1", ""].includes(window.location.hostname)){

        // gets mock data for testing - will be removed when api is ready
        let mockdata = await test();

        if (!props) membership=mockdata;
        else {
            mockdata.forEach((item,i,test) => {
                if (props.id) {
                    if (item.id===parseInt(props.id)){
                        membership=test[i];
                        return false;
                    }
                }
            });
        }
    //}
    
    return (
        Request({
            url: "/membership/get",
            data: props,
            test: {data:membership}  // send mock data to simulate an api call
        })
    );
}


// sets up mock data
const test = async() => {
    return new Promise((resolve, reject) => {
        let dummydata=[
            {
                id: 1,
                name: "Basic Membership",
                description: `<ul>
                    <li>Access to open court (when available)</li>
                    <li>Access to walking track</li>
                    <li>Access to locker room</li>
                    <li>1x free access to recovery (cryotherapy or normatec leg sleeves)</li>
                    <li>1x free Fitness Center workout</li>
                    <li>25% off classes, services, golf simulator (does not apply to league fees)</li>
                    <li>10% off apparel in Impact Store</li>
                    <li>10% off restaurant for individual member</li>
                    <li>1x month guest pass (guests cannot be repeated) (Guest pass includes access to open court, walking track, 1 free Fitness Center workout)</li>
                </ul>`,
                items:[
                    {
                        id: 11,
                        name: "Access to open court (when available)",
                        qty: 1
                    },
                    {
                        id: 12,
                        name: "Access to walking track",
                        qty: 1
                    },
                    {
                        id: 13,
                        name: "Access to locker room",
                        qty: 1
                    },
                    {
                        id: 14,
                        name: "Free access to recovery (cryotherapy or normatec leg sleeves)",
                        qty: 1
                    },
                    {
                        id: 15,
                        name: "Free Fitness Center workout",
                        qty: 1
                    },
                    {
                        id: 16,
                        name: "25% off classes, services, golf simulator (does not apply to league fees)",
                        qty: 1
                    },
                    {
                        id: 17,
                        name: "10% off apparel in Impact Store",
                        qty: 1
                    },
                    {
                        id: 18,
                        name: "10% off restaurant for individual member",
                        qty: 1
                    },
                    {
                        id: 19,
                        name: "Month guest pass (guests cannot be repeated) (Guest pass includes access to open court, walking track, 1 free Fitness Center workout)",
                        qty: 1
                    },
                ],
                billing_price: 39.00,
                billing_period: 0, // month
                users: 239
            },
            {
                id: 2,
                name: "Athlete with team/league/class",
                description: `<ul>
                    <li>1x free access to recovery (cryotherapy or normatec leg sleeves ($25 value)</li>
                    <li>1x free smoothie in Impact Restaurant ($8 value)</li>
                    <li>Upgrade to Individual membership</li>
                </ul>`,
                items:[
                    {
                        id: 14,
                        name: "Free access to recovery (cryotherapy or normatec leg sleeves)",
                        qty: 1
                    },
                    {
                        id: 20,
                        name: "Free smoothie in Impact Restaurant ($8 value)",
                        qty: 1
                    },
                    {
                        id: 21,
                        name: "Upgrade to Individual membership",
                        qty: 1
                    },
                ],
                billing_price: 39.00,
                billing_period: 1, //year
                users: 102
            },
            {
                id: 3,
                name: "Household ",
                description: `<ul>                
                    <li>2 adults living at same address. Up to 3 kids under 18 yrs old free. Additional children additional $10/month</li>
                    <li>Access to open court (when available)</li>
                    <li>Access to walking track</li>
                    <li>Access to locker room</li>
                    <li>2x free access to recovery (cryotherapy or normatec leg sleeves)</li>
                    <li>2x free Fitness Center workout</li>
                    <li>25% off classes, services, golf simulator (does not apply to league fees)</li>
                    <li>10% off apparel in Impact Store</li>
                    <li>10% off restaurant for household members</li>
                    <li>2x month guest pass (guests cannot be repeated) (Guest pass includes access to open court, walking rack, 1 free Fitness Center workout)</li>
                    <li>4x drop in 90-minute Child Care/month (2yrs – 7 yrs and potty trained)</li>
                </ul>`,
                items:[
                    {
                        id: 22,
                        name: "2 adults living at same address. Up to 3 kids under 18 yrs old free. Additional children additional $10/month",
                        qty: 1
                    },
                    {
                        id: 11,
                        name: "Access to open court (when available)",
                        qty: 1
                    },
                    {
                        id: 12,
                        name: "Access to walking track",
                        qty: 1
                    },
                    {
                        id: 13,
                        name: "Access to locker room",
                        qty: 1
                    },
                    {
                        id: 14,
                        name: "Free access to recovery (cryotherapy or normatec leg sleeves)",
                        qty: 2
                    },
                    {
                        id: 15,
                        name: "Free Fitness Center workout",
                        qty: 2
                    },
                    {
                        id: 16,
                        name: "25% off classes, services, golf simulator (does not apply to league fees)",
                        qty: 1
                    },
                    {
                        id: 17,
                        name: "10% off apparel in Impact Store",
                        qty: 1
                    },
                    {
                        id: 18,
                        name: "10% off restaurant for individual member",
                        qty: 1
                    },
                    {
                        id: 19,
                        name: "Month guest pass (guests cannot be repeated) (Guest pass includes access to open court, walking track, 1 free Fitness Center workout)",
                        qty: 2
                    },
                    {
                        id: 23,
                        name: "Drop in 90-minute Child Care/month (2yrs – 7 yrs and potty trained)",
                        qty: 4
                    },
                ],
                billing_price: 79.00,
                billing_period: 0,
                users: 49
            },
            {
                id: 4,
                name: "Student (HS and College)",
                description: `<ul>                
                    <li>Present current year school ID</li>
                    <li>Access to open court (when available)</li>
                </ul>`,
                items:[
                    {
                        id: 11,
                        name: "Access to open court (when available)",
                        qty: 1
                    },
                ],
                billing_price: 19.99,
                billing_period: 0,
                users: 32
            },
            {
                id: 5,
                name: "Military & First Responders",
                description: `<ul>                
                    <li>Present current/valid ID annually</li>
                    <li>10% off Individual ($39) or Household ($79) membership levels</li>
                </ul>`,
                items:[],
                billing_price: 35,
                billing_period: 0,
                users: 17
            },
            {
                id: 6,
                name: "Senior Citizen",
                description: `<ul>                
                    <li>Present current/valid ID. Applies to 55 years and older</li>
                    <li>10% off Individual ($39) or Household ($79) membership levels</li>
                </ul>`,
                items:[],
                billing_price: 35,
                billing_period: 0,
                users: 21
            },
        ];

        resolve(dummydata);
    });
}


const Memberships = {
    get //, create, update, delete, etc. ...
}
  
export default Memberships;