import React, { useState, useEffect } from 'react';
import {Container, Col, Row, Form, Table} from 'react-bootstrap';

import APIPermissions from '../../../../api/Permissions';

const Step3 = (props) => {
    const {change, values, errors} = props;
    const [features, setFeatures] = useState([]);
    const [selected, setSelected] = useState(values?.features || []);

    useEffect(() => {
        if (values?.features) setSelected(values.features);
    }, [values]);

    useEffect(() => {
        const _getFeatures = async () => {
            try {
                const res = await APIPermissions.Features.getAll();
                if (res.data){
                    // reorder by sort_order
                    //res.data.sort((a,b) => (a.sort_order > b.sort_order) ? 1 : ((b.sort_order > a.sort_order) ? -1 : 0));
                    setFeatures(res.data);
                }
            } catch (e) {
                console.error(e);
            }
        }
        _getFeatures();

        return () => {
            setFeatures([]);
        }
    }, []);


    const changeHandler = (e) => {
        const {value, checked} = e.target;
        const _selected = [...selected];

        let _value = {id: value, value: checked ? 1 : 0};
        const index = _selected.findIndex((item) => item.id === value);
        if (index > -1) _selected[index] = _value;
        else _selected.push(_value);

        setSelected(_selected);
        change({target: { name: "features", value: _selected}});
    }

    return (
        <Container fluid>
            <Row>
                <Col sm="12" lg="8">
                    <Table striped hover>
                        <thead>
                            <tr>
                                <th>Feature</th>
                            </tr>
                        </thead>
                        <tbody>
                            {features.map((feature, i) =>{
                                let _checked = feature.default_is_enabled === 1;
                                const _el = selected.find(a => +a.id === +feature.id);
                                if (_el?.value !== undefined) _checked = _el?.value;
                                return (
                                    <tr key={`feature-row-${i}`}>
                                        <td>
                                            <Form.Check 
                                                type="checkbox" 
                                                className="form-switch" 
                                                name={`feature-${feature.id}`} 
                                                defaultChecked={_checked}
                                                value={feature.id} 
                                                label={feature.name}
                                                onChange={changeHandler} 
                                            />
                                        </td>
                                    </tr>
                                );
                            })}
                        </tbody>

                    </Table>
                </Col>
            </Row>
        </Container>
    );
}

export default Step3;