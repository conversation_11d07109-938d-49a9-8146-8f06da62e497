import { useRef } from 'react';
import { Form, Container, Row, Col, Button } from 'react-bootstrap';
import { useDrag, useDrop } from 'react-dnd';

import styles from './ImageCard.module.scss';

export const ImageCard = ({ id, url, index, description, moveImage, removeImage, setPreview, setDescription }) => {
    const ref = useRef(null);

    const clickHandler = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setPreview(url);
    }
    
    const [{ handlerId }, drop] = useDrop({
        accept: "event-image",
        collect(monitor) {
            return {
                handlerId: monitor.getHandlerId(),
            }
        },
        hover(item, monitor) {
            if (!ref.current) return;

            const dragIndex = item.index;
            const hoverIndex = index;

            if (dragIndex === hoverIndex) return; // Don't replace items with themselves

            const hoverBoundingRect = ref.current?.getBoundingClientRect(); // Determine rectangle on screen
            const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2; // Get vertical middle            
            const clientOffset = monitor.getClientOffset(); // Determine mouse position            
            const hoverClientY = clientOffset.y - hoverBoundingRect.top; // Get pixels to the top
            
            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return; // Dragging downwards
            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return; // Dragging upwards
        
            moveImage(dragIndex, hoverIndex);
            item.index = hoverIndex;
        },
    });

    const [{ isDragging }, drag] = useDrag({
        type: "event-image",
        item: () => {
            return { id, url, index }
        },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    drag(drop(ref));
    
    return (
        <Container ref={ref} className={`${styles.card} ${isDragging?styles.drop:""}`} data-handler-id={handlerId} onClick={clickHandler}>
            <Row>
                <Col sm={12} lg="auto">
                    <img src={url} alt={`Event Img #${index+1}`} className={styles.image} />
                </Col>
                <Col>
                    <Form.Group controlId={`event-image-${index}-description`}>
                        <Form.Label>Description</Form.Label>
                        <Form.Control type="text" name="description" value={description || ""} onChange={(e)=>setDescription(e,id)} />
                    </Form.Group>
                </Col>
                <Col sm="auto" className="d-flex align-items-end">
                    <Button className="btn rounded my-0 me-0" variant="outline-light" onClick={e=>removeImage(e,id)} ><i className="far fa-trash-alt m-0"/></Button>
                </Col>
            </Row>
        </Container>
    );
}