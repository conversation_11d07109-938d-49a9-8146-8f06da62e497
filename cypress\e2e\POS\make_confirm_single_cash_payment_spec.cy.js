/*eslint-disable*/

let baseUrl = 'https://portal-qa.impactathleticsny.com/p/';
let staffUserName = Cypress.env('impact_staff_user');
let adminUserName = Cypress.env("impact_admin_user")
let password = Cypress.env('login_password');
let local;

describe('It will log in and visit a register as a staff user', {testIsolation: false, scrollBehavior: "center"}, ()=>{
    
    before(()=>{
        cy.intercept('GET', 'api/user/user/**').as('getUserUser');
        cy.intercept('GET', "/api/cms/my_theme*").as('getTheme');
        cy.intercept('POST', "/api/company_config").as('getConfig');
        cy.loginLocal(baseUrl, staffUserName, password);
            cy.wait('@getTheme')
            cy.wait('@getConfig')    
            cy.wait('@getUserUser').then(()=>{
                local = localStorage.getItem('user');
        })
    });

    beforeEach(()=>{
        cy.viewport(1920, 1080);
    });

    context('It will check a standard order with cash payment',()=>{
        let orderNumber;
        let transactionNumber;
        
        //because we're looking for an adding a product that exists on all environments, the amounts should always stay the same
        it('will navigate to the registers with the menu',()=>{
            cy.get('[data-cy="menu-item-Smack Shack (Impact-05)-38"]')
                .click({force: true});
            cy.url()
                .should("include", "/p/pos/1")
            cy.wait(1000)
        })
    
        it("will add items to the cart and verify that they're there",()=>{
            cy.get('[data-cy="product-card"]')
                .contains('Snacks')
                .click();
            cy.get('.item-name')
                .invoke('text')
                .should('include', 'Snacks')
            cy.get('[data-cy="preview-item"] > .item-price')
                .invoke('text')
                .should('include', '0.97')
            cy.get('[data-cy="order-number"]')
                .invoke('text').as('orderNumber')
            cy.get('@orderNumber').then((text)=>{
                orderNumber = text
            })
        });
        
        it('will make a single payment with cash',()=>{
            cy.intercept('POST', "/api/payment/process").as('processPayment');
            
            cy.log(orderNumber)
            cy.get('[data-cy="pos-button-checkout"]')
                .click();
            cy.get('[data-cy="details-add-to-cart"]')
                .should('have.attr', 'disabled');
            cy.get('[data-cy="payment-type-2"]')
                .click();
            cy.get('[data-cy="cash-payment-button-group"] > :nth-child(1)')
                .click();
            cy.get('[data-cy="cash-payment-button-group"] > :nth-child(1)')
                .click();
            cy.get('#memo')
                .click()
                .type("order created by the super cool CYPRESS")
            cy.get('[data-cy="details-add-to-cart"]')
                .should('not.have.attr', 'disabled');
            cy.get('[data-cy="details-add-to-cart"]')
                .click();
            cy.wait('@processPayment')

            cy.get('[data-cy="success-title"]')
                .invoke('text')
                .should('include', 'Payment Successful');
            cy.get('[data-cy="success-order-number"]')
                .invoke('text')
                .should('include', orderNumber);
            cy.get('[data-cy="success-transaction-number"]')
                .invoke('text').as('transactionNumber');
            cy.get('@transactionNumber').then((text)=>{
                let test = text.split("#")
                transactionNumber = test[1].trim()
            })
            cy.get('[data-cy="success-amount"]')
                .invoke('text')
                .should('include', "1.04");
            cy.get('[data-cy="success-cash"]')
                .invoke('text')
                .should('include', "2.00");
            cy.get('[data-cy="success-change"]')
                .invoke('text')
                .should('include', "0.96")
            cy.get('[data-cy="payment-cancel-button"]')
                .click();   
            cy.stubPrint();
        });

        it("will check the order page for matching data",()=>{
            cy.visit(`${baseUrl}order/${orderNumber}`)
            cy.wait(1000)
            cy.get('.section-title')
                .invoke('text')
                .should('include', orderNumber);
            cy.get('.transaction-hist-wrapper')
                .children()
                .eq(0)
                .invoke('text')
                .should('include', transactionNumber)
            cy.get('[data-cy="order-subtotal"]')
                .invoke('text')
                .should('include', "0.97")
            cy.get('[data-cy="order-tax-total"]')
                .invoke('text')
                .should('include', "0.07");
            cy.get('[data-cy="order-shipping-total"]')
                .invoke('text')
                .should('include', "0.00");
            cy.get('[data-cy="order-total-price"]')
                .invoke('text')
                .should('include', "1.04")
            cy.get('.order-other-details-wrapper > div > :nth-child(5) > :nth-child(2)')
                .invoke('text')
                .should('include', 'CYPRESS');
        })
        
        it("will check the transaction page",()=>{
            cy.log("transactions not loading properly for admin/staff users")
        })
    })
});