import React, {useState, useEffect, useCallback} from 'react';
import { Form, Table, OverlayTrigger, Tooltip, ToggleButtonGroup, ToggleButton } from 'react-bootstrap';

const LayoutMaxWidth = React.forwardRef((props, _) => {
    const {maxWidths, selection} = props;

    const [smUnit, setSmUnit] = useState("%");
    const [mdUnit, setMdUnit] = useState("%");
    const [lgUnit, setLgUnit] = useState("%");
    const [xlUnit, setXlUnit] = useState("%");
    const [smMaxWidth, setSmMaxWidth] = useState(null);
    const [mdMaxWidth, setMdMaxWidth] = useState(null);
    const [lgMaxWidth, setLgMaxWidth] = useState(null);
    const [xlMaxWidth, setXlMaxWidth] = useState(null);
    
    const clickHandler = useCallback((type, maxWidth, unit) => {
        let _prev="";
        switch(type){
            case "sm":
                _prev += mdMaxWidth ? `${_prev?";":""}md:${mdMaxWidth}${mdUnit}` : "";
                _prev += lgMaxWidth ? `${_prev?";":""}lg:${lgMaxWidth}${lgUnit}` : "";
                _prev += xlMaxWidth ? `${_prev?";":""}xl:${xlMaxWidth}${xlUnit}` : "";
                break;
            case "md":
                _prev += smMaxWidth ? `${_prev?";":""}sm:${smMaxWidth}${smUnit}` : "";
                _prev += lgMaxWidth ? `${_prev?";":""}lg:${lgMaxWidth}${lgUnit}` : "";
                _prev += xlMaxWidth ? `${_prev?";":""}xl:${xlMaxWidth}${xlUnit}` : "";
                break;
            case "lg":
                _prev += smMaxWidth ? `${_prev?";":""}sm:${smMaxWidth}${smUnit}` : "";
                _prev += mdMaxWidth ? `${_prev?";":""}md:${mdMaxWidth}${mdUnit}` : "";
                _prev += xlMaxWidth ? `${_prev?";":""}xl:${xlMaxWidth}${xlUnit}` : "";
                break;
            case "xl":
                _prev += smMaxWidth ? `${_prev?";":""}sm:${smMaxWidth}${smUnit}` : "";
                _prev += mdMaxWidth ? `${_prev?";":""}md:${mdMaxWidth}${mdUnit}` : "";
                _prev += lgMaxWidth ? `${_prev?";":""}lg:${lgMaxWidth}${lgUnit}` : "";
                break;
            default:
                break;
        }

        _prev += `${_prev?";":""}${type}:${maxWidth}${unit}`;
        selection(_prev);
    },[selection, smMaxWidth, mdMaxWidth, lgMaxWidth, xlMaxWidth, smUnit, mdUnit, lgUnit, xlUnit]);

    const setValueHandler = useCallback((type, state, value) => {
        switch(type){
            case "sm":
                if (state === "unit") setSmUnit(value);
                else if (state === "maxWidth") setSmMaxWidth(value);
                break;
            case "md":
                if (state === "unit") setMdUnit(value);
                else if (state === "maxWidth") setMdMaxWidth(value);
                break;
            case "lg":
                if (state === "unit") setLgUnit(value);
                else if (state === "maxWidth") setLgMaxWidth(value);
                break;
            case "xl":
                if (state === "unit") setXlUnit(value);
                else if (state === "maxWidth") setXlMaxWidth(value);
                break;
            default:
                break;
        }
    },[]);

    useEffect(() => {
        if (maxWidths){
            maxWidths?.split(";")?.forEach(maxWidth => {
                if (maxWidth){
                    const [_maxWidth, _value] = maxWidth.split(":");
                    if (_value){
                        const unit = _value.replace(/[0-9]/g, '');
                        const value = _value.replace(unit, '');
                        switch(_maxWidth){
                            case "sm":
                                setSmUnit(unit);
                                setSmMaxWidth(value);
                                break;
                            case "md":
                                setMdUnit(unit);
                                setMdMaxWidth(value);
                                break;
                            case "lg":
                                setLgUnit(unit);
                                setLgMaxWidth(value);
                                break;
                            case "xl":
                                setXlUnit(unit);
                                setXlMaxWidth(value);
                                break;
                            default:
                                break;
                        }
                    }
                }
            });
        }
    }, [maxWidths]);

    useEffect(() => {
        return () => {
            setSmUnit("%");
            setMdUnit("%");
            setLgUnit("%");
            setXlUnit("%");
            setSmMaxWidth(null);
            setMdMaxWidth(null);
            setLgMaxWidth(null);
            setXlMaxWidth(null);
        }
    }, []);

    return (
        <Table className="mb-0">
            <tbody>
                {["sm","md","lg","xl"].map((size, i) => {
                    let display_name, icon, default_unit_value, default_value;
                    switch(size){
                        case "sm":
                            icon = "far fa-mobile";
                            display_name = "Small";
                            default_unit_value = smUnit;
                            default_value = smMaxWidth === null ? (smUnit==="%" ? 95 : "") : smMaxWidth;
                            break;
                        case "md":
                            icon = "far fa-tablet";
                            display_name = "Medium";
                            default_unit_value = mdUnit;
                            default_value = mdMaxWidth === null ? (mdUnit==="%" ? 95 : "") : mdMaxWidth;
                            break;
                        case "lg":
                            icon = "far fa-laptop";
                            display_name = "Large";
                            default_unit_value = lgUnit;
                            default_value = lgMaxWidth === null ? (lgUnit==="%" ? 95 : "") : lgMaxWidth;
                            break;
                        case "xl":
                            icon = "far fa-desktop";
                            display_name = "Extra Large";
                            default_unit_value = xlUnit;
                            default_value = xlMaxWidth === null ? (xlUnit==="%" ? 95 : "") : xlMaxWidth;
                            break;
                        default:
                            break;
                    }

                    return (
                        <tr key={`${size}-${i}`}>
                            <td>
                                <OverlayTrigger placement="bottom" overlay={<Tooltip>Sets the border radius for each side of the element</Tooltip>}>
                                    <>
                                        <i className={`${icon} m-2`} />{display_name}
                                    </>
                                </OverlayTrigger>
                            </td>
                            <td>
                                <ToggleButtonGroup type="radio" size="sm" name={`${size}Unit`} value={default_unit_value} style={{marginLeft:"auto"}} className="w-100 justify-content-end" onChange={val=>{
                                    setValueHandler(size, "unit", val);
                                    clickHandler(size, default_value, val);
                                }}>
                                    <ToggleButton variant="link" size="sm" value="px">px</ToggleButton>
                                    <ToggleButton variant="link" size="sm" value="em">em</ToggleButton>
                                    <ToggleButton variant="link" size="sm" value="rem">rem</ToggleButton>
                                    <ToggleButton variant="link" size="sm" value="%">%</ToggleButton>
                                </ToggleButtonGroup>
                                {default_unit_value !== "%" &&
                                    <Form.Control type="number" placeholder="" min="0" defaultValue={default_value} style={{width:"150px", marginLeft:"auto"}} onBlur={(e)=>{
                                        e.preventDefault();
                                        e.stopPropagation();
                                        setValueHandler(size, "maxWidth", e.target.value);
                                        clickHandler(size, e.target.value, default_unit_value);
                                    }}/>
                                }
                                {default_unit_value === "%" &&
                                    <Form.Control type="range" className="form-range" step="1" min="0" max="100" value={default_value} onChange={(e)=>{
                                        setValueHandler(size, "maxWidth", e.target.value);
                                        clickHandler(size, e.target.value, default_unit_value)
                                    }}/>
                                }
                            </td>
                        </tr>
                    );
                })}
            </tbody>
        </Table>
    );
});

export default LayoutMaxWidth;