import React, {useState, useCallback} from 'react';

import { Typeahead } from './Typeahead';
import Products from '../../api/Products';


/**Basic async typeahead for searching service tokens.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Services back
*/
export const ServiceTokenTypeaheads = (props) => {

    const [loading, setLoading]=useState("Loading...");

    const makeRequest= useCallback(async()=>{
        let responseObj;
        try{
            let response = await Products.get({
                product_type_id: 9,
                for_service: 1,
            });
            responseObj={
                data: response.data.products || null,
                errors: response.errors || null
            }
            setLoading();
        }catch(ex){
            console.error(ex);
            setLoading("Currently No Tokens Available")
        }
        return responseObj;
    },[])
    
  return (
    <Typeahead
        {...props}
        id={"service-token-search"}
        makeRequest={makeRequest}
        async={false}
        paginated={false}
        placeholder={loading ? `${loading}`: "Search Service Tokens"}
        initialData={props.initialData}
    />
  )
}
