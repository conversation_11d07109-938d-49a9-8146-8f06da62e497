import React, { useCallback } from 'react'

import { Typeahead } from './Typeahead'
import Companies from '../../api/Companies';

export const CompanyTypeahead = ({async=false, paginated=false, multiple=false, ...props}) => {
    
    const getCompanies = useCallback(async()=>{
        let responseObj;
        try{
            let response = await Companies.getall({})
            responseObj={
                data: response.data || null,
                errors: response.errors || null
            }
        }catch(ex){
            console.error(ex)
            responseObj = {
                data: null,
                errors: ex
            }
        }
        return responseObj;
    },[])

    return (
        <div>
            <Typeahead
                {...props}
                id="company-typeahead"
                makeRequest={getCompanies}
                placeholder={props.placeholder || "Search Companies"}
                paginated={paginated}
                async={async}
                multiple={multiple}
            />
        </div>
    )
}
