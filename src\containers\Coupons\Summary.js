import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, Table } from 'react-bootstrap';
import { format } from 'date-fns';

import * as actions from '../../store/actions';

import './Coupon.scss';

const Summary = (props) => {
    const dispatch = useDispatch();

    const errors = useSelector(state => state.coupon.errors);
    const coupon = useSelector(state => state.coupon.current);
    const paramData = useSelector(state => state.coupon.param_init_data);
    const selectedConditions = useSelector(state => state.coupon.conditions);

    const [pagePartConditions, setPagePartConditions] = useState();

    useEffect(() => {
        if (selectedConditions.length > 0) {
            setPagePartConditions(
                <>
                    {selectedConditions.map(conditionName => {
                        let condition = coupon.params[conditionName];
                        let displayText = "";
                        if(Array.isArray(condition)) {
                            condition.forEach((item, index) => {
                                displayText += (index===0 ? item.name : ", " + item.name);
                            });
                        } else {
                            displayText = condition;
                        }
                        return (
                            <p key={`summary-row-${conditionName}`}>
                                <Form.Label>{paramData[conditionName].name}:</Form.Label><br/>
                                {displayText}
                            </p>
                        )
                    })}
                </>
            );
        } else {
            setPagePartConditions(
                <tr>
                    <td className="summary-param-name">None</td>
                    <td></td>
                </tr>
            );
        }
    },[selectedConditions,coupon,paramData]);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Summary</span>

                <div className="table">
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Name</Col>
                        <Col className="table-row">
                            <div>{coupon.name}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Description</Col>
                        <Col className="table-row">
                            <div>{coupon.description}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Applied Automatically</Col>
                        <Col className="table-row">
                            <div>{coupon.auto_apply ? "Applied Automatically" : "Requires Coupon Code: " + coupon.coupon_code}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Max number of uses</Col>
                        <Col className="table-row">
                            <div>{coupon.unlimited ? "Unlimited" : `Limited to ${coupon.max_uses} uses`}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Start Date</Col>
                        <Col className="table-row">
                            <div>{format(new Date(coupon.valid_from), "MM/dd/yyyy")}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">End Date</Col>
                        <Col className="table-row">
                            <div>{format(new Date(coupon.valid_until), "MM/dd/yyyy")}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Discount Type</Col>
                        <Col className="table-row">
                            <div>{coupon.discount_type ? `Fixed amount: $${coupon.discount_amount}` : `Percentage: ${coupon.discount_amount}%`}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Applies to</Col>
                        <Col className="table-row">
                            <div>{coupon.apply_to_all ? "Entire Order" : "Specific Items"}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Can Combine</Col>
                        <Col className="table-row">
                            <div>{coupon.combinable ? "Combinable with other discounts" : "Must be the only discount used on order"}</div>
                        </Col>
                    </Row>
                    <Row>
                        <Col sm="6" lg="3" className="table-header">Additional Conditions</Col>
                        <Col className="table-row">
                            <div>{pagePartConditions}</div>
                        </Col>
                    </Row>
                </div>
            </Col>
        </Row>
    );
}

export default Summary;