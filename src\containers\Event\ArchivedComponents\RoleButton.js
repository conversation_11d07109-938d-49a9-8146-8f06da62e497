import React, { useState, useEffect, useCallback } from 'react';
import Form from 'react-bootstrap/Form';
import ListGroup from 'react-bootstrap/ListGroup';
import Row from 'react-bootstrap/Row';
import Button from 'react-bootstrap/Button';
import Modal from 'react-bootstrap/Modal';
import { useDispatch } from 'react-redux';

import Events from '../../../api/Events';
import * as actions from '../../../store/actions';

import '../ManageUsers/ManageUsers.scss';

/**
* Creates a button for managing a user's property in an event. This will show a modal with a select to choose a new
* property for the user. Currently supports the role property.
* 
* @param {Object} user The user
* @param {[string]} options The array of roles in the event
* @param {string} btnIcon The icon for the button
*/
const EditUserPropButton = (props) => {

    const dispatch = useDispatch();

    const [user, setUser] = useState({});
    const [propertyName, setPropertyName] = useState("Property");
    const [options, setOptions] = useState([]);
    const [icon, setIcon] = useState("");
    const [eventId, setEventId] = useState(null);
    const [userProperty, setUserProperty] = useState(null);
    const [selectedOption, setSelectedOption] = useState(null);
    const [showModal, setShowModal] = useState(false);

    useEffect(() => {
        let propName = "";
        try {
            if (props.name) {
                setPropertyName(props.name);
                propName = props.name.toLowerCase() + " ";
            } else { console.error('No property name passed to user property button'); }
            if (props.user) {
                setUser(props.user);
                setUserProperty(props.user.event_role_id);
                setSelectedOption(props.user.event_role_id);
            }
            else { console.error(`No user passed to user ${propName}button`); }
            if (props.roles) { setOptions(props.roles); }
            else { console.error(`No event ${propName}array passed to user ${propName}button`); }
            if (props.btnIcon) { setIcon(props.btnIcon); }
            else { setIcon("fas fa-question-square"); }
            if (props.eventId) { setEventId(props.eventId); }
            else { console.error(`No event ID passed to user ${propName}button`); }
        } catch(e) {}
    },[props]);    
    
    const selectedOptionHandler = (e) => {setSelectedOption(e.target.value)};
    const closeModalHandler = () => {setShowModal(false)};
    const openModalHandler = () => {setShowModal(true)};
    
    /**
     * Calls the API to update the role of a group user.
     * 
     * @param {string} id The ID of the group user
     * @param {string} targetStatusID The ID of the desired group user status
     */
    const changeUserProperty = useCallback((id) => {

        Events.edit_role({"event_id": eventId, "user_id": id, "event_role_id": selectedOption})
        .then( response => {
            if (!response.errors) {
                setUserProperty(selectedOption);
                dispatch(actions.setNewEventUserRole(id, selectedOption));
                closeModalHandler();
            } else {
                throw(response.errors);
            }
        }).catch(e => console.error(e));

    },[dispatch, eventId, selectedOption]);

    return (
        <React.Fragment>
            <ListGroup.Item className="user-buttons" action href="#ChangeRole" onClick={()=>{
                setSelectedOption(userProperty);
                openModalHandler();
                }}>
                <i className={icon} data-tip={`Change ${propertyName}`}></i>
            </ListGroup.Item>
            <Modal show={showModal} onHide={closeModalHandler} size={"m"} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Change {propertyName}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Row className="role-modal-row">
                        <span style={{marginRight:'0.5rem'}}>
                            {`Change ${propertyName.toLowerCase()} of ${user.first_name} ${user.last_name} to`}
                        </span>
                        <Form>
                            <Form.Control required custom as="select" name="option_select" value={selectedOption || ""} onChange={selectedOptionHandler}>
                                {options.map((option, i) =><option key={'option_'+i} value={option.id}>{option.name}</option>)}
                            </Form.Control>
                        </Form>
                    </Row>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" action href="#ManageUsers" onClick={closeModalHandler}>
                        Cancel
                    </Button>
                    <Button variant="primary" action href="#ManageUsers" onClick={() => changeUserProperty(user.id)}>
                        OK
                    </Button>
                </Modal.Footer>
            </Modal>
        </React.Fragment>
    );
}

export default EditUserPropButton;