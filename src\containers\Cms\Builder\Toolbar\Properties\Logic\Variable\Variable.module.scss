$toolbar-width: 425px;

.variable-group{
    display: flex;
    flex-direction: column;
    border: 2px solid #212121;
    padding: 0.5rem;
    padding-bottom:2rem;
    margin: 0;
    margin-bottom: 0.25rem;
    border-radius: 0;
    justify-content: center;
}

.variable-container{
    transition: all 0.2s ease-in-out;

    pre{
        display:flex;
        flex-direction: column;
        margin:0;
        margin-bottom:0.25rem;
        margin-left: 1.1rem;
        height: 4rem;
        border: 2px solid transparent;
        top: 0;
        position: relative;
        justify-content: center;

        input, select {
            font-family: var(--bs-font-monospace);
            font-size: 0.75rem;
            font-weight: 400;
            line-height: 1rem;
            color: #000;
            padding: 0.1rem;
            margin: 0 5px;
            border: 1px solid #eee;
            border-radius: 0;
            background-color: #fff;
            text-align: left;
            box-shadow: none;
            text-transform: none;
            display: inline-block;
        }

        input {
            width: 100px;
        }        
    }

    &:hover{
        .variable-overlay{
            display: flex;
        }
        pre {
            background-color: #fafafa;
            border-color:#662d91;
        }
    }
}
