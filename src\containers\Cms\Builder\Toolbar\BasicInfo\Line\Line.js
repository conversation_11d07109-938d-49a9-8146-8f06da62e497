import React, { useRef, useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Form, Tooltip, OverlayTrigger } from 'react-bootstrap';

import GroupTypeahead from '../../../../../../components/Typeahead/GroupTypeahead';
import RoleTypeahead from '../../../../../../components/Typeahead/RoleTypeahead';

import AI from '../../../../Components/Properties/AI';
import { getPageById } from '../../../../../../utils/cms';
import APIThemes from '../../../../../../api/Themes';

export const Line = (props) => {
    const loaded = useRef(false);
    const ref = useRef();
    const cmsSelector = useSelector(state => state.cms);
    const [options, setOptions] = useState([]);
    const [pageProps, setPageProps] = useState();
    const [themes, setThemes] = useState([]);

    useEffect(() => {
        const _getPageByType=async () => {
            try {
                const res=await getPageById({website_id:cmsSelector.currentWebsite,page_type_id:props.type_id});
                if (res) setOptions(res);
            } catch (e){
                console.error(e);
            }
        }
        if (props.type_id) _getPageByType();
    }, [props.type_id, cmsSelector.currentWebsite]);

    useEffect(() => {
        const _getThemes=async () => {
            try {
                const res=await APIThemes.get({website_id:cmsSelector.currentWebsite});
                if (res.data) setThemes(res.data);
            } catch (e){
                console.error(e);
            }
        }
        if (props.field_name==="themes") _getThemes();
    }, [props.field_name, cmsSelector.currentWebsite]);


    useEffect(() => {
        setPageProps(cmsSelector.currentPageProps);
    },[cmsSelector.currentPageProps]);

    useEffect(() => {

        loaded.current = true;

        return () => {
            setOptions([]);
            setPageProps(null);
            setThemes([]);
        }
    }, []);

    const themeChangeHandler=(e) => {
        const {name, value, checked} = e.target;
        let newThemes = pageProps?.themes || [];
        if (!Array.isArray(newThemes)) newThemes = [newThemes];
        if (checked && !newThemes.includes(+value)) newThemes.push(+value); // adds checked ones that aren't already there
        else if (!checked) newThemes = newThemes.filter(theme => theme!==+value); // removes unchecked ones
        props.change({target: {name, value: newThemes}});
    }

    const aiSelectionHandler = (v) => {
        if (v?.text && ref.current){
            ref.current.value = v.text;
            v.text="";
        }
        return false;
    }

    const typeaheadSelectionHandler = (v, typeahead) => {
        let _val = [];
        if (Array.isArray(v) && v.length>0 && loaded.current){
            _val= v.map(a=>a?.id || null);
            //loaded.current = false;
        }
        props.change({target: {name: typeahead, value: _val}}, typeahead);
    }

    if (props?.condition && !cmsSelector?.currentPageProps?.[props.condition.name]) return null;

    return (
        <tr>
            <td>
                <OverlayTrigger placement="bottom" overlay={<Tooltip>{props.tooltip || props.name}</Tooltip>}>
                    <span>{props.name}</span>
                </OverlayTrigger>
            </td>
            <td>
                {props.field_name==="themes" &&
                    themes.map((theme, i) => <Form.Check key={`theme-${i}`} type="checkbox" name="themes" label={theme.name} value={theme.id} onChange={themeChangeHandler} defaultChecked={pageProps?.themes.includes(theme.id)} />)
                }
                {props.type==="textarea" &&
                    <>
                        <Form.Control ref={ref} as="textarea" rows={3} name={props.field_name} placeholder={props.placeholder} defaultValue={pageProps?.[props.field_name] || ""} onBlur={props.change} />
                        {props.field_name==="description" && 
                            <AI text={pageProps?.[props.field_name] || ""} type="meta_description" inline selection={aiSelectionHandler} />
                        }
                    </>
                }
                {props.type==="select" && 
                    <Form.Control as="select" custom onChange={props.change} name={props.field_name} value={pageProps?.[props.field_name]}>
                        <option value="0">None</option>
                        {options.map((option, i) => <option key={`select-${props.name}-${i}`} value={`${option.id}`}>{option.title}</option>)}
                    </Form.Control>
                }
                {props.type==="checkbox" &&
                    <Form.Check type="checkbox" name={props.field_name} checked={+pageProps?.[props.field_name]===1} value="1" onChange={props.change} className="form-switch" style={{paddingLeft:0}} />
                }
                {(!props.type || props.type==="text") &&
                    <>
                        <Form.Control ref={ref} type="text" name={props.field_name} placeholder={props.placeholder} defaultValue={pageProps?.[props.field_name] || ""} onBlur={props.change} />
                        {props.field_name==="keywords" && 
                            <AI text={pageProps?.[props.field_name] || ""} type="meta_keywords" inline selection={aiSelectionHandler} />
                        }
                    </>
                }
                {props.type==="typeahead" && props.field_name === "restricted_groups" &&
                    <GroupTypeahead 
                        multiple={true}
                        async={false}
                        initialDataIds={pageProps?.restricted_groups || null}
                        passSelection={s=>typeaheadSelectionHandler(s, props.field_name)}
                    />
                }
                {props.type==="typeahead" && props.field_name === "restricted_roles" &&
                    <RoleTypeahead 
                        multiple={true} 
                        async={false}
                        initialDataIds={pageProps?.restricted_roles || null}
                        passSelection={s=>typeaheadSelectionHandler(s, props.field_name)}
                    />
                }
            </td>
        </tr>
    );
}
