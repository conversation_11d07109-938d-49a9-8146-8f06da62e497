import React from 'react';

import AI from '../../../../../../../Components/Properties/AI';
import MediaManager from '../../../../../../../Components/Properties/MediaManager';

export const Extras = (props) => {
    if (props?.ai === true){
        return (
            <AI {...props} selection={(v) => {
                if (v && props.elementRef.current){
                    props.elementRef.current.value = v;
                    props.elementRef.current.focus();
                    v="";
                }
                return false;
            }}/>
        );
    }
    if (props?.media === true){
        return (
            <MediaManager {...props} isModal 
                selection={v => {
                    if (v && props.elementRef.current){
                        if (props.mediaMultiSelect) props.elementRef.current.value = JSON.stringify(v);
                        else {
                            props.elementRef.current.value = v;
                            props.elementRef.current.focus();
                        }
                        v="";
                    }
                    return false;
                }}
                close={v => {
                    if (v && props.save) {
                        props.save({
                            preventDefault() {},
                            stopPropagation() {},
                            target: { value: v },
                        }, v, {id: props.id, name: props.name}, null, null);
                        v="";
                    }
                    return false;
                }}
            />
        );
    }
    return null;
}