import React, { useState, useEffect, useMemo, Suspense } from 'react';
import Card from 'react-bootstrap/Card';
import { differenceInHours, differenceInMinutes, getHours, setHours, setMinutes } from 'date-fns';

import './HourlyTimeGrid.scss';

import Locations from '../../../api/Locations';
import { ErrorBoundary } from 'react-error-boundary';
import { ContactSupportOutlined } from '@material-ui/icons';
import { useCallback } from 'react';

// load events into the time table
const LocationRow = ({ displayStartTime, displayEndTime, date, location, locationNames, onSelect, defaultSelected=null, selectedEvents=[] }) => {

    const [locationData, setLocationData] = useState([]);
    const [divArray, setDivArray] = useState([]);
    const [pagePart, setPagePart] = useState();
    const [dateStart, setDateStart] = useState(date.split("T")[0]);

    const day_start_time = displayStartTime || 6; // time when the day starts in 24hr format
    const day_end_time = displayEndTime || 23; // time when the day ends in 24hr format
    const curr_date= useMemo(()=>{ return new Date(date) || new Date()},[date]);

    //let biggest_end=0;
    let current_availability_end = new Date(curr_date).setHours(day_end_time,0,0,0);

    useEffect(() => {
        setDateStart(date.split("T")[0]);  // save the date part in state for use in the cell highlighter
    }, [date]);

    useEffect(() => {
        let unmounted = false;
        // get the structured version of location
        let date_start = dateStart + "T00:00:00-0500";
        let date_end = date.split("T")[0] + "T23:59:59-0500";
        if(!unmounted) {
            Locations.getWithAvailabilities({id: location, start_datetime: date_start, end_datetime: date_end, include_sublocations: 0, include_conflicting_locations: 1})
            .then(response=> {
                setLocationData(response.data);
            }).catch(e => console.error(e));    
        }
        return () => {
            unmounted = true;
        } 
    },[date, dateStart, location]);

    // create all of the blocks
    useEffect(() => {
        let biggest_end=0;
        let availability=[];
        let time_cell=[];    
        if(locationData?.general_availabilities) {
            let current_day_availabilities = locationData.general_availabilities?.filter( row => row.day_of_week===curr_date.getDay() + 1);

            // loop backwards through the availabilities array so that we can keep track of where the current free block space ends
            for (let i=current_day_availabilities.length-1; i>=0; i--) {
                const slot = current_day_availabilities[i];

                const start_time=slot.start_time.split(":");
                const end_time=slot.end_time.split(":");

                // if the slot start time and end time is within the displayed hours
                if(start_time[0]>=day_start_time && end_time[0]<day_end_time) {

                    let start_date = curr_date;
                    start_date = setHours(start_date, parseInt(start_time[0]));
                    start_date = setMinutes(start_date, parseInt(start_time[1]));
                    let end_date = new Date(curr_date+(end_time[0]<start_time[0]?1:0));
                    end_date = setHours(end_date, parseInt(end_time[0]))
                    end_date = setMinutes(end_date, parseInt(end_time[1]));
                    let hours = differenceInHours(end_date,start_date);
                    const minutes = differenceInMinutes(end_date,start_date);

                    // get availability and user selection, to set colors
                    let slot_class="";
                    let popup_text=null;
                    let _availability_end=current_availability_end;
                    // check the database for already occuring events to show as disabled
                    locationData.events.forEach( event => {
                        if (start_date >= new Date(event.start_datetime) && start_date <= new Date(event.end_datetime)) {
                            slot_class="reserved";
                            _availability_end=new Date(event.start_datetime);
                        }
                    })
                    // check the defaultSelected events passed in to component to show as disabled
                    selectedEvents.forEach( event => {
                        if (start_date >= event.start_datetime && start_date <= event.end_datetime) {
                            slot_class="reserved-wizard";
                            popup_text=`Created Event: ${event.name}`;
                            _availability_end=new Date(event.start_datetime);
                        }
                    })
                    // check the selectedEvents to highlight
                    if (!!defaultSelected && parseInt(defaultSelected.location_id)===parseInt(location)
                        && start_date >= defaultSelected.start_datetime && start_date <= defaultSelected.end_datetime) {
                        // use this as a marker for later
                        slot_class="selected";
                    }

                    // start_date = new Date(start_date);
                    if (start_date instanceof Date && Object.prototype.toString.call(start_date) === "[object Date]") {
                        let hour = getHours(start_date);
                        if (hour>=0 && !Array.isArray(availability[hour])) {
                            availability[hour] = [];
                        }
                        // console.log(slot_class);
                        availability[hour].unshift(    // push but to front of array because we are going through availabilities backwards
                            {
                                start_time:start_date,
                                end_time:end_date,
                                hours: hours,
                                minutes: minutes,
                                class_name: slot_class,
                                availability_end: _availability_end,
                                location_id: locationData.id,
                                popup_text: popup_text
                            }
                        );
                    } else {
                        throw new Error(start_date);
                    }

                    if (end_date>biggest_end) {
                        biggest_end=end_date;
                    }
                }
            };

            // if previous hours have no slots, create placeholder empty cells
            for (let i=0;i<+Object.keys(availability)?.[0]-day_start_time;i++){
                if (!Array.isArray(availability[i])) {
                    time_cell.push(<div key={`con-phb${i}`} className="empty" />)
                }
            }

            let defaultSlots = [];

            // make cells for available hours
            availability.forEach((slots,k) => {
                let cell_style={};
                let slot_style={};
                let biggest_diff=0;
                let slot_divs=[];
                let pos=0;

                // individual slot whithin an hour range
                slots.forEach((slot,l)=>{
                    if (slot.minutes<=14){
                        slot_style={flexBasis:"25%"};
                    } else if (slot.minutes<=29){
                        slot_style={flexBasis:"50%"};
                    } else if (slot.minutes<=44){
                        slot_style={flexBasis:"75%"};
                    }
                    if (slot.minutes>biggest_diff){
                        let cell_start = getHours(slot.start_time) - day_start_time;
                        if (cell_start<0) cell_start+=24;
                        cell_start+=2;
                        pos=cell_start;

                        cell_style={gridColumn:cell_start+"/"+(cell_start+slot.hours)};
                        biggest_diff=slot.minutes;
                    }
                    slot.identifier = `cell-${location}-${(k*4)+l}`;
                    slot_style.padding = "0";
                    slot_divs.push(
                        <div
                            style={slot_style}
                            id={`${slot.identifier}`}
                            className={`${slot.class_name}`}
                            key={`innkl-${k}-${l}`}
                            onMouseDown={slot.class_name !== "reserved" || slot.class_name !== "reserved-wizard" ? (e)=>slotHandlerDown(e, slot) : null}
                            onMouseUp={slot.class_name !== "reserved" || slot.class_name !== "reserved-wizard" ? (e)=>slotHandlerUp(e, slot) : null}
                            onMouseOver={slot.class_name !== "reserved" || slot.class_name !== "reserved-wizard" ? (e)=>slotHandlerOver(e, slot) : null}
                        ></div>
                    );
                    if (slot.class_name === "selected") {
                        defaultSlots.push(slot.identifier);
                    }
                });

                // hour range with its slots
                time_cell[pos]=
                    <div className="time-grid" key={`conk${k}`} style={cell_style}>
                        {slot_divs}
                    </div>
                ;
            });

            // fills the remaining available grid space
            if(availability.length > 0) {
                let end_hour = getHours(biggest_end) + 1;
                const diff = day_end_time - end_hour;
                if (diff > 0) {
                    const cell_start = end_hour-day_start_time+2;
                    time_cell.push(
                        <div
                            key={`empty-${locationData.id}-${cell_start}`}
                            style={{gridColumn:cell_start+"/"+(cell_start+diff)}}
                            className="empty"
                        />
                    );
                }
            }
        }
        setDivArray(time_cell);
    },[locationData, defaultSelected,current_availability_end,selectedEvents,day_start_time,day_end_time,curr_date,location]);

    useEffect(() => {
        setPagePart(
            <Suspense fallback={             
                <div>Loading...</div>
            }>
                <React.Fragment key={`frgi${locationData.id}`}>
                    <div key={`con-oi${locationData.id}`} style={{gridColumn: "1/1"}}>
                        <div className="location-name table-header">{locationNames[location]}</div>
                    </div>
                    {divArray}
                </React.Fragment>
            </Suspense>
        );
    },[locationData,divArray,locationNames,location]);

    const [isMouseUp, setIsMouseUp] = useState(false);
    const [startSlot, setStartSlot] = useState(null);
    const [endSlot, setEndSlot] = useState(null);
    const [hoverSlot, setHoverSlot] = useState(null);
    const [tempHoverSlot, setTempHoverSlot] = useState(null);
    const [selectedSlots, setSelectedSlots] = useState([]);
    const [prevSelectedSlots, setPrevSelectedSlots] = useState([]);
    const [selectedClassName, setSelectedClassName] = useState("selected");

    const slotHandlerDown = (event, slot) => {
        event.preventDefault();
        // erase all previous highlights
        setIsMouseUp(false);
        setStartSlot(slot);
        setSelectedSlots([slot.identifier]);
    }

    const slotHandlerUp = (event, slot) => {
        event.preventDefault();
        setEndSlot(slot);
    }

    function slotHandlerOver (event, slot) {
        event.preventDefault();
        setHoverSlot(slot);
    }

    const handleMouseUp = useCallback(() => {
        if (!isMouseUp) setIsMouseUp(true);
    }, [isMouseUp]);
    
    // make sure this effect is canceled if mouseUp happens elsewhere
    window.addEventListener('mouseup', handleMouseUp);

    useEffect(() => {
        return () => {
            setIsMouseUp(false);
            setStartSlot(null);
            setEndSlot(null);
            setHoverSlot(null);
            setTempHoverSlot(null);
            setSelectedSlots([]);
            setPrevSelectedSlots([]);
            setSelectedClassName("selected");
            window.removeEventListener('mouseup', handleMouseUp);
        }
    //Having the 'handleMouseUp' in the dependency array causes an invisible infinite loop
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[]);

    // when the date changes (but not if time changes)
    useEffect(() => {
        setSelectedSlots([]);
        setPrevSelectedSlots([]);
    },[dateStart]);

    // what to do when click and drag is happening
    useEffect(() => {
        if (startSlot && hoverSlot && !isMouseUp) {
                let startIdentifier = startSlot.identifier.split("-");
                let startId = startIdentifier[2];
                let hoverId = hoverSlot.identifier.split("-")[2];
                let lowerId = startId>hoverId ? hoverId : startId;
                let higherId = startId>hoverId ? startId : hoverId;
                let selected = [];
                let className = "selected";
                for (let id=lowerId; id<=higherId; id++) {
                    let cellId = startIdentifier[0]+"-"+startIdentifier[1]+"-"+id;
                    let classList = document.getElementById(cellId).classList;
                    if (classList.contains("reserved") || classList.contains("reserved-wizard")) {
                        className = "select-error";
                    }
                    selected.push(cellId);
                }
                setSelectedClassName(className);
                setSelectedSlots([...selected]);
                setTempHoverSlot(hoverSlot);
        }
    },[startSlot,hoverSlot,isMouseUp]);

    // highlight selected cells during click and drag
    useEffect(() => {
        // set all cells to not selected
        Array.from(document.getElementsByClassName("selected")).forEach(element => element.classList.remove("selected"));
        Array.from(document.getElementsByClassName("select-error")).forEach(element => element.classList.remove("select-error"));
        // loop through the list and highlight them
        if(Array.isArray(selectedSlots) && selectedSlots.length>0) {
            selectedSlots.forEach(id => {
                let cell = document.getElementById(id);
                if (cell) cell.classList.add(selectedClassName);
            });
        }
    },[selectedSlots, selectedClassName]);

    useEffect(() => {
        if(isMouseUp) {
            // if start and end slots are valid
            let lastSlot = endSlot || tempHoverSlot || null;
            if(startSlot && lastSlot && startSlot.location_id===lastSlot.location_id) {
                // check if they are within the same availability block
                let first = startSlot;
                let second = lastSlot;
                if(new Date(startSlot.start_time)>new Date(lastSlot.start_time)) {
                    first = lastSlot;
                    second = startSlot;
                }
                // is end time within the listed availability block?
                let start_time = new Date(first.start_time);
                let end_time = new Date(second.end_time);
                if (new Date(first.availability_end)>end_time) {
                    onSelect(startSlot.location_id, start_time, end_time); 
                    setPrevSelectedSlots(selectedSlots);
                } else {
                    // if this is not a valid selection, reset highlight to last saved selection
                    setSelectedClassName("selected");
                    setSelectedSlots(prevSelectedSlots);
                }
            }
            setStartSlot(null);
            setHoverSlot(null);
            setTempHoverSlot(null);
            setEndSlot(null);
        }
    },[startSlot, endSlot, tempHoverSlot, isMouseUp, selectedSlots, prevSelectedSlots, onSelect]);

    return (
        <>
            {pagePart}
        </>    
    );
}

// create a time array
const timeArray = (date, start_time, end_time) =>{  
    // currently start_time and end_time must be an integeter hour in the same 24-hour day

    if (!date) date=new Date();
    let tasks=[];

    let timeArray = {
        header: {
            start_time: start_time,
            end_time: end_time
        },
        times:[],
        tasks:[]
    };

    const d = new Date(date);    

    //let dayIndex="";
    let j=0;
    for (let i=0+start_time;i<end_time;i++){
        if (i>23) j=i-24;
        else j=i;
        
        if (!timeArray.times[i]){
            timeArray.tasks[i]=[];
            timeArray.times[i]={
                date:new Date(d),
                hour24:new Date(d.setHours(j)).toLocaleTimeString("en-US", { hour12: false, hour: '2-digit' }),
                hour:new Date(d.setHours(j)).toLocaleTimeString("en-US", { hour12: true, hour: '2-digit' }),
            }
        }
    }

    return timeArray;
}

// create a daily view scheduler
const HourlyTimeGrid = ({ locations, locationNames, date, selectedEvents=[], onSelect=()=>{}, displayStartTime=6, displayEndTime=21, defaultSelected=null }) => {
    // currently start_time and end_time must be an integeter hour in the same 24-hour day
    // display will end at the end time listed, for example if it says 11, the chart will end at 11:00, not including the 11-12 events.

    const [calendar,setCalendar] = useState();
    const [pagePart, setPagePart] = useState();

	useEffect(() => {
        setCalendar(timeArray(new Date(date), displayStartTime, displayEndTime));
	}, [date, displayEndTime, displayStartTime]);

    useEffect(() => {
        if (locations.length>0) {
            setPagePart(
                <div className="timeline-container table">
                    <div className="timeline" id="timeline" style={{gridTemplateColumns: `auto repeat(${displayEndTime-displayStartTime}, minmax(50px, 1fr))`}}>
                        <div className="topleft table-header"></div>
                        {calendar && calendar.times.map((time,i) => (
                            <div className="hour table-header" key={`spdaysi${i}`}>
                                {time.hour}
                            </div>
                        ))}

                        {locations.map((location_id,i)=>(
                            <LocationRow
                                key={`location-row-${location_id}`}
                                date={date}
                                location={location_id}
                                locationNames={locationNames}
                                selectedEvents={selectedEvents.filter(event => event.location_id===parseInt(location_id))} // events to be blocked out on the calendar from the current wizard
                                displayStartTime={displayStartTime}
                                displayEndTime={displayEndTime}
                                onSelect={onSelect}
                                defaultSelected={defaultSelected}
                            />
                        ))}
                    </div>
                </div>
            );
        }
    },[locations, locationNames, calendar, defaultSelected, date, displayEndTime, displayStartTime, onSelect, selectedEvents]);

    return (
        <>
            {pagePart}
        </>
    );
}

export default HourlyTimeGrid;