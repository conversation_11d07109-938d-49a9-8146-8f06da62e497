// main theme

@import url('https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap');

$fonts: (
    'primary': 'Ubuntu:300,400,500,700',
    'secondary': 'Ubuntu:300,400,500,700'
);
$company-name: "Elevate Basketball Club";
$background-image: "https://siteboss.s3.amazonaws.com/themes/4/elevate-2.mp4";
$background-image-filter: blur(5px) grayscale(0.4);
$themeS3Bucket: "https://siteboss.s3.amazonaws.com/themes/4/";

$logoNoText: $themeS3Bucket + 'logo-short.svg';
$logoHeader: $themeS3Bucket + 'logo.svg';
$logo: $themeS3Bucket + 'logo.svg';
$backgroundEvents: $themeS3Bucket + 'events.png';

:export {
    backgroundImage: $background-image;
    companyName: $company-name;
    logo: $logo;
    logoHeader: $logoHeader;
    logoNoText: $logoNoText;
    backgroundEvents: $backgroundEvents;
}

$primary-font-family: 'Ubuntu', sans-serif;
$primary-font-size: 1rem;
$primary-line-height: 1.25rem;
$primary-font-color: #000;
$primary-inverse-color: #fff;
$primary-font-weight: 400;
$secondary-font-family: 'Ubuntu', sans-serif;
$secondary-font-size: 1rem;
$secondary-line-height: 1.25rem;
$secondary-font-color: #000;
$secondary-inverse-color: #fff;
$secondary-font-weight: 500;

$bold-font-weight: 700;
$light-font-weight: 300;
$regular-font-weight: 400;
$big-font-size: 1.25rem;
$big-font-line-height: 1.5rem;
$small-font-size: 0.7rem;
$small-font-line-height: 0.8rem;

$primary-color: #C4942F;
$primary-hover-color: #9C7625;
$primary-light-color: #E7D4AB;
$secondary-color: #C4942F;
$secondary-hover-color: #9C7625;
$secondary-light-color: #E7D4AB;
$tertiary-color: #f44336;
$tertiary-hover-color: #d32f2f;
$tertiary-light-color: #ef9a9a;
$neutral-background-color: #D3D6D7;
$neutral-color: $primary-font-color;
$neutral-hover-background-color: #A8ADAF;
$neutral-hover-color: $primary-font-color;

$shadow-color:#000;
$shadow-elevation-0: none;
$shadow-elevation-1: rgba($shadow-color, .2) 0px 2px 1px -1px, rgba($shadow-color, .14) 0px 1px 1px 0px, rgba($shadow-color, .12) 0px 1px 3px 0px;
$shadow-elevation-2: rgba($shadow-color, .2) 0px 3px 1px -2px, rgba($shadow-color, .14) 0px 2px 2px 0px, rgba($shadow-color, .12) 0px 1px 5px 0px;
$shadow-elevation-3: rgba($shadow-color, .2) 0px 3px 3px -2px, rgba($shadow-color, .14) 0px 3px 4px 0px, rgba($shadow-color, .12) 0px 1px 8px 0px;

$error-color: #f44336;
$error-text-color: #fff;
$success-color: #4caf50;
$warning-color: #ffe082;
$disabled-color: #626b6f;

$background-color: #263238;

$divider-color: #D3D6D7;
$divider-border: 1px solid $divider-color;
$divider-margin: 0.5rem 0 1rem 0;

$scrollbar-color: #626b6f;
$scrollbar-background-color: #263238;
$scrollbar-width: 10px;
$scrollbar-border-radius: 0px;

$main-padding: 2rem;
$content-font-size: 1rem;
$content-padding: 0 $main-padding $main-padding $main-padding;


$small-main-padding: 1rem;
$small-content-padding: 0 $small-main-padding $small-main-padding $small-main-padding;

$header-background-color:#131516;
$header-font-color: #eee;
$header-hover-color: #fff;
$header-font-size: .85rem;
$header-font-weight: 400;
$header-font-family: $primary-font-family;
$header-height: 3rem;
$header-padding: 0 1rem;
$header-text-decoration: none;

$logo-height: 40px;
$logo-width: 40px;
$logo-url: url($logo);
$logo-background-position: center center;
$logo-background-repeat: no-repeat;
$logo-background-size: contain;
$logo-filter: none;

$link-color: $primary-color;
$link-visited-color: $primary-color;
$link-hover-color: $primary-hover-color;
$link-active-color: $primary-color;
$link-font-size: 0.85rem;
$link-font-weight: 500;
$link-text-decoration: none;
$link-hover-text-decoration: underline;
$link-padding:0;
$link-margin:0;

$button-font-family: $secondary-font-family;
$button-font-size: 0.8rem;
$button-line-height:1rem;
$button-font-weight: 500;
$button-border-radius: 5px;
$button-border: 0px;
$button-border-color: unset;
$button-padding: .5rem 1.5rem;
$button-margin: 0 0.5rem 0.5rem 0;
$button-background-color: $primary-color;
$button-color: #eee;
$button-shadow: $shadow-elevation-1;
$button-hover-background-color: $primary-hover-color;
$button-hover-color: #fff;
$button-active-background-color: $primary-hover-color;
$button-active-color: #fff;
$button-active-filter: brightness(0.9);
$button-disabled-background-color: $disabled-color;
$button-disabled-color: $primary-font-color;
$button-border-width: 2px;
$button-text-transform: uppercase;
$button-text-shadow: none;
$button-transparent: rgba($background-color, 0.9);
$button-small-font-size: 0.75rem;
$button-small-padding: .25rem .75rem;
$button-round-border-radius: 50%;
$button-round-padding: 0.5rem;
$button-transparent-background-color: rgba($header-background-color, 0.9);
$button-transparent-color: $header-font-color;
$button-transparent-hover-color: $header-hover-color;

$highlight-color: $primary-color;
$highlight-hover-color: $primary-hover-color;
$highlight-background-color: transparent;
$highlight-background-hover-color: transparent;
$highlight-font-weight: 500;

$breadcrumb-background-color: transparent;
$breadcrumb-color: $primary-light-color;
$breadcrumb-default-color: #eee;
$breadcrumb-font-size: .85rem;
$breadcrumb-text-decoration: none;
$breadcrumb-hover-color: #fff;
$breadcrumb-hover-background-color: transparent;
$breadcrumb-padding: 1rem 0;
$breadcrumb-margin: 0;
$breadcrumb-border-radius: 0;

$chip-font-family: $secondary-font-family;
$chip-background-color: #293134;
$chip-padding:0.3rem 0.5rem;
$chip-margin: 0 .5rem .5rem 0;
$chip-font-size: 0.8rem;
$chip-font-weight: 400;
$chip-line-height: 1rem;
$chip-border-radius: 1rem;
$chip-border: 0;
$chip-color: #eee;
$chip-hover-background-color: #131516;
$chip-hover-color: #eee;

$badge-font-family: $primary-font-family;
$badge-background-color: $primary-color;
$badge-color: $primary-inverse-color;
$badge-padding: .2rem .35rem;
$badge-margin: 0 .3rem 0 0;
$badge-font-size: 0.7rem;
$badge-font-weight: 400;
$badge-line-height: 1rem;
$badge-border-radius: 50%;
$badge-border: 0;
$badge-hover-background-color: $primary-hover-color;
$badge-hover-color: $primary-inverse-color;
$badge-size: 1rem;

$modal-border: 0;
$modal-border-radius: 5px;
$modal-header-font-family: $secondary-font-family;
$modal-header-font-size: 1rem;
$modal-header-border: 0;
$modal-header-background-color: transparent;
$modal-header-padding: 0;
$modal-header-margin: 0rem 0;
$modal-header-content: "";
$modal-body-font-family: $primary-font-family;
$modal-body-font-size: 0.85rem;
$modal-body-line-height: 1rem;
$modal-body-shadow: $shadow-elevation-3;
$modal-body-border: 0;
$modal-body-padding: $main-padding;
$modal-body-background-color: #f5f5f5;
$modal-body-border-radius: 5px;
$modal-background-color: transparent;
$modal-close-button-border: 1px solid #212121;
$modal-close-button-border-radius: $button-round-border-radius;
$modal-close-button-background-color: $header-background-color;
$modal-close-button-color: $button-transparent-color;
$modal-close-button-padding: $button-round-padding;
$modal-close-button-margin: 1rem 0 1rem auto;
$modal-close-button-opacity: 1;
$modal-close-button-size: 2.5rem;
$modal-close-button-shadow: $shadow-elevation-1;
$modal-close-button-hover-background-color: $primary-hover-color;
$modal-close-button-hover-color: $button-transparent-hover-color;
$modal-close-button-hover-opacity: 1;

$profile-image-border-radius:50%;
$profile-image-small-size: 1.5rem;
$profile-image-size: 3.5rem;
$profile-image-big-size: 120px;
$profile-image-huge-size: 20rem;
$profile-image-padding: 0.5rem;
$profile-image-margin: 0 1rem 1rem 0;

$card-background-color: rgba(255,255,255,.95);
$card-color:#000;
$card-border-radius: 8px;
$card-border: 0;
$card-padding: 2rem;
$card-margin: 0 1rem 1rem 0;
$card-font-size: 0.85rem;

$card-title-font-family: $secondary-font-family;
$card-title-font-size: 1.25rem;
$card-title-font-weight: 700;
$card-title-line-height: 1.5rem;
$card-title-margin: 0;
$card-title-padding: 0.5rem 0;
$card-title-color: #000;

$card-subtitle-font-family: $secondary-font-family;
$card-subtitle-font-size: 0.85rem;
$card-subtitle-font-weight: 700;
$card-subtitle-line-height: 1rem;
$card-subtitle-margin: 0;
$card-subtitle-padding: 0 0 0.5rem 0;
$card-subtitle-color: #000;

$card-footer-background-color: transparent;
$card-footer-border: 1px solid $divider-color;
$card-footer-border-radius: 0;
$card-footer-padding: default;
$card-footer-margin: default;

$profile-card-background-color: $card-background-color;
$profile-card-padding: 1.25rem;
$profile-card-margin: 1rem 0;

$profile-card-image-width: 100%;
$profile-card-image-height: 250px;
$profile-card-image-border-radius: 0.625rem;
$profile-card-image-border: 1px solid $background-color;
$profile-card-image-margin: 0 0 0.625rem;

$form-control-label-font-family: $secondary-font-family;
$form-control-label-font-size: 0.85rem;
$form-control-label-font-weight: 400;
$form-control-label-line-height: 1rem;
$form-control-label-margin: 0.5rem 0;
$form-control-label-color: #000;
$form-control-font-family: $primary-font-family;
$form-control-font-size: 0.85rem;
$form-control-font-weight: 400;
$form-control-line-height: 1rem;
$form-control-color: #000;
$form-control-background-color: #fff;
$form-control-border-color: #3b464b;
$form-control-border: 1px solid $form-control-border-color;
$form-control-border-radius: 2px;
$form-control-padding: 0.5rem 0.75rem;
$form-control-margin: 0 0 1rem 0;
$form-control-placeholder-color: #757575;
$form-control-placeholder-font-weight: 400;
$form-control-placeholder-font-size: 1rem;
$form-control-placeholder-line-height: 1.5rem;
$form-control-switch-border-radius: 1rem;
$form-control-switch-padding: 0 0 0 1rem;

$dropdown-font-family: $secondary-font-family;
$dropdown-background-color: #fafafa;
$dropdown-border: 0;
$dropdown-border-radius: 2px;
$dropdown-padding:0;
$dropdown-margin: 0;
$dropdown-font-size: 0.85rem;
$dropdown-font-weight: 400;
$dropdown-line-height: 1rem;
$dropdown-color: $primary-font-color;
$dropdown-shadow: $shadow-elevation-1;
$dropdown-item-font-family: $dropdown-font-family;
$dropdown-item-background-color: $dropdown-background-color;
$dropdown-item-border: 0;
$dropdown-item-border-radius: $dropdown-border-radius;
$dropdown-item-padding: 0.75rem 1rem;
$dropdown-item-margin: 0;
$dropdown-item-font-size: $dropdown-font-size;
$dropdown-item-font-weight: $dropdown-font-weight;
$dropdown-item-line-height: $dropdown-line-height;
$dropdown-item-color: $dropdown-color;
$dropdown-item-hover-background-color: $primary-color;
$dropdown-item-hover-color: $primary-inverse-color;
$dropdown-item-hover-font-weight: $dropdown-font-weight;
$dropdown-item-hover-text-decoration: none;
$dropdown-item-active-background-color: $primary-color;
$dropdown-item-active-color: $primary-inverse-color;
$dropdown-item-active-font-weight: $dropdown-font-weight;
$dropdown-item-disabled-background-color: $neutral-background-color;
$dropdown-item-disabled-color: $primary-inverse-color;
$dropdown-item-disabled-font-weight: $dropdown-font-weight;

$table-margin: $main-padding 0;
$table-header-font-family: $secondary-font-family;
$table-header-font-size: 0.8rem;
$table-header-font-weight: 700;
$table-header-line-height: 1rem;
$table-header-color: $primary-font-color;
$table-header-padding: 0.5rem;
$table-header-margin: 0;
$table-header-border: 0px solid #3b464b;
$table-header-border-radius: 0;
$table-header-background-color: #b0bec5;
$table-header-text-align: left;
$table-header-shadow: $shadow-elevation-0;
$table-header-text-transform: uppercase;
$table-row-font-family: $primary-font-family;
$table-row-font-size: 0.85rem;
$table-row-font-weight: 400;
$table-row-line-height: 1.25rem;
$table-row-color: $primary-font-color;
$table-row-padding: 0.5rem;
$table-row-margin: 0;
$table-row-border: 0;
$table-row-border-bottom: 0px solid #ddd;
$table-row-border-radius: 0;
$table-row-background-color: transparent;
$table-row-text-align: left;
$table-row-shadow: $shadow-elevation-0;
$table-row-text-transform: default;
$table-row-background-color-odd: transparent;
$table-row-hover-background-color: #BFCBD0;
$table-row-hover-color: $primary-font-color;

$pagination-container-margin: 0 0 0 auto;
$pagination-container-justify: flex-end;
$pagination-height: 2.25rem;
$pagination-font-family: $secondary-font-family;
$pagination-font-size: 0.7rem;
$pagination-font-weight: 700;
$pagination-line-height: 1rem;
$pagination-color: $primary-font-color;
$pagination-padding: 0.5rem 0.75rem;
$pagination-margin: 0 0 1rem 0;
$pagination-border: 2px solid transparent;
$pagination-border-radius: 0;
$pagination-background-color: transparent;
$pagination-text-align: center;
$pagination-shadow: $shadow-elevation-0;
$pagination-text-transform: uppercase;
$pagination-hover-background-color: #fafafa;
$pagination-hover-color: $primary-font-color;
$pagination-hover-text-decoration: none;
$pagination-active-background-color: #fff;
$pagination-active-color: $primary-font-color;
$pagination-active-border: 2px solid $primary-color;
$pagination-active-shadow: $shadow-elevation-1;
$pagination-disabled-background-color: $background-color;
$pagination-disabled-color: $disabled-color;
$pagination-disabled-border: 2px solid transparent;

$tabs-font-family: $secondary-font-family;
$tabs-color: inherit;
$tabs-font-size: 0.85rem;
$tabs-font-weight: 500;
$tabs-line-height: 2rem;
$tabs-background-color: transparent;
$tabs-border-top: 1px solid transparent;
$tabs-border-bottom: 1px solid $divider-color;
$tabs-border-left: 0;
$tabs-border-right: 0;
$tabs-padding:0;
$tabs-margin: 0 0 1rem 0;

$tab-background-color: transparent;
$tab-border-top:0;
$tab-border-right:0;
$tab-border-bottom: 2px solid transparent;
$tab-border-left:0;
$tab-padding: 0;
$tab-margin: 0 1rem 0 0;
$tab-border-color: $divider-color;
$tab-active-color: inherit;
$tab-active-background-color: transparent;
$tab-active-border-color: $primary-color;
$tab-active-border-top:0;
$tab-active-border-right:0;
$tab-active-border-bottom:2px solid $primary-color;
$tab-active-border-left:0;
$tab-active-font-weight: 500;
$tab-hover-color: inherit;
$tab-hover-background-color: transparent;
$tab-hover-border-color: $divider-color;
$tab-hover-border-top:0;
$tab-hover-border-right:0;
$tab-hover-border-bottom:2px solid $divider-color;
$tab-hover-border-left:0;
$tab-hover-font-weight: 500;

$menu-font-family: $secondary-font-family;
$menu-background-color: #3B464B;
$menu-width: 250px;
$menu-box-shadow: $shadow-elevation-3;
$menu-hover-background-color: $primary-color;
$menu-hover-color: #fff;
$menu-active-background-color: $primary-color;
$menu-active-color: #fff;
$menu-active-hover-background-color: $primary-hover-color;
$menu-item-background-color: transparent;
$menu-item-color: #eee;
$menu-item-border-radius: 0;
$menu-divider-color: #3B464B;
$menu-scrollbar-color: #293134;
$menu-scrollbar-background-color: #353F43;
$menu-scrollbar-width: 5px;
$menu-scrollbar-border-radius: 0px;

$date-picker-day-border-radius: 0;

$alert-background-color: #000;
$alert-font-color: #eee;

$pos-menu-width: 300px;
$pos-secondary-color: #757D81;
$pos-column-color:#3B464B;

$cms-drop-ready-background-color: inherit;
$cms-drop-background-color: inherit; 

$water-mark-filter: grayscale(100%);

div.error-boundary-fallback.card,
div.description-banner > *,
.rel-title{
    color:#eee !important;
}

.prof-he-line {
    border-color:#3b464b !important;
}

div.prof-bg{
    hr.prof-he-line {
        border-color:$divider-color !important;
    }
}

div.pos-main-row,
div.pos-container{

    div.preview-container,
    div.user-container{
        background-color: #eee !important;
    }

}