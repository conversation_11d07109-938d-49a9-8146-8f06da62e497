@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';
@import '../../../assets/css/scss/mixins';

.character-counter-input-wrapper{
    &.column{
        display: flex;
        flex-direction: column;
        .count-limit{
            display: flex;
            justify-content: flex-end;
        }
    }
    &.row{
        @include basic-flex-row;
        flex-wrap: nowrap;
    }
}
