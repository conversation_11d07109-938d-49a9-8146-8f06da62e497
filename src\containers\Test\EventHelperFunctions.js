import { differenceInYears } from 'date-fns';
import Groups from '../../api/Groups';

/** We need this because the other calls for group don't include the group member roles to know when the user is an admin or not becuase it's different for each group type and we don't wnat to hardcode */
    export const getIndividualFamily=async(groupId)=>{
        try{
            let response = await Groups.get({id: groupId})
            if(response.data[0]) return response.data[0]
        }catch(ex){
            console.error(ex)
        }
    }

/**If need be, we make a new family */
    export const createNewFamilyGroup=async(user, getFamily)=>{
        try{
            //this should, in theory, add the auth user because the group_member_role_id is included (according to the docs)
            let response = await Groups.create({
                name: user?.last_name + " Family",
                description: "Family Group",
                group_type_id: 4,
                group_status_id: 1,
                group_member_role_id: 10
            })
            if(response.status === 200) getFamily();
        }
        catch(ex){
            console.error(ex)
        }
    }

/** Check if the user is allowed to manage the family */
    export const checkFamilyAllowedByRole=async(family, user)=>{
        //the status of the user logged in for the family being checked
        let currentUserStatusId = family.group_members?.filter((member)=>member.user_id === user.id)[0]?.group_member_role_id; 
        //if the role for that group type is an admin;
        if(family?.group_member_roles?.filter((role)=>role.id === currentUserStatusId)[0]?.is_admin === 1) return true;
        else return false;  
    }

/**Check if the user is allowed by age*/
    export const checkAllowedAge = (user, min, max)=>{
        const age = differenceInYears(new Date(), new Date(user.dob));
        let allowed={
            isAllowed: true,
            notAllowedReason: ""
        };
        if(min || max){
            if(min && max){
                allowed.isAllowed = age >= min && age <= max ? true : false;
                allowed.notAllowedReason = "Participant is not within the age range";
            }else if(min){
                allowed.isAllowed = age >= min ? true : false;
                allowed.notAllowedReason = "Participant is below the age limit";
            }else if(max){
                allowed.isAllowed = age <= max ? true : false;
                allowed.notAllowedReason = "Participant is above the age limit";
            }else allowed.isAllowed = true;
        }
        return allowed;
    }

/**Check to see if a user is already registered for an event */    
    export const checkIsRegistered = (user, eventDetails)=> {
        let isRegistered = eventDetails?.advancedDetails?.users?.filter((member)=> member.id === user.user_id)
        if(isRegistered?.length > 0) return true;
        else return false;
    }
    
//**To get and sort and filter and modify the family */
    export const getFamily=async(user, loadingStates)=>{
        loadingStates.current.family = true;
        let familyGroups = [];
        try{
            let response = await Groups.groupFilter({filters:{user_id: user.id}})
            //get rid of the groups that are not family and the user doesn't have active status
            familyGroups = response.data.groups?.filter((group)=>group.group_type_name === "Family" && group?.group_status_id === 1)
            //if we don't have a family, we make one
            if(familyGroups.length === 0) await createNewFamilyGroup(user, getFamily);
            else{
                let finalGroups = []
                for(let i = 0; i < familyGroups.length; i++){
                    console.log(familyGroups[i])
                    let extraDetails = await getIndividualFamily(familyGroups[i].id)
                    familyGroups[i].tags = extraDetails?.tags || [];
                    familyGroups[i].group_member_roles = extraDetails.group_member_roles || [];
                    //check permission to manage the family (is_admin) first
                    if(await checkFamilyAllowedByRole(familyGroups[i], user)){
                        finalGroups.push(familyGroups[i])
                    }
                }
                loadingStates.current.family = false;
                return finalGroups;
            }
        }catch(ex){
            console.error(ex)
            loadingStates.current.family = "ERROR";
        }
    }

/** Adding details to each family members */
    export const eachFamilyMember = async(checkedFamily, i, eventDetails)=>{
        let memberDetails;
        let memberArray = []
        try{
            checkedFamily?.group_members.forEach((member)=>{
                memberDetails = {
                    key: i + 2,
                    id: member.user_id,
                    firstName: member.first_name,
                    lastName: member.last_name,
                    name: member.first_name + " " + member.last_name,
                    isSelf: false,
                    roleName: member.group_member_role_name,
                    ageAllowed: checkAllowedAge(member, eventDetails.min_age, eventDetails.max_age),
                    registered: checkIsRegistered(member, eventDetails)
                }
                memberArray.push(memberDetails)
            })
            
            return memberArray;
        }catch(ex){
            console.error(ex)
        }
    }

/** Adding initial user and then cycling through and adding details to each family member */
    export const checkFamilyStatuses=async(loadingStates, user, family, eventDetails)=>{
        loadingStates.current.family = true;
        let familyMembers = [];
        //at the very least, self will be added to the family array
        familyMembers.push({
            key: 1,
            id: user.id,
            firstName: user.first_name,
            lastName: user.last_name,
            name: user.first_name + " " + user.last_name,
            isSelf: true,
            ageAllowed: checkAllowedAge(user, eventDetails.min_age, eventDetails.max_age),
            registered: checkIsRegistered(user, eventDetails),
        });

        //we need to know if this family member has the credentials to register their family for the event.
        for(let i = 0; i < family.length; i++){
            //need to get the family
            let familyMember = await eachFamilyMember(family[i], i, eventDetails)
            if(familyMember?.length) familyMembers=([...familyMembers, ...familyMember])
        }
        //filter by unique
        familyMembers = familyMembers.filter((object, index, self) =>
            index === self.findIndex((member) => member.id === object.id)
        );
        loadingStates.current.family = false;
        return familyMembers
    }