import React, {useEffect} from 'react';

export const Step1 = (props) => {
    const {click} = props;

    useEffect(() => {
        if (props?.requires_registration === 1 && !props.currentStep){
            click({
                stopPropagation: () => {},
                preventDefault: () => {}
            }, 1);
        }
    }, [props?.requires_registration, props.currentStep, click]);

    return (
        <>
            <p dangerouslySetInnerHTML={{__html:props.description}} />
            <div>
                <p>
                    <label className="form-label">When</label><br/>
                    {props.date}
                </p>
                {props.location_name &&
                    <p>
                        <label className="form-label">Where</label><br/>
                        {props.location_name}
                    </p>
                }
                {props.default_variant_price && 
                    <p>
                        <label className="form-label">Event Fee</label><br/> 
                        ${props.default_variant_price}
                    </p>
                }
                <p>
                    {props.requires_registration !==1 && <span className="bold">No registration required</span> }
                    {props.requires_membership === 1 && <span className="bold">Requires membership to attend</span>}
                </p>
            </div>
        </>
    );
}