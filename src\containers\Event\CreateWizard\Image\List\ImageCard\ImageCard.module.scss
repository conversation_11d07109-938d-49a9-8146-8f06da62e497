@import '../../../../../../assets/css/scss/themes.scss';


.card{
    border: 1px dashed $disabled-color;
    padding: calc($main-padding / 4);
    margin-bottom: .5rem;
    cursor: move;
    align-items: center;

    &:first-child{
        //background-color: $neutral-background-color;
        position: relative;

        &::after{
            content: 'Main Image';
            position: absolute;
            top: 0;
            right: 0;
            font-size: $badge-font-size;
            font-family: $badge-font-family;
            background-color: $badge-background-color;
            color: $badge-color;
            padding: $badge-padding;
        }
    }

    &.drop{
        background-color: $primary-light-color;
        
        &:first-child{
            //background-color: $neutral-background-color;
        }

        *{
            opacity: 0;
        }
    }

    &.droparea{
        display:flex;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        height: 90px;
    }

    .image{
        height: 80px;
        width: 80px;
        object-fit: cover;
    }
}