import React, { useState, useEffect } from 'react';
import { Modal, Button } from 'react-bootstrap';
import { Reports } from '../../../containers/Reports/Reports';

/**
 * This component is meant to be used to add a report modal for a single report to another page
 * @param {*} reportsAvailableModuleIds send in an array of module ids that you want to be available
 * @param {*} show when to show the modal
 * @param {*} onClose what to do when the modal is closed
 * @param {*} hidePageElements Hides the bits of the report page such as breadcrumbs from view.  Should probably always be true, but just in case
 */
export const ReportModal = ({
    reportsAvailableModuleIds,
    show,
    onClose,
    hidePageElements=true
}) => {

    return(
        <Modal show={show} onHide={onClose}>
            <Modal.Header closeButton>
                
            </Modal.Header>
            <Modal.Body>
                <Reports 
                    reportsAvailableModuleIds={reportsAvailableModuleIds} 
                    hidePageElements={hidePageElements} 
                />
            </Modal.Body>
        </Modal>
    )
}