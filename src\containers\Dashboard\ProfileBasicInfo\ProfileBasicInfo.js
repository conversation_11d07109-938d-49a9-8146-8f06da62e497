import React, { useContext } from 'react';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';

import { FileURLContext } from '../../../contexts/FileURLContext';

export const ProfileBasicInfo = (props) => {
    //let history = useHistory();

    const imageURL = useContext(FileURLContext);
    
    return (
        <React.Fragment>
            <div className="">
                    <Row>
                        <Col md="4">
                            <img src={imageURL.base+'profile.jpeg'} alt="IMPACT ATHLETICS" />
                            </Col>
                            <Col md="8">
                            <h5 className="profile-card-name"><PERSON></h5>
                            <br />
                            <div className="profile-card-info">Member,</div>
                            <div className="profile-card-info">Head of jones Family</div>
                            <div className="profile-card-info">COach of Albamy city Rocks</div>
                        </Col>
                    </Row>
            </div>
        </React.Fragment>
    );
}